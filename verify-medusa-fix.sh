#!/bin/bash

# Comprehensive Medusa v2 API Fix Verification Script
# This script verifies that the tenant setup and API endpoints are working correctly

set -e  # Exit on any error

BACKEND_URL="http://localhost:9000"
TENANT_ID="my-kirana-store"
API_KEY="pk_3d67561dece2d466dc798c18c1f80523f84f3b2f01316e0bf915e51f3a59b98b"
DB_HOST="localhost"
DB_USER="strapi"
DB_NAME="medusa_backend"

echo "🔧 Medusa v2 Multi-Tenant API Fix Verification"
echo "=============================================="
echo "Backend URL: $BACKEND_URL"
echo "Tenant ID: $TENANT_ID"
echo "Database: $DB_HOST/$DB_NAME"
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to test API endpoint
test_endpoint() {
    local endpoint="$1"
    local description="$2"
    
    echo "🧪 Testing $description"
    echo "   Endpoint: $endpoint"
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        --location "$BACKEND_URL$endpoint" \
        --header "Accept: application/json" \
        --header "Content-Type: application/json" \
        --header "x-tenant-id: $TENANT_ID" \
        --header "x-publishable-api-key: $API_KEY")
    
    http_code=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo "$response" | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        echo "   ✅ Status: $http_code (Success)"
        
        # Try to parse JSON and count items
        if command_exists jq; then
            count=$(echo "$body" | jq -r '.count // .product_categories // .collections // .tags | length // 0' 2>/dev/null || echo "0")
            echo "   📊 Items returned: $count"
            
            # Show sample data if available
            if [ "$count" -gt 0 ]; then
                sample=$(echo "$body" | jq -r '.product_categories[0].name // .collections[0].title // .tags[0].value // "N/A"' 2>/dev/null || echo "N/A")
                echo "   📝 Sample item: $sample"
            fi
        else
            echo "   📄 Response length: ${#body} characters"
        fi
    else
        echo "   ❌ Status: $http_code (Error)"
        if command_exists jq; then
            error_msg=$(echo "$body" | jq -r '.message // .error // "Unknown error"' 2>/dev/null || echo "Parse error")
            echo "   💬 Error: $error_msg"
        fi
    fi
    echo ""
}

# Step 1: Check if backend is running
echo "🏥 Checking Backend Health"
echo "--------------------------"
if curl -s "$BACKEND_URL/health" >/dev/null 2>&1; then
    echo "✅ Backend is running on $BACKEND_URL"
else
    echo "❌ Backend is not responding on $BACKEND_URL"
    echo "   Please ensure Medusa backend is running"
    exit 1
fi
echo ""

# Step 2: Check database connection
echo "🗄️  Checking Database Connection"
echo "--------------------------------"
if command_exists psql; then
    if PGPASSWORD="strapi_password" psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" >/dev/null 2>&1; then
        echo "✅ Database connection successful"
        
        # Check if tenant exists
        tenant_exists=$(PGPASSWORD="strapi_password" psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM tenant_config WHERE id = '$TENANT_ID';" 2>/dev/null | tr -d ' ')
        
        if [ "$tenant_exists" -eq 1 ]; then
            echo "✅ Tenant '$TENANT_ID' exists in database"
        else
            echo "❌ Tenant '$TENANT_ID' not found in database"
            echo "   Run: psql -h $DB_HOST -U $DB_USER -d $DB_NAME -f fix-tenant-setup.sql"
            exit 1
        fi
        
        # Check if tenant has data
        category_count=$(PGPASSWORD="strapi_password" psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM product_category WHERE tenant_id = '$TENANT_ID' AND is_active = true AND is_internal = false;" 2>/dev/null | tr -d ' ')
        echo "📊 Active categories for tenant: $category_count"
        
    else
        echo "❌ Cannot connect to database"
        echo "   Please check database credentials and ensure PostgreSQL is running"
    fi
else
    echo "⚠️  psql not found, skipping database checks"
fi
echo ""

# Step 3: Test API Endpoints
echo "🌐 Testing API Endpoints"
echo "------------------------"

test_endpoint "/store/product-categories" "Product Categories"
test_endpoint "/store/collections" "Product Collections"  
test_endpoint "/store/tags" "Product Tags"
test_endpoint "/store/products?limit=5" "Products (Sample)"

# Step 4: Test with different parameters
echo "🔍 Testing with Parameters"
echo "--------------------------"

test_endpoint "/store/product-categories?limit=10&offset=0" "Categories with Pagination"
test_endpoint "/store/product-categories?is_active=true" "Active Categories Only"

# Step 5: Summary
echo "📋 Verification Summary"
echo "----------------------"
echo "If all tests passed, your Medusa v2 multi-tenant API is working correctly!"
echo ""
echo "🔧 Troubleshooting Tips:"
echo "- If you get 403 errors: Check tenant exists and is active"
echo "- If you get empty responses: Check if tenant has data"
echo "- If you get 500 errors: Check backend logs for detailed errors"
echo "- If you get connection errors: Ensure backend is running on port 9000"
echo ""
echo "📚 Next Steps:"
echo "- Add more sample data using the seed scripts"
echo "- Test with your frontend application"
echo "- Monitor backend logs for any issues"
echo ""
echo "✅ Verification Complete!"
