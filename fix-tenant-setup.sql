-- Fix Medusa v2 Multi-Tenant API Issue
-- This script creates the missing tenant and sample data

-- ============================================================================
-- 1. CREATE MISSING TENANT
-- ============================================================================

INSERT INTO "tenant_config" (id, name, domain, status, settings, created_at, updated_at) 
VALUES (
  'my-kirana-store', 
  'My Kirana Store', 
  'my-kirana-store.localhost', 
  'active', 
  '{"currency": "INR", "timezone": "Asia/Kolkata", "features": ["products", "orders", "customers", "analytics", "inventory"]}',
  NOW(), 
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  domain = EXCLUDED.domain,
  status = EXCLUDED.status,
  settings = EXCLUDED.settings,
  updated_at = NOW();

-- ============================================================================
-- 2. CREATE SAMPLE PRODUCT CATEGORIES
-- ============================================================================

INSERT INTO "product_category" (id, name, description, handle, is_active, is_internal, tenant_id, created_at, updated_at) VALUES 
('cat_kirana_001', 'Groceries', 'Fresh groceries and daily essentials', 'groceries', true, false, 'my-kirana-store', NOW(), NOW()),
('cat_kirana_002', 'Vegetables', 'Fresh vegetables and greens', 'vegetables', true, false, 'my-kirana-store', NOW(), NOW()),
('cat_kirana_003', 'Fruits', 'Fresh seasonal fruits', 'fruits', true, false, 'my-kirana-store', NOW(), NOW()),
('cat_kirana_004', 'Dairy Products', 'Milk, cheese, yogurt and dairy items', 'dairy-products', true, false, 'my-kirana-store', NOW(), NOW()),
('cat_kirana_005', 'Snacks', 'Chips, biscuits and snack items', 'snacks', true, false, 'my-kirana-store', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  handle = EXCLUDED.handle,
  is_active = EXCLUDED.is_active,
  is_internal = EXCLUDED.is_internal,
  tenant_id = EXCLUDED.tenant_id,
  updated_at = NOW();

-- ============================================================================
-- 3. CREATE SAMPLE PRODUCT COLLECTIONS
-- ============================================================================

INSERT INTO "product_collection" (id, title, handle, tenant_id, created_at, updated_at) VALUES 
('coll_kirana_001', 'Daily Essentials', 'daily-essentials', 'my-kirana-store', NOW(), NOW()),
('coll_kirana_002', 'Fresh Produce', 'fresh-produce', 'my-kirana-store', NOW(), NOW()),
('coll_kirana_003', 'Breakfast Items', 'breakfast-items', 'my-kirana-store', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  title = EXCLUDED.title,
  handle = EXCLUDED.handle,
  tenant_id = EXCLUDED.tenant_id,
  updated_at = NOW();

-- ============================================================================
-- 4. CREATE SAMPLE PRODUCT TAGS (if product_tag table has tenant_id)
-- ============================================================================

-- Check if product_tag table exists and has tenant_id column
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'product_tag' AND column_name = 'tenant_id'
  ) THEN
    INSERT INTO "product_tag" (id, value, tenant_id, created_at, updated_at) VALUES 
    ('tag_kirana_001', 'organic', 'my-kirana-store', NOW(), NOW()),
    ('tag_kirana_002', 'fresh', 'my-kirana-store', NOW(), NOW()),
    ('tag_kirana_003', 'local', 'my-kirana-store', NOW(), NOW()),
    ('tag_kirana_004', 'premium', 'my-kirana-store', NOW(), NOW())
    ON CONFLICT (id) DO UPDATE SET
      value = EXCLUDED.value,
      tenant_id = EXCLUDED.tenant_id,
      updated_at = NOW();
  END IF;
END $$;

-- ============================================================================
-- 5. VERIFY DATA CREATION
-- ============================================================================

-- Check tenant creation
SELECT 'TENANT CHECK' as check_type, id, name, status FROM "tenant_config" WHERE id = 'my-kirana-store';

-- Check categories
SELECT 'CATEGORIES CHECK' as check_type, COUNT(*) as count FROM "product_category" WHERE tenant_id = 'my-kirana-store';

-- Check collections  
SELECT 'COLLECTIONS CHECK' as check_type, COUNT(*) as count FROM "product_collection" WHERE tenant_id = 'my-kirana-store';

-- List created categories
SELECT 'CATEGORY LIST' as check_type, id, name, handle, is_active FROM "product_category" 
WHERE tenant_id = 'my-kirana-store' ORDER BY name;

-- List created collections
SELECT 'COLLECTION LIST' as check_type, id, title, handle FROM "product_collection" 
WHERE tenant_id = 'my-kirana-store' ORDER BY title;
