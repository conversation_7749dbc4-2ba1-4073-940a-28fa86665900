#!/bin/bash

# Test Medusa v2 Store API Endpoints
# Make sure your Medusa backend is running on localhost:9000

BACKEND_URL="http://localhost:9000"
TENANT_ID="my-kirana-store"
API_KEY="pk_3d67561dece2d466dc798c18c1f80523f84f3b2f01316e0bf915e51f3a59b98b"

echo "🚀 Testing Medusa v2 Store API Endpoints"
echo "=========================================="
echo "Backend URL: $BACKEND_URL"
echo "Tenant ID: $TENANT_ID"
echo ""

# Test 1: Product Categories
echo "📁 Testing /store/product-categories"
echo "------------------------------------"
curl -s --location "$BACKEND_URL/store/product-categories" \
  --header "Accept: application/json" \
  --header "Content-Type: application/json" \
  --header "x-tenant-id: $TENANT_ID" \
  --header "x-publishable-api-key: $API_KEY" | jq '.' || echo "❌ Failed to parse JSON response"

echo -e "\n"

# Test 2: Product Collections
echo "📦 Testing /store/collections"
echo "------------------------------"
curl -s --location "$BACKEND_URL/store/collections" \
  --header "Accept: application/json" \
  --header "Content-Type: application/json" \
  --header "x-tenant-id: $TENANT_ID" \
  --header "x-publishable-api-key: $API_KEY" | jq '.' || echo "❌ Failed to parse JSON response"

echo -e "\n"

# Test 3: Product Tags
echo "🏷️  Testing /store/tags"
echo "------------------------"
curl -s --location "$BACKEND_URL/store/tags" \
  --header "Accept: application/json" \
  --header "Content-Type: application/json" \
  --header "x-tenant-id: $TENANT_ID" \
  --header "x-publishable-api-key: $API_KEY" | jq '.' || echo "❌ Failed to parse JSON response"

echo -e "\n"

# Test 4: Products (if available)
echo "🛍️  Testing /store/products"
echo "----------------------------"
curl -s --location "$BACKEND_URL/store/products?limit=5" \
  --header "Accept: application/json" \
  --header "Content-Type: application/json" \
  --header "x-tenant-id: $TENANT_ID" \
  --header "x-publishable-api-key: $API_KEY" | jq '.' || echo "❌ Failed to parse JSON response"

echo -e "\n"

# Test 5: Health Check
echo "❤️  Testing Backend Health"
echo "---------------------------"
curl -s --location "$BACKEND_URL/health" | jq '.' || echo "❌ Backend health check failed"

echo -e "\n"
echo "✅ API Testing Complete!"
echo ""
echo "💡 Tips:"
echo "- If you get 403 errors, check if tenant exists in database"
echo "- If you get empty responses, check if data exists for the tenant"
echo "- Check backend logs for detailed error information"
echo "- Ensure Medusa backend is running on port 9000"
