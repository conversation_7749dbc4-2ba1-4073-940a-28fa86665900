-- Debug Medusa v2 Multi-Tenant API Issues
-- Run this to check database state and identify problems

-- ============================================================================
-- 1. <PERSON><PERSON><PERSON> TENANT CONFIGURATION
-- ============================================================================

SELECT 
  '=== TENANT CONFIGURATION ===' as section,
  id as tenant_id,
  name,
  domain,
  status,
  settings,
  created_at
FROM "tenant_config" 
ORDER BY created_at;

-- ============================================================================
-- 2. CHECK PRODUCT CATEGORIES BY TENANT
-- ============================================================================

SELECT 
  '=== PRODUCT CATEGORIES BY TENANT ===' as section,
  tenant_id,
  COUNT(*) as category_count,
  COUNT(CASE WHEN is_active = true THEN 1 END) as active_count,
  COUNT(CASE WHEN is_internal = false THEN 1 END) as public_count
FROM "product_category" 
GROUP BY tenant_id
ORDER BY tenant_id;

-- ============================================================================
-- 3. CHECK SPECIFIC TENANT DATA
-- ============================================================================

-- Categories for my-kirana-store
SELECT 
  '=== MY-KIRANA-STORE CATEGORIES ===' as section,
  id,
  name,
  handle,
  is_active,
  is_internal,
  parent_category_id,
  created_at
FROM "product_category" 
WHERE tenant_id = 'my-kirana-store'
ORDER BY name;

-- Collections for my-kirana-store
SELECT 
  '=== MY-KIRANA-STORE COLLECTIONS ===' as section,
  id,
  title,
  handle,
  created_at
FROM "product_collection" 
WHERE tenant_id = 'my-kirana-store'
ORDER BY title;

-- ============================================================================
-- 4. CHECK DATABASE SCHEMA
-- ============================================================================

-- Check if required columns exist
SELECT 
  '=== SCHEMA CHECK ===' as section,
  table_name,
  column_name,
  data_type,
  is_nullable
FROM information_schema.columns 
WHERE table_name IN ('product_category', 'product_collection', 'product_tag', 'tenant_config')
  AND column_name IN ('tenant_id', 'id', 'is_active', 'is_internal')
ORDER BY table_name, column_name;

-- ============================================================================
-- 5. CHECK INDEXES
-- ============================================================================

-- Check tenant-related indexes
SELECT 
  '=== INDEX CHECK ===' as section,
  schemaname,
  tablename,
  indexname,
  indexdef
FROM pg_indexes 
WHERE indexname LIKE '%tenant%' 
   OR indexname LIKE '%category%'
   OR indexname LIKE '%collection%'
ORDER BY tablename, indexname;

-- ============================================================================
-- 6. CHECK FOR COMMON ISSUES
-- ============================================================================

-- Check for categories without tenant_id
SELECT 
  '=== ORPHANED CATEGORIES ===' as section,
  COUNT(*) as count
FROM "product_category" 
WHERE tenant_id IS NULL OR tenant_id = '';

-- Check for inactive tenants
SELECT 
  '=== INACTIVE TENANTS ===' as section,
  id,
  name,
  status
FROM "tenant_config" 
WHERE status != 'active';

-- Check for internal categories (should not appear in store API)
SELECT 
  '=== INTERNAL CATEGORIES ===' as section,
  tenant_id,
  COUNT(*) as internal_count
FROM "product_category" 
WHERE is_internal = true
GROUP BY tenant_id;

-- ============================================================================
-- 7. SAMPLE QUERIES FOR TESTING
-- ============================================================================

-- This is what the store API should return for my-kirana-store
SELECT 
  '=== STORE API SIMULATION ===' as section,
  id,
  name,
  description,
  handle,
  is_active,
  parent_category_id
FROM "product_category" 
WHERE tenant_id = 'my-kirana-store'
  AND is_active = true
  AND is_internal = false
ORDER BY name
LIMIT 50;
