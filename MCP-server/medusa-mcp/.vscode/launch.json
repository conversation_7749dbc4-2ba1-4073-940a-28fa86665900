{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Program",
            "program": "${workspaceFolder}\\node_modules\\@modelcontextprotocol\\inspector\\bin\\cli.js",
            "console": "integratedTerminal",
            "preLaunchTask": "npm: build",
            "sourceMaps": true,
            "args": [
                "server",
                "dist/index.js",
            ],
            "request": "launch",
            
            "skipFiles": [
                "<node_internals>/**"
            ],
            "type": "node"
        },

    ]
}