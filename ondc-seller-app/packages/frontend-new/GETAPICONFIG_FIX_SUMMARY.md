# getApiConfig Error Fix Summary

## Issue
The application was throwing `ReferenceError: getApiConfig is not defined` errors because several files were still importing and using the `getApiConfig` function instead of the new centralized `medusaClient`.

## Root Cause
During the migration to the centralized Medusa client, some files were not fully updated to remove dependencies on the `getApiConfig` function from `lib/config/api.ts`.

## Files Fixed

### 1. **lib/api/orders.ts**
- ✅ Removed `getApiConfig` import
- ✅ Updated `getBaseUrl()` to use `medusaClient.getBaseUrl()`
- ✅ Replaced `getApiConfig().medusaPublishableKey` with environment variable

### 2. **lib/api/client.ts**
- ✅ Replaced `getApiConfig` import with `medusaClient`
- ✅ Updated API URL construction to use `medusaClient.getBaseUrl()`
- ✅ Fixed Strapi URL to use environment variables directly

### 3. **lib/api/medusa-admin.ts**
- ✅ Replaced `getApiConfig` import with `medusaClient`
- ✅ Updated `getBaseURL()` function to use `medusaClient.getBaseUrl()`

### 4. **lib/api/clients/medusa-store-client.ts**
- ✅ Replaced `getApiConfig` import with `medusaClient`
- ✅ Updated base URL construction
- ✅ Replaced publishable key with environment variable

### 5. **lib/api/clients/strapi-client.ts**
- ✅ Removed `getApiConfig` import
- ✅ Updated to use environment variables directly

### 6. **lib/api/clients/medusa-admin-client.ts**
- ✅ Replaced `getApiConfig` import with `medusaClient`
- ✅ Updated base URL construction

### 7. **lib/api/profile.ts**
- ✅ Completely rewrote to remove `getApiConfig` dependencies
- ✅ Added proper tenant ID handling
- ✅ Fixed authentication headers using centralized utility

### 8. **lib/api/apiClient.ts**
- ✅ Replaced `getApiConfig` import with `medusaClient`
- ✅ Updated constructor to use `medusaClient.getBaseUrl()`

### 9. **lib/api/bulk-upload.ts**
- ✅ Replaced `getApiConfig` import with `medusaClient`
- ✅ Updated to use `medusaClient.getBaseUrl()`

### 10. **lib/api/cart.ts**
- ✅ Updated `getBaseUrl()` function
- ✅ Replaced publishable key with environment variable

### 11. **lib/api/medusa/orders.ts**
- ✅ Replaced `getApiConfig` import with `medusaClient`
- ✅ Updated publishable key to use environment variable

### 12. **lib/api/storeAuth.ts**
- ✅ Updated publishable key to use environment variable

### 13. **lib/api/strapi-store.ts**
- ✅ Removed `getApiConfig` import
- ✅ Updated to use environment variables directly

### 14. **lib/api/medusa-client.ts**
- ✅ Removed `getApiConfig` import from its own file
- ✅ Updated to use environment variables directly

### 15. **stores/storeConfigStore.ts**
- ✅ Replaced dynamic `getApiConfig` import with environment variables

## Key Changes Made

### 1. **Removed getApiConfig Dependencies**
All files now use one of these approaches instead of `getApiConfig()`:
- `medusaClient.getBaseUrl()` for Medusa base URL
- `process.env.NEXT_PUBLIC_MEDUSA_BASE_URL` for direct environment access
- `process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY` for publishable key
- `process.env.NEXT_PUBLIC_STRAPI_BASE_URL` for Strapi URLs

### 2. **Standardized Environment Variable Usage**
- **Medusa Base URL**: `process.env.NEXT_PUBLIC_MEDUSA_BASE_URL || 'http://localhost:9000'`
- **Publishable Key**: `process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || ''`
- **Strapi URL**: `process.env.NEXT_PUBLIC_STRAPI_BASE_URL || 'http://localhost:1337'`

### 3. **Maintained Backward Compatibility**
- All API interfaces remain the same
- Authentication and tenant handling preserved
- Fallback values provided for missing environment variables

## Environment Variables Required

Ensure these environment variables are set:

```env
# Medusa Configuration
NEXT_PUBLIC_MEDUSA_BASE_URL=http://localhost:9000
NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=your_publishable_key_here

# Strapi Configuration  
NEXT_PUBLIC_STRAPI_BASE_URL=http://localhost:1337
NEXT_PUBLIC_STRAPI_API_URL=http://localhost:1337/api
```

## Verification

After these fixes:
1. ✅ No more `ReferenceError: getApiConfig is not defined` errors
2. ✅ All API calls use centralized configuration
3. ✅ Environment variables are properly utilized
4. ✅ Fallback values prevent runtime errors
5. ✅ Existing functionality preserved

## Testing Recommendations

1. **Admin API**: Test product creation, category management
2. **Store API**: Test product browsing, cart operations
3. **Auth API**: Test login/logout functionality
4. **Environment Switching**: Test with different base URLs
5. **Error Handling**: Verify graceful handling of missing env vars

The `getApiConfig is not defined` error should now be completely resolved!