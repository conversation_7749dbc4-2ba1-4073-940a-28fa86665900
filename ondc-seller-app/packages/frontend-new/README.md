# Multi-Tenant E-commerce Frontend

A production-ready Next.js 14 frontend application for multi-tenant e-commerce platform with Material-UI components, complete cart system, and tenant isolation.

## 🚀 Quick Start

### Prerequisites
- Node.js v18 or higher
- npm or pnpm package manager
- Backend API running (see backend-new/medusa-backend)

### Installation
```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

The frontend will be available at `http://localhost:3000`

## 🏗 Architecture

### Technology Stack
- **Framework:** Next.js 14 with App Router
- **UI Library:** Material-UI (MUI) v7 with Material Design 3
- **Styling:** Tailwind CSS for utility classes
- **State Management:** Zustand for global state
- **Forms:** React Hook Form with Zod validation
- **HTTP Client:** Axios for API communication
- **Charts:** Recharts for analytics dashboards
- **Rich Text:** Lexical editor for content management

### Key Features
- **Multi-tenant Architecture:** Complete tenant isolation and branding
- **Responsive Design:** Mobile-first responsive UI components
- **Dynamic Theming:** Tenant-specific color palettes and branding
- **Admin Dashboard:** Comprehensive admin interface for store management
- **E-commerce Features:** Cart, checkout, order management, product catalog
- **Authentication:** JWT-based authentication with tenant context

## 📁 Project Structure

```
frontend-new/
├── app/                     # Next.js App Router pages
│   ├── [storeHandle]/       # Tenant-specific store pages
│   ├── admin/               # Admin dashboard pages
│   ├── login/               # Authentication pages
│   └── providers/           # Global providers
├── components/              # Reusable React components
│   ├── admin/               # Admin-specific components
│   ├── auth/                # Authentication components
│   ├── cart/                # Shopping cart components
│   ├── checkout/            # Checkout flow components
│   ├── product/             # Product display components
│   ├── store/               # Storefront components
│   └── ui/                  # Base UI components
├── hooks/                   # Custom React hooks
├── lib/                     # Utility libraries and API clients
├── stores/                  # Zustand state stores
├── types/                   # TypeScript type definitions
├── styles/                  # Global styles and themes
└── validators/              # Zod validation schemas
```

## 🛠 Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
```

## 🎨 Theming System

### Dynamic Theming
The application supports tenant-specific theming with automatic color palette generation:

```typescript
// Store theme configuration
const storeTheme = {
  primary: '#1976d2',
  secondary: '#dc004e',
  accent: '#9c27b0',
  background: '#ffffff',
  surface: '#f5f5f5'
}
```

### Material Design 3
- Uses MUI v7 with Material Design 3 components
- Dynamic color system with tenant-specific palettes
- Responsive breakpoints and spacing system
- Consistent typography and elevation

## 🏪 Multi-Tenant Features

### Tenant Isolation
- **URL Structure:** `/{storeHandle}/...` for tenant-specific pages
- **API Headers:** Automatic `x-tenant-id` header injection
- **Data Isolation:** Complete separation of tenant data
- **Branding:** Tenant-specific logos, colors, and themes

### Store Management
- **Admin Dashboard:** Tenant-scoped admin interface
- **Product Management:** Tenant-specific product catalogs
- **Order Management:** Tenant-isolated order processing
- **Customer Management:** Tenant-specific customer data

## 🛒 E-commerce Features

### Shopping Cart
- **Persistent Cart:** Local storage with API synchronization
- **Multi-variant Support:** Product variants and options
- **Quantity Management:** Add, update, remove items
- **Tenant Isolation:** Cart data isolated per tenant

### Checkout Process
- **Multi-step Checkout:** Contact info, shipping, payment
- **Address Management:** Save and select addresses
- **Payment Integration:** Multiple payment method support
- **Order Confirmation:** Complete order processing

### Product Catalog
- **Product Listing:** Grid and list views with filtering
- **Product Details:** Image galleries, variants, specifications
- **Categories:** Hierarchical category navigation
- **Search:** Product search with filters and sorting

## 🔐 Authentication

### Admin Authentication
- **JWT Tokens:** Secure token-based authentication
- **Role-based Access:** Admin and user role management
- **Tenant Context:** Authentication scoped to tenant

### Store Authentication
- **Customer Accounts:** Customer registration and login
- **Guest Checkout:** Support for guest users
- **Session Management:** Secure session handling

## 📱 Responsive Design

### Breakpoints
- **Mobile:** 0-599px
- **Tablet:** 600-959px
- **Desktop:** 960px+

### Components
- All components are fully responsive
- Mobile-first design approach
- Touch-friendly interfaces
- Optimized for all screen sizes

## 🔧 Configuration

### Environment Variables
```env
NEXT_PUBLIC_MEDUSA_BACKEND_URL=http://localhost:9000
NEXT_PUBLIC_STORE_URL=http://localhost:3000
```

### API Integration
- **Medusa Backend:** Complete integration with Medusa v2 APIs
- **Multi-tenant Headers:** Automatic tenant context in API calls
- **Error Handling:** Comprehensive error handling and user feedback
- **Loading States:** Loading indicators for all async operations

## 🚀 Deployment

### Production Build
```bash
# Build the application
npm run build

# Start production server
npm run start
```

### Environment Setup
1. Set production environment variables
2. Configure API endpoints
3. Set up CDN for static assets
4. Configure domain routing for multi-tenancy

### Deployment Platforms
- **Vercel:** Recommended for Next.js applications
- **Netlify:** Alternative deployment platform
- **Docker:** Containerized deployment option

## 🧪 Development Guidelines

### Code Style
- **TypeScript:** Strict type checking enabled
- **ESLint:** Code linting and formatting
- **Component Structure:** Consistent component organization
- **Naming Conventions:** Clear and descriptive naming

### State Management
- **Zustand:** For global application state
- **React Hook Form:** For form state management
- **React Query:** For server state management (if needed)

### Testing
- Write unit tests for utility functions
- Test components with React Testing Library
- E2E testing for critical user flows

## 📚 Documentation

### Component Documentation
- Each component includes JSDoc comments
- Props interfaces are well-documented
- Usage examples in component files

### API Integration
- API client documentation in `lib/api/`
- Type definitions for all API responses
- Error handling patterns documented

## 🐛 Troubleshooting

### Common Issues
1. **API Connection:** Verify backend is running and accessible
2. **Tenant Context:** Ensure `x-tenant-id` header is set correctly
3. **Authentication:** Check JWT token validity and format
4. **Styling:** Verify MUI theme configuration

### Debug Mode
```bash
# Enable debug logging
DEBUG=* npm run dev
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes with proper TypeScript types
4. Test multi-tenant functionality
5. Submit a pull request

### Multi-Tenant Testing
Test with different store handles:
- `/electronics/` - Electronics store
- `/fashion/` - Fashion store  
- `/books/` - Books store

## 📄 License

MIT License - see LICENSE file for details
