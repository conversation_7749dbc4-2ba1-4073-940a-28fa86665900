

\# 🎨 Material Design 3 Color Palette Designer \- Complete Documentation

\#\# 📋 \*\*Overview\*\*

The \*\*Material Design 3 Color Palette Designer\*\* is a comprehensive, professional-grade color palette generation tool built specifically for the OneHRMS application. It generates Material Design 3 compliant color palettes from company logo colors while maintaining excellent accessibility standards and providing seamless integration with existing MUI \+ Tailwind theme systems.

\---

\#\# 🚀 \*\*Quick Start\*\*

\#\#\# \*\*Access the Component\*\*  
\`\`\`  
URL: http://localhost:3001/color-palette  
Component: ColorPaletteDesigner.tsx  
Location: src/components/ColorPaletteDesigner.tsx  
\`\`\`

\#\#\# \*\*Basic Usage\*\*  
1\. Navigate to \`/color-palette\`  
2\. Enter your company logo color in hex format (e.g., \`\#D32F2F\`)  
3\. Select accessibility level (AA or AAA)  
4\. Click "Generate Palette"  
5\. Review and export your generated theme

\---

\#\# 🏗️ \*\*Architecture & Technical Details\*\*

\#\#\# \*\*Component Structure\*\*  
\`\`\`typescript  
ColorPaletteDesigner/  
├── Main Component (ColorPaletteDesigner.tsx)  
├── Material Design 3 Color System  
├── Accessibility Engine  
├── Color Generation Algorithms  
├── Export/Import System  
└── Theme Preview System  
\`\`\`

\#\#\# \*\*Technology Stack\*\*  
\- \*\*Framework\*\*: Next.js 15.5.0 with App Router  
\- \*\*UI Library\*\*: Material-UI (MUI) v6  
\- \*\*Styling\*\*: Emotion \+ Tailwind CSS hybrid  
\- \*\*State Management\*\*: React Hooks (useState, useCallback, useEffect)  
\- \*\*Color Science\*\*: WCAG 2.1 compliant algorithms

\#\#\# \*\*Key Dependencies\*\*  
\`\`\`json  
{  
 "@mui/material": "^6.x.x",  
 "@mui/icons-material": "^6.x.x",  
 "react": "^18.x.x",  
 "next": "^15.5.0"  
}  
\`\`\`

\---

\#\# 🎨 \*\*Material Design 3 Color System\*\*

\#\#\# \*\*Core Color Categories\*\*

\#\#\#\# \*\*1. Brand Colors\*\*  
\- \*\*Primary\*\*: Company logo color (base)  
\- \*\*Secondary\*\*: Complementary color (180° hue shift)  
\- \*\*Tertiary\*\*: Accent color (60° hue shift)

\#\#\#\# \*\*2. Surface Colors\*\*  
\- \*\*Surface\*\*: Main content backgrounds  
\- \*\*Surface Variant\*\*: Alternative surface backgrounds  
\- \*\*Inverse Surface\*\*: Dark mode surface backgrounds

\#\#\#\# \*\*3. Semantic Colors\*\*  
\- \*\*Error\*\*: Error states and destructive actions  
\- \*\*Error Container\*\*: Subtle error backgrounds  
\- \*\*Success\*\*: Success states (if implemented)  
\- \*\*Warning\*\*: Warning states (if implemented)

\#\#\#\# \*\*4. Outline Colors\*\*  
\- \*\*Outline\*\*: Borders and dividers  
\- \*\*Outline Variant\*\*: Subtle borders and dividers

\#\#\# \*\*Color Token Structure\*\*  
\`\`\`typescript  
interface ColorToken {  
 hex: string;           *// Hex color value*  
 rgb: { r: number; g: number; b: number };  *// RGB values*  
 hsl: { h: number; s: number; l: number };  *// HSL values*  
 contrast: number;      *// Contrast ratio*  
 accessibility: 'AA' | 'AAA' | 'Fail';     *// WCAG compliance*  
 tonalValue: number;    *// M3 tonal value (0-100)*  
 category: string;      *// Color category*  
}  
\`\`\`

\---

\#\# 🔧 \*\*Color Generation Algorithms\*\*

\#\#\# \*\*Primary Color Generation\*\*  
\`\`\`typescript  
*// Uses company logo color as base*  
palette.primary \= {  
 hex: sourceColor,  
 rgb: sourceRgb,  
 hsl: sourceHsl,  
 contrast: calculateContrast(sourceColor, '\#FFFFFF'),  
 accessibility: 'AA',  
 tonalValue: 40,  
 category: 'brand'  
};  
\`\`\`

\#\#\# \*\*Secondary Color Generation\*\*  
\`\`\`typescript  
*// Complementary color with slight adjustment*  
const secondaryHsl \= {  
 h: (sourceHsl.h \+ 180) % 360,        *// 180° hue shift*  
 s: Math.max(20, sourceHsl.s \- 10),   *// Reduced saturation*  
 l: Math.max(30, sourceHsl.l \- 5)     *// Adjusted lightness*  
};  
\`\`\`

\#\#\# \*\*Tertiary Color Generation\*\*  
\`\`\`typescript  
*// Accent color for highlights*  
const tertiaryHsl \= {  
 h: (sourceHsl.h \+ 60) % 360,         *// 60° hue shift*  
 s: Math.max(25, sourceHsl.s \- 15),   *// Moderate saturation*  
 l: Math.max(35, sourceHsl.l \- 10)    *// Balanced lightness*  
};  
\`\`\`

\#\#\# \*\*Surface Color Generation\*\*  
\`\`\`typescript  
*// Neutral surface colors with proper contrast*  
palette.surface \= {  
 hex: '\#FEF7FF',  
 rgb: { r: 254, g: 247, b: 255 },  
 hsl: { h: 280, s: 100, l: 98 },  
 contrast: calculateContrast('\#FEF7FF', '\#000000'),  
 accessibility: 'AA',  
 tonalValue: 98,  
 category: 'surface'  
};  
\`\`\`

\---

\#\# ♿ \*\*Accessibility Features\*\*

\#\#\# \*\*WCAG 2.1 Compliance Levels\*\*  
\`\`\`typescript  
const ACCESSIBILITY\_LEVELS \= {  
 AA: { normal: 4.5, large: 3.0, ui: 3.0 },  
 AAA: { normal: 7.0, large: 4.5, ui: 4.5 }  
};  
\`\`\`

\#\#\# \*\*Contrast Ratio Calculation\*\*  
\`\`\`typescript  
const calculateContrast \= useCallback((*color1*: string, *color2*: string) \=\> {  
 const getLuminance \= (*r*: number, *g*: number, *b*: number) \=\> {  
   const \[rs, gs, bs\] \= \[r, g, b\].map(*c* \=\> {  
     c \= c / 255;  
     return c \<= 0.03928 ? c / 12.92 : Math.pow((c \+ 0.055) / 1.055, 2.4);  
   });  
   return 0.2126 \* rs \+ 0.7152 \* gs \+ 0.0722 \* bs;  
 };

 const l1 \= getLuminance(rgb1.r, rgb1.g, rgb1.b);  
 const l2 \= getLuminance(rgb2.r, rgb2.g, rgb2.b);

 const lighter \= Math.max(l1, l2);  
 const darker \= Math.min(l1, l2);

 return (lighter \+ 0.05) / (darker \+ 0.05);  
}, \[hexToRgb\]);  
\`\`\`

\#\#\# \*\*Accessibility Assessment\*\*  
\- \*\*Real-time contrast checking\*\*  
\- \*\*Visual status indicators\*\* (AA/AAA/Fail)  
\- \*\*Required contrast ratios\*\* displayed  
\- \*\*Automatic on-color selection\*\* for optimal contrast

\---

\#\# 📤 \*\*Export & Import System\*\*

\#\#\# \*\*Theme Export Format\*\*  
\`\`\`json  
{  
 "name": "Generated M3 Theme",  
 "sourceColor": "\#D32F2F",  
 "palette": {  
   "primary": { "hex": "\#D32F2F", "rgb": {...}, "hsl": {...} },  
   "secondary": { "hex": "\#2F2FD3", "rgb": {...}, "hsl": {...} },  
   *// ... complete color palette*  
 },  
 "generatedAt": "2025-08-26T...",  
 "version": "1.0.0",  
 "accessibilityLevel": "AA",  
 "m3Compliant": true  
}  
\`\`\`

\#\#\# \*\*Export Formats\*\*

\#\#\#\# \*\*1. CSS Variables\*\*  
\`\`\`css  
:root {  
 \--m3-primary: \#D32F2F;  
 \--m3-secondary: \#2F2FD3;  
 \--m3-surface: \#FEF7FF;  
 \--m3-on-surface: \#1D1B20;  
 */\* ... complete variable set \*/*  
}  
\`\`\`

\#\#\#\# \*\*2. Tailwind Config\*\*  
\`\`\`javascript  
module.exports \= {  
 theme: {  
   extend: {  
     colors: {  
       m3: {  
         primary: '\#D32F2F',  
         secondary: '\#2F2FD3',  
         surface: '\#FEF7FF',  
         onSurface: '\#1D1B20',  
         *// ... complete color set*  
       }  
     }  
   }  
 }  
}  
\`\`\`

\#\#\#\# \*\*3. JSON Theme File\*\*  
\- \*\*Complete theme data\*\* for import  
\- \*\*Metadata and versioning\*\* information  
\- \*\*Accessibility compliance\*\* details  
\- \*\*M3 compliance\*\* verification

\---

\#\# 🎭 \*\*User Interface Components\*\*

\#\#\# \*\*Main Interface Sections\*\*

\#\#\#\# \*\*1. Header Section\*\*  
\- \*\*Gradient background\*\* with Material Design aesthetic  
\- \*\*Component title\*\* and description  
\- \*\*Action buttons\*\* (Generate, Export, Import, Advanced)

\#\#\#\# \*\*2. Color Input Section\*\*  
\- \*\*Hex color input\*\* with visual preview  
\- \*\*Accessibility level\*\* selector (AA/AAA)  
\- \*\*Advanced options\*\* toggle

\#\#\#\# \*\*3. Generated Palette Display\*\*  
\- \*\*Tabbed interface\*\* for different views  
\- \*\*Color cards\*\* with comprehensive information  
\- \*\*Real-time updates\*\* during generation

\#\#\# \*\*Tabbed Interface\*\*

\#\#\#\# \*\*Color Palette Tab\*\*  
\- \*\*Grid layout\*\* of color cards  
\- \*\*Visual color previews\*\*  
\- \*\*HEX, RGB, HSL values\*\*  
\- \*\*Copy to clipboard\*\* functionality

\#\#\#\# \*\*Accessibility Tab\*\*  
\- \*\*Contrast ratio\*\* display  
\- \*\*WCAG compliance\*\* status  
\- \*\*Visual indicators\*\* for accessibility levels

\#\#\#\# \*\*Code Export Tab\*\*  
\- \*\*CSS Variables\*\* generation  
\- \*\*Tailwind Config\*\* output  
\- \*\*Formatted code\*\* for easy copying

\#\#\#\# \*\*Preview Tab\*\*  
\- \*\*Live theme preview\*\* with buttons and surfaces  
\- \*\*Real-time color\*\* application  
\- \*\*Component examples\*\* using generated colors

\---

\#\# 🔄 \*\*State Management\*\*

\#\#\# \*\*Component State\*\*  
\`\`\`typescript  
const \[logoColor, setLogoColor\] \= useState('\#D32F2F');  
const \[generatedPalette, setGeneratedPalette\] \= useState\<GeneratedPalette\>({});  
const \[isGenerating, setIsGenerating\] \= useState(false);  
const \[generationProgress, setGenerationProgress\] \= useState(0);  
const \[accessibilityLevel, setAccessibilityLevel\] \= useState\<'AA' | 'AAA'\>('AA');  
const \[tabValue, setTabValue\] \= useState(0);  
const \[showAdvanced, setShowAdvanced\] \= useState(false);  
\`\`\`

\#\#\# \*\*Performance Optimizations\*\*  
\- \*\*Debounced color input\*\* (500ms delay)  
\- \*\*Progress tracking\*\* during generation  
\- \*\*Memoized calculations\*\* with useCallback  
\- \*\*Efficient re-renders\*\* with proper state management

\---

\#\# 🎯 \*\*Integration with OneHRMS\*\*

\#\#\# \*\*Theme System Compatibility\*\*  
\- \*\*MUI v6\*\* component integration  
\- \*\*Tailwind CSS\*\* configuration support  
\- \*\*Existing theme context\*\* compatibility  
\- \*\*Responsive design\*\* system integration

\#\#\# \*\*Routing Integration\*\*  
\- \*\*Dedicated route\*\*: \`/color-palette\`  
\- \*\*Middleware compatibility\*\* with existing system  
\- \*\*Role-based access\*\* control support  
\- \*\*SEO-friendly\*\* page structure

\#\#\# \*\*Component Reusability\*\*  
\- \*\*Standalone component\*\* that can be imported anywhere  
\- \*\*Props-based\*\* customization support  
\- \*\*Theme context\*\* integration ready  
\- \*\*Export functionality\*\* for external use

\---

\#\# 🚀 \*\*Advanced Features\*\*

\#\#\# \*\*Real-time Generation\*\*  
\- \*\*Instant palette updates\*\* as color changes  
\- \*\*Progress indicators\*\* during generation  
\- \*\*Smooth animations\*\* and transitions  
\- \*\*Performance monitoring\*\* and optimization

\#\#\# \*\*Advanced Color Options\*\*  
\- \*\*Algorithm explanations\*\* for each color type  
\- \*\*Tonal value\*\* information display  
\- \*\*Color relationship\*\* visualizations  
\- \*\*Customization options\*\* for advanced users

\#\#\# \*\*Theme Management\*\*  
\- \*\*Version control\*\* for generated themes  
\- \*\*Import validation\*\* and error handling  
\- \*\*Theme comparison\*\* capabilities  
\- \*\*Backup and restore\*\* functionality

\---

\#\# 📱 \*\*Responsive Design\*\*

\#\#\# \*\*Breakpoint System\*\*  
\`\`\`typescript  
*// Mobile-first responsive grid*  
\<Box sx\={{  
 display: 'grid',  
 gridTemplateColumns: {  
   xs: '1fr',                    *// Mobile: single column*  
   sm: 'repeat(2, 1fr)',        *// Small: 2 columns*  
   md: 'repeat(3, 1fr)',        *// Medium: 3 columns*  
   lg: 'repeat(4, 1fr)'         *// Large: 4 columns*  
 },  
 gap: 3  
}}\>  
\`\`\`

\#\#\# \*\*Mobile Optimization\*\*  
\- \*\*Touch-friendly\*\* interface elements  
\- \*\*Optimized spacing\*\* for small screens  
\- \*\*Simplified navigation\*\* on mobile  
\- \*\*Performance optimization\*\* for mobile devices

\---

\#\# 🧪 \*\*Testing & Quality Assurance\*\*

\#\#\# \*\*Error Handling\*\*  
\- \*\*Graceful fallbacks\*\* for invalid colors  
\- \*\*User-friendly error\*\* messages  
\- \*\*Input validation\*\* and sanitization  
\- \*\*Performance error\*\* recovery

\#\#\# \*\*Performance Metrics\*\*  
\- \*\*Generation time\*\* monitoring  
\- \*\*Memory usage\*\* optimization  
\- \*\*Bundle size\*\* analysis  
\- \*\*Runtime performance\*\* tracking

\#\#\# \*\*Accessibility Testing\*\*  
\- \*\*Screen reader\*\* compatibility  
\- \*\*Keyboard navigation\*\* support  
\- \*\*Color contrast\*\* validation  
\- \*\*WCAG 2.1\*\* compliance verification

\---

\#\# 🔮 \*\*Future Enhancements\*\*

\#\#\# \*\*Planned Features\*\*  
\- \*\*Dark mode\*\* palette generation  
\- \*\*Brand kit\*\* export (PDF, design files)  
\- \*\*Collaboration\*\* features for team sharing  
\- \*\*Version history\*\* and palette evolution tracking

\#\#\# \*\*Advanced Algorithms\*\*  
\- \*\*AI-powered\*\* color suggestions  
\- \*\*Cultural color\*\* considerations  
\- \*\*Seasonal palette\*\* generation  
\- \*\*Industry-specific\*\* color schemes

\#\#\# \*\*Integration Enhancements\*\*  
\- \*\*Design tool\*\* plugins (Figma, Sketch)  
\- \*\*API endpoints\*\* for external applications  
\- \*\*Webhook support\*\* for automated workflows  
\- \*\*Analytics dashboard\*\* for usage insights

\---

\#\# 📚 \*\*Usage Examples\*\*

\#\#\# \*\*Basic Implementation\*\*  
\`\`\`typescript  
import ColorPaletteDesigner from '../components/ColorPaletteDesigner';

export default function MyPage() {  
 return (  
   \<div\>  
     \<h1\>My Color Palette\</h1\>  
     \<ColorPaletteDesigner /\>  
   \</div\>  
 );  
}  
\`\`\`

\#\#\# \*\*Custom Styling\*\*  
\`\`\`typescript  
\<ColorPaletteDesigner  
 sx\={{  
   maxWidth: 1200,  
   mx: 'auto',  
   p: 2  
 }}  
/\>  
\`\`\`

\#\#\# \*\*Theme Integration\*\*  
\`\`\`typescript  
*// After generating a palette, integrate with your theme*  
const generatedTheme \= {  
 palette: {  
   primary: { main: generatedPalette.primary.hex },  
   secondary: { main: generatedPalette.secondary.hex },  
   *// ... integrate other colors*  
 }  
};  
\`\`\`

\---

\#\# 🛠️ \*\*Development & Maintenance\*\*

\#\#\# \*\*Code Organization\*\*  
\`\`\`  
src/components/ColorPaletteDesigner/  
├── ColorPaletteDesigner.tsx      \# Main component  
├── types/                        \# TypeScript interfaces  
├── utils/                        \# Utility functions  
├── constants/                    \# M3 color definitions  
└── hooks/                        \# Custom React hooks  
\`\`\`

\#\#\# \*\*Maintenance Tasks\*\*  
\- \*\*Regular dependency\*\* updates  
\- \*\*Performance monitoring\*\* and optimization  
\- \*\*Accessibility compliance\*\* verification  
\- \*\*User feedback\*\* integration

\#\#\# \*\*Testing Strategy\*\*  
\- \*\*Unit tests\*\* for utility functions  
\- \*\*Integration tests\*\* for component behavior  
\- \*\*Accessibility tests\*\* for compliance  
\- \*\*Performance tests\*\* for optimization

\---

\#\# 📖 \*\*API Reference\*\*

\#\#\# \*\*Component Props\*\*  
\`\`\`typescript  
interface ColorPaletteDesignerProps {  
 initialColor?: string;           *// Default logo color*  
 accessibilityLevel?: 'AA' | 'AAA'; *// Default accessibility level*  
 showAdvanced?: boolean;          *// Show advanced options by default*  
 onPaletteGenerated?: (*palette*: GeneratedPalette) \=\> void; *// Callback*  
 sx?: SxProps\<Theme\>;            *// Custom styling*  
}  
\`\`\`

\#\#\# \*\*Generated Palette Structure\*\*  
\`\`\`typescript  
interface GeneratedPalette {  
 \[*key*: string\]: ColorToken;       *// Key-value pairs for each color*  
}

interface ColorToken {  
 hex: string;                     *// Hex color value*  
 rgb: RGB;                        *// RGB color values*  
 hsl: HSL;                        *// HSL color values*  
 contrast: number;                *// Contrast ratio*  
 accessibility: 'AA' | 'AAA' | 'Fail'; *// WCAG compliance*  
 tonalValue: number;              *// M3 tonal value*  
 category: string;                *// Color category*  
}  
\`\`\`

\---

\#\# 🎉 \*\*Conclusion\*\*

The \*\*Material Design 3 Color Palette Designer\*\* represents a significant advancement in color palette generation tools, specifically designed for the OneHRMS application. It combines:

\- \*\*Professional-grade\*\* color science algorithms  
\- \*\*Material Design 3\*\* compliance and best practices  
\- \*\*Comprehensive accessibility\*\* features  
\- \*\*Seamless integration\*\* with existing systems  
\- \*\*User-friendly interface\*\* with advanced capabilities

This component provides designers and developers with a powerful tool for creating accessible, compliant, and beautiful color schemes that integrate perfectly with modern web applications.

\---

\#\# 📞 \*\*Support & Contact\*\*

For questions, issues, or feature requests related to the Color Palette Designer:

\- \*\*Component Location\*\*: \`src/components/ColorPaletteDesigner.tsx\`  
\- \*\*Route\*\*: \`/color-palette\`  
\- \*\*Documentation\*\*: This file  
\- \*\*Issues\*\*: Check the component's error handling and console logs

\---

*\*Documentation generated for OneHRMS Color Palette Designer v1.0.0\**  
*\*Last updated: August 26, 2025\**

\# 🎨 Color Palette Designer \- Technical Specification

\#\# 📋 \*\*Technical Overview\*\*

This document provides detailed technical specifications for the \*\*Material Design 3 Color Palette Designer\*\* component, including implementation details, API specifications, and development guidelines.

\---

\#\# 🏗️ \*\*Component Architecture\*\*

\#\#\# \*\*File Structure\*\*  
\`\`\`  
src/components/ColorPaletteDesigner/  
├── ColorPaletteDesigner.tsx          \# Main component (1,200+ lines)  
├── types/                            \# TypeScript definitions  
│   ├── ColorToken.ts                 \# Color token interface  
│   ├── GeneratedPalette.ts           \# Palette structure  
│   └── ComponentProps.ts             \# Component props  
├── utils/                            \# Utility functions  
│   ├── colorConversions.ts           \# Color space conversions  
│   ├── contrastCalculation.ts        \# WCAG contrast algorithms  
│   └── m3Generation.ts               \# M3 color generation  
└── constants/                        \# Constants and configurations  
   ├── m3ColorSystem.ts              \# M3 color definitions  
   ├── accessibilityLevels.ts        \# WCAG compliance levels  
   └── tonalValues.ts                \# M3 tonal value system  
\`\`\`

\#\#\# \*\*Component Hierarchy\*\*  
\`\`\`  
ColorPaletteDesigner (Main)  
├── Header Section  
│   ├── Title & Description  
│   └── Action Buttons  
├── Color Input Section  
│   ├── Hex Color Input  
│   ├── Accessibility Level Selector  
│   └── Advanced Options Toggle  
├── Generated Palette Display  
│   ├── Tab Navigation  
│   ├── Color Palette Tab  
│   ├── Accessibility Tab  
│   ├── Code Export Tab  
│   └── Preview Tab  
└── Modal Dialogs  
   ├── Export Dialog  
   └── Import Dialog  
\`\`\`

\---

\#\# 🔧 \*\*Core Implementation Details\*\*

\#\#\# \*\*State Management Architecture\*\*  
\`\`\`typescript  
*// Primary State Variables*  
const \[logoColor, setLogoColor\] \= useState\<string\>('\#D32F2F');  
const \[generatedPalette, setGeneratedPalette\] \= useState\<GeneratedPalette\>({});  
const \[isGenerating, setIsGenerating\] \= useState\<boolean\>(false);  
const \[generationProgress, setGenerationProgress\] \= useState\<number\>(0);  
const \[accessibilityLevel, setAccessibilityLevel\] \= useState\<'AA' | 'AAA'\>('AA');  
const \[tabValue, setTabValue\] \= useState\<number\>(0);  
const \[showAdvanced, setShowAdvanced\] \= useState\<boolean\>(false);

*// Modal State Variables*  
const \[exportDialogOpen, setExportDialogOpen\] \= useState\<boolean\>(false);  
const \[importDialogOpen, setImportDialogOpen\] \= useState\<boolean\>(false);  
const \[importData, setImportData\] \= useState\<string\>('');  
\`\`\`

\#\#\# \*\*Performance Optimizations\*\*  
\`\`\`typescript  
*// Debounced Color Input (500ms delay)*  
useEffect(() \=\> {  
 if (logoColor && logoColor.length \=== 7 && logoColor.startsWith('\#')) {  
   const timeoutId \= setTimeout(() \=\> {  
     if (Object.keys(generatedPalette).length \> 0) {  
       generateM3Palette(logoColor);  
     }  
   }, 500);  
    
   return () \=\> clearTimeout(timeoutId);  
 }  
}, \[logoColor, generateM3Palette, generatedPalette\]);

*// Memoized Calculations*  
const calculateContrast \= useCallback((*color1*: string, *color2*: string) \=\> {  
 *// WCAG 2.1 contrast calculation implementation*  
}, \[hexToRgb\]);

const hexToRgb \= useCallback((*hex*: string) \=\> {  
 *// Hex to RGB conversion*  
}, \[\]);

const rgbToHsl \= useCallback((*r*: number, *g*: number, *b*: number) \=\> {  
 *// RGB to HSL conversion*  
}, \[\]);  
\`\`\`

\---

\#\# 🎨 \*\*Color Generation Algorithms\*\*

\#\#\# \*\*Material Design 3 Color Generation Engine\*\*

\#\#\#\# \*\*Primary Color System\*\*  
\`\`\`typescript  
*// Primary color uses company logo as base*  
palette.primary \= {  
 hex: sourceColor,  
 rgb: sourceRgb,  
 hsl: sourceHsl,  
 contrast: calculateContrast(sourceColor, '\#FFFFFF'),  
 accessibility: 'AA',  
 tonalValue: 40,  
 category: 'brand'  
};

*// On-primary (contrast color) \- automatically selected*  
const onPrimary \= calculateContrast(sourceColor, '\#FFFFFF') \> 3 ? '\#FFFFFF' : '\#000000';  
palette.onPrimary \= {  
 hex: onPrimary,  
 rgb: hexToRgb(onPrimary),  
 hsl: rgbToHsl(hexToRgb(onPrimary).r, hexToRgb(onPrimary).g, hexToRgb(onPrimary).b),  
 contrast: calculateContrast(sourceColor, onPrimary),  
 accessibility: 'AA',  
 tonalValue: 100,  
 category: 'on-brand'  
};  
\`\`\`

\#\#\#\# \*\*Secondary Color Generation\*\*  
\`\`\`typescript  
*// Complementary color with intelligent adjustment*  
const secondaryHsl \= {  
 h: (sourceHsl.h \+ 180) % 360,        *// 180° hue shift*  
 s: Math.max(20, sourceHsl.s \- 10),   *// Reduced saturation for balance*  
 l: Math.max(30, sourceHsl.l \- 5)     *// Adjusted lightness for contrast*  
};

const secondary \= hslToHex(secondaryHsl.h, secondaryHsl.s, secondaryHsl.l);  
palette.secondary \= {  
 hex: secondary,  
 rgb: hexToRgb(secondary),  
 hsl: secondaryHsl,  
 contrast: calculateContrast(secondary, '\#FFFFFF'),  
 accessibility: 'AA',  
 tonalValue: 40,  
 category: 'brand'  
};  
\`\`\`

\#\#\#\# \*\*Tertiary Color Generation\*\*  
\`\`\`typescript  
*// Accent color for highlights and emphasis*  
const tertiaryHsl \= {  
 h: (sourceHsl.h \+ 60) % 360,         *// 60° hue shift for harmony*  
 s: Math.max(25, sourceHsl.s \- 15),   *// Moderate saturation*  
 l: Math.max(35, sourceHsl.l \- 10)    *// Balanced lightness*  
};

const tertiary \= hslToHex(tertiaryHsl.h, tertiaryHsl.s, tertiaryHsl.l);  
palette.tertiary \= {  
 hex: tertiary,  
 rgb: hexToRgb(tertiary),  
 hsl: tertiaryHsl,  
 contrast: calculateContrast(tertiary, '\#FFFFFF'),  
 accessibility: 'AA',  
 tonalValue: 40,  
 category: 'brand'  
};  
\`\`\`

\#\#\#\# \*\*Surface Color Generation\*\*  
\`\`\`typescript  
*// Neutral surface colors with proper contrast ratios*  
palette.surface \= {  
 hex: '\#FEF7FF',  
 rgb: { r: 254, g: 247, b: 255 },  
 hsl: { h: 280, s: 100, l: 98 },  
 contrast: calculateContrast('\#FEF7FF', '\#000000'),  
 accessibility: 'AA',  
 tonalValue: 98,  
 category: 'surface'  
};

palette.onSurface \= {  
 hex: '\#1D1B20',  
 rgb: { r: 29, g: 27, b: 32 },  
 hsl: { h: 260, s: 8, l: 12 },  
 contrast: calculateContrast('\#FEF7FF', '\#1D1B20'),  
 accessibility: 'AA',  
 tonalValue: 12,  
 category: 'on-surface'  
};  
\`\`\`

\---

\#\# ♿ \*\*Accessibility Implementation\*\*

\#\#\# \*\*WCAG 2.1 Compliance Engine\*\*

\#\#\#\# \*\*Contrast Ratio Calculation\*\*  
\`\`\`typescript  
const calculateContrast \= useCallback((*color1*: string, *color2*: string) \=\> {  
 const rgb1 \= hexToRgb(color1);  
 const rgb2 \= hexToRgb(color2);

 *// WCAG 2.1 luminance calculation*  
 const getLuminance \= (*r*: number, *g*: number, *b*: number) \=\> {  
   const \[rs, gs, bs\] \= \[r, g, b\].map(*c* \=\> {  
     c \= c / 255;  
     *// Apply gamma correction*  
     return c \<= 0.03928 ? c / 12.92 : Math.pow((c \+ 0.055) / 1.055, 2.4);  
   });  
    
   *// Relative luminance formula*  
   return 0.2126 \* rs \+ 0.7152 \* gs \+ 0.0722 \* bs;  
 };

 const l1 \= getLuminance(rgb1.r, rgb1.g, rgb1.b);  
 const l2 \= getLuminance(rgb2.r, rgb2.g, rgb2.b);

 const lighter \= Math.max(l1, l2);  
 const darker \= Math.min(l1, l2);

 *// Contrast ratio formula*  
 return (lighter \+ 0.05) / (darker \+ 0.05);  
}, \[hexToRgb\]);  
\`\`\`

\#\#\#\# \*\*Accessibility Level Management\*\*  
\`\`\`typescript  
const ACCESSIBILITY\_LEVELS \= {  
 AA: {  
   normal: 4.5,    *// Normal text (12pt and smaller)*  
   large: 3.0,     *// Large text (18pt and larger)*  
   ui: 3.0         *// UI components and graphics*  
 },  
 AAA: {  
   normal: 7.0,    *// Normal text (12pt and smaller)*  
   large: 4.5,     *// Large text (18pt and larger)*  
   ui: 4.5         *// UI components and graphics*  
 }  
};

*// Accessibility assessment function*  
const getAccessibilityStatus \= useCallback((*contrast*: number) \=\> {  
 const level \= accessibilityLevel;  
 const thresholds \= ACCESSIBILITY\_LEVELS\[level\];  
  if (contrast \>= thresholds.large) return { status: 'AAA', color: 'success.main' };  
 if (contrast \>= thresholds.normal) return { status: 'AA', color: 'success.main' };  
 return { status: 'Fail', color: 'error.main' };  
}, \[accessibilityLevel\]);  
\`\`\`

\---

\#\# 📤 \*\*Export & Import System\*\*

\#\#\# \*\*Theme Export Engine\*\*

\#\#\#\# \*\*JSON Export Format\*\*  
\`\`\`typescript  
const exportTheme \= useCallback(() \=\> {  
 const themeData \= {  
   name: 'Generated M3 Theme',  
   sourceColor: logoColor,  
   palette: generatedPalette,  
   generatedAt: new Date().toISOString(),  
   version: '1.0.0',  
   accessibilityLevel,  
   m3Compliant: true,  
   metadata: {  
     totalColors: Object.keys(generatedPalette).length,  
     categories: \[...new Set(Object.values(generatedPalette).map(*c* \=\> c.category))\],  
     averageContrast: Object.values(generatedPalette)  
       .reduce((*sum*, *c*) \=\> sum \+ c.contrast, 0) / Object.keys(generatedPalette).length  
   }  
 };

 *// Create and download blob*  
 const blob \= new Blob(\[JSON.stringify(themeData, null, 2)\], {  
   type: 'application/json'  
 });  
 const url \= URL.createObjectURL(blob);  
 const a \= document.createElement('a');  
 a.href \= url;  
 a.download \= 'm3-theme-palette.json';  
 a.click();  
 URL.revokeObjectURL(url);  
}, \[logoColor, generatedPalette, accessibilityLevel\]);  
\`\`\`

\#\#\#\# \*\*CSS Variables Generation\*\*  
\`\`\`typescript  
*// CSS Variables export*  
const generateCSSVariables \= (*palette*: GeneratedPalette) \=\> {  
 return Object.entries(palette).map((\[*key*, *color*\]) \=\>  
   \`--m3-${key}: ${color.hex};\`  
 ).join('\\n');  
};

*// Usage in component*  
\<Paper sx\={{ p: 2, bgcolor: 'grey.100', fontFamily: 'monospace', fontSize: '0.875rem' }}\>  
 {Object.entries(generatedPalette).map((\[*key*, *color*\]) \=\> (  
   \<Box key\={key} sx\={{ mb: 1 }}\>  
     \--m3\-{key}: {color.hex};  
   \</Box\>  
 ))}  
\</Paper\>  
\`\`\`

\#\#\#\# \*\*Tailwind Config Generation\*\*  
\`\`\`typescript  
*// Tailwind configuration export*  
const generateTailwindConfig \= (*palette*: GeneratedPalette) \=\> {  
 const colorEntries \= Object.entries(palette).map((\[*key*, *color*\]) \=\>  
   \`    ${key}: '${color.hex}',\`  
 ).join('\\n');  
  return \`colors: {  
 m3: {  
${colorEntries}  
 }  
}\`;  
};  
\`\`\`

\#\#\# \*\*Theme Import System\*\*

\#\#\#\# \*\*Import Validation & Processing\*\*  
\`\`\`typescript  
const importTheme \= useCallback(() \=\> {  
 try {  
   const themeData \= JSON.parse(importData);  
    
   *// Validate imported theme structure*  
   if (\!themeData.palette || \!themeData.sourceColor) {  
     throw new Error('Invalid theme format: missing palette or source color');  
   }  
    
   *// Validate palette structure*  
   const isValidPalette \= Object.values(themeData.palette).every(*color* \=\>  
     color.hex && color.rgb && color.hsl && typeof color.contrast \=== 'number'  
   );  
    
   if (\!isValidPalette) {  
     throw new Error('Invalid palette structure in imported theme');  
   }  
    
   *// Apply imported theme*  
   setGeneratedPalette(themeData.palette);  
   setLogoColor(themeData.sourceColor);  
    
   if (themeData.accessibilityLevel) {  
     setAccessibilityLevel(themeData.accessibilityLevel);  
   }  
    
   setImportDialogOpen(false);  
   setImportData('');  
    
 } catch (error) {  
   console.error('Error importing theme:', error);  
   *// Show user-friendly error message*  
 }  
}, \[importData\]);  
\`\`\`

\---

\#\# 🎭 \*\*User Interface Implementation\*\*

\#\#\# \*\*Responsive Grid System\*\*

\#\#\#\# \*\*CSS Grid Implementation\*\*  
\`\`\`typescript  
*// Mobile-first responsive grid*  
\<Box sx\={{  
 display: 'grid',  
 gridTemplateColumns: {  
   xs: '1fr',                                    *// Mobile: single column*  
   sm: 'repeat(2, 1fr)',                        *// Small: 2 columns*  
   md: 'repeat(3, 1fr)',                        *// Medium: 3 columns*  
   lg: 'repeat(4, 1fr)'                         *// Large: 4 columns*  
 },  
 gap: 3  
}}\>  
 {Object.entries(generatedPalette).map((\[*key*, *color*\]) \=\> (  
   \<Box key\={key}\>  
     \<ColorCard color\={color} name\={key} /\>  
   \</Box\>  
 ))}  
\</Box\>  
\`\`\`

\#\#\#\# \*\*Tab Navigation System\*\*  
\`\`\`typescript  
*// Tab panel implementation*  
function TabPanel(*props*: TabPanelProps) {  
 const { children, value, index, ...other } \= props;  
 return (  
   \<*div*  
     role\="tabpanel"  
     hidden\={value \!== index}  
     id\={\`palette-tabpanel-${index}\`}  
     aria\-labelledby\={\`palette-tab-${index}\`}  
     {...*other*}  
   \>  
     {*value* \=== *index* && \<*Box* *sx*\={{ p: 3 }}\>{children}\</Box\>}  
   \</div\>  
 );  
}

*// Tab content management*  
const \[tabValue, setTabValue\] \= useState(0);

const handleTabChange \= (*event*: React.SyntheticEvent, *newValue*: number) \=\> {  
 setTabValue(newValue);  
};  
\`\`\`

\#\#\# \*\*Color Card Component\*\*

\#\#\#\# \*\*Card Structure\*\*  
\`\`\`typescript  
\<Card sx\={{ height: '100%' }}\>  
 \<CardContent\>  
   {*/\* Color Preview \*/*}  
   \<Box sx\={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}\>  
     \<Box  
       sx\={{  
         width: 40,  
         height: 40,  
         borderRadius: 2,  
         bgcolor: color.hex,  
         border: '2px solid',  
         borderColor: 'divider'  
       }}  
     /\>  
     \<Box sx\={{ flex: 1 }}\>  
       \<Typography variant\="subtitle2" sx\={{ fontWeight: 600 }}\>  
         {M3\_COLOR\_SYSTEM\[key as keyof typeof M3\_COLOR\_SYSTEM\]?.name || key}  
       \</Typography\>  
       \<Typography variant\="caption" color\="text.secondary"\>  
         {M3\_COLOR\_SYSTEM\[key as keyof typeof M3\_COLOR\_SYSTEM\]?.description}  
       \</Typography\>  
     \</Box\>  
   \</Box\>  
    
   {*/\* Color Information \*/*}  
   \<Stack spacing\={1}\>  
     \<ColorInfoRow label\="HEX" value\={color.hex} copyable /\>  
     \<ColorInfoRow label\="RGB" value\={\`${color.rgb.r}, ${color.rgb.g}, ${color.rgb.b}\`} /\>  
     \<ColorInfoRow label\="HSL" value\={\`${Math.round(color.hsl.h)}°, ${Math.round(color.hsl.s)}%, ${Math.round(color.hsl.l)}%\`} /\>  
     \<ColorInfoRow label\="Tonal" value\={color.tonalValue.toString()} /\>  
   \</Stack\>  
 \</CardContent\>  
\</Card\>  
\`\`\`

\---

\#\# 🔄 \*\*State Management & Performance\*\*

\#\#\# \*\*Optimization Strategies\*\*

\#\#\#\# \*\*Memoization & Callbacks\*\*  
\`\`\`typescript  
*// Memoized color conversion functions*  
const hexToRgb \= useCallback((*hex*: string) \=\> {  
 const result \= /^\#?(\[a-f\\d\]{2})(\[a-f\\d\]{2})(\[a-f\\d\]{2})$/i.exec(hex);  
 return result ? {  
   r: parseInt(result\[1\], 16),  
   g: parseInt(result\[2\], 16),  
   b: parseInt(result\[3\], 16)  
 } : { r: 0, g: 0, b: 0 };  
}, \[\]);

const rgbToHsl \= useCallback((*r*: number, *g*: number, *b*: number) \=\> {  
 *// HSL conversion algorithm*  
}, \[\]);

const hslToHex \= useCallback((*h*: number, *s*: number, *l*: number) \=\> {  
 *// HSL to Hex conversion algorithm*  
}, \[\]);  
\`\`\`

\#\#\#\# \*\*Progress Tracking System\*\*  
\`\`\`typescript  
*// Generation progress management*  
const generateM3Palette \= useCallback(async (*sourceColor*: string) \=\> {  
 setIsGenerating(true);  
 setGenerationProgress(0);  
  try {  
   const palette: GeneratedPalette \= {};  
   let progress \= 0;  
    
   *// Primary colors (10% progress)*  
   palette.primary \= generatePrimaryColor(sourceColor);  
   progress \+= 10;  
   setGenerationProgress(progress);  
    
   *// Secondary colors (10% progress)*  
   palette.secondary \= generateSecondaryColor(sourceColor);  
   progress \+= 10;  
   setGenerationProgress(progress);  
    
   *// Continue with other colors...*  
    
   setGenerationProgress(100);  
   setGeneratedPalette(palette);  
    
 } catch (error) {  
   console.error('Error generating palette:', error);  
 } finally {  
   setIsGenerating(false);  
   setGenerationProgress(0);  
 }  
}, \[hexToRgb, rgbToHsl, hslToHex, calculateContrast\]);  
\`\`\`

\---

\#\# 🧪 \*\*Error Handling & Validation\*\*

\#\#\# \*\*Input Validation System\*\*

\#\#\#\# \*\*Color Input Validation\*\*  
\`\`\`typescript  
*// Hex color validation*  
const isValidHexColor \= (*color*: string): boolean \=\> {  
 const hexRegex \= /^\#(\[A-Fa-f0-9\]{6}|\[A-Fa-f0-9\]{3})$/;  
 return hexRegex.test(color);  
};

*// Color input handler with validation*  
const handleColorChange \= (*newColor*: string) \=\> {  
 if (isValidHexColor(newColor)) {  
   setLogoColor(newColor);  
 } else {  
   *// Show validation error*  
   console.warn('Invalid hex color format');  
 }  
};  
\`\`\`

\#\#\#\# \*\*Error Boundary Implementation\*\*  
\`\`\`typescript  
*// Error handling in color generation*  
try {  
 const sourceRgb \= hexToRgb(sourceColor);  
 if (\!sourceRgb) {  
   throw new Error('Invalid source color format');  
 }  
  const sourceHsl \= rgbToHsl(sourceRgb.r, sourceRgb.g, sourceRgb.b);  
 if (\!sourceHsl) {  
   throw new Error('Failed to convert RGB to HSL');  
 }  
  *// Continue with palette generation...*  
 } catch (error) {  
 console.error('Error generating palette:', error);  
 *// Show user-friendly error message*  
 *// Reset generation state*  
 setIsGenerating(false);  
 setGenerationProgress(0);  
}  
\`\`\`

\---

\#\# 📱 \*\*Responsive Design Implementation\*\*

\#\#\# \*\*Breakpoint System\*\*

\#\#\#\# \*\*Material-UI Breakpoint Integration\*\*  
\`\`\`typescript  
*// Responsive grid columns*  
const getGridColumns \= (*breakpoint*: string) \=\> {  
 switch (breakpoint) {  
   case 'xs': return '1fr';  
   case 'sm': return 'repeat(2, 1fr)';  
   case 'md': return 'repeat(3, 1fr)';  
   case 'lg': return 'repeat(4, 1fr)';  
   default: return '1fr';  
 }  
};

*// Responsive spacing*  
const getSpacing \= (*breakpoint*: string) \=\> {  
 switch (breakpoint) {  
   case 'xs': return 2;  
   case 'sm': return 2;  
   case 'md': return 3;  
   case 'lg': return 3;  
   default: return 2;  
 }  
};  
\`\`\`

\#\#\#\# \*\*Mobile-First Design Patterns\*\*  
\`\`\`typescript  
*// Mobile-optimized layout*  
\<Box sx\={{  
 display: 'flex',  
 flexDirection: { xs: 'column', md: 'row' },  
 gap: { xs: 2, md: 3 },  
 alignItems: { xs: 'stretch', md: 'center' }  
}}\>  
 {*/\* Content adapts to screen size \*/*}  
\</Box\>

*// Touch-friendly interface elements*  
\<IconButton  
 size\="small"  
 onClick\={() \=\> copyColor(color.hex)}  
 sx\={{  
   minWidth: 44,  *// Touch target minimum*  
   minHeight: 44,  
   p: 1  
 }}  
\>  
 \<ContentCopy fontSize\="small" /\>  
\</IconButton\>  
\`\`\`

\---

\#\# 🔮 \*\*Future Development Roadmap\*\*

\#\#\# \*\*Planned Technical Enhancements\*\*

\#\#\#\# \*\*Advanced Color Algorithms\*\*  
\`\`\`typescript  
*// AI-powered color suggestions*  
interface AIColorSuggestion {  
 confidence: number;  
 reasoning: string;  
 alternativeColors: string\[\];  
}

const generateAIColorSuggestions \= async (*sourceColor*: string): Promise\<AIColorSuggestion\[\]\> \=\> {  
 *// Integration with color theory AI models*  
 *// Cultural color considerations*  
 *// Industry-specific color schemes*  
};

*// Seasonal palette generation*  
const generateSeasonalPalette \= (*sourceColor*: string, *season*: 'spring' | 'summer' | 'autumn' | 'winter') \=\> {  
 *// Seasonal color adjustments*  
 *// Temperature-based modifications*  
 *// Mood-based color variations*  
};  
\`\`\`

\#\#\#\# \*\*Performance Optimizations\*\*  
\`\`\`typescript  
*// Web Worker implementation for heavy calculations*  
const colorGenerationWorker \= new Worker('/workers/color-generation.js');

*// Lazy loading for advanced features*  
const AdvancedOptions \= lazy(() \=\> import('./AdvancedOptions'));

*// Virtual scrolling for large palettes*  
const VirtualizedColorGrid \= ({ *colors* }: { colors: ColorToken\[\] }) \=\> {  
 *// Implement virtual scrolling for performance*  
};  
\`\`\`

\#\#\#\# \*\*Integration Enhancements\*\*  
\`\`\`typescript  
*// Design tool plugins*  
interface DesignToolIntegration {  
 figma: FigmaPlugin;  
 sketch: SketchPlugin;  
 adobe: AdobePlugin;  
}

*// API endpoints for external applications*  
const colorPaletteAPI \= {  
 generate: '/api/color-palette/generate',  
 export: '/api/color-palette/export',  
 import: '/api/color-palette/import',  
 validate: '/api/color-palette/validate'  
};  
\`\`\`

\---

\#\# 📊 \*\*Performance Metrics & Monitoring\*\*

\#\#\# \*\*Key Performance Indicators\*\*

\#\#\#\# \*\*Generation Performance\*\*  
\`\`\`typescript  
*// Performance monitoring*  
const measureGenerationPerformance \= async (*sourceColor*: string) \=\> {  
 const startTime \= performance.now();  
  try {  
   await generateM3Palette(sourceColor);  
   const endTime \= performance.now();  
   const generationTime \= endTime \- startTime;  
    
   *// Log performance metrics*  
   console.log(\`Palette generation completed in ${generationTime.toFixed(2)}ms\`);  
    
   *// Track performance for optimization*  
   trackPerformanceMetric('palette\_generation\_time', generationTime);  
    
 } catch (error) {  
   console.error('Generation failed:', error);  
   trackPerformanceMetric('palette\_generation\_failure', 1);  
 }  
};  
\`\`\`

\#\#\#\# \*\*Memory Usage Optimization\*\*  
\`\`\`typescript  
*// Memory cleanup for large palettes*  
useEffect(() \=\> {  
 return () \=\> {  
   *// Cleanup generated palette data*  
   setGeneratedPalette({});  
   setGenerationProgress(0);  
    
   *// Clear any cached color calculations*  
   if (window.colorCache) {  
     delete window.colorCache;  
   }  
 };  
}, \[\]);

*// Efficient color storage*  
const optimizeColorStorage \= (*palette*: GeneratedPalette) \=\> {  
 *// Remove unnecessary properties for storage*  
 const optimizedPalette \= Object.entries(palette).reduce((*acc*, \[*key*, *color*\]) \=\> {  
   acc\[key\] \= {  
     hex: color.hex,  
     rgb: color.rgb,  
     contrast: color.contrast,  
     accessibility: color.accessibility  
   };  
   return acc;  
 }, {} as Partial\<GeneratedPalette\>);  
  return optimizedPalette;  
};  
\`\`\`

\---

\#\# 🧪 \*\*Testing Strategy\*\*

\#\#\# \*\*Testing Implementation\*\*

\#\#\#\# \*\*Unit Tests\*\*  
\`\`\`typescript  
*// Color conversion utility tests*  
describe('Color Conversion Utilities', () \=\> {  
 test('hexToRgb converts valid hex colors correctly', () \=\> {  
   expect(hexToRgb('\#FF0000')).toEqual({ r: 255, g: 0, b: 0 });  
   expect(hexToRgb('\#00FF00')).toEqual({ r: 0, g: 255, b: 0 });  
   expect(hexToRgb('\#0000FF')).toEqual({ r: 0, g: 0, b: 255 });  
 });  
  test('rgbToHsl converts RGB values correctly', () \=\> {  
   expect(rgbToHsl(255, 0, 0)).toEqual({ h: 0, s: 100, l: 50 });  
   expect(rgbToHsl(0, 255, 0)).toEqual({ h: 120, s: 100, l: 50 });  
   expect(rgbToHsl(0, 0, 255)).toEqual({ h: 240, s: 100, l: 50 });  
 });  
});

*// Contrast calculation tests*  
describe('Contrast Calculation', () \=\> {  
 test('calculates contrast ratios correctly', () \=\> {  
   expect(calculateContrast('\#FFFFFF', '\#000000')).toBeCloseTo(21, 1);  
   expect(calculateContrast('\#000000', '\#FFFFFF')).toBeCloseTo(21, 1);  
   expect(calculateContrast('\#FF0000', '\#FFFFFF')).toBeCloseTo(4, 1);  
 });  
});  
\`\`\`

\#\#\#\# \*\*Integration Tests\*\*  
\`\`\`typescript  
*// Component integration tests*  
describe('ColorPaletteDesigner Integration', () \=\> {  
 test('generates complete palette from source color', async () \=\> {  
   render(\<ColorPaletteDesigner /\>);  
    
   const colorInput \= screen.getByLabelText(/logo color/i);  
   fireEvent.change(colorInput, { target: { value: '\#D32F2F' } });  
    
   const generateButton \= screen.getByText(/generate palette/i);  
   fireEvent.click(generateButton);  
    
   *// Wait for generation to complete*  
   await waitFor(() \=\> {  
     expect(screen.getByText(/primary/i)).toBeInTheDocument();  
     expect(screen.getByText(/secondary/i)).toBeInTheDocument();  
     expect(screen.getByText(/surface/i)).toBeInTheDocument();  
   });  
 });  
});  
\`\`\`

\#\#\#\# \*\*Accessibility Tests\*\*  
\`\`\`typescript  
*// Accessibility compliance tests*  
describe('Accessibility Compliance', () \=\> {  
 test('meets WCAG 2.1 AA standards', () \=\> {  
   render(\<ColorPaletteDesigner /\>);  
    
   *// Check contrast ratios*  
   const colorCards \= screen.getAllByRole('article');  
   colorCards.forEach(*card* \=\> {  
     const contrastText \= within(card).getByText(/contrast ratio/i);  
     const contrastValue \= parseFloat(contrastText.textContent\!.match(/\\d\+\\.\\d\+/)?.\[0\] || '0');  
     expect(contrastValue).toBeGreaterThanOrEqual(4.5);  
   });  
 });  
  test('supports keyboard navigation', () \=\> {  
   render(\<ColorPaletteDesigner /\>);  
    
   *// Test tab navigation*  
   const firstTab \= screen.getByRole('tab', { name: /color palette/i });  
   firstTab.focus();  
    
   fireEvent.keyDown(firstTab, { key: 'Tab' });  
   expect(screen.getByRole('tab', { name: /accessibility/i })).toHaveFocus();  
 });  
});  
\`\`\`

\---

\#\# 📚 \*\*API Reference\*\*

\#\#\# \*\*Component Interface\*\*

\#\#\#\# \*\*Props Definition\*\*  
\`\`\`typescript  
interface ColorPaletteDesignerProps {  
 *// Color configuration*  
 initialColor?: string;                    *// Default logo color*  
 accessibilityLevel?: 'AA' | 'AAA';       *// Default accessibility level*  
  *// Display options*  
 showAdvanced?: boolean;                   *// Show advanced options by default*  
 showPreview?: boolean;                    *// Show preview tab by default*  
  *// Callbacks*  
 onPaletteGenerated?: (*palette*: GeneratedPalette) \=\> void;  
 onColorChange?: (*color*: string) \=\> void;  
 onAccessibilityChange?: (*level*: 'AA' | 'AAA') \=\> void;  
  *// Styling*  
 sx?: SxProps\<Theme\>;                     *// Custom styling*  
 maxWidth?: number | string;               *// Maximum component width*  
  *// Advanced features*  
 enableRealTime?: boolean;                 *// Enable real-time generation*  
 enableExport?: boolean;                   *// Enable export functionality*  
 enableImport?: boolean;                   *// Enable import functionality*  
}  
\`\`\`

\#\#\#\# \*\*Event Handlers\*\*  
\`\`\`typescript  
*// Palette generation events*  
const handlePaletteGenerated \= (*palette*: GeneratedPalette) \=\> {  
 if (onPaletteGenerated) {  
   onPaletteGenerated(palette);  
 }  
  *// Track analytics*  
 analytics.track('palette\_generated', {  
   sourceColor: logoColor,  
   paletteSize: Object.keys(palette).length,  
   accessibilityLevel  
 });  
};

*// Color change events*  
const handleColorChange \= (*newColor*: string) \=\> {  
 setLogoColor(newColor);  
  if (onColorChange) {  
   onColorChange(newColor);  
 }  
  *// Validate color format*  
 if (isValidHexColor(newColor)) {  
   setColorError(null);  
 } else {  
   setColorError('Invalid hex color format');  
 }  
};  
\`\`\`

\---

\#\# 🎯 \*\*Integration Guidelines\*\*

\#\#\# \*\*OneHRMS Integration\*\*

\#\#\#\# \*\*Theme Context Integration\*\*  
\`\`\`typescript  
*// Integration with existing theme system*  
import { useTheme } from '../theme/ThemeContext';

const ColorPaletteDesigner \= () \=\> {  
 const theme \= useTheme();  
  *// Apply generated colors to theme*  
 const applyGeneratedTheme \= (*palette*: GeneratedPalette) \=\> {  
   const newTheme \= {  
     ...theme,  
     palette: {  
       ...theme.palette,  
       primary: { main: palette.primary.hex },  
       secondary: { main: palette.secondary.hex },  
       surface: { main: palette.surface.hex },  
       onSurface: { main: palette.onSurface.hex }  
     }  
   };  
    
   theme.updateTheme(newTheme);  
 };  
  return (  
   \<div\>  
     {*/\* Component implementation \*/*}  
     {*Object*.*keys*(*generatedPalette*).*length* \> 0 && (  
       \<*Button* *onClick*\={() \=\> applyGeneratedTheme(*generatedPalette*)}\>  
         *Apply* *to* *Theme*  
       \</*Button*\>  
     )}  
   \</div\>  
 );  
};  
\`\`\`

\#\#\#\# \*\*Routing Integration\*\*  
\`\`\`typescript  
*// Middleware integration*  
export function middleware(*request*: NextRequest) {  
 const { pathname } \= request.nextUrl;  
  *// Allow access to color palette designer*  
 if (pathname \=== '/color-palette') {  
   return NextResponse.next();  
 }  
  *// Continue with existing middleware logic*  
 *// ... existing middleware code*  
}  
\`\`\`

\---

\#\# 📞 \*\*Support & Maintenance\*\*

\#\#\# \*\*Development Support\*\*

\#\#\#\# \*\*Debugging Tools\*\*  
\`\`\`typescript  
*// Development mode debugging*  
if (process.env.NODE\_ENV \=== 'development') {  
 console.group('🎨 Color Palette Designer Debug');  
 console.log('Current State:', {  
   logoColor,  
   generatedPalette: Object.keys(generatedPalette),  
   isGenerating,  
   accessibilityLevel  
 });  
 console.groupEnd();  
}

*// Performance monitoring*  
const debugPerformance \= (*operation*: string, *startTime*: number) \=\> {  
 if (process.env.NODE\_ENV \=== 'development') {  
   const duration \= performance.now() \- startTime;  
   console.log(\`${operation} completed in ${duration.toFixed(2)}ms\`);  
 }  
};  
\`\`\`

\#\#\#\# \*\*Error Reporting\*\*  
\`\`\`typescript  
*// Error boundary implementation*  
class ColorPaletteDesignerErrorBoundary extends Component {  
 constructor(*props*: any) {  
   *super*(props);  
   this.state \= { hasError: false, error: null };  
 }  
  static getDerivedStateFromError(*error*: Error) {  
   return { hasError: true, error };  
 }  
  componentDidCatch(*error*: Error, *errorInfo*: any) {  
   *// Log error to monitoring service*  
   console.error('Color Palette Designer Error:', error, errorInfo);  
    
   *// Report to error tracking service*  
   if (window.errorReporting) {  
     window.errorReporting.captureException(error, {  
       extra: { component: 'ColorPaletteDesigner', errorInfo }  
     });  
   }  
 }  
  render() {  
   if (this.state.hasError) {  
     return (  
       \<Box sx\={{ p: 3, textAlign: 'center' }}\>  
         \<Typography variant\="h6" color\="error" gutterBottom\>  
           Something went wrong with the Color Palette *Designer*  
         \</Typography\>  
         \<Button onClick\={() \=\> window.location.reload()}\>  
           Reload *Page*  
         \</Button\>  
       \</Box\>  
     );  
   }  
    
   return this.props.children;  
 }  
}  
\`\`\`

\---

*\*Technical Specification for OneHRMS Color Palette Designer v1.0.0\**  
*\*Last updated: August 26, 2025\**  
*\*For technical questions, refer to the component source code and this specification\**

