"use client";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Paper,
  Alert,
  Box,
  CircularProgress,
} from "@mui/material";
import { useState } from "react";
import { bulkUploadApi } from '@/lib/api/bulk-upload';

export default function BulkUpload({ storeHandle }: { storeHandle: string }) {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [successMsg, setSuccessMsg] = useState("");
  const [errorMsg, setErrorMsg] = useState("");

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.length) {
      setFile(e.target.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!file) return;
    setUploading(true);
    setErrorMsg("");
    setSuccessMsg("");

    try {
      const result = await bulkUploadApi.uploadProducts(file);
      
      if (result.success) {
        setSuccessMsg(result.message || "Upload successful!");
      } else {
        setErrorMsg(result.error || "Upload failed");
      }
    } catch (error) {
      setErrorMsg("Something went wrong while uploading!");
    } finally {
      setUploading(false);
    }
  };

  const handleSkip = () => {
    window.location.href = `/${storeHandle}/admin`;
  };

  return (
    <Paper className="p-6 space-y-6 rounded-2xl shadow-md">
      <Typography variant="h6">Bulk Product Upload (Optional)</Typography>
      <input
        type="file"
        accept=".xlsx, .xls"
        onChange={handleFileChange}
        className="block"
      />

      {successMsg && <Alert severity="success">{successMsg}</Alert>}
      {errorMsg && <Alert severity="error">{errorMsg}</Alert>}

      <Box className="flex gap-4 pt-2">
        <Button
          variant="contained"
          disabled={!file || uploading}
          onClick={handleUpload}
        >
          {uploading ? (
            <CircularProgress size={20} color="inherit" />
          ) : (
            "Upload"
          )}
        </Button>

        <Button variant="outlined" color="warning" onClick={handleSkip}>
          Skip Step
        </Button>
      </Box>

      <Box className="pt-6">
        <Button
          variant="contained"
          color="success"
          onClick={() => (window.location.href = `/${storeHandle}/admin`)}
        >
          🚀 Launch App
        </Button>
      </Box>
    </Paper>
  );
}
