'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { StoreRegistrationStep } from './steps/StoreRegistrationStep';
import { CategoryManagementStep } from './steps/CategoryManagementStep';
import { ProductManagementStep } from './steps/ProductManagementStep';
import { BulkImportStep } from './steps/BulkImportStep';
import { LaunchStoreStep } from './steps/LaunchStoreStep';
import { OnboardingProgress } from './OnboardingProgress';
import { OnboardingData } from '@/types/onboarding';
import { useAuthStore } from '@/stores/authStore';
import { authApi } from '@/lib/api/auth';
import { useRouter } from 'next/navigation';
import { strapiApi } from '@/lib/api/strapi';
import { useStoreConfigStore } from '@/stores/storeConfigStore';

// Material Design 3 Components
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  CircularProgress,
  Stack,
  Chip,
} from '@mui/material';
import {
  Store as StoreIcon,
} from '@mui/icons-material';

const ONBOARDING_STEPS = [
  { id: 1, title: 'Store Registration', description: 'Set up your store details' },
  { id: 2, title: 'Category Management', description: 'Add categories and subcategories' },
  { id: 3, title: 'Product Management', description: 'Add your first products' },
  { id: 4, title: 'Bulk Import', description: 'Import products in bulk (Optional)' },
  { id: 5, title: 'Launch Store', description: 'Go live with your store' }
];

export const OnboardingWizard: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    store: null,
    categories: [],
    subcategories: [],
    products: [],
    bulkImportCompleted: false
  });
  const [isCheckingExistingConfig, setIsCheckingExistingConfig] = useState(true);
  
  const { user, token, setAuth } = useAuthStore();
  const { storeConfig } = useStoreConfigStore();
  const router = useRouter();
  
  // Check for existing configuration and determine starting step

  const checkExistingConfiguration = async () => {
    console.log('=== ONBOARDING WIZARD INITIALIZING ===');
    console.log('User from auth store:', user);
    
    if (!user) {
      setIsCheckingExistingConfig(false);
      return;
    }

    try {
      const userData = user.user || user;
      const userId = userData?.id || user?.id || '';
      
      if (userId) {
        // Check if store configuration already exists
        const existingStoreConfig = await strapiApi.getStoreConfigurationByUserId(userId);
        
        console.log('OnboardingWizard - API response:', existingStoreConfig);
        
        if (existingStoreConfig && existingStoreConfig?.data && existingStoreConfig?.data?.id) {
          console.log('Found existing store configuration, step 1 is complete');
          
          // Convert Strapi data to OnboardingStore format
          const configData = existingStoreConfig.data;
          const storeData = {
            name: configData.store_name || '',
            description: configData.store_description || '',
            handle: configData.store_handle || '',
            gstNumber: configData.gst_number || '',
            ownerEmail: configData.email || '',
            storeEmail: configData.store_email || '',
            phone: configData.phone || '',
            website: configData.website || '',
            addressLine1: configData.address_line_1 || '',
            addressLine2: configData.address_line_2 || '',
            city: configData.city || '',
            state: configData.state || '',
            pincode: configData.pincode || '',
            country: configData.country || 'India',
            logo: configData.store_logo_url || undefined,
            logo_url: configData.store_logo_url || '',
            color_palette: configData.store_color_palette || '',
            paymentMethods: [], // Will be converted from object format
            businessType: configData.business_type || '',
            businessCategory: configData.business_category || '',
            id:configData.documentId
          };
          
          // Convert payment methods from object to array
          const paymentMethods: string[] = [];
          if (configData.payment_methods) {
            if (configData.payment_methods.upi) paymentMethods.push('UPI');
            if (configData.payment_methods.bnpl) paymentMethods.push('BNPL');
            if (configData.payment_methods.wallet) paymentMethods.push('Wallets');
            if (configData.payment_methods.debit_card || configData.payment_methods.credit_card) paymentMethods.push('Credit/Debit Cards');
            if (configData.payment_methods.net_banking) paymentMethods.push('Net Banking');
            if (configData.payment_methods.cash_on_delivery) paymentMethods.push('Cash on Delivery');
          }
          storeData.paymentMethods = paymentMethods;
          
          setOnboardingData(prev => ({
            ...prev,
            store: storeData
          }));
          
          // Move to step 2 since step 1 is already complete
          setCurrentStep(2);
        } else {
          console.log('No existing store configuration found, starting from step 1');
          
          // Pre-fill store data with user information if no existing config
          const userMetadata = userData.metadata || user.metadata || {};
          
          const preFilledStore = {
            name: userMetadata.store_name || '',
            description: '',
            handle: userMetadata.store_handle || userData.store_handle || user.store_handle || '',
            gstNumber: userMetadata.gst_number || '',
            // Contact Information
            ownerEmail: userData.email || user.email || '',
            storeEmail: userMetadata.store_email || userData.email || user.email || '',
            phone: userMetadata.phone || userMetadata.contact_number || '',
            website: userMetadata.website || '',
            // Address Information
            addressLine1: userMetadata.address_line_1 || userMetadata.address || '',
            addressLine2: userMetadata.address_line_2 || '',
            city: userMetadata.city || '',
            state: userMetadata.state || '',
            pincode: userMetadata.pincode || '',
            country: userMetadata.country || 'India',
            logo: undefined,
            paymentMethods: userMetadata.payment_methods || [],
            businessType: userMetadata.business_type || '',
            businessCategory: userMetadata.business_category || '',
          };
          
          console.log('Pre-filled store data:', preFilledStore);
          
          setOnboardingData(prev => ({
            ...prev,
            store: preFilledStore
          }));
        }
      }
    } catch (error) {
      console.error('Error checking existing configuration:', error);
      // Continue with normal flow if there's an error
    } finally {
      setIsCheckingExistingConfig(false);
    }
  };

  useEffect(() => {
    checkExistingConfiguration();
  }, [user]);

  const handleOnboardingComplete = async () => {
    try {
      console.log('=== COMPLETING ONBOARDING ===');
      
      if (token && user) {
        // Update onboarding status to completed
        const updatedUser = await authApi.updateOnboardingStatus(token, 'completed');
        
        // Update auth store with new user data
        setAuth(token, updatedUser);
        
        console.log('Onboarding status updated to completed');
        
        // Redirect to admin dashboard
        const userData = user.user || user;
        const userMetadata = userData?.metadata || user?.metadata || {};
        const storeHandle = onboardingData.store?.handle || userMetadata.store_handle || userData?.store_handle || user?.store_handle;
        
        if (storeHandle) {
          router.push(`/${storeHandle}/admin`);
        } else {
          router.push('/admin');
        }
      }
    } catch (error) {
      console.error('Error completing onboarding:', error);
      // Still redirect even if API call fails
      const userData = user?.user || user;
      const userMetadata = userData?.metadata || user?.metadata || {};
      const storeHandle = onboardingData.store?.handle || userMetadata?.store_handle || userData?.store_handle || user?.store_handle;
      
      if (storeHandle) {
        router.push(`/${storeHandle}/admin`);
      } else {
        router.push('/admin');
      }
    }
  };

  const handleStepComplete = async (stepData: any) => {
    switch (currentStep) {
      case 1:
        setOnboardingData(prev => ({ ...prev, store: stepData }));
        break;
      case 2:
        console.log('=== STEP 2 COMPLETED - SAVING CATEGORIES ===');
        console.log('Step data received:', stepData);
        console.log('Categories:', stepData.categories);
        console.log('Subcategories:', stepData.subcategories);
        setOnboardingData(prev => ({ 
          ...prev, 
          categories: stepData.categories,
          subcategories: stepData.subcategories 
        }));
        break;
      case 3:
        setOnboardingData(prev => ({ ...prev, products: stepData }));
        break;
      case 4:
        setOnboardingData(prev => ({ ...prev, bulkImportCompleted: true }));
        break;
      case 5:
        // Launch store - update onboarding status and redirect to admin
        await handleOnboardingComplete();
        return;
    }
    
    if (currentStep < ONBOARDING_STEPS.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleStepBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkipStep = () => {
    if (currentStep === 4) { // Only bulk import is skippable
      setCurrentStep(currentStep + 1);
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <StoreRegistrationStep
            onComplete={handleStepComplete}
            initialData={onboardingData.store}
          />
        );
      case 2:
        return (
          <CategoryManagementStep
            onComplete={handleStepComplete}
            onBack={handleStepBack}
            storeHandle={onboardingData.store?.handle || ''}
            initialData={{
              categories: onboardingData.categories,
              subcategories: onboardingData.subcategories
            }}
          />
        );
      case 3:
        console.log('=== RENDERING PRODUCT MANAGEMENT STEP ===');
        console.log('Onboarding data categories:', onboardingData.categories);
        console.log('Onboarding data subcategories:', onboardingData.subcategories);
        return (
          <ProductManagementStep
            onComplete={handleStepComplete}
            onBack={handleStepBack}
            storeHandle={onboardingData.store?.handle || ''}
            categories={onboardingData.categories}
            subcategories={onboardingData.subcategories}
            initialData={onboardingData.products}
          />
        );
      case 4:
        return (
          <BulkImportStep
            onComplete={handleStepComplete}
            onBack={handleStepBack}
            onSkip={handleSkipStep}
            storeHandle={onboardingData.store?.handle || ''}
            categories={onboardingData.categories}
            subcategories={onboardingData.subcategories}
          />
        );
      case 5:
        return (
          <LaunchStoreStep
            onComplete={handleStepComplete}
            onBack={handleStepBack}
            onboardingData={onboardingData}
          />
        );
      default:
        return null;
    }
  };

  // Show loading state while checking existing configuration
  if (isCheckingExistingConfig) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          bgcolor: 'background.default',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Stack alignItems="center" spacing={2}>
          <CircularProgress size={48} />
          <Typography variant="body1" color="text.secondary">
            Checking existing configuration...
          </Typography>
        </Stack>
      </Box>
    );
  }

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Header */}
      <Box
        sx={{
          bgcolor: 'background.paper',
          boxShadow: 1,
          borderBottom: 1,
          borderColor: 'divider',
        }}
      >
        <Container maxWidth="lg" sx={{ py: 3 }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Box>
              <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
                Store Setup
              </Typography>
              <Typography variant="body1" color="text.secondary">
                {currentStep === 1 ? "Let's get your store ready to launch" : "Continue setting up your store"}
              </Typography>
            </Box>
            <Chip
              label={`Step ${currentStep} of ${ONBOARDING_STEPS.length}`}
              variant="outlined"
              color="primary"
            />
          </Stack>
        </Container>
      </Box>

      {/* Progress Bar */}
      <OnboardingProgress 
        steps={ONBOARDING_STEPS}
        currentStep={currentStep}
      />

      {/* Main Content */}
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Card>
          <CardContent sx={{ p: { xs: 3, lg: 4 } }}>
            {renderCurrentStep()}
          </CardContent>
        </Card>
      </Container>
    </Box>
  );
};