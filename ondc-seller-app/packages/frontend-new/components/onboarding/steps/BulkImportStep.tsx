'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Stack,
  Alert,
  CircularProgress,
  LinearProgress,
  Divider,
} from '@mui/material';
import {
  ArrowBack,
  ArrowForward,
  Download,
  Upload,
  CheckCircle,
  Error,
  CloudUpload,
} from '@mui/icons-material';
import { OnboardingCategory } from '@/types/onboarding';
import { useToast } from '@/app/providers/toast-provider';
import { bulkImportApi } from '@/lib/api/bulk-import';

interface BulkImportStepProps {
  onComplete: () => void;
  onBack: () => void;
  onSkip: () => void;
  storeHandle: string;
  categories: OnboardingCategory[];
  subcategories: OnboardingCategory[];
}

export const BulkImportStep: React.FC<BulkImportStepProps> = ({
  onComplete,
  onBack,
  onSkip,
  storeHandle,
  categories,
  subcategories
}) => {
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadResults, setUploadResults] = useState<{
    success: boolean;
    message?: string;
    error?: string;
  } | null>(null);

  const { showToast } = useToast();

  // Download XLSX template from API
  const downloadTemplate = async () => {
    setIsDownloading(true);
    try {
      console.log('Downloading template...');
      const blob = await bulkImportApi.downloadTemplate();
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${storeHandle}-product-import-template.xlsx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      
      showToast('Template downloaded successfully!', 'success');
    } catch (error) {
      console.error('Error downloading template:', error);
      showToast('Failed to download template. Please try again.', 'error');
    } finally {
      setIsDownloading(false);
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Check if file is Excel format
      const isExcelFile = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                          file.type === 'application/vnd.ms-excel' ||
                          file.name.endsWith('.xlsx') ||
                          file.name.endsWith('.xls');
      
      if (!isExcelFile) {
        showToast('Please upload an Excel file (.xlsx or .xls)', 'error');
        return;
      }
      
      setUploadedFile(file);
      setUploadResults(null);
    }
  };

  const processUpload = async () => {
    if (!uploadedFile) return;

    setIsProcessing(true);
    setUploadProgress(0);

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      // Upload file to API
      const result = await bulkImportApi.uploadProductImport(uploadedFile);
      
      // Clear progress interval
      clearInterval(progressInterval);
      setUploadProgress(100);

      if (result.success) {
        setUploadResults({
          success: true,
          message: result.message || 'Products imported successfully!',
        });
        showToast(result.message || 'Products imported successfully!', 'success');
      } else {
        setUploadResults({
          success: false,
          error: result.error || 'Failed to import products',
        });
        showToast(result.error || 'Failed to import products', 'error');
      }
    } catch (error) {
      console.error('Error processing upload:', error);
      setUploadResults({
        success: false,
        error: 'An error occurred while processing the file',
      });
      showToast('An error occurred while processing the file', 'error');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleComplete = () => {
    onComplete();
  };

  return (
    <Box>
      <Stack spacing={4} sx={{ mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h2" sx={{ fontWeight: 'bold', mb: 1 }}>
            Bulk Import Products
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Import multiple products at once using an Excel file. This step is optional - you can skip it and add products manually later.
          </Typography>
        </Box>
      </Stack>

      <Stack spacing={4}>
        {/* Template Download Section */}
        <Card sx={{ bgcolor: 'primary.50', borderColor: 'primary.200' }}>
          <CardContent>
            <Stack direction="row" spacing={2} alignItems="flex-start">
              <Download sx={{ color: 'primary.main', fontSize: 32 }} />
              <Box sx={{ flex: 1 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, color: 'primary.dark', mb: 1 }}>
                  Download Template
                </Typography>
                <Typography variant="body2" sx={{ color: 'primary.dark', mb: 2 }}>
                  Start by downloading our Excel template with sample data and instructions. This will help you format your product data correctly.
                </Typography>
                <Button
                  onClick={downloadTemplate}
                  disabled={isDownloading}
                  variant="contained"
                  startIcon={isDownloading ? <CircularProgress size={16} /> : <Download />}
                >
                  {isDownloading ? 'Downloading...' : 'Download Excel Template'}
                </Button>
              </Box>
            </Stack>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card sx={{ bgcolor: 'grey.50' }}>
          <CardContent>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              Instructions
            </Typography>
            <Stack spacing={2}>
              <Stack direction="row" spacing={2} alignItems="flex-start">
                <Box sx={{
                  width: 24,
                  height: 24,
                  bgcolor: 'primary.100',
                  color: 'primary.main',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '0.75rem',
                  fontWeight: 600,
                  flexShrink: 0
                }}>
                  1
                </Box>
                <Typography variant="body2">Download the Excel template above</Typography>
              </Stack>
              <Stack direction="row" spacing={2} alignItems="flex-start">
                <Box sx={{
                  width: 24,
                  height: 24,
                  bgcolor: 'primary.100',
                  color: 'primary.main',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '0.75rem',
                  fontWeight: 600,
                  flexShrink: 0
                }}>
                  2
                </Box>
                <Typography variant="body2">Fill in your product data following the sample format</Typography>
              </Stack>
              <Stack direction="row" spacing={2} alignItems="flex-start">
                <Box sx={{
                  width: 24,
                  height: 24,
                  bgcolor: 'primary.100',
                  color: 'primary.main',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '0.75rem',
                  fontWeight: 600,
                  flexShrink: 0
                }}>
                  3
                </Box>
                <Typography variant="body2">Make sure category names match exactly with the categories you created</Typography>
              </Stack>
              <Stack direction="row" spacing={2} alignItems="flex-start">
                <Box sx={{
                  width: 24,
                  height: 24,
                  bgcolor: 'primary.100',
                  color: 'primary.main',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '0.75rem',
                  fontWeight: 600,
                  flexShrink: 0
                }}>
                  4
                </Box>
                <Typography variant="body2">Save the file in Excel format (.xlsx) and upload it below</Typography>
              </Stack>
            </Stack>

            <Alert severity="warning" sx={{ mt: 3 }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                Important Notes:
              </Typography>
              <Box component="ul" sx={{ m: 0, pl: 2 }}>
                <li>Category names must match exactly (case-sensitive)</li>
                <li>Prices should be in decimal format (e.g., 29.99)</li>
                <li>Status should be either &apos;draft&apos; or &apos;published&apos;</li>
                <li>Images should be valid URLs separated by commas</li>
                <li>Follow the exact column format provided in the template</li>
              </Box>
            </Alert>
          </CardContent>
        </Card>

        {/* Available Categories */}
        {/* <div className="bg-white border rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Available Categories</h3>
          <p className="text-gray-600 mb-4">Use these exact category names in your Excel file:</p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Categories:</h4>
              <div className="space-y-1">
                {categories.length > 0 ? (
                  categories.map((category) => (
                    <div key={category.id} className="text-sm text-gray-700 bg-gray-50 px-3 py-1 rounded">
                      {category.name}
                    </div>
                  ))
                ) : (
                  <div className="text-sm text-gray-500 italic">No categories available</div>
                )}
              </div>
            </div>
            
            {subcategories.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Subcategories:</h4>
                <div className="space-y-1">
                  {subcategories.map((subcategory) => {
                    const parentCategory = categories.find(cat => cat.id === subcategory.parentId);
                    return (
                      <div key={subcategory.id} className="text-sm text-gray-700 bg-gray-50 px-3 py-1 rounded">
                        {subcategory.name} <span className="text-gray-500">({parentCategory?.name})</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div> */}

        {/* File Upload Section */}
        <div className="bg-white border-2 border-dashed border-gray-300 rounded-lg p-8">
          <div className="text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
            </svg>
            <div className="mt-4">
              <label htmlFor="excel-upload" className="cursor-pointer">
                <span className="mt-2 block text-sm font-medium text-gray-900">
                  Upload your Excel file
                </span>
                <span className="mt-1 block text-sm text-gray-500">
                  or drag and drop
                </span>
              </label>
              <input
                id="excel-upload"
                type="file"
                accept=".xlsx,.xls,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                onChange={handleFileUpload}
                className="hidden"
              />
            </div>
            <p className="mt-2 text-xs text-gray-500">
              Excel files only (.xlsx, .xls), up to 10MB
            </p>
          </div>

          {uploadedFile && (
            <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm font-medium text-green-800">{uploadedFile.name}</span>
                  <span className="text-sm text-green-600">({(uploadedFile.size / 1024).toFixed(1)} KB)</span>
                </div>
                <button
                  onClick={() => {
                    setUploadedFile(null);
                    setUploadResults(null);
                  }}
                  className="text-green-600 hover:text-green-800"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Process Upload Button */}
        {uploadedFile && !uploadResults && (
          <div className="text-center">
            <button
              onClick={processUpload}
              disabled={isProcessing}
              className={`px-8 py-3 rounded-lg font-medium focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 ${
                isProcessing
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-green-600 text-white hover:bg-green-700'
              }`}
            >
              {isProcessing ? 'Processing...' : 'Import Products'}
            </button>
          </div>
        )}

        {/* Progress Bar */}
        {isProcessing && (
          <div className="bg-white border rounded-lg p-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Importing products...</span>
              <span className="text-sm text-gray-500">{Math.round(uploadProgress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              />
            </div>
          </div>
        )}

        {/* Upload Results */}
        {uploadResults && (
          <div className="bg-white border rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Import Results</h3>
            
            <div className="space-y-4">
              {uploadResults.success ? (
                <div className="flex items-center space-x-2">
                  <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-green-800 font-medium">
                    {uploadResults.message}
                  </span>
                </div>
              ) : (
                <div className="flex items-start space-x-2">
                  <svg className="w-5 h-5 text-red-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <span className="text-red-800 font-medium">Import failed:</span>
                    <p className="text-red-700 mt-1">{uploadResults.error}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </Stack>

      {/* Navigation Buttons */}
      <Stack direction="row" justifyContent="space-between" sx={{ pt: 4, mt: 4, borderTop: 1, borderColor: 'divider' }}>
        <Button
          onClick={onBack}
          variant="outlined"
          startIcon={<ArrowBack />}
          size="large"
        >
          Back to Products
        </Button>
        <Stack direction="row" spacing={2}>
          <Button
            onClick={onSkip}
            variant="outlined"
            size="large"
          >
            Skip This Step
          </Button>
          <Button
            onClick={handleComplete}
            variant="contained"
            endIcon={<ArrowForward />}
            size="large"
          >
            Continue to Launch
          </Button>
        </Stack>
      </Stack>
    </Box>
  );
};