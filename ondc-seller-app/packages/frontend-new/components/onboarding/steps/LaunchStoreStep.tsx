'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  <PERSON>ton,
  Card,
  CardContent,
  Stack,
  LinearProgress,
  Chip,
  Divider,
} from '@mui/material';
import {
  ArrowBack,
  Launch,
  CheckCircle,
  Store,
  Celebration,
} from '@mui/icons-material';
import { OnboardingData } from '@/types/onboarding';
import { useAuthStore } from '@/stores/authStore';
import { authApi } from '@/lib/api/auth';
import { useToast } from '@/app/providers/toast-provider';

interface LaunchStoreStepProps {
  onComplete: () => void;
  onBack: () => void;
  onboardingData: OnboardingData;
}

export const LaunchStoreStep: React.FC<LaunchStoreStepProps> = ({
  onComplete,
  onBack,
  onboardingData
}) => {
  const { showToast } = useToast();
  const [isLaunching, setIsLaunching] = useState(false);
  const [launchProgress, setLaunchProgress] = useState(0);

  const { user, token, getStoreHandle } = useAuthStore();

  const handleLaunchStore = async () => {
    setIsLaunching(true);
    setLaunchProgress(0);

    try {
      // Get user ID and store handle
      const userId = user?.user?.id || user?.id;
      const storeHandle = getStoreHandle();
      
      if (!userId) {
        throw new Error('User ID not found');
      }

      console.log('Launching store for user:', userId);
      console.log('Store handle:', storeHandle);

      // Simulate store setup process with progress updates
      const steps = [
        'Creating store database...',
        'Setting up categories...',
        'Importing products...',
        'Configuring payment methods...',
        'Setting up store theme...',
        'Generating store pages...',
        'Finalizing setup...'
      ];

     

      // Call the API to complete onboarding
      await authApi.completeOnboarding(userId, token || undefined, storeHandle || undefined);
      
      console.log('Onboarding completed successfully!');

      // Complete the onboarding
      onComplete();
 
    } catch (error) {
      console.error('Error launching store:', error);
      setIsLaunching(false);
      setLaunchProgress(0);
      // You might want to show an error message to the user here
      // alert('Failed to launch store. Please try again.');
     
      showToast('Failed to launch store. Please try again.', 'error');

    }

  };

  const { store, categories, subcategories, products } = onboardingData;

  return (
    <Box>
      <Stack spacing={4} sx={{ mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h2" sx={{ fontWeight: 'bold', mb: 1 }}>
            Launch Your Store
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Congratulations! You&apos;ve completed the setup process. Review your store details and launch when you&apos;re ready.
          </Typography>
        </Box>
      </Stack>

      {!isLaunching ? (
        <Stack spacing={4}>
          {/* Store Summary */}
          <Card sx={{
            background: 'linear-gradient(to right, #eff6ff, #eef2ff)',
            borderColor: 'primary.200'
          }}>
            <CardContent>
              <Typography variant="h5" sx={{ fontWeight: 600, color: 'primary.dark', mb: 3 }}>
                Store Summary
              </Typography>

              <Stack direction={{ xs: 'column', md: 'row' }} spacing={4}>
                <Stack spacing={3} sx={{ flex: 1 }}>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: 'primary.dark', mb: 1 }}>
                      Store Information
                    </Typography>
                    <Stack spacing={0.5} sx={{ color: 'primary.dark' }}>
                      <Typography variant="body2"><strong>Name:</strong> {store?.name}</Typography>
                      <Typography variant="body2"><strong>Handle:</strong> {store?.handle}</Typography>
                      <Typography variant="body2"><strong>Business Type:</strong> {store?.businessType}</Typography>
                      <Typography variant="body2"><strong>Category:</strong> {store?.businessCategory}</Typography>
                    </Stack>
                  </Box>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: 'primary.dark', mb: 1 }}>
                      Contact Details
                    </Typography>
                    <Stack spacing={0.5} sx={{ color: 'primary.dark' }}>
                      <Typography variant="body2"><strong>Email:</strong> {store?.ownerEmail}</Typography>
                      {store?.phone && <Typography variant="body2"><strong>Phone:</strong> {store?.phone}</Typography>}
                    </Stack>
                  </Box>
                  
                </Stack>

                <Stack spacing={3} sx={{ flex: 1 }}>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: 'primary.dark', mb: 1 }}>
                      Content Statistics
                    </Typography>
                    <Stack direction="row" spacing={2}>
                      <Card sx={{ flex: 1, textAlign: 'center' }}>
                        <CardContent>
                          <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                            {categories.length}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Categories
                          </Typography>
                        </CardContent>
                      </Card>
                      <Card sx={{ flex: 1, textAlign: 'center' }}>
                        <CardContent>
                          <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                            {subcategories.length}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Subcategories
                          </Typography>
                        </CardContent>
                      </Card>
                    </Stack>
                    <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
                      <Card sx={{ flex: 1, textAlign: 'center' }}>
                        <CardContent>
                          <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'secondary.main' }}>
                            {products.length}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Products
                          </Typography>
                        </CardContent>
                      </Card>
                      {/* <Card sx={{ flex: 1, textAlign: 'center' }}>
                        <CardContent>
                          <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                            {store?.paymentMethods?.length || 0}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Payment Methods
                          </Typography>
                        </CardContent>
                      </Card> */}
                    </Stack>
                  </Box>

                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: 'primary.dark', mb: 1 }}>
                      Store URL
                    </Typography>
                    <Card sx={{ p: 2, bgcolor: 'grey.50' }}>
                      <Typography
                        component="code"
                        sx={{
                          color: 'primary.main',
                          fontFamily: 'monospace',
                          fontSize: '0.875rem'
                        }}
                      >
                        https://yourstore.com/{store?.handle}
                      </Typography>
                    </Card>
                  </Box>
                </Stack>
              </Stack>
            </CardContent>
          </Card>

       

          {/* Launch Checklist */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-green-900 mb-4">Pre-Launch Checklist</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-green-800">Store information completed</span>
              </div>
              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-green-800">Categories and subcategories added</span>
              </div>
              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-green-800">Products added and configured</span>
              </div>
              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-green-800">Payment methods configured</span>
              </div>
              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-green-800">Ready to launch!</span>
              </div>
            </div>
          </div>

          {/* Launch Button */}
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Button
              onClick={handleLaunchStore}
              variant="contained"
              size="large"
              startIcon={<Launch />}
              sx={{
                background: 'linear-gradient(45deg, #4caf50 30%, #2196f3 90%)',
                px: 6,
                py: 2,
                fontSize: '1.125rem',
                fontWeight: 600,
                '&:hover': {
                  background: 'linear-gradient(45deg, #388e3c 30%, #1976d2 90%)',
                  transform: 'scale(1.05)',
                },
                transition: 'all 0.2s',
                boxShadow: 3,
              }}
            >
              <Celebration sx={{ mr: 1 }} />
              Launch My Store
            </Button>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              This will make your store live and accessible to customers
            </Typography>
          </Box>
        </Stack>
      ) : (
        /* Launch Progress */
        <div className="text-center py-12">
          <div className="max-w-md mx-auto">
            <div className="mb-8">
              <div className="w-24 h-24 mx-auto mb-6 relative">
                <div className="w-24 h-24 border-4 border-blue-200 rounded-full animate-spin">
                  <div className="w-6 h-6 bg-blue-600 rounded-full absolute top-0 left-1/2 transform -translate-x-1/2"></div>
                </div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Launching Your Store...</h3>
              <p className="text-gray-600">Please wait while we set up everything for you</p>
            </div>

            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Setup Progress</span>
                <span className="text-sm text-gray-500">{Math.round(launchProgress)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-500"
                  style={{ width: `${launchProgress}%` }}
                />
              </div>
            </div>

            <div className="space-y-2 text-sm text-gray-600">
              <p>✓ Creating store database</p>
              <p>✓ Setting up categories</p>
              <p>✓ Importing products</p>
              <p>✓ Configuring payment methods</p>
              <p>✓ Setting up store theme</p>
              <p>✓ Generating store pages</p>
              <p className={launchProgress === 100 ? 'text-green-600 font-medium' : ''}>
                {launchProgress === 100 ? '✓' : '⏳'} Finalizing setup
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Navigation Buttons */}
      {!isLaunching && (
        <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ pt: 4, mt: 4, borderTop: 1, borderColor: 'divider' }}>
          <Button
            onClick={onBack}
            variant="outlined"
            startIcon={<ArrowBack />}
            size="large"
          >
            Back to Import
          </Button>
          <Stack direction="row" alignItems="center" spacing={1}>
            <CheckCircle sx={{ fontSize: 16, color: 'text.secondary' }} />
            <Typography variant="body2" color="text.secondary">
              You can always modify these settings later from your admin panel
            </Typography>
          </Stack>
        </Stack>
      )}
    </Box>
  );
};