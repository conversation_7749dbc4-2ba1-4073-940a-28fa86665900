'use client';

import React, { useState, useEffect } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Stack,
  Alert,
  CircularProgress,
  Grid,
  MenuItem,
  Select,
  InputLabel,
  FormControl,
  FormHelperText,
  Checkbox,
  FormGroup,
  FormControlLabel,
} from '@mui/material';
import {
  ArrowForward,
  CheckCircle,
  Error,
  Save,
} from '@mui/icons-material';
import { OnboardingStore, BUSINESS_TYPES, BUSINESS_CATEGORIES, PAYMENT_METHODS, INDIAN_STATES, COUNTRIES } from '@/types/onboarding';
import { useAuthStore } from '@/stores/authStore';
import { ImageUpload } from '@/components/common/ImageUpload';
import { ColorPalette } from '@/lib/utils/colorPalette';
import { strapiApi, StoreConfigurationPayload } from '@/lib/api/strapi';
import { useToast } from '@/app/providers/toast-provider';
import { useStoreConfigStore } from '@/stores/storeConfigStore';

const storeRegistrationSchema = z.object({
  name: z.string().optional(),
  description: z.string().optional(),
  handle: z.string().optional(),
  gstNumber: z.string().optional(),
  // Contact Information
  ownerEmail: z.string().optional(),
  storeEmail: z.string().optional(),
  phone: z.string().optional(),
  website: z.string().optional(),
  // Address Information
  addressLine1: z.string().optional(),
  addressLine2: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  pincode: z.string().optional(),
  country: z.string().optional(),
  paymentMethods: z.array(z.string()).optional(),
  businessType: z.string().optional(),
  businessCategory: z.string().optional(),
});

type StoreRegistrationForm = z.infer<typeof storeRegistrationSchema>;

interface StoreRegistrationStepProps {
  onComplete: (data: OnboardingStore) => void;
  initialData?: OnboardingStore | null;
}

const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: 48 * 4.5 + 8,
      width: 250,
    },
  },
};

export const StoreRegistrationStep: React.FC<StoreRegistrationStepProps> = ({
  onComplete,
  initialData
}) => {
  const [logoUrl, setLogoUrl] = useState<string>('');
  const [colorPalette, setColorPalette] = useState<ColorPalette | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasExistingData, setHasExistingData] = useState(false);
  const [hasAttemptedFetch, setHasAttemptedFetch] = useState(false);
  const { user } = useAuthStore();
  const { showToast } = useToast();
  const { 
    storeConfig, 
    setStoreConfig, 
    markAsSaved, 
    fromOnboardingStore, 
    toStrapiPayload 
  } = useStoreConfigStore();

  // Get user data from nested API response structure
  const userData = user?.user || user;
  const userMetadata = userData?.metadata || user?.metadata || {};

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting }
  } = useForm<StoreRegistrationForm>({
    resolver: zodResolver(storeRegistrationSchema),
    defaultValues: initialData || {
      name: userMetadata.store_name || '',
      description: '',
      handle: userMetadata.store_handle || '',
      gstNumber: userMetadata.gst_number || '',
      // Contact Information - auto-fill from API response
      ownerEmail: userData?.email || user?.email || '',
      storeEmail: userMetadata.store_email || userData?.email || user?.email || '',
      phone: userMetadata.phone || userMetadata.contact_number || '',
      website: userMetadata.website || '',
      // Address Information
      addressLine1: userMetadata.address_line_1 || userMetadata.address || '',
      addressLine2: userMetadata.address_line_2 || '',
      city: userMetadata.city || '',
      state: userMetadata.state || '',
      pincode: userMetadata.pincode || '',
      country: userMetadata.country || 'India',
      paymentMethods: userMetadata.payment_methods || [],
      businessType: userMetadata.business_type || BUSINESS_TYPES[0].value, // Auto-select first business type
      businessCategory: userMetadata.business_category || BUSINESS_CATEGORIES[0].value, // Auto-select first business category
    }
  });

  // Fetch existing store configuration on component mount
  useEffect(() => {
    const fetchExistingConfig = async () => {
      if (!user || hasAttemptedFetch) {
        setIsLoading(false);
        return;
      }
      
      setHasAttemptedFetch(true);

      try {
        console.log('=== FETCHING EXISTING STORE CONFIGURATION ===');
        const userData = user?.user || user;
        const userId = userData?.id || user?.id || '';
        
        if (!userId) {
          console.log('No user ID found, skipping fetch');
          setIsLoading(false);
          return;
        }

        // Fetch existing store configuration from Strapi
        // const existingStoreConfig = await strapiApi.getStoreConfigurationByUserId(userId);
        
        console.log('API response:', {initialData,storeConfig});
        return
        if (existingStoreConfig && existingStoreConfig.data) {
          console.log('Found existing store configuration:', existingStoreConfig.data);
          setHasExistingData(true);
          
          // Store the configuration in Zustand store
          const configData = existingStoreConfig.data;
          
          if (configData) {
            const storeConfigData = {
              store_name: configData.store_name || '',
              store_description: configData.store_description || '',
              store_handle: configData.store_handle || '',
              gst_number: configData.gst_number || '',
              owner_email: configData.email || '',
              store_email: configData.store_email || '',
              phone: configData.phone || '',
              website: configData.website || '',
              address_line_1: configData.address_line_1 || '',
              address_line_2: configData.address_line_2 || '',
              city: configData.city || '',
              state: configData.state || '',
              pincode: configData.pincode || '',
              country: configData.country || 'India',
              business_type: configData.business_type || '',
              business_category: configData.business_category || '',
              store_logo_url: configData.store_logo_url || '',
              store_color_palette: configData.store_color_palette || null,
              payment_methods: configData.payment_methods || {
                upi: false,
                bnpl: false,
                wallet: false,
                debit_card: false,
                credit_card: false,
                net_banking: false,
                cash_on_delivery: false,
              },
              user_id: userId,
              created_by_user: configData.created_by_user || 'admin',
              store_status: configData.store_status || 'active',
              onboarding_completed: configData.onboarding_completed || false,
              onboarding_step: configData.onboarding_step || 1,
              strapi_document_id: existingStoreConfig.data.documentId || existingStoreConfig.data.id?.toString(),
              last_updated: new Date().toISOString(),
            };
            
            console.log('Setting store config data:', storeConfigData);
            setStoreConfig(storeConfigData);
            markAsSaved(storeConfigData.strapi_document_id);
            
            showToast('Existing store configuration loaded successfully!', 'success');
          } else {
            console.log('No attributes found in existing store configuration');
            setHasExistingData(false);
          }
        } else {
          console.log('No existing store configuration found');
          setHasExistingData(false);
        }
      } catch (error) {
        console.error('Error fetching existing store configuration:', error);
        // Only show error toast for actual errors, not for "no data found" cases
        if (error instanceof Error && !error?.message.includes('404')) {
          showToast('Failed to load existing configuration. You can still proceed with setup.', 'warning');
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchExistingConfig();
  }, [user, hasAttemptedFetch]); // Add hasAttemptedFetch to prevent multiple calls

  // Auto-fill form when user data is available or when store config exists
  useEffect(() => {
    if (isLoading) return; // Wait for fetch to complete
    
    console.log('=== STORE REGISTRATION AUTO-FILL DEBUG ===');
    console.log('User data:', user);
    console.log('Store config:', storeConfig);
    console.log('Initial data:', initialData);
    console.log('Has existing data:', hasExistingData);
    
    // Priority: initialData > existingConfig/storeConfig > user metadata
    if (initialData) {
      console.log('Using initial data for auto-fill');
      // Form is already initialized with initialData in defaultValues
      if(initialData?.logo_url) setLogoUrl(initialData?.logo_url);
      if(initialData?.color_palette) setColorPalette(initialData?.color_palette);
      setHasExistingData(true);

      return;
    }
    
    if (storeConfig) {
      console.log('Using store config for auto-fill');
      // Auto-fill from store config
      const autoFillData = {
        name: storeConfig.store_name,
        description: storeConfig.store_description,
        handle: storeConfig.store_handle,
        gstNumber: storeConfig.gst_number,
        ownerEmail: storeConfig.owner_email,
        storeEmail: storeConfig.store_email,
        phone: storeConfig.phone,
        website: storeConfig.website,
        addressLine1: storeConfig.address_line_1,
        addressLine2: storeConfig.address_line_2,
        city: storeConfig.city,
        state: storeConfig.state,
        pincode: storeConfig.pincode,
        country: storeConfig.country,
        businessType: storeConfig.business_type,
        businessCategory: storeConfig.business_category,
      };
      
      // Convert payment methods object to array
      const paymentMethodsArray: string[] = [];
      if (storeConfig.payment_methods.upi) paymentMethodsArray.push('UPI');
      if (storeConfig.payment_methods.bnpl) paymentMethodsArray.push('BNPL');
      if (storeConfig.payment_methods.wallet) paymentMethodsArray.push('Wallets');
      if (storeConfig.payment_methods.debit_card || storeConfig.payment_methods.credit_card) paymentMethodsArray.push('Credit/Debit Cards');
      if (storeConfig.payment_methods.net_banking) paymentMethodsArray.push('Net Banking');
      if (storeConfig.payment_methods.cash_on_delivery) paymentMethodsArray.push('Cash on Delivery');
      
      // Set form values
      Object.entries(autoFillData).forEach(([key, value]) => {
        if (value) {
          setValue(key as keyof StoreRegistrationForm, value);
        }
      });
      
      setValue('paymentMethods', paymentMethodsArray);
      
      // Set logo and color palette
      if (storeConfig.store_logo_url) {
        setLogoUrl(storeConfig.store_logo_url);
      }
      if (storeConfig.store_color_palette) {
        setColorPalette(storeConfig.store_color_palette);
      }
      setHasExistingData(true);
      
    } else if (user && userData) {
      console.log('Using user metadata for auto-fill');
      // Auto-fill form fields with user data
      const autoFillData = {
        name: userMetadata.store_name || '',
        handle: userMetadata.store_handle || '',
        ownerEmail: userData?.email || user?.email || '',
        storeEmail: userMetadata.store_email || userData?.email || user?.email || '',
        phone: userMetadata.phone || userMetadata.contact_number || '',
        website: userMetadata.website || '',
        addressLine1: userMetadata.address_line_1 || userMetadata.address || '',
        addressLine2: userMetadata.address_line_2 || '',
        city: userMetadata.city || '',
        state: userMetadata.state || '',
        pincode: userMetadata.pincode || '',
        country: userMetadata.country || 'India',
        businessType: userMetadata.business_type || BUSINESS_TYPES[0].value, // Auto-select first business type
        businessCategory: userMetadata.business_category || BUSINESS_CATEGORIES[0].value, // Auto-select first business category
        gstNumber: userMetadata.gst_number || '',
      };
      
      console.log('Auto-filling form with user metadata:', autoFillData);
      
      // Set form values
      Object.entries(autoFillData).forEach(([key, value]) => {
        if (value) {
          setValue(key as keyof StoreRegistrationForm, value);
        }
      });
      
      // Set payment methods if available
      if (userMetadata.payment_methods && Array.isArray(userMetadata.payment_methods)) {
        setValue('paymentMethods', userMetadata.payment_methods);
      }
      setHasExistingData(false);

    }
  }, [user, userData, userMetadata, storeConfig, initialData, hasExistingData, setValue, isLoading]);

  const storeName = watch('name');

  // Auto-generate handle from store name
  useEffect(() => {
    if (storeName) {
      const handle = storeName
        .toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');
      setValue('handle', handle);
    }
  }, [storeName, setValue]);

  const handleImageUpload = (imageUrl: string, extractedColorPalette: ColorPalette | null) => {
    console.log('=== IMAGE UPLOAD CALLBACK ===');
    console.log('Image URL:', imageUrl);
    console.log('Color palette:', extractedColorPalette);
    
    setLogoUrl(imageUrl);
    setColorPalette(extractedColorPalette);
  };

  const handleUrlSubmit = (imageUrl: string, extractedColorPalette: ColorPalette | null) => {
    console.log('=== URL SUBMIT CALLBACK ===');
    console.log('Image URL:', imageUrl);
    console.log('Color palette:', extractedColorPalette);
    
    setLogoUrl(imageUrl);
    setColorPalette(extractedColorPalette);
  };

  const onSubmit = async (data: StoreRegistrationForm) => {
    setIsSaving(true);
    setSubmitError(null);
    
    try {
      console.log('=== SUBMITTING STORE REGISTRATION ===');
      console.log('Form data:', data);
      console.log('Logo URL:', logoUrl);
      console.log('Color palette:', colorPalette);
      
      // Get user data for API payload
      const userData = user?.user || user;
      const userId = userData?.id || user?.id || '';
      const createdBy = (userData?.first_name && userData?.last_name) ? `${userData.first_name} ${userData.last_name}` : 'admin';
      
      // Prepare data for next step
      const storeData: OnboardingStore = {
        ...data,
        logo: logoUrl
      };
      
      // Convert to store configuration format
      const newStoreConfig = fromOnboardingStore(storeData, userId, createdBy);
      
      // Add color palette if available
      if (colorPalette) {
        newStoreConfig.store_color_palette = colorPalette;
      }
      
      // Update store config in Zustand
      setStoreConfig(newStoreConfig);
      
      // Get Strapi payload
      const strapiPayload = toStrapiPayload([]);
    
  
      if (!strapiPayload) {
        throw new Error('Failed to prepare API payload');
      }
      
      console.log('Strapi payload:', strapiPayload);
      console.log('Has existing data:', hasExistingData);
      console.log('Store config document ID:', storeConfig?.strapi_document_id);
      
      let strapiResponse;
      console.log("hasExistingData::::::",{hasExistingData,storeConfig})
      if (hasExistingData && storeConfig?.strapi_document_id) {
        // Store data exists - make EDIT API call
        delete strapiPayload.store_handle;
        console.log('=== UPDATING EXISTING STORE CONFIGURATION ===');
        console.log('Making EDIT API call to update existing store');
        strapiResponse = await strapiApi.updateStoreConfiguration(storeConfig.strapi_document_id, strapiPayload);
        console.log('Store configuration updated successfully');
        showToast('Store configuration updated successfully!', 'success');
      } else {
        // No store data exists - make ADD API call
        console.log('=== CREATING NEW STORE CONFIGURATION ===');
        console.log('Making ADD API call to create new store');
        strapiResponse = await strapiApi.saveStoreConfiguration(strapiPayload);
        console.log('Store configuration created successfully');
        
        // After successful creation, mark as having existing data
        setHasExistingData(true);
        showToast('Store configuration created successfully!', 'success');
      }
      
      console.log('Strapi response:', strapiResponse);
      
      // Mark as saved with document ID
      const documentId = strapiResponse.data.documentId || strapiResponse.data.id?.toString();
      markAsSaved(documentId);
      
      // Always proceed to next step
      onComplete(storeData);
      
    } catch (error: any) {
      console.error('Error submitting store registration:', error);
      const errorMessage = error.message || 'Failed to save store configuration. Please try again.';
      setSubmitError(errorMessage);
      showToast(errorMessage, 'error');
    } finally {
      setIsSaving(false);
    }
  };

  // Show loading state while fetching existing configuration
  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 6 }}>
        <Stack alignItems="center" spacing={2}>
          <CircularProgress />
          <Typography color="text.secondary">
            Loading store configuration...
          </Typography>
        </Stack>
      </Box>
    );
  }

  return (

  <Box>
    <Stack spacing={4} sx={{ mb: 4 }}>
      <Box>
        <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 1 }}>
          <Typography variant="h4" component="h2" sx={{ fontWeight: 'bold' }}>
            Store Registration
          </Typography>
          {/* <Box
            sx={{
              px: 2,
              py: 0.5,
              borderRadius: 1,
              bgcolor: hasExistingData ? 'info.main' : 'success.main',
              color: 'white',
              fontSize: '0.75rem',
              fontWeight: 'bold',
            }}
          >
            {hasExistingData ? 'EDIT MODE' : 'CREATE MODE'}
          </Box> */}
        </Stack>
        <Typography variant="body1" color="text.secondary">
          {hasExistingData 
            ? 'Update your store details. You can modify any information as needed.' 
            : "Let's start by setting up your store details. All fields are optional - you can fill them now or update them later."
          }
        </Typography>
      </Box>

      {/* {hasExistingData && (
        <Alert severity="info" icon={<CheckCircle />}>
          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
            Edit Mode - Existing Configuration Found
          </Typography>
          <Typography variant="body2" sx={{ mt: 1 }}>
            We found your existing store configuration and have pre-filled the form.
            You can review and update any details as needed.
          </Typography>
        </Alert>
      )} */}
      
      {/* {!hasExistingData && (
        <Alert severity="success" icon={<CheckCircle />}>
          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
            Create Mode - New Store Setup
          </Typography>
          <Typography variant="body2" sx={{ mt: 1 }}>
            Let's create your store configuration. All fields are optional - you can fill them now or update them later.
          </Typography>
        </Alert>
      )} */}
    </Stack>

    <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
      {/* Basic Information */}
      <Paper variant="outlined" sx={{ p: 4, borderRadius: 3 }}>
        <Typography variant="h6" gutterBottom sx={{ marginBottom: 4 }}>
          Basic Information
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Store Name"
                  fullWidth
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  placeholder="Enter your store name"
                  variant="outlined"
                />
              )}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Controller
              name="handle"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Store Handle"
                  fullWidth
                  error={!!errors.handle}
                  helperText={errors.handle?.message || `This will be your store URL: yourstore.com/${watch('handle')}`}
                  placeholder="store-handle"
                  variant="outlined"
                />
              )}
            />
          </Grid>
        </Grid>

        <Box sx={{ mt: 3 }}>
          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label="Store Description"
                fullWidth
                multiline
                rows={4}
                error={!!errors.description}
                helperText={errors.description?.message}
                placeholder="Describe your store and what you sell..."
              />
            )}
          />
        </Box>

        <Box sx={{ mt: 3 }}>
          <Typography variant="body1" sx={{ mb: 2, fontWeight: 500 }}>
            Store Logo
          </Typography>
          <ImageUpload
            onImageUpload={handleImageUpload}
            onUrlSubmit={handleUrlSubmit}
            currentImageUrl={logoUrl}
            currentColorPalette={colorPalette}
          />
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Upload your store logo or provide an image URL. We'll automatically extract a color palette for your store theme.
          </Typography>
        </Box>
      </Paper>

      {/* Business Information */}
      <Paper variant="outlined" sx={{ p: 4, borderRadius: 3 }}>
        <Typography variant="h6" gutterBottom sx={{ mb: 4 }}>
          Business Information
        </Typography>

        <Grid container spacing={3} >
          <Grid item size={{xs: 12, md: 4}}>
            <Controller
              name="businessType"
              control={control}
              render={({ field }) => (
                <FormControl fullWidth error={!!errors.businessType}>
                  <InputLabel>Business Type</InputLabel>
                  <Select {...field} label="Business Type" MenuProps={MenuProps}>
                    <MenuItem value="">Select business type</MenuItem>
                    {BUSINESS_TYPES.map(type => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.businessType && (
                    <FormHelperText>{errors.businessType.message}</FormHelperText>
                  )}
                </FormControl>
              )}
            />
          </Grid>

          <Grid item size={{xs: 12, md: 4}}>
            <Controller
              name="businessCategory"
              control={control}
              render={({ field }) => (
                <FormControl fullWidth error={!!errors.businessCategory}>
                  <InputLabel>Business Category</InputLabel>
                  <Select {...field} label="Business Category" MenuProps={MenuProps}>
                    <MenuItem value="">Select business category</MenuItem>
                    {BUSINESS_CATEGORIES.map(category => (
                      <MenuItem key={category.value} value={category.value}>
                        {category.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.businessCategory && (
                    <FormHelperText>{errors.businessCategory.message}</FormHelperText>
                  )}
                </FormControl>
              )}
            />
          </Grid>

          <Grid item size={{xs: 12, md: 4}}>
            <Controller
              name="gstNumber"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="GST Number"
                  fullWidth
                  error={!!errors.gstNumber}
                  helperText={errors.gstNumber?.message}
                  placeholder="Enter GST number (optional)"
                />
              )}
            />
          </Grid>
        </Grid>
      </Paper>

      {/* Contact Information */}
      <Paper variant="outlined" sx={{ p: 4, borderRadius: 3 }}>
        <Typography variant="h6" gutterBottom sx={{mb : 4}}>
          Contact Information
        </Typography>

        <Grid container spacing={3}>
          <Grid item size={{xs: 12, md: 4}}>
            <Controller
              name="ownerEmail"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Owner Email Address"
                  fullWidth
                  error={!!errors.ownerEmail}
                  helperText={errors.ownerEmail?.message || "This is your account email address (read-only)"}
                  InputProps={{ readOnly: true }}
                  sx={{ bgcolor: 'action.hover' }}
                />
              )}
            />
          </Grid>

          <Grid item size={{xs: 12, md: 4}}>
            <Controller
              name="storeEmail"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Store Email Address"
                  fullWidth
                  error={!!errors.storeEmail}
                  helperText={errors.storeEmail?.message || "This email will be used for store communications"}
                  placeholder="<EMAIL>"
                />
              )}
            />
          </Grid>

          <Grid item size={{xs: 12, md: 4}}>
            <Controller
              name="phone"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Phone Number"
                  fullWidth
                  error={!!errors.phone}
                  helperText={errors.phone?.message}
                  placeholder="+91 9876543210"
                />
              )}
            />
          </Grid>

          <Grid item size={{xs: 12, md: 4}}>
            <Controller
              name="website"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Website"
                  fullWidth
                  error={!!errors.website}
                  helperText={errors.website?.message}
                  placeholder="https://www.yourstore.com (optional)"
                />
              )}
            />
          </Grid>
        </Grid>
      </Paper>

      {/* Address Information */}
      <Paper variant="outlined" sx={{ p: 4, borderRadius: 3 }}>
        <Typography variant="h6" gutterBottom sx={{mb: 4}}>
          Address Information
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item size={{xs: 12, md: 4}}>
            <Controller
              name="addressLine1"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Address Line 1"
                  fullWidth
                  error={!!errors.addressLine1}
                  helperText={errors.addressLine1?.message}
                  placeholder="Street address, building number"
                />
              )}
            />
          </Grid>

          <Grid item size={{xs: 12, md: 4}}>
            <Controller
              name="addressLine2"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Address Line 2"
                  fullWidth
                  error={!!errors.addressLine2}
                  helperText={errors.addressLine2?.message}
                  placeholder="Apartment, suite, floor (optional)"
                />
              )}
            />
          </Grid>

          <Grid item size={{xs: 12, md: 4}}>
            <Controller
              name="city"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="City"
                  fullWidth
                  error={!!errors.city}
                  helperText={errors.city?.message}
                  placeholder="City"
                />
              )}
            />
          </Grid>

          <Grid item size={{xs: 12, md: 4}}>
            <Controller
              name="state"
              control={control}
              render={({ field }) => (
                <FormControl fullWidth error={!!errors.state}>
                  <InputLabel>State</InputLabel>
                  <Select {...field} label="State" MenuProps={MenuProps}>
                    <MenuItem value="">Select state</MenuItem>
                    {INDIAN_STATES.map(state => (
                      <MenuItem key={state} value={state}>
                        {state}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.state && (
                    <FormHelperText>{errors.state.message}</FormHelperText>
                  )}
                </FormControl>
              )}
            />
          </Grid>

          <Grid item size={{xs: 12, md: 4}}>
            <Controller
              name="pincode"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Pincode"
                  fullWidth
                  error={!!errors.pincode}
                  helperText={errors.pincode?.message}
                  placeholder="123456"
                />
              )}
            />
          </Grid>

          <Grid item size={{xs: 12, md: 4}}>
            <Controller
              name="country"
              control={control}
              render={({ field }) => (
                <FormControl fullWidth error={!!errors.country}>
                  <InputLabel>Country</InputLabel>
                  <Select {...field} label="Country" MenuProps={MenuProps}>
                    {COUNTRIES.map(country => (
                      <MenuItem key={country} value={country}>
                        {country}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.country && (
                    <FormHelperText>{errors.country.message}</FormHelperText>
                  )}
                </FormControl>
              )}
            />
          </Grid>
        </Grid>
      </Paper>

      {/* Payment Methods */}
      <Paper variant="outlined" sx={{ p: 4, borderRadius: 3 }}>
        <Typography variant="h6" gutterBottom>
          Payment Methods
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Select the payment methods you want to accept
        </Typography>

        <Controller
          name="paymentMethods"
          control={control}
          render={({ field }) => (
            <FormGroup row>
              {PAYMENT_METHODS.map(method => (
                <FormControlLabel
                  key={method}
                  control={
                    <Checkbox
                      checked={field.value?.includes(method) || false}
                      onChange={e => {
                        const newValue = e.target.checked
                          ? [...(field.value || []), method]
                          : (field.value || []).filter(val => val !== method);
                        field.onChange(newValue);
                      }}
                    />
                  }
                  label={method}
                />
              ))}
            </FormGroup>
          )}
        />
        {errors.paymentMethods && (
          <Typography variant="caption" color="error" sx={{ mt: 1, display: 'block' }}>
            {errors.paymentMethods.message}
          </Typography>
        )}
      </Paper>

      {/* Error Display */}
      {submitError && (
        <Alert severity="error" icon={<Error />}>
          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
            Error saving store configuration
          </Typography>
          <Typography variant="body2" sx={{ mt: 1 }}>
            {submitError}
          </Typography>
        </Alert>
      )}

      {/* Submit Button */}
      <Stack direction="row" justifyContent="flex-end" sx={{ pt: 3, borderTop: 1, borderColor: 'divider' }}>
        <Button
          type="submit"
          disabled={isSaving || isSubmitting}
          variant="contained"
          size="large"
          startIcon={isSaving ? <CircularProgress size={16} /> : <Save />}
          sx={{ px: 4 }}
        >
          {isSaving 
            ? (hasExistingData ? 'Updating store...' : 'Creating store...') 
            : (hasExistingData ? 'Update & Continue' : 'Create & Continue')
          }
        </Button>
        {hasExistingData && <Button
          variant="contained"
          size="large"
          onClick={()=> onComplete(initialData)}
          sx={{ px: 4 ,ml: 3 }}
          endIcon={<ArrowForward />}

        >
          Continue to Cateogires
        </Button>}
       
      </Stack>
    </Box>
  </Box>

  );
};