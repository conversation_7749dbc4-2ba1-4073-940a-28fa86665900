'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Box,
  Typography,
  Button,
  Stack,
  CircularProgress,
  Card,
  CardContent,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Chip,
  Avatar,
  IconButton,
  Alert,
  AlertTitle,
  Divider,
  Paper,
  Grid,
  Fab,
  Tooltip,
  Badge,
} from '@mui/material';
import {
  ArrowBack,
  ArrowForward,
  Add,
  PhotoCamera,
  Collections,
  Inventory,
  Category,
  Visibility,
  Edit,
  CheckCircle,
  Warning,
  CloudUpload,
  Delete,
} from '@mui/icons-material';
import { OnboardingProduct, OnboardingCategory } from '@/types/onboarding';
import { useToast } from '@/app/providers/toast-provider';
import { categoriesApi } from '@/lib/api/categories';
import { productsApi, Product } from '@/lib/api/products';

const productSchema = z.object({
  name: z.string().min(2, 'Product name must be at least 2 characters'),
  handle: z.string().min(2, 'Handle must be at least 2 characters').regex(/^[a-z0-9-]+$/, 'Handle can only contain lowercase letters, numbers, and hyphens'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  originalPrice: z.number().min(0.01, 'Original price must be greater than 0'),
  salePrice: z.number().optional(),
  stock: z.number().min(0, 'Stock cannot be negative'),
  status: z.enum(['draft', 'published']),
  categoryId: z.string().min(1, 'Please select a category'),
  subcategoryId: z.string().optional(),
});

type ProductForm = z.infer<typeof productSchema>;

interface ProductManagementStepProps {
  onComplete: (data: OnboardingProduct[]) => void;
  onBack: () => void;
  storeHandle: string;
  categories: OnboardingCategory[];
  subcategories: OnboardingCategory[];
  initialData?: OnboardingProduct[];
}

interface ApiCategory {
  id: string;
  name: string;
  handle: string;
  description: string;
  parent_category_id?: string;
  created_at?: string;
  updated_at?: string;
}

type FlowStep = 'loading' | 'add-product' | 'view-existing';

export const ProductManagementStep: React.FC<ProductManagementStepProps> = ({
  onComplete,
  onBack,
  storeHandle,
  categories: initialCategories,
  subcategories: initialSubcategories,
  initialData
}) => {
  const [currentStep, setCurrentStep] = useState<FlowStep>('loading');
  const [existingProducts, setExistingProducts] = useState<Product[]>([]);
  const [existingCategories, setExistingCategories] = useState<ApiCategory[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  const [thumbnailPreview, setThumbnailPreview] = useState<string>('');
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [selectedProductIndex, setSelectedProductIndex] = useState<number>(0);
  const [hasInitialized, setHasInitialized] = useState(false);
  
  const { showToast } = useToast();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset: resetForm,
    formState: { errors }
  } = useForm<ProductForm>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: '',
      handle: '',
      description: '',
      originalPrice: 0,
      salePrice: undefined,
      stock: 0,
      status: 'draft',
      categoryId: '',
      subcategoryId: '',
    }
  });

  const productName = watch('name');
  const selectedCategoryId = watch('categoryId');

  // Auto-generate handle from product name (only when adding new product)
  useEffect(() => {
    if (productName && currentStep === 'add-product') {
      const handle = productName
        .toLowerCase()
        .replace(/[^a-z0-9\\s]/g, '')
        .replace(/\\s+/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');
      setValue('handle', handle);
    }
  }, [productName, setValue, currentStep]);

  // Auto-fill form when viewing existing products
  useEffect(() => {
    if (currentStep === 'view-existing' && existingProducts.length > 0) {
      const product = existingProducts[selectedProductIndex];
      if (product) {
        console.log('Auto-filling form with product data:', product);
        
        // Get price data from metadata or variants
        const originalPrice = product.metadata?.additional_data?.product_prices?.[0]?.original_price ||
                             product.variants?.[0]?.metadata?.original_price || 0;
        const salePrice = product.metadata?.additional_data?.product_prices?.[0]?.sale_price ||
                         product.variants?.[0]?.metadata?.sale_price;
        const stock = product.metadata?.additional_data?.product_quantity ||
                     product.variants?.[0]?.metadata?.product_quantity || 0;
        
        // Get category IDs
        const categoryId = product.categories?.[0]?.id || '';
        const subcategoryId = product.categories?.[1]?.id || '';
        
        // Fill form with existing product data
        setValue('name', product.title || '');
        setValue('handle', product.handle || '');
        setValue('description', product.description || '');
        setValue('originalPrice', originalPrice);
        setValue('salePrice', salePrice);
        setValue('stock', stock);
        setValue('status', product.status || 'draft');
        setValue('categoryId', categoryId);
        setValue('subcategoryId', subcategoryId);
        
        // Set images
        setThumbnailPreview(product.thumbnail || '');
        setImagePreviews(product.images || []);
      }
    }
  }, [currentStep, existingProducts, selectedProductIndex, setValue]);

  // Fetch existing products and categories on component mount
  useEffect(() => {
    let isMounted = true; // Flag to prevent state updates if component unmounts
    
    const fetchExistingData = async () => {
      if (!isMounted) return;
      
      setIsLoading(true);
      try {
        console.log('=== FETCHING EXISTING PRODUCTS AND CATEGORIES ===');
        
        // Fetch both products and categories in parallel
        console.log('Fetching data for store:', storeHandle);
        const [productsResult, categoriesResult] = await Promise.all([
          productsApi.getProducts(storeHandle),
          categoriesApi.getCategories(storeHandle)
        ]);
        
        if (!isMounted) return; // Don't update state if component unmounted
        
        // Handle categories
        if (categoriesResult.success && categoriesResult.data) {
          console.log('Fetched categories:', categoriesResult.data);
          setExistingCategories(categoriesResult.data);
        } else {
          console.log('No categories found:', categoriesResult.error);
          showToast('Unable to load categories. Please ensure categories are created first.', 'warning');
        }
        
        // Handle products
        if (productsResult.success && productsResult.data) {
          console.log('Fetched products:', productsResult.data);
          setExistingProducts(productsResult.data);
          
          if (productsResult.data.length > 0) {
            // Products exist, show form with existing data
            setCurrentStep('view-existing');
          } else {
            // No products exist, need to add product
            setCurrentStep('add-product');
          }
        } else {
          console.log('No products found or API error:', productsResult.error);
          setCurrentStep('add-product');
        }
        
      } catch (error) {
        if (!isMounted) return;
        console.error('Error fetching existing data:', error);
        showToast('Failed to load existing data', 'error');
        setCurrentStep('add-product');
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    // Only fetch if we haven't already initialized
    if (!hasInitialized) {
      setHasInitialized(true);
      fetchExistingData();
    } else {
      setIsLoading(false);
    }
    
    return () => {
      isMounted = false; // Cleanup flag
    };
  }, []); // Remove dependencies to prevent re-fetching

  const handleThumbnailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (currentStep === 'view-existing') return; // Prevent changes when viewing existing
    
    const file = e.target.files?.[0];
    if (file) {
      setThumbnailFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setThumbnailPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleImagesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (currentStep === 'view-existing') return; // Prevent changes when viewing existing
    
    const files = Array.from(e.target.files || []);
    setImageFiles(files);
    
    const previews: string[] = [];
    files.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        previews.push(e.target?.result as string);
        if (previews.length === files.length) {
          setImagePreviews(previews);
        }
      };
      reader.readAsDataURL(file);
    });
  };

  const onSubmit = async (data: ProductForm) => {
    if (currentStep === 'view-existing') {
      // Don't save when viewing existing products, just proceed
      handleProceedToNext();
      return;
    }
    
    // Prevent multiple submissions
    if (isSubmitting) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Prepare product payload
      const productPayload: Partial<Product> = {
        title: data.name,
        handle: data.handle,
        description: data.description,
        status: data.status,
        thumbnail: thumbnailPreview || undefined,
        images: imagePreviews.length > 0 ? imagePreviews : undefined,
        categories: [
          { id: data.categoryId },
          ...(data.subcategoryId ? [{ id: data.subcategoryId }] : [])
        ],
        options: [
          {
            title: "default",
            values: ["default"]
          }
        ],
        metadata: {
          additional_data: {
            product_prices: [
              {
                sale_price: data.salePrice,
                original_price: data.originalPrice
              }
            ],
            product_quantity: data.stock,
            product_inventory_status: "in_stock",
            product_overview: "",
            product_features: "",
            product_specifications: ""
          }
        },
        variants: [
          {
            title: "Default",
            sku: `${data.handle}-default`,
            prices: [
              {
                currency_code: "inr",
                amount: data.salePrice || data.originalPrice
              }
            ],
            metadata: {
              sale_price: data.salePrice,
              original_price: data.originalPrice,
              product_quantity: data.stock,
              product_inventory_status: "in_stock"
            }
          }
        ]
      };

      console.log('Creating product with payload:', productPayload);
      
      console.log('Creating product for store:', storeHandle);
      const result = await productsApi.createProduct(productPayload, storeHandle);

      if (result.success) {
        showToast('Product created successfully!', 'success');
        
        // Add the new product to existing products
        if (result.data) {
          setExistingProducts(prev => [...prev, result.data!]);
        }
        
        // Reset form
        resetForm();
        setThumbnailFile(null);
        setThumbnailPreview('');
        setImageFiles([]);
        setImagePreviews([]);
        
        // Move to view existing step
        setCurrentStep('view-existing');
      } else {
        showToast(result.error || 'Failed to create product', 'error');
      }
    } catch (error) {
      console.error('Error creating product:', error);
      showToast('An error occurred while creating the product', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleProceedToNext = () => {
    // Convert API products to OnboardingProduct format
    const products: OnboardingProduct[] = existingProducts.map(product => ({
      id: product.id,
      title: product.title,
      handle: product.handle,
      description: product.description,
      status: product.status,
      thumbnail: product.thumbnail,
      images: product.images,
      categories: product.categories,
      variants: product.variants,
      metadata: product.metadata,
      options: product.options,
    }));

    onComplete(products);
  };

  const handleProductSelection = (index: number) => {
    setSelectedProductIndex(index);
  };

  // Check if categories are available
  const hasCategories = existingCategories.length > 0;
  const mainCategories = existingCategories.filter(cat => !cat.parent_category_id);
  const subcategories = existingCategories.filter(cat => cat.parent_category_id);

  // Determine if form should be disabled
  const isFormDisabled = currentStep === 'view-existing';

  // Get current product for display
  const currentProduct = currentStep === 'view-existing' && existingProducts.length > 0 
    ? existingProducts[selectedProductIndex] 
    : null;

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
        <Stack alignItems="center" spacing={3}>
          <CircularProgress size={60} />
          <Typography variant="h6" color="text.secondary">
            Loading existing products...
          </Typography>
        </Stack>
      </Box>
    );
  }

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Header */}
      <Stack spacing={3} sx={{ mb: 4 }}>
        <Box>
          <Typography variant="h3" component="h1" sx={{ fontWeight: 700, mb: 1 }}>
            Product Management
          </Typography>
          <Typography variant="h6" color="text.secondary">
            {currentStep === 'add-product' && 'Add your first product to get started with your store.'}
            {currentStep === 'view-existing' && 'Your existing product details are shown below. You can proceed to the next step.'}
          </Typography>
        </Box>
      </Stack>

      {/* Categories Required Alert
      {!hasCategories && (
        <Alert severity="warning" sx={{ mb: 4 }}>
          <AlertTitle>Categories Required</AlertTitle>
          You need to add categories before creating products.{' '}
          <Button 
            onClick={onBack}
            variant="text"
            size="small"
            sx={{ textTransform: 'none' }}
          >
            Go back to Category Management
          </Button> to add categories first.
        </Alert>
      )} */}
{console.log('currentStep:::::::',currentStep)}
      {/* Product Selection (when viewing existing and multiple products) */}
      {/* {currentStep === 'view-existing' && existingProducts.length > 0 && (
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
              <Inventory />
              Select Product to View ({existingProducts.length} total)
            </Typography>
            <Grid container spacing={2}>
              {existingProducts.map((product, index) => (
                <Grid item xs={12} sm={6} md={4} key={product.id}>
                  <Card 
                    variant={selectedProductIndex === index ? "outlined" : "elevation"}
                    sx={{ 
                      cursor: 'pointer',
                      border: selectedProductIndex === index ? 2 : 1,
                      borderColor: selectedProductIndex === index ? 'primary.main' : 'divider',
                      '&:hover': { 
                        boxShadow: 3,
                        borderColor: 'primary.light'
                      }
                    }}
                    onClick={() => handleProductSelection(index)}
                  >
                    <CardContent>
                      <Stack direction="row" justifyContent="space-between" alignItems="flex-start" sx={{ mb: 2 }}>
                        <Typography variant="h6" noWrap sx={{ flex: 1 }}>
                          {product.title}
                        </Typography>
                        {selectedProductIndex === index && (
                          <Chip 
                            label="Selected" 
                            color="primary" 
                            size="small"
                            icon={<CheckCircle />}
                          />
                        )}
                      </Stack>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        {product.description}
                      </Typography>
                      <Chip 
                        label={`Status: ${product.status}`} 
                        size="small" 
                        variant="outlined"
                        color={product.status === 'published' ? 'success' : 'default'}
                      />
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      )} */}

      {/* Main Content */}
      <Grid container spacing={4}>
        {/* Form Section */}
        <Grid item size={12} >
          <Card sx={{ opacity: !hasCategories ? 0.5 : 1, pointerEvents: !hasCategories ? 'none' : 'auto' }}>
            <CardContent sx={{ p: 4 }}>
              <Stack spacing={3} sx={{ mb: 4 }}>
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 600, mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Add />
                    {currentStep === 'add-product' ? 'Add Product' : 'Product Details'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {currentStep === 'add-product' ? 'Create your first product' : 'Viewing existing product information'}
                  </Typography>
                  
                  {currentStep === 'view-existing' && (
                    <Alert severity="info" sx={{ mt: 2 }}>
                      Product details are displayed for review. Fields are disabled to prevent accidental changes.
                    </Alert>
                  )}
                </Box>
              </Stack>

              <Box component="form" onSubmit={handleSubmit(onSubmit)}>
                <Stack spacing={4}>
                  {/* Basic Information */}
                  <Paper variant="outlined" sx={{ p: 3 }}>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Edit />
                      Basic Information
                    </Typography>
                    <Grid container spacing={3}>
                      <Grid item size={{xs:12, md:4}}>
                        <TextField
                          label="Product Name"
                          {...register('name')}
                          disabled={isFormDisabled}
                          error={!!errors.name}
                          helperText={errors.name?.message}
                          placeholder="Enter product name"
                          required
                          fullWidth
                        />
                      </Grid>
                      <Grid item size={{xs:12, md:4}}>
                        <TextField
                          label="Handle"
                          {...register('handle')}
                          disabled={isFormDisabled}
                          error={!!errors.handle}
                          helperText={errors.handle?.message || `URL: /${storeHandle}/products/${watch('handle')}`}
                          placeholder="product-handle"
                          required
                          fullWidth
                        />
                      </Grid>
                      <Grid item size={{xs:12, md:4}}>
                        <TextField
                          label="Description"
                          {...register('description')}
                          disabled={isFormDisabled}
                          error={!!errors.description}
                          helperText={errors.description?.message}
                          placeholder="Describe your product..."
                          multiline
                          rows={4}
                          required
                          fullWidth
                        />
                      </Grid>
                    </Grid>
                  </Paper>

                  {/* Pricing and Inventory */}
                  <Paper variant="outlined" sx={{ p: 3 }}>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Inventory />
                      Pricing & Inventory
                    </Typography>
                    <Grid container spacing={3}>
                      <Grid item size={{xs:12, md:4}}>
                        <TextField
                          label="Original Price"
                          type="number"
                          inputProps={{ step: "0.01" }}
                          {...register('originalPrice', { valueAsNumber: true })}
                          disabled={isFormDisabled}
                          error={!!errors.originalPrice}
                          helperText={errors.originalPrice?.message}
                          placeholder="0.00"
                          required
                          fullWidth
                        />
                      </Grid>
                      <Grid item size={{xs:12, md:4}}>
                        <TextField
                          label="Sale Price"
                          type="number"
                          inputProps={{ step: "0.01" }}
                          {...register('salePrice', { valueAsNumber: true })}
                          disabled={isFormDisabled}
                          error={!!errors.salePrice}
                          helperText={errors.salePrice?.message || "Optional"}
                          placeholder="0.00"
                          fullWidth
                        />
                      </Grid>
                      <Grid item size={{xs:12, md:4}}>
                        <TextField
                          label="Stock"
                          type="number"
                          {...register('stock', { valueAsNumber: true })}
                          disabled={isFormDisabled}
                          error={!!errors.stock}
                          helperText={errors.stock?.message}
                          placeholder="0"
                          required
                          fullWidth
                        />
                      </Grid>
                    </Grid>
                  </Paper>

                  {/* Category and Status */}
                  <Paper variant="outlined" sx={{ p: 3 }}>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Category />
                      Category & Status
                    </Typography>
                    <Grid container spacing={3}>
                      <Grid item size={{xs:12, md:4}}>
                        <FormControl fullWidth required error={!!errors.categoryId}>
                          <InputLabel>Category</InputLabel>
                          <Select
                            {...register('categoryId')}
                            disabled={isFormDisabled}
                            label="Category"
                            value={watch('categoryId') || ''}
                          >
                            
                            {mainCategories.map((category) => (
                              <MenuItem key={category.id} value={category.id}>
                                {category.name}
                              </MenuItem>
                            ))}
                          </Select>
                          {errors.categoryId && (
                            <FormHelperText>{errors.categoryId.message}</FormHelperText>
                          )}
                        </FormControl>
                      </Grid>
                      <Grid item size={{xs:12, md:4}}>
                        <FormControl fullWidth disabled={isFormDisabled || !selectedCategoryId}>
                          <InputLabel>Subcategory</InputLabel>
                          <Select
                            {...register('subcategoryId')}
                            label="Subcategory"
                            value={watch('subcategoryId') || ''}
                          >
                            <MenuItem value="">
                              <em>Select subcategory (optional)</em>
                            </MenuItem>
                            {subcategories
                              .filter(sub => sub.parent_category_id === selectedCategoryId)
                              .map((subcategory) => (
                                <MenuItem key={subcategory.id} value={subcategory.id}>
                                  {subcategory.name}
                                </MenuItem>
                              ))}
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item size={{xs:12, md:4}}>
                        <FormControl fullWidth required>
                          <InputLabel>Status</InputLabel>
                          <Select
                            {...register('status')}
                            disabled={isFormDisabled}
                            label="Status"
                            value={watch('status') || 'draft'}
                          >
                            <MenuItem value="draft">Draft</MenuItem>
                            <MenuItem value="published">Published</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                    </Grid>
                  </Paper>

                  {/* Images */}
                  <Paper variant="outlined" sx={{ p: 3 }}>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PhotoCamera />
                      Product Images
                    </Typography>
                    
                    {/* Thumbnail */}
                    <Box sx={{ mb: 4 }}>
                      <Typography variant="subtitle1" sx={{ mb: 2 }}>
                        Thumbnail Image
                      </Typography>
                      <Stack direction="row" spacing={3} alignItems="center">
                        {thumbnailPreview && (
                          <Avatar
                            src={thumbnailPreview}
                            alt="Thumbnail preview"
                            sx={{ width: 80, height: 80 }}
                            variant="rounded"
                          />
                        )}
                        <Box>
                          <input
                            type="file"
                            accept="image/*"
                            onChange={handleThumbnailChange}
                            disabled={isFormDisabled}
                            style={{ display: 'none' }}
                            id="thumbnail-upload"
                          />
                          <label htmlFor="thumbnail-upload">
                            <Button
                              variant="outlined"
                              component="span"
                              disabled={isFormDisabled}
                              startIcon={<CloudUpload />}
                              sx={{ mb: 1 }}
                            >
                              {isFormDisabled ? 'Image Set' : 'Choose Thumbnail'}
                            </Button>
                          </label>
                          <Typography variant="caption" display="block" color="text.secondary">
                            Recommended: 400x400px, PNG or JPG
                          </Typography>
                        </Box>
                      </Stack>
                    </Box>

                    {/* Additional Images */}
                    <Box>
                      <Typography variant="subtitle1" sx={{ mb: 2 }}>
                        Additional Images
                      </Typography>
                      {imagePreviews.length > 0 && (
                        <Stack direction="row" spacing={1} sx={{ mb: 2 }} flexWrap="wrap">
                          {imagePreviews.map((preview, index) => (
                            <Avatar
                              key={index}
                              src={preview}
                              alt={`Preview ${index + 1}`}
                              sx={{ width: 60, height: 60 }}
                              variant="rounded"
                            />
                          ))}
                        </Stack>
                      )}
                      <Box>
                        <input
                          type="file"
                          accept="image/*"
                          multiple
                          onChange={handleImagesChange}
                          disabled={isFormDisabled}
                          style={{ display: 'none' }}
                          id="images-upload"
                        />
                        <label htmlFor="images-upload">
                          <Button
                            variant="outlined"
                            component="span"
                            disabled={isFormDisabled}
                            startIcon={<Collections />}
                            sx={{ mb: 1 }}
                          >
                            {isFormDisabled ? 'Images Set' : 'Choose Images'}
                          </Button>
                        </label>
                        <Typography variant="caption" display="block" color="text.secondary">
                          You can select multiple images
                        </Typography>
                      </Box>
                    </Box>
                  </Paper>

                  {/* Submit Button */}
                  {currentStep === 'add-product' && <Button
                    type="submit"
                    disabled={!hasCategories || isSubmitting}
                    variant="contained"
                    size="large"
                    fullWidth
                    startIcon={isSubmitting ? <CircularProgress size={20} /> : <Add />}
                    sx={{ py: 2 }}
                  >
                    {(isSubmitting ? 'Creating Product...' : 'Add Product') }
                  </Button>}
                </Stack>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Summary Section */}
        <Grid item size={12} >
          <Stack spacing={3}>
            {/* Progress Indicator */}
            {/* <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Visibility />
                  Progress
                </Typography>
                <Stack spacing={2}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <CheckCircle color={hasCategories ? 'success' : 'disabled'} />
                    <Typography variant="body2" color={hasCategories ? 'text.primary' : 'text.secondary'}>
                      Categories Created
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <CheckCircle color={existingProducts.length > 0 ? 'success' : 'disabled'} />
                    <Typography variant="body2" color={existingProducts.length > 0 ? 'text.primary' : 'text.secondary'}>
                      Products Added ({existingProducts.length})
                    </Typography>
                  </Box>
                </Stack>
              </CardContent>
            </Card> */}

            {/* Instructions */}
            <Alert severity="info">
              <AlertTitle>Next Steps</AlertTitle>
              {currentStep === 'add-product' && 'Create your first product to start selling.'}
              {currentStep === 'view-existing' && 'Review your product details and continue to the next step.'}
            </Alert>
          </Stack>
        </Grid>
      </Grid>

      {/* Navigation Buttons */}
      <Stack direction="row" justifyContent="space-between" sx={{ pt: 4, mt: 4, borderTop: 1, borderColor: 'divider' }}>
        <Button
          onClick={onBack}
          variant="outlined"
          startIcon={<ArrowBack />}
          size="large"
        >
          Back to Categories
        </Button>

        {currentStep === 'view-existing' && (
          <Button
            onClick={handleProceedToNext}
            variant="contained"
            endIcon={<ArrowForward />}
            size="large"
          >
            Continue to Next Step
          </Button>
        )}
      </Stack>
    </Box>
  );
};