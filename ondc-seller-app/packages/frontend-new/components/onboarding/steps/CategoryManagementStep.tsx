'use client';

import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Box,
  Typography,
  TextField,
  Button,
  Card,
  CardContent,
  Stack,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Chip,
  Alert,
  AlertTitle,
  Divider,
  Paper,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Stepper,
  Step,
  StepLabel,
  StepContent,
} from '@mui/material';
import {
  ArrowBack,
  ArrowForward,
  Add,
  Category,
  SubdirectoryArrowRight,
  CheckCircle,
  Warning,
  Folder,
  FolderOpen,
  Done,
  SkipNext,
} from '@mui/icons-material';
import { OnboardingCategory } from '@/types/onboarding';
import { useToast } from '@/app/providers/toast-provider';
import { categoriesApi } from '@/lib/api/categories';

const categorySchema = z.object({
  name: z.string().min(2, 'Category name must be at least 2 characters'),
  handle: z.string().min(2, 'Handle must be at least 2 characters').regex(/^[a-z0-9-]+$/, 'Handle can only contain lowercase letters, numbers, and hyphens'),
  description: z.string().min(5, 'Description must be at least 5 characters'),
  parentId: z.string().optional(),
});

type CategoryForm = z.infer<typeof categorySchema>;

interface CategoryManagementStepProps {
  onComplete: (data: { categories: OnboardingCategory[], subcategories: OnboardingCategory[] }) => void;
  onBack: () => void;
  storeHandle: string;
  initialData?: {
    categories: OnboardingCategory[];
    subcategories: OnboardingCategory[];
  };
}

interface ApiCategory {
  id: string;
  name: string;
  handle: string;
  description: string;
  parent_category_id?: string;
  created_at?: string;
  updated_at?: string;
}

type FlowStep = 'loading' | 'add-category' | 'add-subcategory' | 'complete';

export const CategoryManagementStep: React.FC<CategoryManagementStepProps> = ({
  onComplete,
  onBack,
  storeHandle,
  initialData
}) => {
  const [currentStep, setCurrentStep] = useState<FlowStep>('loading');
  const [existingCategories, setExistingCategories] = useState<ApiCategory[]>([]);
  const [existingSubcategories, setExistingSubcategories] = useState<ApiCategory[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');
  const [hasInitialized, setHasInitialized] = useState(false);
  
  const { showToast } = useToast();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset: resetForm,
    formState: { errors }
  } = useForm<CategoryForm>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: '',
      handle: '',
      description: '',
      parentId: '',
    }
  });

  const categoryName = watch('name');

  // Auto-generate handle from category name
  useEffect(() => {
    if (categoryName) {
      const handle = categoryName
        .toLowerCase()
        .replace(/[^a-z0-9\\s]/g, '')
        .replace(/\\s+/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');
      setValue('handle', handle);
    }
  }, [categoryName, setValue]);

  // Fetch existing categories on component mount
  useEffect(() => {
    let isMounted = true; // Flag to prevent state updates if component unmounts
    
    const fetchExistingCategories = async () => {
      if (!isMounted) return;
      
      setIsLoading(true);
      try {
        console.log('=== FETCHING EXISTING CATEGORIES (MOUNT) ===');
        const result = await categoriesApi.getCategories(storeHandle);
        
        if (!isMounted) return; // Don't update state if component unmounted
        
        if (result.success && result.data) {
          console.log('Fetched categories:', result.data);
          
          // Separate categories and subcategories based on parent_category_id
          const categories = result.data.filter(cat => !cat.parent_category_id);
          const subcategories = result.data.filter(cat => cat.parent_category_id);
          
          console.log('Categories (no parent):', categories);
          console.log('Subcategories (with parent):', subcategories);
          
          setExistingCategories(categories);
          setExistingSubcategories(subcategories);
          
          // Determine the next step based on existing data
          if (categories.length === 0) {
            // No categories exist, need to add category first
            setCurrentStep('add-category');
          } else if (subcategories.length === 0) {
            // Categories exist but no subcategories, offer to add subcategory
            setCurrentStep('add-subcategory');
            // Pre-select the first category
            if (categories.length > 0) {
              setSelectedCategoryId(categories[0].id);
              setValue('parentId', categories[0].id);
            }
          } else {
            // Both categories and subcategories exist, can proceed to products
            setCurrentStep('complete');
          }
        } else {
          console.log('No categories found or API error:', result.error);
          // No categories found, start with adding category
          setCurrentStep('add-category');
        }
      } catch (error) {
        if (!isMounted) return;
        console.error('Error fetching categories:', error);
        showToast('Failed to load existing categories', 'error');
        setCurrentStep('add-category');
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    // Only fetch if we haven't already initialized
    if (!hasInitialized) {
      setHasInitialized(true);
      fetchExistingCategories();
    } else {
      setIsLoading(false);
    }
    
    return () => {
      isMounted = false; // Cleanup flag
    };
  }, []); // Remove dependencies to prevent re-fetching

  const onSubmit = useCallback(async (data: CategoryForm) => {
    setIsSubmitting(true);
    
    try {
      // Prepare API payload
      const payload = {
        name: data.name,
        handle: data.handle,
        description: data.description,
        ...(currentStep === 'add-subcategory' && data.parentId && { parent_category_id: data.parentId }),
      };

      console.log('Creating category/subcategory with payload:', payload);
      
      const result = await categoriesApi.createCategory(payload, storeHandle);

      if (result.success) {
        const isCategory = currentStep === 'add-category';
        showToast(
          `${isCategory ? 'Category' : 'Subcategory'} created successfully!`, 
          'success'
        );
        
        // Reset form
        resetForm();
        
        if (isCategory) {
          // After creating category, add it to existing categories without refetching
          const newCategory: ApiCategory = {
            id: result.data.id || `temp-${Date.now()}`,
            name: data.name,
            handle: data.handle,
            description: data.description,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };
          
          setExistingCategories(prev => [...prev, newCategory]);
          
          // Pre-select the newly created category
          setSelectedCategoryId(newCategory.id);
          setValue('parentId', newCategory.id);
          
          setCurrentStep('add-subcategory');
        } else {
          // After creating subcategory, add it to existing subcategories without refetching
          const newSubcategory: ApiCategory = {
            id: result.data.id || `temp-${Date.now()}`,
            name: data.name,
            handle: data.handle,
            description: data.description,
            parent_category_id: data.parentId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };
          
          setExistingSubcategories(prev => [...prev, newSubcategory]);
          
          setCurrentStep('complete');
        }
      } else {
        showToast(result.error || 'Failed to create category', 'error');
      }
    } catch (error) {
      console.error('Error creating category:', error);
      showToast('An error occurred while creating the category', 'error');
    } finally {
      setIsSubmitting(false);
    }
  }, [currentStep, resetForm, showToast, setValue, storeHandle]);

  const handleSkipSubcategory = useCallback(() => {
    setCurrentStep('complete');
  }, []);

  const handleProceedToProducts = useCallback(() => {
    // Convert API categories to OnboardingCategory format
    const categories: OnboardingCategory[] = existingCategories.map(cat => ({
      id: cat.id,
      name: cat.name,
      handle: cat.handle,
      description: cat.description,
      isSubcategory: false,
    }));

    const subcategories: OnboardingCategory[] = existingSubcategories.map(cat => ({
      id: cat.id,
      name: cat.name,
      handle: cat.handle,
      description: cat.description,
      parentId: cat.parent_category_id,
      isSubcategory: true,
    }));

    onComplete({ categories, subcategories });
  }, [existingCategories, existingSubcategories, onComplete]);

  const getActiveStep = () => {
    switch (currentStep) {
      case 'add-category': return 0;
      case 'add-subcategory': return 1;
      case 'complete': return 2;
      default: return 0;
    }
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
        <Stack alignItems="center" spacing={3}>
          <CircularProgress size={60} />
          <Typography variant="h6" color="text.secondary">
            Loading existing categories...
          </Typography>
        </Stack>
      </Box>
    );
  }

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Header */}
      <Stack spacing={3} sx={{ mb: 4 }}>
        <Box>
          <Typography variant="h3" component="h1" sx={{ fontWeight: 700, mb: 1 }}>
            Category Management
          </Typography>
          <Typography variant="h6" color="text.secondary">
            {currentStep === 'add-category' && 'Create a category for your products to get started.'}
            {currentStep === 'add-subcategory' && 'Add a subcategory to better organize your products within the category.'}
            {currentStep === 'complete' && 'Your categories are set up! You can now proceed to add products.'}
          </Typography>
        </Box>
      </Stack>

      <Grid container spacing={4} size={12}>
        {/* Form Section */}
        {currentStep !== 'complete' && 
          <Grid item size={12}>
          <Card>
            <CardContent sx={{ p: 4 }}>
              {/* Progress Stepper */}
        

              {/* Category Form */}
              {currentStep === 'add-category' && (
                <Paper variant="outlined" sx={{ p: 3 }}>
                  <Typography variant="h5" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Category />
                    Create Category
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 4 }}>
                    Add a main category for your products
                  </Typography>

                  <Box component="form" onSubmit={handleSubmit(onSubmit)}>
                    <Stack spacing={3}>
                      <TextField
                        label="Category Name"
                        {...register('name')}
                        error={!!errors.name}
                        helperText={errors.name?.message}
                        placeholder="Enter category name"
                        required
                        fullWidth
                      />

                      <TextField
                        label="Handle"
                        {...register('handle')}
                        error={!!errors.handle}
                        helperText={errors.handle?.message || `URL: /${storeHandle}/category/${watch('handle')}`}
                        placeholder="category-handle"
                        required
                        fullWidth
                      />

                      <TextField
                        label="Description"
                        {...register('description')}
                        error={!!errors.description}
                        helperText={errors.description?.message}
                        placeholder="Describe this category..."
                        multiline
                        rows={3}
                        required
                        fullWidth
                      />

                      <Button
                        type="submit"
                        disabled={isSubmitting}
                        variant="contained"
                        fullWidth
                        size="large"
                        startIcon={isSubmitting ? <CircularProgress size={20} /> : <Add />}
                        sx={{ py: 2 }}
                      >
                        {isSubmitting ? 'Creating Category...' : 'Create Category'}
                      </Button>
                    </Stack>
                  </Box>
                </Paper>
              )}

              {/* Subcategory Form */}
              {currentStep === 'add-subcategory' && (
                <Paper variant="outlined" sx={{ p: 3 }}>
                  <Typography variant="h5" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <SubdirectoryArrowRight />
                    Create Subcategory
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 4 }}>
                    Add a subcategory under your main category
                  </Typography>

                  <Box component="form" onSubmit={handleSubmit(onSubmit)}>
                    <Stack spacing={3}>
                      <FormControl fullWidth required error={!!errors.parentId}>
                        <InputLabel>Parent Category</InputLabel>
                        <Select
                          {...register('parentId', { required: 'Please select a parent category' })}
                          label="Parent Category"
                          value={watch('parentId') || ''}
                        >
                          <MenuItem value="">
                            <em>Select parent category</em>
                          </MenuItem>
                          {existingCategories.map((category) => (
                            <MenuItem key={category.id} value={category.id}>
                              {category.name}
                            </MenuItem>
                          ))}
                        </Select>
                        {errors.parentId && (
                          <FormHelperText>{errors.parentId.message}</FormHelperText>
                        )}
                      </FormControl>

                      <TextField
                        label="Subcategory Name"
                        {...register('name')}
                        error={!!errors.name}
                        helperText={errors.name?.message}
                        placeholder="Enter subcategory name"
                        required
                        fullWidth
                      />

                      <TextField
                        label="Handle"
                        {...register('handle')}
                        error={!!errors.handle}
                        helperText={errors.handle?.message || `URL: /${storeHandle}/subcategory/${watch('handle')}`}
                        placeholder="subcategory-handle"
                        required
                        fullWidth
                      />

                      <TextField
                        label="Description"
                        {...register('description')}
                        error={!!errors.description}
                        helperText={errors.description?.message}
                        placeholder="Describe this subcategory..."
                        multiline
                        rows={3}
                        required
                        fullWidth
                      />

                      <Stack direction="row" spacing={2} justifyContent='end'>
                        <Button
                          type="submit"
                          disabled={isSubmitting}
                          variant="contained"
                          startIcon={isSubmitting ? <CircularProgress size={20} /> : <Add />}
                          sx={{ py: 2 }}
                        >
                          {isSubmitting ? 'Creating...' : 'Create Subcategory'}
                        </Button>
                      </Stack>
                    </Stack>
                  </Box>
                </Paper>
              )}

            
            </CardContent>
          </Card>
          </Grid>
        }

        {/* Summary Section */}
        <Grid item size={12}>
          <Stack spacing={3}>
            {/* Existing Categories */}
            {existingCategories.length > 0 && (
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Folder />
                    Categories ({existingCategories.length})
                  </Typography>
                  <Stack spacing={2}>
                    {existingCategories.map((category) => (
                      <Paper key={category.id} variant="outlined" sx={{ p: 2, bgcolor: 'success.50' }}>
                        <Stack direction="row" justifyContent="space-between" alignItems="flex-start" sx={{ mb: 1 }}>
                          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                            {category.name}
                          </Typography>
                          <Chip 
                            label="Added" 
                            color="success" 
                            size="small"
                            icon={<CheckCircle />}
                          />
                        </Stack>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          {category.description}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Handle: {category.handle}
                        </Typography>
                      </Paper>
                    ))}
                  </Stack>
                </CardContent>
              </Card>
            )}

            {/* Existing Subcategories */}
            {existingSubcategories.length > 0 && (
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <FolderOpen />
                    Subcategories ({existingSubcategories.length})
                  </Typography>
                  <Stack spacing={2}>
                    {existingSubcategories.map((subcategory) => {
                      const parentCategory = existingCategories.find(cat => cat.id === subcategory.parent_category_id);
                      return (
                        <Paper key={subcategory.id} variant="outlined" sx={{ p: 2, bgcolor: 'info.50' }}>
                          <Stack direction="row" justifyContent="space-between" alignItems="flex-start" sx={{ mb: 1 }}>
                            <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                              {subcategory.name}
                            </Typography>
                            <Chip 
                              label="Added" 
                              color="info" 
                              size="small"
                              icon={<CheckCircle />}
                            />
                          </Stack>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                            {subcategory.description}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Parent: {parentCategory?.name} | Handle: {subcategory.handle}
                          </Typography>
                        </Paper>
                      );
                    })}
                  </Stack>
                </CardContent>
              </Card>
            )}

            {/* Instructions */}
            <Alert severity="info">
              <AlertTitle>Next Steps</AlertTitle>
              {currentStep === 'add-category' && 'Create at least one category to organize your products.'}
              {currentStep === 'add-subcategory' && 'Optionally add subcategories for better product organization, or skip to proceed.'}
              {currentStep === 'complete' && 'Your categories are ready! Click "Continue to Products" to start adding products.'}
            </Alert>
          </Stack>
        </Grid>
      </Grid>

      {/* Navigation Buttons */}
      <Stack direction="row" justifyContent="space-between" sx={{ pt: 4, mt: 4, borderTop: 1, borderColor: 'divider' }}>
        <Button
          onClick={onBack}
          variant="outlined"
          startIcon={<ArrowBack />}
          size="large"
        >
          Back to Store Details
        </Button>

        {currentStep === 'complete' && (
          <Button
            onClick={handleProceedToProducts}
            variant="contained"
            endIcon={<ArrowForward />}
            size="large"
          >
            Continue to Products
          </Button>
        )}
      </Stack>
    </Box>
  );
};