import { useF<PERSON>, Controller } from "react-hook-form";
import { TextField, Button, MenuItem, Typography, Paper } from "@mui/material";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useRouter } from "next/navigation";


const storeConfigSchema = z.object({
  storeName: z.string(),
  storeHandle: z.string(),
  storeDescription: z.string(),
  businessType: z.string(),
  businessCategory: z.string(),
  paymentModes: z.array(z.string()),
  email: z.string().email(),
  contactNumber: z.string(),
  address: z.object({
    street: z.string(),
    city: z.string(),
    state: z.string(),
    country: z.string(),
    postalCode: z.string(),
  }),
  theme: z.string(),
  logoUrl: z.string().optional(),
});

export default function StoreConfigurationForm({ defaultValues }) {
    const router = useRouter();
  const {
    control,
    handleSubmit,
    register,
    formState: { errors },
  } = useForm({
    defaultValues,
    resolver: zodResolver(storeConfigSchema),
  });

  const onSubmit = (data) => {
    
    console.log("Form Submitted:", data);
    // redirect to next stage with query param: /onboarding?stage=add-product
      // router.push(`/${data.storeHandle}/onboarding?stage=add-product`);
      // router.push(`/${data.storeHandle}/onboarding?stage=import-products`);
      
  
  };

  return (
    <Paper className="p-6 rounded-2xl shadow-md space-y-4">
      <Typography variant="h6">Basic Store Information</Typography>
      <TextField
        label="Store Name"
        fullWidth
        {...register("storeName")}
        disabled
      />
      <TextField
        label="Store Handle"
        fullWidth
        {...register("storeHandle")}
        disabled
      />
      <TextField
        label="Description"
        fullWidth
        multiline
        rows={3}
        {...register("storeDescription")}
      />

      <TextField
        label="Business Type"
        select
        fullWidth
        {...register("businessType")}
      >
        <MenuItem value="Individual">Individual</MenuItem>
        <MenuItem value="Company">Company</MenuItem>
      </TextField>

      <TextField
        label="Business Category"
        fullWidth
        {...register("businessCategory")}
      />

      <Typography variant="h6" className="pt-4">
        Payment Modes
      </Typography>
      {/* Replace with checkbox group or chips */}
      <TextField
        label="Payment Modes (comma-separated)"
        fullWidth
        {...register("paymentModes")}
      />

      <Typography variant="h6" className="pt-4">
        Contact Info
      </Typography>
      <TextField label="Email" fullWidth {...register("email")} />
      <TextField
        label="Contact Number"
        fullWidth
        {...register("contactNumber")}
      />

      <Typography variant="h6" className="pt-4">
        Store Address
      </Typography>
      <TextField label="Street" fullWidth {...register("address.street")} />
      <TextField label="City" fullWidth {...register("address.city")} />
      <TextField label="State" fullWidth {...register("address.state")} />
      <TextField label="Country" fullWidth {...register("address.country")} />
      <TextField
        label="Postal Code"
        fullWidth
        {...register("address.postalCode")}
      />

      <Typography variant="h6" className="pt-4">
        Store Branding
      </Typography>
      <TextField label="Theme" fullWidth {...register("theme")} />
      <TextField label="Logo URL" fullWidth {...register("logoUrl")} />
      {/* Optionally add Upload logo button */}

      <Button
        onClick={handleSubmit(onSubmit)}
        variant="contained"
        fullWidth
        className="mt-4"
      >
        Save & Continue
      </Button>
    </Paper>
  );
}
