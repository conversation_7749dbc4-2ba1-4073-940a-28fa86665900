import React from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import {
  Paper,
  Typography,
  TextField,
  Button,
} from '@mui/material';
import VariantFields from "./VariantFields";

export default function AddProduct({ categories }) {
  const router = useRouter();
  const { register, control, handleSubmit } = useForm({
    defaultValues: {
      variants: [
        {
          variantName: "",
          sku: "",
          salePrice: "",
          originalPrice: "",
          quantity: "",
          inventoryStatus: "in-stock",
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "variants",
  });

  const onSubmit = async (data) => {
    // API call to save product
    console.log("Product:", data);
          router.push(`/${data.storeHandle}/onboarding?stage=import-products`);
    
  };

  return (
    <Paper className="p-6 space-y-4">
      <Typography variant="h6">Add Product</Typography>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <TextField
          label="Product Name"
          fullWidth
          {...register("productName")}
          required
        />
        <TextField
          label="Slug"
          fullWidth
          {...register("productSlug")}
          required
        />
        <TextField
          label="Description"
          fullWidth
          multiline
          rows={3}
          {...register("productDescription")}
        />

        <Typography variant="subtitle1">Variants</Typography>
        {fields.map((field, index) => (
          <VariantFields
            key={field.id}
            index={index}
            register={register}
            remove={remove}
          />
        ))}

        <Button variant="outlined" onClick={() => append({})}>
          Add Variant
        </Button>

        <Button type="submit" variant="contained">
          Save Product
        </Button>
      </form>
    </Paper>
  );
}
