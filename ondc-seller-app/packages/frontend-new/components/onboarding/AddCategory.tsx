"use client";
import { useForm } from "react-hook-form";
import { TextField, Button, Typography, Paper } from "@mui/material";

export default function AddCategory({ onCategorySave }) {
  const { register, handleSubmit, reset } = useForm();

  const onSubmit = async (data) => {
    // Save to DB via API
    await onCategorySave(data);
    reset();
  };

  return (
    <Paper className="p-6 rounded-2xl shadow-sm space-y-4 mb-6">
      <Typography variant="h6">Add Category</Typography>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <TextField
          label="Category Name"
          fullWidth
          {...register("categoryName")}
          required
        />
        <TextField
          label="Category Handle"
          fullWidth
          {...register("categoryHandle")}
          required
        />
        <Button type="submit" variant="contained">
          Save Category
        </Button>
      </form>
    </Paper>
  );
}
