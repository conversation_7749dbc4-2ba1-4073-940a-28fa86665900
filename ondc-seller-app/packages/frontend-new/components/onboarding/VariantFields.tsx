import React from 'react';
import {
  TextField,
  MenuItem,
  Button,
} from '@mui/material';

export default function VariantFields({ index, register, remove }) {
  return (
    <div className="grid grid-cols-2 gap-4 mb-4 border p-4 rounded">
      <TextField
        label="Variant Name"
        fullWidth
        {...register(`variants.${index}.variantName`)}
      />
      <TextField label="SKU" fullWidth {...register(`variants.${index}.sku`)} />
      <TextField
        label="Sale Price"
        fullWidth
        {...register(`variants.${index}.salePrice`)}
        type="number"
      />
      <TextField
        label="Original Price"
        fullWidth
        {...register(`variants.${index}.originalPrice`)}
        type="number"
      />
      <TextField
        label="Quantity"
        fullWidth
        {...register(`variants.${index}.quantity`)}
        type="number"
      />
      <TextField
        label="Inventory Status"
        select
        fullWidth
        {...register(`variants.${index}.inventoryStatus`)}
      >
        <MenuItem value="in-stock">In Stock</MenuItem>
        <MenuItem value="out-of-stock">Out of Stock</MenuItem>
      </TextField>
      <Button onClick={() => remove(index)} color="error">
        Remove
      </Button>
    </div>
  );
}
