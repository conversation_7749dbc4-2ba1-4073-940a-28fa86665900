'use client';

import React from 'react';

// Material Design 3 Components
import {
  Box,
  Container,
  Stack,
  Typography,
  Stepper,
  Step,
  StepLabel,
  StepConnector,
  stepConnectorClasses,
  StepIconProps,
} from '@mui/material';
import {
  Check as CheckIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

interface StepData {
  id: number;
  title: string;
  description: string;
}

interface OnboardingProgressProps {
  steps: StepData[];
  currentStep: number;
}

// Custom Step Connector
const CustomConnector = styled(StepConnector)(({ theme }) => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: 22,
  },
  [`&.${stepConnectorClasses.active}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundColor: theme.palette.primary.main,
    },
  },
  [`&.${stepConnectorClasses.completed}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundColor: theme.palette.success.main,
    },
  },
  [`& .${stepConnectorClasses.line}`]: {
    height: 3,
    border: 0,
    backgroundColor: theme.palette.grey[300],
    borderRadius: 1,
  },
}));

// Custom Step Icon
const CustomStepIcon = (props: StepIconProps) => {
  const { active, completed, className } = props;

  return (
    <Box
      className={className}
      sx={{
        width: 40,
        height: 40,
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '0.875rem',
        fontWeight: 'medium',
        bgcolor: completed
          ? 'success.main'
          : active
          ? 'primary.main'
          : 'grey.300',
        color: completed || active ? 'white' : 'grey.600',
      }}
    >
      {completed ? (
        <CheckIcon sx={{ fontSize: 20 }} />
      ) : (
        props.icon
      )}
    </Box>
  );
};

export const OnboardingProgress: React.FC<OnboardingProgressProps> = ({
  steps,
  currentStep
}) => {
  return (
    <Box sx={{ bgcolor: 'background.paper', borderBottom: 1, borderColor: 'divider' }}>
      <Container maxWidth="lg" sx={{ py: 3 }}>
        {/* Desktop Stepper */}
        <Box sx={{ display: { xs: 'none', sm: 'block' } }}>
          <Stepper
            activeStep={currentStep - 1}
            connector={<CustomConnector />}
            alternativeLabel
          >
            {steps.map((step) => (
              <Step key={step.id}>
                <StepLabel
                  StepIconComponent={CustomStepIcon}
                  sx={{
                    '& .MuiStepLabel-label': {
                      fontWeight: step.id <= currentStep ? 'medium' : 'normal',
                      color: step.id <= currentStep ? 'text.primary' : 'text.secondary',
                    },
                  }}
                >
                  <Typography variant="body2" fontWeight="inherit">
                    {step.title}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {step.description}
                  </Typography>
                </StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        {/* Mobile Step Info */}
        <Box sx={{ display: { xs: 'block', sm: 'none' }, textAlign: 'center' }}>
          <Stack direction="row" justifyContent="center" alignItems="center" spacing={2} sx={{ mb: 2 }}>
            <CustomStepIcon
              active={true}
              completed={false}
              icon={currentStep}
            />
            <Typography variant="body1" fontWeight="medium">
              {steps[currentStep - 1]?.title}
            </Typography>
          </Stack>
          <Typography variant="body2" color="text.secondary">
            {steps[currentStep - 1]?.description}
          </Typography>
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            Step {currentStep} of {steps.length}
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};