import React from 'react';
import { useForm } from 'react-hook-form';
import {
  Paper,
  Typography,
  TextField,
  MenuItem,
  Button,
} from '@mui/material';

export default function AddSubcategory({ categories, onSubcategorySave }) {
  const { register, handleSubmit, reset } = useForm();

  const onSubmit = async (data) => {
    await onSubcategorySave(data);
    reset();
  };

  return (
    <Paper className="p-6 rounded-2xl shadow-sm space-y-4 mb-6">
      <Typography variant="h6">Add Subcategory</Typography>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <TextField
          label="Parent Category"
          select
          fullWidth
          {...register("categoryId")}
          required
        >
          {categories.map((cat) => (
            <MenuItem key={cat.id} value={cat.id}>
              {cat.categoryName}
            </MenuItem>
          ))}
        </TextField>
        <TextField label="Subcategory Name" fullWidth {...register("subcategoryName")} required />
        <TextField label="Subcategory Handle" fullWidth {...register("subcategoryHandle")} required />
        <Button type="submit" variant="contained">Save Subcategory</Button>
      </form>
    </Paper>
  );
}
