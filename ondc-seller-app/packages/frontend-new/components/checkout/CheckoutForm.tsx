'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useCartContext } from '../cart/CartProvider';
import { formatPrice, calculateCartTotals } from '@/lib/cart/mockCartData';

interface CheckoutFormProps {
  storeHandle: string;
}

interface CheckoutFormData {
  // Customer Information
  email: string;
  firstName: string;
  lastName: string;
  phone: string;

  // Shipping Address
  shippingAddress: string;
  shippingCity: string;
  shippingState: string;
  shippingZip: string;
  shippingCountry: string;

  // Billing Address
  billingAddress: string;
  billingCity: string;
  billingState: string;
  billingZip: string;
  billingCountry: string;
  sameAsShipping: boolean;

  // Payment Information
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  cardName: string;

  // Order Notes
  orderNotes: string;
}

export const CheckoutForm: React.FC<CheckoutFormProps> = ({ storeHandle }) => {
  const router = useRouter();
  const { cart, clearCart } = useCartContext();
  const [isProcessing, setIsProcessing] = useState(false);
  const [formData, setFormData] = useState<CheckoutFormData>({
    email: '',
    firstName: '',
    lastName: '',
    phone: '',
    shippingAddress: '',
    shippingCity: '',
    shippingState: '',
    shippingZip: '',
    shippingCountry: 'US',
    billingAddress: '',
    billingCity: '',
    billingState: '',
    billingZip: '',
    billingCountry: 'US',
    sameAsShipping: true,
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardName: '',
    orderNotes: '',
  });

  const totals = calculateCartTotals(cart.items);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsProcessing(true);

    try {
      // Simulate order processing
      console.log('Processing order:', {
        cart,
        formData,
        totals,
      });

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Clear cart and redirect to success page
      clearCart();
      router.push(`/${storeHandle}/checkout/success`);

    } catch (error) {
      console.error('Order processing error:', error);
      alert('Order processing failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div 
      className="bg-white rounded-lg shadow-sm border p-6"
      style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Customer Information */}
        <div>
          <h2 
            className="text-lg font-semibold mb-4"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            Contact Information
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label 
                className="block text-sm font-medium mb-2"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                Email Address *
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                style={{ 
                  borderColor: 'var(--theme-border, #d1d5db)',
                  '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                }}
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label 
                className="block text-sm font-medium mb-2"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                Phone Number
              </label>
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                style={{ 
                  borderColor: 'var(--theme-border, #d1d5db)',
                  '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                }}
                placeholder="+****************"
              />
            </div>
            <div>
              <label 
                className="block text-sm font-medium mb-2"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                First Name *
              </label>
              <input
                type="text"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                style={{ 
                  borderColor: 'var(--theme-border, #d1d5db)',
                  '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                }}
                placeholder="John"
              />
            </div>
            <div>
              <label 
                className="block text-sm font-medium mb-2"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                Last Name *
              </label>
              <input
                type="text"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                style={{ 
                  borderColor: 'var(--theme-border, #d1d5db)',
                  '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                }}
                placeholder="Doe"
              />
            </div>
          </div>
        </div>

        {/* Shipping Address */}
        <div>
          <h2 
            className="text-lg font-semibold mb-4"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            Shipping Address
          </h2>
          <div className="space-y-4">
            <div>
              <label 
                className="block text-sm font-medium mb-2"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                Street Address *
              </label>
              <input
                type="text"
                name="shippingAddress"
                value={formData.shippingAddress}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                style={{ 
                  borderColor: 'var(--theme-border, #d1d5db)',
                  '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                }}
                placeholder="123 Main Street"
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label 
                  className="block text-sm font-medium mb-2"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  City *
                </label>
                <input
                  type="text"
                  name="shippingCity"
                  value={formData.shippingCity}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                  style={{ 
                    borderColor: 'var(--theme-border, #d1d5db)',
                    '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                  }}
                  placeholder="New York"
                />
              </div>
              <div>
                <label 
                  className="block text-sm font-medium mb-2"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  State *
                </label>
                <input
                  type="text"
                  name="shippingState"
                  value={formData.shippingState}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                  style={{ 
                    borderColor: 'var(--theme-border, #d1d5db)',
                    '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                  }}
                  placeholder="NY"
                />
              </div>
              <div>
                <label 
                  className="block text-sm font-medium mb-2"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  ZIP Code *
                </label>
                <input
                  type="text"
                  name="shippingZip"
                  value={formData.shippingZip}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                  style={{ 
                    borderColor: 'var(--theme-border, #d1d5db)',
                    '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                  }}
                  placeholder="10001"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Payment Information */}
        <div>
          <h2 
            className="text-lg font-semibold mb-4"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            Payment Information
          </h2>
          <div className="space-y-4">
            <div>
              <label 
                className="block text-sm font-medium mb-2"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                Card Number *
              </label>
              <input
                type="text"
                name="cardNumber"
                value={formData.cardNumber}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                style={{ 
                  borderColor: 'var(--theme-border, #d1d5db)',
                  '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                }}
                placeholder="1234 5678 9012 3456"
                maxLength={19}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label 
                  className="block text-sm font-medium mb-2"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  Expiry Date *
                </label>
                <input
                  type="text"
                  name="expiryDate"
                  value={formData.expiryDate}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                  style={{ 
                    borderColor: 'var(--theme-border, #d1d5db)',
                    '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                  }}
                  placeholder="MM/YY"
                  maxLength={5}
                />
              </div>
              <div>
                <label 
                  className="block text-sm font-medium mb-2"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  CVV *
                </label>
                <input
                  type="text"
                  name="cvv"
                  value={formData.cvv}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                  style={{ 
                    borderColor: 'var(--theme-border, #d1d5db)',
                    '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                  }}
                  placeholder="123"
                  maxLength={4}
                />
              </div>
              <div>
                <label 
                  className="block text-sm font-medium mb-2"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  Cardholder Name *
                </label>
                <input
                  type="text"
                  name="cardName"
                  value={formData.cardName}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                  style={{ 
                    borderColor: 'var(--theme-border, #d1d5db)',
                    '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                  }}
                  placeholder="John Doe"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Order Notes */}
        <div>
          <label 
            className="block text-sm font-medium mb-2"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            Order Notes (Optional)
          </label>
          <textarea
            name="orderNotes"
            value={formData.orderNotes}
            onChange={handleInputChange}
            rows={3}
            className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
            style={{ 
              borderColor: 'var(--theme-border, #d1d5db)',
              '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
            }}
            placeholder="Special delivery instructions, gift message, etc."
          />
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <button
            type="submit"
            disabled={isProcessing}
            className={`w-full py-3 px-6 rounded-lg font-semibold transition-all duration-200 ${
              isProcessing 
                ? 'opacity-50 cursor-not-allowed' 
                : 'hover:shadow-lg'
            }`}
            style={{ 
              backgroundColor: 'var(--btn-primary, #3b82f6)',
              color: 'var(--btn-text, #ffffff)',
            }}
          >
            {isProcessing 
              ? 'Processing Order...' 
              : `Complete Order • ${formatPrice(totals.finalTotal)}`
            }
          </button>
        </div>
      </form>
    </div>
  );
};