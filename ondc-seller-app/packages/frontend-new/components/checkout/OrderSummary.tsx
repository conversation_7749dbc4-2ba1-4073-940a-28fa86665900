'use client';

import React from 'react';
import Image from 'next/image';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Stack,
  IconButton,
  TextField,
  Button,
  Divider,
  Chip,
  Avatar,
  Alert,
  Link,
} from '@mui/material';
import {
  Add,
  Remove,
  ShoppingCart,
  LocalShipping,
  Security,
  Info,
} from '@mui/icons-material';
import { useCartContext } from '../cart/CartProvider';
import { formatPrice, calculateCartTotals } from '@/lib/cart/mockCartData';

interface OrderSummaryProps {
  storeHandle: string;
}

export const OrderSummary: React.FC<OrderSummaryProps> = ({ storeHandle }) => {
  const { cart, updateQuantity, removeItem } = useCartContext();
  const totals = calculateCartTotals(cart.items);

  return (
    <Card sx={{ position: 'sticky', top: 32 }}>
      <CardContent sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
          Order Summary
        </Typography>

      {/* Cart Items */}
      <div className="space-y-4 mb-6">
        {cart.items.map((item) => (
          <div key={item.id} className="flex space-x-4">
            {/* Product Image */}
            <div className="relative w-16 h-16 rounded-lg overflow-hidden flex-shrink-0 bg-gray-100">
              <Image
                src={item.image}
                alt={item.title}
                fill
                className="object-cover"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              
              {/* Quantity Badge */}
              <div 
                className="absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white"
                style={{ backgroundColor: 'var(--theme-primary, #3b82f6)' }}
              >
                {item.quantity}
              </div>
            </div>

            {/* Product Details */}
            <div className="flex-1 min-w-0">
              <h3 
                className="font-medium text-sm truncate"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                {item.title}
              </h3>
              
              {item.variant && (
                <p 
                  className="text-xs mt-1"
                  style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                >
                  {Object.entries(item.variant.options).map(([key, value]) => 
                    `${key}: ${value}`
                  ).join(', ')}
                </p>
              )}

              <div className="flex items-center justify-between mt-2">
                <span 
                  className="font-semibold text-sm"
                  style={{ color: 'var(--theme-primary, #3b82f6)' }}
                >
                  {formatPrice(item.price * item.quantity)}
                </span>

                {/* Quantity Controls */}
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => updateQuantity(item.id, item.quantity - 1)}
                    className="w-6 h-6 rounded-full flex items-center justify-center transition-colors hover:opacity-80"
                    style={{ 
                      backgroundColor: 'var(--theme-background, #f3f4f6)',
                      color: 'var(--theme-text, #111827)',
                    }}
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                    </svg>
                  </button>
                  
                  <span 
                    className="text-sm font-medium min-w-[20px] text-center"
                    style={{ color: 'var(--theme-text, #111827)' }}
                  >
                    {item.quantity}
                  </span>
                  
                  <button
                    onClick={() => updateQuantity(item.id, item.quantity + 1)}
                    className="w-6 h-6 rounded-full flex items-center justify-center transition-colors hover:opacity-80"
                    style={{ 
                      backgroundColor: 'var(--theme-background, #f3f4f6)',
                      color: 'var(--theme-text, #111827)',
                    }}
                    disabled={item.quantity >= (item.maxQuantity || 99)}
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Promo Code */}
      <div className="mb-6">
        <div className="flex space-x-2">
          <input
            type="text"
            placeholder="Promo code"
            className="flex-1 px-3 py-2 border rounded-lg text-sm focus:ring-2 focus:border-transparent transition-colors"
            style={{ 
              borderColor: 'var(--theme-border, #d1d5db)',
              '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
            }}
          />
          <button
            className="px-4 py-2 border rounded-lg text-sm font-medium transition-colors hover:opacity-80"
            style={{ 
              borderColor: 'var(--theme-border, #d1d5db)',
              color: 'var(--theme-text, #111827)',
            }}
          >
            Apply
          </button>
        </div>
      </div>

      {/* Order Totals */}
      <div className="space-y-3">
        <div className="flex justify-between text-sm">
          <span style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>
            Subtotal ({cart.totalItems} items):
          </span>
          <span style={{ color: 'var(--theme-text, #111827)' }}>
            {formatPrice(totals.subtotal)}
          </span>
        </div>
        
        <div className="flex justify-between text-sm">
          <span style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>
            Shipping:
          </span>
          <span style={{ color: 'var(--theme-text, #111827)' }}>
            {totals.shipping === 0 ? 'Free' : formatPrice(totals.shipping)}
          </span>
        </div>
        
        <div className="flex justify-between text-sm">
          <span style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>
            Tax:
          </span>
          <span style={{ color: 'var(--theme-text, #111827)' }}>
            {formatPrice(totals.tax)}
          </span>
        </div>
        
        <div 
          className="flex justify-between text-lg font-bold pt-3 border-t"
          style={{ borderTopColor: 'var(--theme-border, #e5e7eb)' }}
        >
          <span style={{ color: 'var(--theme-text, #111827)' }}>
            Total:
          </span>
          <span style={{ color: 'var(--theme-primary, #3b82f6)' }}>
            {formatPrice(totals.finalTotal)}
          </span>
        </div>
      </div>

      {/* Security Notice */}
      <div 
        className="mt-6 p-4 rounded-lg"
        style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}
      >
        <div className="flex items-start space-x-3">
          <svg 
            className="w-5 h-5 mt-0.5 flex-shrink-0" 
            fill="none" 
            stroke="var(--theme-primary, #3b82f6)" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <div>
            <h4 
              className="text-sm font-medium"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              Secure Checkout
            </h4>
            <p 
              className="text-xs mt-1"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
            >
              Your payment information is encrypted and secure. We never store your credit card details.
            </p>
          </div>
        </div>
      </div>

      {/* Return Policy */}
      <div className="mt-4 text-center">
        <a 
          href={`/${storeHandle}/returns`}
          className="text-xs underline transition-colors hover:opacity-80"
          style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
        >
          Return Policy
        </a>
        <span 
          className="mx-2 text-xs"
          style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
        >
          •
        </span>
        <a 
          href={`/${storeHandle}/shipping-policy`}
          className="text-xs underline transition-colors hover:opacity-80"
          style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
        >
          Shipping Info
        </a>
      </div>
    </div>
  );
};