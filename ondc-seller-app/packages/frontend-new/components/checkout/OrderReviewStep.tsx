'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useCheckoutContext } from './CheckoutProvider';
import { Cart, getStoreCartAPI } from '@/lib/api/cart';
import { formatPrice, calculateCartTotals } from '@/lib/cart/mockCartData';
import { ActionButton } from '../ui/ActionButton';
import { useAsyncAction } from '@/hooks/useAsyncAction';

interface OrderReviewStepProps {
  storeHandle: string;
  cart: Cart;
}

export const OrderReviewStep: React.FC<OrderReviewStepProps> = ({ storeHandle, cart }) => {
  const router = useRouter();
  const { checkoutState, goToPreviousStep, updateOrderNotes, setProcessing } = useCheckoutContext();
  const [isPlacingOrder, setIsPlacingOrder] = useState(false);
  const [orderStep, setOrderStep] = useState('');
  
  // Get store-specific cart API
  const cartAPI = getStoreCartAPI(storeHandle);
  
  // Convert API cart items to the format expected by calculateCartTotals
  const cartItems = cart.items.map(item => ({
    id: item.id,
    productId: item.variant_id, // Using variant_id as productId
    title: item.title,
    image: item.thumbnail || '',
    price: item.unit_price,
    quantity: item.quantity,
    variant: {
      id: item.variant_id,
      title: item.title,
      options: {}
    },
    maxQuantity: 999
  }));
  
  const totalItems = cart.items.reduce((sum, item) => sum + item.quantity, 0);
  const totals = calculateCartTotals(cartItems);
  const { contactInfo, shippingAddress, selectedPaymentMethod, orderNotes } = checkoutState;

  const handlePlaceOrder = async () => {
    setIsPlacingOrder(true);
    setProcessing(true);

    try {
      // Debug: Check cart state before starting
      console.log('🔍 Pre-order debug info:');
      console.log('- Store handle:', storeHandle);
      console.log('- Cart prop:', cart);
      console.log('- Cart items count:', cart?.items?.length || 0);
      
      const cartId = cartAPI.getStoredCartId();
      console.log('- Stored cart ID:', cartId);
      
      if (!cartId) {
        console.error('❌ No cart ID found for store:', storeHandle);
        console.error('- Available localStorage keys:', Object.keys(localStorage).filter(key => key.includes('cart')));
        throw new Error('No cart found. Please add items to your cart and try again.');
      }
      
      // Verify cart still exists and has items
      if (!cart || !cart.items || cart.items.length === 0) {
        console.error('❌ Cart is empty or invalid:', cart);
        throw new Error('Your cart is empty. Please add items to your cart and try again.');
      }

      console.log('🛒 Starting order placement process for cart:', cartId, 'store:', storeHandle);
      
      // Step 0: Verify cart is still valid
      setOrderStep('Verifying cart...');
      console.log('🔍 Step 0: Verifying cart is still valid...');
      try {
        const currentCart = await cartAPI.getCart(cartId);
        if (!currentCart.cart || !currentCart.cart.items || currentCart.cart.items.length === 0) {
          throw new Error('Cart is empty or no longer valid');
        }
        console.log('✅ Cart verification successful:', currentCart.cart.items.length, 'items');
      } catch (verifyError) {
        console.error('❌ Cart verification failed:', verifyError);
        throw new Error('Cart is no longer valid. Please refresh the page and try again.');
      }

      // Step 1: Get shipping options
      setOrderStep('Getting shipping options...');
      console.log('🚚 Step 1: Getting shipping options...');
      const shippingOptionsResponse = await cartAPI.getShippingOptions(cartId);
      
      if (!shippingOptionsResponse.shipping_options || shippingOptionsResponse.shipping_options.length === 0) {
        throw new Error('No shipping options available');
      }
      
      const firstShippingOption = shippingOptionsResponse.shipping_options[0];
      console.log('Using shipping option:', firstShippingOption.id);

      // Step 2: Add shipping method to cart
      setOrderStep('Setting up shipping...');
      console.log('📦 Step 2: Adding shipping method...');
      await cartAPI.addShippingMethod(
        cartId,
        { option_id: firstShippingOption.id }
      );
      
      console.log('Shipping method added successfully');

      // Step 3: Create payment collection
      setOrderStep('Creating payment collection...');
      console.log('💳 Step 3: Creating payment collection...');
      const paymentCollectionResponse = await cartAPI.createPaymentCollection(
        { cart_id: cartId }
      );
      
      const paymentCollectionId = paymentCollectionResponse.payment_collection.id;
      console.log('Payment collection created:', paymentCollectionId);

      // Step 4: Create payment session
      setOrderStep('Setting up payment...');
      console.log('🔒 Step 4: Creating payment session...');
      const paymentSessionResponse = await cartAPI.createPaymentSession(
        paymentCollectionId,
        { provider_id: 'pp_system_default' }
      );
      
      console.log('Payment session created:', paymentSessionResponse.payment_collection.id);

      // Step 5: Complete cart and create order
      setOrderStep('Finalizing order...');
      console.log('✅ Step 5: Completing cart...');
      const orderResponse = await cartAPI.completeCart(cartId);
      const order = orderResponse.order;

      console.log('🎉 Order created successfully:', order);

      // Clear cart from storage
      cartAPI.removeStoredCartId();

      // Redirect to order page
      router.push(`/${storeHandle}/orders/${order.id}`);

    } catch (error) {
      console.error('Order placement error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Order placement failed. Please try again.';
      alert(errorMessage);
    } finally {
      setIsPlacingOrder(false);
      setProcessing(false);
      setOrderStep('');
    }
  };

  return (
    <div 
      className="bg-white rounded-lg shadow-sm border p-8"
      style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
    >
      <div className="space-y-8">
        {/* Order Review Header */}
        <div>
          <h2 
            className="text-xl font-semibold mb-2"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            Review Your Order
          </h2>
          <p 
            className="text-sm"
            style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
          >
            Please review all details before placing your order
          </p>
        </div>

        {/* Contact Information Summary */}
        <div 
          className="p-6 rounded-lg"
          style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}
        >
          <div className="flex items-center justify-between mb-4">
            <h3 
              className="font-semibold"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              Contact Information
            </h3>
            <button
              onClick={() => goToPreviousStep()}
              className="text-sm underline transition-colors hover:opacity-80"
              style={{ color: 'var(--theme-primary, #3b82f6)' }}
            >
              Edit
            </button>
          </div>
          <div className="space-y-2 text-sm">
            <p style={{ color: 'var(--theme-text, #111827)' }}>
              <span className="font-medium">Name:</span> {contactInfo.firstName} {contactInfo.lastName}
            </p>
            <p style={{ color: 'var(--theme-text, #111827)' }}>
              <span className="font-medium">Email:</span> {contactInfo.email}
            </p>
            {contactInfo.phone && (
              <p style={{ color: 'var(--theme-text, #111827)' }}>
                <span className="font-medium">Phone:</span> {contactInfo.phone}
              </p>
            )}
          </div>
        </div>

        {/* Shipping Address Summary */}
        <div 
          className="p-6 rounded-lg"
          style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}
        >
          <div className="flex items-center justify-between mb-4">
            <h3 
              className="font-semibold"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              Shipping Address
            </h3>
            <button
              onClick={() => goToPreviousStep()}
              className="text-sm underline transition-colors hover:opacity-80"
              style={{ color: 'var(--theme-primary, #3b82f6)' }}
            >
              Edit
            </button>
          </div>
          <div className="text-sm" style={{ color: 'var(--theme-text, #111827)' }}>
            <p>{shippingAddress.address}</p>
            <p>{shippingAddress.city}, {shippingAddress.state} {shippingAddress.zipCode}</p>
            <p>{shippingAddress.country}</p>
          </div>
        </div>

        {/* Payment Method Summary */}
        <div 
          className="p-6 rounded-lg"
          style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}
        >
          <div className="flex items-center justify-between mb-4">
            <h3 
              className="font-semibold"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              Payment Method
            </h3>
            <button
              onClick={() => goToPreviousStep()}
              className="text-sm underline transition-colors hover:opacity-80"
              style={{ color: 'var(--theme-primary, #3b82f6)' }}
            >
              Edit
            </button>
          </div>
          {selectedPaymentMethod && (
            <div className="flex items-center space-x-3">
              <div className="text-xl">{selectedPaymentMethod.icon}</div>
              <div>
                <p 
                  className="font-medium text-sm"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  {selectedPaymentMethod.name}
                </p>
                <p 
                  className="text-xs"
                  style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                >
                  {selectedPaymentMethod.description}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Order Notes */}
        <div>
          <label 
            className="block text-sm font-medium mb-2"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            Order Notes (Optional)
          </label>
          <textarea
            value={orderNotes}
            onChange={(e) => updateOrderNotes(e.target.value)}
            rows={3}
            className="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
            style={{ 
              borderColor: 'var(--theme-border, #d1d5db)',
              '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
            }}
            placeholder="Special delivery instructions, gift message, etc."
          />
        </div>

        {/* Order Summary */}
        <div 
          className="p-6 rounded-lg border"
          style={{ 
            backgroundColor: 'var(--theme-surface, #ffffff)',
            borderColor: 'var(--theme-border, #e5e7eb)',
          }}
        >
          <h3 
            className="font-semibold mb-4"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            Order Summary
          </h3>
          
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>
                Subtotal ({totalItems} items):
              </span>
              <span style={{ color: 'var(--theme-text, #111827)' }}>
                {formatPrice(totals.subtotal)}
              </span>
            </div>
            
            <div className="flex justify-between text-sm">
              <span style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>
                Shipping:
              </span>
              <span style={{ color: 'var(--theme-text, #111827)' }}>
                {totals.shipping === 0 ? 'Free' : formatPrice(totals.shipping)}
              </span>
            </div>
            
            <div className="flex justify-between text-sm">
              <span style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>
                Tax:
              </span>
              <span style={{ color: 'var(--theme-text, #111827)' }}>
                {formatPrice(totals.tax)}
              </span>
            </div>
            
            <div 
              className="flex justify-between text-lg font-bold pt-3 border-t"
              style={{ borderTopColor: 'var(--theme-border, #e5e7eb)' }}
            >
              <span style={{ color: 'var(--theme-text, #111827)' }}>
                Total:
              </span>
              <span style={{ color: 'var(--theme-primary, #3b82f6)' }}>
                {formatPrice(totals.finalTotal)}
              </span>
            </div>
          </div>
        </div>

        {/* Terms and Conditions */}
        <div 
          className="p-4 rounded-lg"
          style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}
        >
          <div className="flex items-start space-x-3">
            <input
              type="checkbox"
              id="terms"
              className="mt-1"
              style={{ accentColor: 'var(--theme-primary, #3b82f6)' }}
              required
            />
            <label 
              htmlFor="terms" 
              className="text-sm"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
            >
              I agree to the{' '}
              <a 
                href={`/${storeHandle}/terms-and-conditions`}
                className="underline transition-colors hover:opacity-80"
                style={{ color: 'var(--theme-primary, #3b82f6)' }}
              >
                Terms and Conditions
              </a>{' '}
              and{' '}
              <a 
                href={`/${storeHandle}/privacy-policy`}
                className="underline transition-colors hover:opacity-80"
                style={{ color: 'var(--theme-primary, #3b82f6)' }}
              >
                Privacy Policy
              </a>
            </label>
          </div>
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between pt-6">
          <ActionButton
            variant="outline"
            size="lg"
            onClick={goToPreviousStep}
            disabled={isPlacingOrder}
            showGlobalLoader={false}
          >
            Back to Payment
          </ActionButton>
          
          <ActionButton
            variant="primary"
            size="lg"
            onClick={handlePlaceOrder}
            disabled={isPlacingOrder}
            loading={isPlacingOrder}
            loadingText={orderStep || 'Placing Order...'}
            loadingType="backdrop"
            actionId="place-order"
            className="min-w-[200px]"
          >
            {`Place Order • ${formatPrice(totals.finalTotal)}`}
          </ActionButton>
        </div>
      </div>
    </div>
  );
};