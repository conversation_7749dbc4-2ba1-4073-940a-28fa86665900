'use client';

import React from 'react';
import { CheckoutStep } from '@/types/checkout';

interface CheckoutStepIndicatorProps {
  steps: CheckoutStep[];
  currentStep: number;
}

export const CheckoutStepIndicator: React.FC<CheckoutStepIndicatorProps> = ({ 
  steps, 
  currentStep 
}) => {
  return (
    <div className="w-full">
      <div className="flex items-center justify-between">
        {steps.map((step, index) => (
          <React.Fragment key={step.id}>
            {/* Step Circle */}
            <div className="flex flex-col items-center">
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold transition-all duration-200 ${
                  step.isCompleted
                    ? 'text-white'
                    : step.isActive
                    ? 'text-white'
                    : 'text-gray-400 bg-gray-200'
                }`}
                style={{
                  backgroundColor: step.isCompleted || step.isActive 
                    ? 'var(--theme-primary, #3b82f6)' 
                    : undefined,
                }}
              >
                {step.isCompleted ? (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                ) : (
                  step.id
                )}
              </div>
              
              {/* Step Info */}
              <div className="mt-3 text-center">
                <p 
                  className={`text-sm font-medium ${
                    step.isActive 
                      ? 'text-var(--theme-text, #111827)' 
                      : 'text-gray-500'
                  }`}
                  style={{ 
                    color: step.isActive 
                      ? 'var(--theme-text, #111827)' 
                      : 'var(--theme-text-secondary, #6b7280)' 
                  }}
                >
                  {step.title}
                </p>
                <p 
                  className="text-xs mt-1"
                  style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                >
                  {step.description}
                </p>
              </div>
            </div>

            {/* Connector Line */}
            {index < steps.length - 1 && (
              <div className="flex-1 mx-4">
                <div 
                  className={`h-0.5 transition-all duration-200 ${
                    step.isCompleted ? '' : 'bg-gray-200'
                  }`}
                  style={{
                    backgroundColor: step.isCompleted 
                      ? 'var(--theme-primary, #3b82f6)' 
                      : undefined,
                  }}
                />
              </div>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};