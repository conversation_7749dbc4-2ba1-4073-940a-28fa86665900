'use client';

import React from 'react';
import { useCheckoutContext } from './CheckoutProvider';
import { getStorePaymentMethods, getDefaultPaymentMethods } from '@/lib/checkout/storePaymentMethods';
import { useStoreConfigStore } from '@/stores/storeConfigStore';
import { PaymentMethod } from '@/types/checkout';

interface PaymentMethodStepProps {
  storeHandle: string;
}

export const PaymentMethodStep: React.FC<PaymentMethodStepProps> = ({ storeHandle }) => {
  const { 
    checkoutState, 
    selectPaymentMethod, 
    updateCreditCardDetails,
    goToNextStep, 
    goToPreviousStep,
    validateCurrentStep 
  } = useCheckoutContext();

  const { selectedPaymentMethod, creditCardDetails } = checkoutState;
  
  // Get store-specific configuration
  const { storeData } = useStoreConfigStore(storeHandle);
  
  // Get payment methods from store config or use defaults
  const availablePaymentMethods = React.useMemo(() => {
    console.log('💳 Loading payment methods for store:', storeHandle);
    
    if (storeData) {
      const storeMethods = getStorePaymentMethods(storeData);
      console.log('✅ Using store-specific payment methods:', storeMethods.length);
      return storeMethods;
    } else {
      console.log('⚠️ Store data not available, using default payment methods');
      return getDefaultPaymentMethods();
    }
  }, [storeData, storeHandle]);

  const handlePaymentMethodSelect = (method: PaymentMethod) => {
    selectPaymentMethod(method);
  };

  const handleCreditCardChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    updateCreditCardDetails({ [name]: value });
  };

  const handleContinue = () => {
    if (validateCurrentStep()) {
      goToNextStep();
    }
  };

  const isValid = validateCurrentStep();

  return (
    <div 
      className="bg-white rounded-lg shadow-sm border p-8"
      style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
    >
      <div className="space-y-8">
        {/* Payment Method Selection */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <h2 
              className="text-xl font-semibold"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              Choose Payment Method
            </h2>
            
            {/* Store-specific indicator */}
            {storeData && (
              <div className="flex items-center space-x-2">
                <div 
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: 'var(--theme-accent, #10b981)' }}
                />
                <span 
                  className="text-xs font-medium"
                  style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                >
                  Store-specific methods
                </span>
              </div>
            )}
          </div>
          
          <div className="space-y-4">
            {availablePaymentMethods.map((method) => (
              <div
                key={method.id}
                className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                  selectedPaymentMethod?.id === method.id 
                    ? 'ring-2' 
                    : 'hover:shadow-sm'
                }`}
                style={{ 
                  borderColor: selectedPaymentMethod?.id === method.id 
                    ? 'var(--theme-primary, #3b82f6)' 
                    : 'var(--theme-border, #e5e7eb)',
                  '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                }}
                onClick={() => handlePaymentMethodSelect(method)}
              >
                <div className="flex items-center space-x-4">
                  <div className="text-2xl">{method.icon}</div>
                  <div className="flex-1">
                    <h3 
                      className="font-medium"
                      style={{ color: 'var(--theme-text, #111827)' }}
                    >
                      {method.name}
                    </h3>
                    <p 
                      className="text-sm"
                      style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                    >
                      {method.description}
                    </p>
                  </div>
                  <div>
                    <div 
                      className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                        selectedPaymentMethod?.id === method.id ? 'border-current' : ''
                      }`}
                      style={{ 
                        borderColor: selectedPaymentMethod?.id === method.id 
                          ? 'var(--theme-primary, #3b82f6)' 
                          : 'var(--theme-border, #d1d5db)',
                      }}
                    >
                      {selectedPaymentMethod?.id === method.id && (
                        <div 
                          className="w-2.5 h-2.5 rounded-full"
                          style={{ backgroundColor: 'var(--theme-primary, #3b82f6)' }}
                        />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Credit Card Details */}
        {selectedPaymentMethod?.type === 'credit_card' && (
          <div>
            <h3 
              className="text-lg font-semibold mb-6"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              Card Details
            </h3>
            
            <div className="space-y-6">
              <div>
                <label 
                  className="block text-sm font-medium mb-2"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  Card Number *
                </label>
                <input
                  type="text"
                  name="cardNumber"
                  value={creditCardDetails.cardNumber}
                  onChange={handleCreditCardChange}
                  required
                  className="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                  style={{ 
                    borderColor: 'var(--theme-border, #d1d5db)',
                    '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                  }}
                  placeholder="1234 5678 9012 3456"
                  maxLength={19}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label 
                    className="block text-sm font-medium mb-2"
                    style={{ color: 'var(--theme-text, #111827)' }}
                  >
                    Expiry Date *
                  </label>
                  <input
                    type="text"
                    name="expiryDate"
                    value={creditCardDetails.expiryDate}
                    onChange={handleCreditCardChange}
                    required
                    className="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                    style={{ 
                      borderColor: 'var(--theme-border, #d1d5db)',
                      '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                    }}
                    placeholder="MM/YY"
                    maxLength={5}
                  />
                </div>
                
                <div>
                  <label 
                    className="block text-sm font-medium mb-2"
                    style={{ color: 'var(--theme-text, #111827)' }}
                  >
                    CVV *
                  </label>
                  <input
                    type="text"
                    name="cvv"
                    value={creditCardDetails.cvv}
                    onChange={handleCreditCardChange}
                    required
                    className="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                    style={{ 
                      borderColor: 'var(--theme-border, #d1d5db)',
                      '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                    }}
                    placeholder="123"
                    maxLength={4}
                  />
                </div>
                
                <div>
                  <label 
                    className="block text-sm font-medium mb-2"
                    style={{ color: 'var(--theme-text, #111827)' }}
                  >
                    Cardholder Name *
                  </label>
                  <input
                    type="text"
                    name="cardholderName"
                    value={creditCardDetails.cardholderName}
                    onChange={handleCreditCardChange}
                    required
                    className="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                    style={{ 
                      borderColor: 'var(--theme-border, #d1d5db)',
                      '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                    }}
                    placeholder="John Doe"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Other Payment Method Info */}
        {selectedPaymentMethod && selectedPaymentMethod.type !== 'credit_card' && (
          <div 
            className="p-6 rounded-lg border"
            style={{ 
              backgroundColor: 'var(--theme-background, #f9fafb)',
              borderColor: 'var(--theme-border, #e5e7eb)'
            }}
          >
            <div className="flex items-center space-x-3">
              <div className="text-3xl">{selectedPaymentMethod.icon}</div>
              <div className="flex-1">
                <h3 
                  className="font-semibold mb-1"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  {selectedPaymentMethod.name}
                </h3>
                <p 
                  className="text-sm mb-2"
                  style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                >
                  {selectedPaymentMethod.description}
                </p>
                
                {/* Payment method specific instructions */}
                {selectedPaymentMethod.type === 'upi' && (
                  <div className="text-xs p-2 rounded" style={{ backgroundColor: 'var(--theme-surface, #f3f4f6)' }}>
                    <p style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>
                      You'll be redirected to your UPI app to complete the payment.
                    </p>
                  </div>
                )}
                
                {selectedPaymentMethod.type === 'net_banking' && (
                  <div className="text-xs p-2 rounded" style={{ backgroundColor: 'var(--theme-surface, #f3f4f6)' }}>
                    <p style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>
                      You'll be redirected to your bank's secure login page.
                    </p>
                  </div>
                )}
                
                {selectedPaymentMethod.type === 'cash_on_delivery' && (
                  <div className="text-xs p-2 rounded" style={{ backgroundColor: 'var(--theme-surface, #f3f4f6)' }}>
                    <p style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>
                      Pay in cash when your order is delivered to your doorstep.
                    </p>
                  </div>
                )}
                
                {!['upi', 'net_banking', 'cash_on_delivery'].includes(selectedPaymentMethod.type) && (
                  <div className="text-xs p-2 rounded" style={{ backgroundColor: 'var(--theme-surface, #f3f4f6)' }}>
                    <p style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>
                      You'll be redirected to complete your payment securely.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between pt-6">
          <button
            onClick={goToPreviousStep}
            className="px-6 py-3 rounded-lg font-medium transition-colors border"
            style={{ 
              borderColor: 'var(--theme-border, #d1d5db)',
              color: 'var(--theme-text, #111827)',
            }}
          >
            Back to Contact
          </button>
          
          <button
            onClick={handleContinue}
            disabled={!isValid}
            className={`px-8 py-3 rounded-lg font-semibold transition-all duration-200 ${
              isValid 
                ? 'hover:shadow-lg' 
                : 'opacity-50 cursor-not-allowed'
            }`}
            style={{ 
              backgroundColor: 'var(--btn-primary, #3b82f6)',
              color: 'var(--btn-text, #ffffff)',
            }}
          >
            Review Order
          </button>
        </div>
      </div>
    </div>
  );
};