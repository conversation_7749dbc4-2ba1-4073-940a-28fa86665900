'use client';

import React, { useState, useEffect } from 'react';
import { formatPrice } from '@/lib/cart/mockCartData';
import { getStoreCartAPI, Cart } from '@/lib/api/cart';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Stack,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Skeleton,
} from '@mui/material';
import { ShoppingCart as CartIcon } from '@mui/icons-material';

interface CheckoutOrderSummaryProps {
  storeHandle: string;
}

export const CheckoutOrderSummary: React.FC<CheckoutOrderSummaryProps> = ({ storeHandle }) => {
  const [cart, setCart] = useState<Cart | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // Get store-specific cart API
  const cartAPI = getStoreCartAPI(storeHandle);

  // Load cart data
  useEffect(() => {
    const loadCart = async () => {
      console.log(`🛒 Loading cart for checkout summary - store: ${storeHandle}`);
      const cartId = cartAPI.getStoredCartId();
      if (!cartId) {
        console.log('❌ No cart ID found for checkout summary');
        setIsLoading(false);
        return;
      }
      
      try {
        console.log(`📦 Fetching cart data for checkout summary: ${cartId}`);
        const cartResponse = await cartAPI.getCart(cartId);
        setCart(cartResponse.cart);
        console.log(`✅ Cart loaded for checkout summary:`, cartResponse.cart.items.length, 'items');
      } catch (error) {
        console.error('❌ Failed to load cart for checkout summary:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadCart();
  }, [storeHandle, cartAPI]);

  // Calculate totals
  const calculateTotals = () => {
    if (!cart || !cart.items || cart.items.length === 0) {
      return { subtotal: 0, tax: 0, shipping: 0, total: 0 };
    }
    
    const subtotal = cart.items.reduce((sum, item) => sum + (item.unit_price * item.quantity), 0);
    const tax = subtotal * 0.08; // 8% tax
    const shipping = subtotal > 5000 ? 0 : 50; // Free shipping over $50
    const total = subtotal + tax + shipping;
    
    return { subtotal, tax, shipping, total };
  };
  
  const { subtotal, tax, shipping, total } = calculateTotals();
  
  if (isLoading) {
    return (
      <Card>
        <CardContent>
          <Skeleton variant="text" width="60%" height={32} />
          <Stack spacing={2} sx={{ mt: 2 }}>
            <Skeleton variant="text" height={24} />
            <Skeleton variant="text" height={24} />
            <Skeleton variant="text" height={24} />
          </Stack>
        </CardContent>
      </Card>
    );
  }
  
  if (!cart || !cart.items || cart.items.length === 0) {
    return (
      <Card>
        <CardContent>
          <Typography variant="body2" color="text.secondary">
            No items in cart
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom fontWeight="bold">
          Order Summary
        </Typography>

        {/* Cart Items */}
        <List sx={{ mb: 2 }}>
          {cart.items.map((item) => (
            <ListItem key={item.id} sx={{ px: 0, py: 1 }}>
              <ListItemAvatar>
                <Avatar
                  variant="rounded"
                  src={item.thumbnail}
                  sx={{ width: 48, height: 48 }}
                >
                  <CartIcon />
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="medium">
                    {item.title}
                  </Typography>
                }
                secondary={
                  <Typography variant="caption" color="text.secondary">
                    Qty: {item.quantity}
                  </Typography>
                }
              />
              <Typography variant="body2" fontWeight="medium">
                {formatPrice(item.unit_price * item.quantity)}
              </Typography>
            </ListItem>
          ))}
        </List>

        <Divider sx={{ my: 2 }} />

        {/* Totals */}
        <Stack spacing={1}>
          <Box display="flex" justifyContent="space-between">
            <Typography variant="body2" color="text.secondary">
              Subtotal:
            </Typography>
            <Typography variant="body2">
              {formatPrice(subtotal)}
            </Typography>
          </Box>
          
          <Box display="flex" justifyContent="space-between">
            <Typography variant="body2" color="text.secondary">
              Tax:
            </Typography>
            <Typography variant="body2">
              {formatPrice(tax)}
            </Typography>
          </Box>
          
          <Box display="flex" justifyContent="space-between">
            <Typography variant="body2" color="text.secondary">
              Shipping:
            </Typography>
            <Typography variant="body2">
              {shipping === 0 ? 'Free' : formatPrice(shipping)}
            </Typography>
          </Box>
          
          <Divider sx={{ my: 1 }} />
          
          <Box display="flex" justifyContent="space-between">
            <Typography variant="h6" fontWeight="bold">
              Total:
            </Typography>
            <Typography variant="h6" fontWeight="bold" color="primary.main">
              {formatPrice(total)}
            </Typography>
          </Box>
        </Stack>
      </CardContent>
    </Card>
  );
};