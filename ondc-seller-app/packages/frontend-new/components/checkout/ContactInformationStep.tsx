'use client';

import React, { useState, useEffect } from 'react';
import { useCheckoutContext } from './CheckoutProvider';
import { useStoreAuthContext } from '../auth/StoreAuthProvider';
import { useStoreAuthStore } from '@/stores/storeAuthStore';
import { SavedAddress } from '@/types/checkout';
import { UserAddress } from '@/types/auth';
import { getStoreCartAPI, UpdateCartPayload } from '@/lib/api/cart';
import {
  Card,
  CardContent,
  Typography,
  Button,
  Box,
  Grid,
  Chip,
  Paper,
  Divider,
  Stack,
  Avatar
} from '@mui/material';
import {
  LocationOn as LocationIcon,
  Home as HomeIcon,
  Business as BusinessIcon,
  Phone as PhoneIcon,
  Person as PersonIcon
} from '@mui/icons-material';

interface ContactInformationStepProps {
  storeHandle: string;
}

export const ContactInformationStep: React.FC<ContactInformationStepProps> = ({ storeHandle }) => {
  const { 
    checkoutState, 
    updateContactInfo, 
    updateShippingAddress, 
    updateBillingAddress,
    toggleSameAddressForBilling,
    goToNextStep, 
    validateCurrentStep,
    setProcessing
  } = useCheckoutContext();
  
  // Get store-specific cart API
  const cartAPI = getStoreCartAPI(storeHandle);

  const { user, isAuthenticated } = useStoreAuthContext();
  const { addresses } = useStoreAuthStore(storeHandle);
  const [showSavedAddresses, setShowSavedAddresses] = useState(false);
  const [showAddNewAddress, setShowAddNewAddress] = useState(false);
  const [isUpdatingCart, setIsUpdatingCart] = useState(false);

  const { contactInfo, shippingAddress, billingAddress, useSameAddressForBilling } = checkoutState;

  // Initialize contact info from user data if logged in
  useEffect(() => {
    if (isAuthenticated && user && !contactInfo.email) {
      updateContactInfo({
        email: user.email,
        firstName: user.first_name || '',
        lastName: user.last_name || '',
        phone: user.phone || '',
      });
    }
  }, [isAuthenticated, user, contactInfo.email, updateContactInfo]);
  
  // Initialize default state and country for India
  useEffect(() => {
    if (!shippingAddress.state || !shippingAddress.country) {
      updateShippingAddress({
        state: 'in',
        country: 'in'
      });
    }
    if (!billingAddress.state || !billingAddress.country) {
      updateBillingAddress({
        state: 'in',
        country: 'in'
      });
    }
  }, [shippingAddress.state, shippingAddress.country, billingAddress.state, billingAddress.country, updateShippingAddress, updateBillingAddress]);

  const handleContactChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    updateContactInfo({ [name]: value });
  };

  const handleShippingAddressChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    // Always set state and country for India
    const updates: any = { [name]: value };
    if (name === 'city' || name === 'zipCode' || name === 'address') {
      updates.state = 'in'; // Default state for India
      updates.country = 'in'; // Default country for India
    }
    updateShippingAddress(updates);
  };

  const handleBillingAddressChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    // Always set state and country for India
    const updates: any = { [name]: value };
    if (name === 'city' || name === 'zipCode' || name === 'address') {
      updates.state = 'in'; // Default state for India
      updates.country = 'in'; // Default country for India
    }
    updateBillingAddress(updates);
  };

  const handleUseSameAddressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    toggleSameAddressForBilling(e.target.checked);
  };

  const handleSelectSavedAddress = (address: any) => {
    const addressData = {
      address: address.address_1,
      address2: address.address_2 || '',
      city: address.city,
      state: 'in', // Default state for India
      zipCode: address.postal_code,
      country: 'in', // Default country code for India
    };

    updateShippingAddress(addressData);
    setShowSavedAddresses(false);
  };

  const convertAddressToMedusaFormat = (address: typeof shippingAddress) => {
    return {
      first_name: contactInfo.firstName,
      last_name: contactInfo.lastName,
      address_1: address.address,
      address_2: address.address2 || undefined,
      city: address.city,
      province: 'in', // Default province for India
      postal_code: address.zipCode,
      country_code: 'in', // Default country code for India
      phone: contactInfo.phone || undefined,
    };
  };

  const handleContinue = async () => {
    if (!validateCurrentStep()) {
      return;
    }

    setIsUpdatingCart(true);
    setProcessing(true);

    try {
      const cartId = cartAPI.getStoredCartId();
      if (!cartId) {
        throw new Error('No cart found');
      }

      // Prepare the update payload
      const updatePayload: UpdateCartPayload = {
        shipping_address: convertAddressToMedusaFormat(shippingAddress),
        billing_address: useSameAddressForBilling 
          ? convertAddressToMedusaFormat(shippingAddress)
          : convertAddressToMedusaFormat(billingAddress),
      };

      // Update cart with addresses
      await cartAPI.updateCart(cartId, updatePayload);

      // Proceed to next step
      goToNextStep();
    } catch (error) {
      console.error('Failed to update cart:', error);
      // You might want to show an error message to the user here
      alert('Failed to update cart. Please try again.');
    } finally {
      setIsUpdatingCart(false);
      setProcessing(false);
    }
  };

  const isValid = validateCurrentStep();

  return (
    <div 
      className="bg-white rounded-lg shadow-sm border p-8"
      style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
    >
      <div className="space-y-8">
        {/* Contact Information */}
        <div>
          <h2 
            className="text-xl font-semibold mb-6"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            Contact Information
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label 
                className="block text-sm font-medium mb-2"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                Email Address *
              </label>
              <input
                type="email"
                name="email"
                value={contactInfo.email}
                onChange={handleContactChange}
                required
                className="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                style={{ 
                  borderColor: 'var(--theme-border, #d1d5db)',
                  '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                }}
                placeholder="<EMAIL>"
              />
            </div>
            
            <div>
              <label 
                className="block text-sm font-medium mb-2"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                Phone Number
              </label>
              <input
                type="tel"
                name="phone"
                value={contactInfo.phone}
                onChange={handleContactChange}
                className="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                style={{ 
                  borderColor: 'var(--theme-border, #d1d5db)',
                  '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                }}
                placeholder="+****************"
              />
            </div>
            
            <div>
              <label 
                className="block text-sm font-medium mb-2"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                First Name *
              </label>
              <input
                type="text"
                name="firstName"
                value={contactInfo.firstName}
                onChange={handleContactChange}
                required
                className="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                style={{ 
                  borderColor: 'var(--theme-border, #d1d5db)',
                  '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                }}
                placeholder="John"
              />
            </div>
            
            <div>
              <label 
                className="block text-sm font-medium mb-2"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                Last Name *
              </label>
              <input
                type="text"
                name="lastName"
                value={contactInfo.lastName}
                onChange={handleContactChange}
                required
                className="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                style={{ 
                  borderColor: 'var(--theme-border, #d1d5db)',
                  '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                }}
                placeholder="Doe"
              />
            </div>
          </div>
        </div>

        {/* Shipping Address */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <h2 
              className="text-xl font-semibold"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              Shipping Address
            </h2>
            
            {/* Saved Addresses Options for Logged In Users */}
            {isAuthenticated && user && addresses.length > 0 && (
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => setShowSavedAddresses(!showSavedAddresses)}
                  className="text-sm font-medium transition-colors hover:opacity-80 flex items-center space-x-1"
                  style={{ color: 'var(--theme-primary, #3b82f6)' }}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <span>{showSavedAddresses ? 'Hide' : 'Use'} Saved Address ({addresses.length})</span>
                </button>
              </div>
            )}
          </div>

          {/* Saved Addresses List */}
          {showSavedAddresses && isAuthenticated && user && addresses.length > 0 && (
            <Card elevation={1} sx={{ mb: 3, bgcolor: 'grey.50' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom fontWeight="bold">
                  <LocationIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Select from saved addresses:
                </Typography>
                <Grid container spacing={2}>
                  {addresses.map((address) => (
                    <Grid item xs={12} md={6} key={address.id}>
                      <Paper 
                        elevation={2}
                        sx={{ 
                          p: 2, 
                          cursor: 'pointer',
                          transition: 'all 0.2s',
                          '&:hover': {
                            elevation: 4,
                            transform: 'translateY(-2px)',
                            bgcolor: 'primary.50'
                          }
                        }}
                        onClick={() => handleSelectSavedAddress(address)}
                      >
                        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                          <Box flex={1}>
                            <Stack direction="row" spacing={1} alignItems="center" mb={1}>
                              {address.address_name ? (
                                <Chip 
                                  icon={<HomeIcon />}
                                  label={address.address_name}
                                  size="small"
                                  color="primary"
                                  variant="filled"
                                />
                              ) : (
                                <Chip 
                                  icon={<PersonIcon />}
                                  label={`${address.first_name} ${address.last_name}`}
                                  size="small"
                                  color="default"
                                  variant="outlined"
                                />
                              )}
                            </Stack>
                            
                            <Typography variant="body2" color="text.secondary" gutterBottom>
                              <LocationIcon sx={{ fontSize: 14, mr: 0.5, verticalAlign: 'middle' }} />
                              {address.address_1}
                              {address.address_2 && `, ${address.address_2}`}
                            </Typography>
                            
                            <Typography variant="body2" color="text.secondary" gutterBottom>
                              {address.city}, {address.province} {address.postal_code}
                            </Typography>
                            
                            {address.phone && (
                              <Typography variant="body2" color="text.secondary">
                                <PhoneIcon sx={{ fontSize: 14, mr: 0.5, verticalAlign: 'middle' }} />
                                {address.phone}
                              </Typography>
                            )}
                          </Box>
                          
                          <Button
                            variant="contained"
                            size="small"
                            sx={{ 
                              ml: 2,
                              borderRadius: 2,
                              textTransform: 'none',
                              fontWeight: 600
                            }}
                          >
                            Use Address
                          </Button>
                        </Box>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          )}
          
          <div className="space-y-6">

            {/* Address Line 1 */}
            <div>
              <label 
                className="block text-sm font-medium mb-2"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                Address Line 1 *
              </label>
              <input
                type="text"
                name="address"
                value={shippingAddress.address}
                onChange={handleShippingAddressChange}
                required
                className="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                style={{ 
                  borderColor: 'var(--theme-border, #d1d5db)',
                  '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                }}
                placeholder="123 Main Street"
              />
            </div>

            {/* Address Line 2 */}
            <div>
              <label 
                className="block text-sm font-medium mb-2"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                Address Line 2 (Optional)
              </label>
              <input
                type="text"
                name="address2"
                value={shippingAddress.address2}
                onChange={handleShippingAddressChange}
                className="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                style={{ 
                  borderColor: 'var(--theme-border, #d1d5db)',
                  '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                }}
                placeholder="Apartment, suite, etc."
              />
            </div>
            
            {/* City and PIN Code */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label 
                  className="block text-sm font-medium mb-2"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  City *
                </label>
                <input
                  type="text"
                  name="city"
                  value={shippingAddress.city}
                  onChange={handleShippingAddressChange}
                  required
                  className="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                  style={{ 
                    borderColor: 'var(--theme-border, #d1d5db)',
                    '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                  }}
                  placeholder="Mumbai, Delhi, Bangalore"
                />
              </div>
              
              <div>
                <label 
                  className="block text-sm font-medium mb-2"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  PIN Code *
                </label>
                <input
                  type="text"
                  name="zipCode"
                  value={shippingAddress.zipCode}
                  onChange={handleShippingAddressChange}
                  required
                  maxLength={6}
                  pattern="[0-9]{6}"
                  className="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                  style={{ 
                    borderColor: 'var(--theme-border, #d1d5db)',
                    '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                  }}
                  placeholder="400001"
                />
              </div>
            </div>
            
            {/* Country Info Display */}
            <div className="p-3 rounded-lg" style={{ backgroundColor: 'var(--theme-background, #f0f9ff)', borderColor: 'var(--theme-primary, #3b82f6)', border: '1px solid' }}>
              <div className="flex items-center space-x-2">
                <svg className="w-5 h-5" fill="none" stroke="var(--theme-primary, #3b82f6)" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span className="text-sm font-medium" style={{ color: 'var(--theme-primary, #3b82f6)' }}>
                  Country: India | All deliveries within India
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Billing Address Section */}
        <div>
          <div className="flex items-center mb-6">
            <input
              type="checkbox"
              id="useSameAddress"
              checked={useSameAddressForBilling}
              onChange={handleUseSameAddressChange}
              className="mr-3"
              style={{ accentColor: 'var(--theme-primary, #3b82f6)' }}
            />
            <label 
              htmlFor="useSameAddress"
              className="text-sm font-medium cursor-pointer"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              Use shipping address as billing address
            </label>
          </div>

          {!useSameAddressForBilling && (
            <div>
              <h2 
                className="text-xl font-semibold mb-6"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                Billing Address
              </h2>
              
              <div className="space-y-6">

                {/* Address Line 1 */}
                <div>
                  <label 
                    className="block text-sm font-medium mb-2"
                    style={{ color: 'var(--theme-text, #111827)' }}
                  >
                    Address Line 1 *
                  </label>
                  <input
                    type="text"
                    name="address"
                    value={billingAddress.address}
                    onChange={handleBillingAddressChange}
                    required
                    className="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                    style={{ 
                      borderColor: 'var(--theme-border, #d1d5db)',
                      '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                    }}
                    placeholder="123 Main Street"
                  />
                </div>

                {/* Address Line 2 */}
                <div>
                  <label 
                    className="block text-sm font-medium mb-2"
                    style={{ color: 'var(--theme-text, #111827)' }}
                  >
                    Address Line 2 (Optional)
                  </label>
                  <input
                    type="text"
                    name="address2"
                    value={billingAddress.address2}
                    onChange={handleBillingAddressChange}
                    className="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                    style={{ 
                      borderColor: 'var(--theme-border, #d1d5db)',
                      '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                    }}
                    placeholder="Apartment, suite, etc."
                  />
                </div>
                
                {/* City and PIN Code */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label 
                      className="block text-sm font-medium mb-2"
                      style={{ color: 'var(--theme-text, #111827)' }}
                    >
                      City *
                    </label>
                    <input
                      type="text"
                      name="city"
                      value={billingAddress.city}
                      onChange={handleBillingAddressChange}
                      required
                      className="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                      style={{ 
                        borderColor: 'var(--theme-border, #d1d5db)',
                        '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                      }}
                      placeholder="Mumbai, Delhi, Bangalore"
                    />
                  </div>
                  
                  <div>
                    <label 
                      className="block text-sm font-medium mb-2"
                      style={{ color: 'var(--theme-text, #111827)' }}
                    >
                      PIN Code *
                    </label>
                    <input
                      type="text"
                      name="zipCode"
                      value={billingAddress.zipCode}
                      onChange={handleBillingAddressChange}
                      required
                      maxLength={6}
                      pattern="[0-9]{6}"
                      className="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                      style={{ 
                        borderColor: 'var(--theme-border, #d1d5db)',
                        '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                      }}
                      placeholder="400001"
                    />
                  </div>
                </div>
                
                {/* Country Info Display */}
                <div className="p-3 rounded-lg" style={{ backgroundColor: 'var(--theme-background, #f0f9ff)', borderColor: 'var(--theme-primary, #3b82f6)', border: '1px solid' }}>
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5" fill="none" stroke="var(--theme-primary, #3b82f6)" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span className="text-sm font-medium" style={{ color: 'var(--theme-primary, #3b82f6)' }}>
                      Country: India | All deliveries within India
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Continue Button */}
        <div className="flex justify-end pt-6">
          <button
            onClick={handleContinue}
            disabled={!isValid || isUpdatingCart}
            className={`px-8 py-3 rounded-lg font-semibold transition-all duration-200 flex items-center space-x-2 ${
              isValid && !isUpdatingCart
                ? 'hover:shadow-lg' 
                : 'opacity-50 cursor-not-allowed'
            }`}
            style={{ 
              backgroundColor: 'var(--btn-primary, #3b82f6)',
              color: 'var(--btn-text, #ffffff)',
            }}
          >
            {isUpdatingCart && (
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            )}
            <span>{isUpdatingCart ? 'Updating...' : 'Continue to Payment'}</span>
          </button>
        </div>
      </div>
    </div>
  );
};