'use client';

import React from 'react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface CheckoutLoadingStateProps {
  message?: string;
  submessage?: string;
}

export const CheckoutLoadingState: React.FC<CheckoutLoadingStateProps> = ({ 
  message = 'Loading checkout...', 
  submessage = 'Please wait while we prepare your checkout experience' 
}) => {
  return (
    <div 
      className="min-h-screen flex items-center justify-center" 
      style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}
    >
      <div className="text-center max-w-md mx-auto px-4">
        {/* Loading Spinner */}
        <div className="mb-6">
          <LoadingSpinner size="lg" />
        </div>
        
        {/* Main Message */}
        <h2 
          className="text-xl font-semibold mb-3"
          style={{ color: 'var(--theme-text, #111827)' }}
        >
          {message}
        </h2>
        
        {/* Sub Message */}
        <p 
          className="text-sm leading-relaxed"
          style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
        >
          {submessage}
        </p>
        
        {/* Progress Dots */}
        <div className="flex justify-center space-x-2 mt-6">
          <div 
            className="w-2 h-2 rounded-full animate-pulse"
            style={{ backgroundColor: 'var(--theme-primary, #3b82f6)' }}
          ></div>
          <div 
            className="w-2 h-2 rounded-full animate-pulse"
            style={{ 
              backgroundColor: 'var(--theme-primary, #3b82f6)',
              animationDelay: '0.2s'
            }}
          ></div>
          <div 
            className="w-2 h-2 rounded-full animate-pulse"
            style={{ 
              backgroundColor: 'var(--theme-primary, #3b82f6)',
              animationDelay: '0.4s'
            }}
          ></div>
        </div>
      </div>
    </div>
  );
};