'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { useCheckout } from '@/hooks/useCheckout';
import { CheckoutState, ContactInformation, ShippingAddress, BillingAddress, PaymentMethod, CreditCardDetails } from '@/types/checkout';

interface CheckoutContextType {
  checkoutState: CheckoutState;
  updateContactInfo: (contactInfo: Partial<ContactInformation>) => void;
  updateShippingAddress: (shippingAddress: Partial<ShippingAddress>) => void;
  updateBillingAddress: (billingAddress: Partial<BillingAddress>) => void;
  toggleSameAddressForBilling: (useSame: boolean) => void;
  selectPaymentMethod: (paymentMethod: PaymentMethod) => void;
  updateCreditCardDetails: (creditCardDetails: Partial<CreditCardDetails>) => void;
  updateOrderNotes: (orderNotes: string) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  goToStep: (stepNumber: number) => void;
  setProcessing: (isProcessing: boolean) => void;
  resetCheckout: () => void;
  validateCurrentStep: () => boolean;
}

const CheckoutContext = createContext<CheckoutContextType | undefined>(undefined);

interface CheckoutProviderProps {
  children: ReactNode;
}

export const CheckoutProvider: React.FC<CheckoutProviderProps> = ({ children }) => {
  const checkoutData = useCheckout();

  return (
    <CheckoutContext.Provider value={checkoutData}>
      {children}
    </CheckoutContext.Provider>
  );
};

export const useCheckoutContext = (): CheckoutContextType => {
  const context = useContext(CheckoutContext);
  if (context === undefined) {
    throw new Error('useCheckoutContext must be used within a CheckoutProvider');
  }
  return context;
};