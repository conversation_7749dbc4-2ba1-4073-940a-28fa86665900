'use client';

import React from 'react';
import { useCheckoutContext } from './CheckoutProvider';
import { CheckoutStepIndicator } from './CheckoutStepIndicator';
import { ContactInformationStep } from './ContactInformationStep';
import { PaymentMethodStep } from './PaymentMethodStep';
import { OrderReviewStep } from './OrderReviewStep';
import { CheckoutOrderSummary } from './CheckoutOrderSummary';
import { Cart } from '@/lib/api/cart';

interface MultiStepCheckoutProps {
  storeHandle: string;
  cart: Cart;
}

export const MultiStepCheckout: React.FC<MultiStepCheckoutProps> = ({ storeHandle, cart }) => {
  const { checkoutState } = useCheckoutContext();

  console.log(`📋 MultiStepCheckout rendered for store: ${storeHandle}`);
  console.log(`📦 Cart data:`, cart ? `${cart.items.length} items` : 'no cart');

  const renderCurrentStep = () => {
    switch (checkoutState.currentStep) {
      case 1:
        return <ContactInformationStep storeHandle={storeHandle} />;
      case 2:
        return <PaymentMethodStep storeHandle={storeHandle} />;
      case 3:
        return <OrderReviewStep storeHandle={storeHandle} cart={cart} />;
      default:
        return <ContactInformationStep storeHandle={storeHandle} />;
    }
  };
  
  // Ensure cart data is available
  if (!cart) {
    return (
      <div className="text-center py-8">
        <p style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>
          Loading checkout data...
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Step Indicator */}
      <CheckoutStepIndicator steps={checkoutState.steps} currentStep={checkoutState.currentStep} />

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Checkout Step Content */}
        <div className="lg:col-span-2">
          {renderCurrentStep()}
        </div>

        {/* Order Summary Sidebar */}
        <div className="lg:col-span-1">
          <CheckoutOrderSummary storeHandle={storeHandle} />
        </div>
      </div>
    </div>
  );
};