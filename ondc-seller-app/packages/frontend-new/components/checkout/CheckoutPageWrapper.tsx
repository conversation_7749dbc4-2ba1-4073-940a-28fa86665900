'use client';

import React, { useEffect, useState } from 'react';
import { useStoreConfig } from '@/hooks/useStoreConfig';
import { CheckoutLoadingState } from './CheckoutLoadingState';

interface CheckoutPageWrapperProps {
  children: React.ReactNode;
  storeHandle: string;
}

export const CheckoutPageWrapper: React.FC<CheckoutPageWrapperProps> = ({ 
  children, 
  storeHandle 
}) => {
  const [themeReady, setThemeReady] = useState(false);
  const { storeData, isLoading: storeLoading } = useStoreConfig(storeHandle);

  useEffect(() => {
    // Wait for store data and theme to be loaded
    if (storeData && !storeLoading) {
      // Small delay to ensure theme CSS variables are applied
      const timer = setTimeout(() => {
        setThemeReady(true);
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [storeData, storeLoading]);

  // Show loading until theme is ready
  if (storeLoading || !themeReady) {
    return (
      <CheckoutLoadingState 
        message="Setting up checkout..."
        submessage="Loading store theme and configuration"
      />
    );
  }

  return <>{children}</>;
};