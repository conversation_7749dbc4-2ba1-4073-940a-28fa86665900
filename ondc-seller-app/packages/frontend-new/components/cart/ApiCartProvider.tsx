'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { useApiCart, AddToCartItem } from '@/hooks/useApiCart';
import { Cart, Region } from '@/lib/api/cart';

interface ApiCartContextType {
  cart: Cart | null;
  isOpen: boolean;
  isLoading: boolean;
  region: Region | null;
  addToCart: (item: AddToCartItem) => Promise<void>;
  removeItem: (lineItemId: string) => Promise<void>;
  updateQuantity: (lineItemId: string, quantity: number) => Promise<void>;
  clearCart: () => Promise<void>;
  openCart: () => void;
  closeCart: () => void;
  toggleCart: () => void;
  refreshCart: () => Promise<void>;
}

const ApiCartContext = createContext<ApiCartContextType | undefined>(undefined);

interface ApiCartProviderProps {
  children: ReactNode;
  storeHandle: string;
}

export const ApiCartProvider: React.FC<ApiCartProviderProps> = ({ children, storeHandle }) => {
  const cartData = useApiCart(storeHandle);

  return (
    <ApiCartContext.Provider value={cartData}>
      {children}
    </ApiCartContext.Provider>
  );
};

export const useApiCartContext = (): ApiCartContextType => {
  const context = useContext(ApiCartContext);
  if (context === undefined) {
    throw new Error('useApiCartContext must be used within an ApiCartProvider');
  }
  return context;
};
