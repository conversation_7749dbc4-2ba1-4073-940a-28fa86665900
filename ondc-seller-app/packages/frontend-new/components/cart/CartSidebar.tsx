'use client';

import React from 'react';
import Image from 'next/image';
import { useCartContext } from './CartProvider';
import { formatPrice, calculateCartTotals } from '@/lib/cart/mockCartData';

export const CartSidebar: React.FC = () => {
  const { cart, isOpen, isLoading, closeCart, removeItem, updateQuantity } = useCartContext();
  
  const totals = calculateCartTotals(cart.items);

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop Overlay with Blur */}
      <div 
        className="fixed inset-0 z-50 transition-all duration-300 ease-in-out"
        style={{ 
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          backdropFilter: 'blur(8px)',
          WebkitBackdropFilter: 'blur(8px)',
        }}
        onClick={closeCart}
      />

      {/* Cart Sidebar */}
      <div 
        className="fixed top-0 right-0 h-full w-full max-w-md z-50 transform transition-transform duration-300 ease-in-out shadow-2xl"
        style={{ 
          backgroundColor: 'var(--theme-surface)',
          transform: isOpen ? 'translateX(0)' : 'translateX(100%)',
        }}
      >
        {/* Header */}
        <div 
          className="flex items-center justify-between p-6 border-b"
          style={{ borderBottomColor: 'var(--theme-border)' }}
        >
          <div className="flex items-center space-x-3">
            <div 
              className="w-8 h-8 rounded-full flex items-center justify-center"
              style={{ backgroundColor: 'var(--theme-primary)' }}
            >
              <svg 
                className="w-5 h-5" 
                fill="none" 
                stroke="var(--btn-text)" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
              </svg>
            </div>
            <div>
              <h2 
                className="text-lg font-bold"
                style={{ color: 'var(--theme-text)' }}
              >
                Shopping Cart
              </h2>
              <p 
                className="text-sm"
                style={{ color: 'var(--theme-text-secondary)' }}
              >
                {cart.totalItems} {cart.totalItems === 1 ? 'item' : 'items'}
              </p>
            </div>
          </div>
          
          <button
            onClick={closeCart}
            className="p-2 rounded-full transition-colors hover:opacity-80"
            style={{ backgroundColor: 'var(--theme-background)' }}
          >
            <svg 
              className="w-5 h-5" 
              fill="none" 
              stroke="var(--theme-text)" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Cart Items */}
        <div className="flex-1 overflow-y-auto p-6 space-y-4" style={{ maxHeight: 'calc(100vh - 280px)' }}>
          {cart.items.length === 0 ? (
            <div className="text-center py-12">
              <div 
                className="w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4"
                style={{ backgroundColor: 'var(--theme-background)' }}
              >
                <svg 
                  className="w-8 h-8" 
                  fill="none" 
                  stroke="var(--theme-text-secondary)" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                </svg>
              </div>
              <h3 
                className="text-lg font-medium mb-2"
                style={{ color: 'var(--theme-text)' }}
              >
                Your cart is empty
              </h3>
              <p 
                className="text-sm"
                style={{ color: 'var(--theme-text-secondary)' }}
              >
                Add some products to get started
              </p>
            </div>
          ) : (
            cart.items.map((item) => (
              <div 
                key={item.id} 
                className="flex space-x-4 p-4 rounded-lg border transition-all duration-200 hover:shadow-sm"
                style={{ 
                  backgroundColor: 'var(--theme-surface)',
                  borderColor: 'var(--theme-border)',
                }}
              >
                {/* Product Image */}
                <div className="relative w-16 h-16 rounded-lg overflow-hidden flex-shrink-0">
                  <Image
                    src={item.image}
                    alt={item.title}
                    fill
                    className="object-cover"
                  />
                </div>

                {/* Product Details */}
                <div className="flex-1 min-w-0">
                  <h3 
                    className="font-medium text-sm truncate"
                    style={{ color: 'var(--theme-text)' }}
                  >
                    {item.title}
                  </h3>
                  
                  {item.variant && (
                    <p 
                      className="text-xs mt-1"
                      style={{ color: 'var(--theme-text-secondary)' }}
                    >
                      {Object.entries(item.variant.options).map(([key, value]) => 
                        `${key}: ${value}`
                      ).join(', ')}
                    </p>
                  )}

                  <div className="flex items-center justify-between mt-2">
                    <span 
                      className="font-semibold text-sm"
                      style={{ color: 'var(--theme-primary)' }}
                    >
                      {formatPrice(item.price)}
                    </span>

                    {/* Quantity Controls */}
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => updateQuantity(item.id, item.quantity - 1)}
                        className="w-6 h-6 rounded-full flex items-center justify-center transition-colors"
                        style={{ 
                          backgroundColor: 'var(--theme-background)',
                          color: 'var(--theme-text)',
                        }}
                        disabled={isLoading}
                      >
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                        </svg>
                      </button>
                      
                      <span 
                        className="text-sm font-medium min-w-[20px] text-center"
                        style={{ color: 'var(--theme-text)' }}
                      >
                        {item.quantity}
                      </span>
                      
                      <button
                        onClick={() => updateQuantity(item.id, item.quantity + 1)}
                        className="w-6 h-6 rounded-full flex items-center justify-center transition-colors"
                        style={{ 
                          backgroundColor: 'var(--theme-background)',
                          color: 'var(--theme-text)',
                        }}
                        disabled={isLoading || item.quantity >= (item.maxQuantity || 99)}
                      >
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>

                {/* Remove Button */}
                <button
                  onClick={() => removeItem(item.id)}
                  className="p-1 rounded-full transition-colors hover:opacity-80 flex-shrink-0"
                  style={{ color: 'var(--theme-text-secondary)' }}
                  disabled={isLoading}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            ))
          )}
        </div>

        {/* Cart Summary & Checkout */}
        {cart.items.length > 0 && (
          <div 
            className="border-t p-6 space-y-4"
            style={{ borderTopColor: 'var(--theme-border)' }}
          >
            {/* Totals */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span style={{ color: 'var(--theme-text-secondary)' }}>Subtotal:</span>
                <span style={{ color: 'var(--theme-text)' }}>{formatPrice(totals.subtotal)}</span>
              </div>
              
              <div className="flex justify-between text-sm">
                <span style={{ color: 'var(--theme-text-secondary)' }}>Tax:</span>
                <span style={{ color: 'var(--theme-text)' }}>{formatPrice(totals.tax)}</span>
              </div>
              
              <div className="flex justify-between text-sm">
                <span style={{ color: 'var(--theme-text-secondary)' }}>Shipping:</span>
                <span style={{ color: 'var(--theme-text)' }}>
                  {totals.shipping === 0 ? 'Free' : formatPrice(totals.shipping)}
                </span>
              </div>
              
              <div 
                className="flex justify-between text-lg font-bold pt-2 border-t"
                style={{ borderTopColor: 'var(--theme-border)' }}
              >
                <span style={{ color: 'var(--theme-text)' }}>Total:</span>
                <span style={{ color: 'var(--theme-primary)' }}>{formatPrice(totals.finalTotal)}</span>
              </div>
            </div>

            {/* Checkout Button */}
            <button
              className="w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
              style={{ 
                backgroundColor: 'var(--btn-primary)',
                color: 'var(--btn-text)',
              }}
              onMouseEnter={(e) => {
                if (!isLoading) {
                  e.currentTarget.style.backgroundColor = 'var(--btn-primary-hover)';
                }
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--btn-primary)';
              }}
              disabled={isLoading}
              onClick={() => {
                // Handle checkout
                console.log('Proceeding to checkout with:', cart);
                alert('Proceeding to checkout...');
              }}
            >
              {isLoading ? 'Processing...' : `Checkout • ${formatPrice(totals.finalTotal)}`}
            </button>

            {/* Continue Shopping */}
            <button
              onClick={closeCart}
              className="w-full py-2 px-4 text-sm font-medium transition-colors"
              style={{ color: 'var(--theme-text-secondary)' }}
            >
              Continue Shopping
            </button>
          </div>
        )}
      </div>
    </>
  );
};