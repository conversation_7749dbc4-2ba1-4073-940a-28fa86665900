'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { useCartContext } from './CartProvider';
import { formatPrice, calculateCartTotals } from '@/lib/cart/mockCartData';

export const FixedCartSidebar: React.FC = () => {
  const { cart, isOpen, isLoading, closeCart, removeItem, updateQuantity } = useCartContext();
  const [isCheckingOut, setIsCheckingOut] = useState(false);
  
  const totals = calculateCartTotals(cart.items);

  const handleCheckout = async () => {
    setIsCheckingOut(true);
    
    try {
      // Simulate checkout process
      console.log('Proceeding to checkout with:', cart);
      console.log('Total amount:', formatPrice(totals.finalTotal));
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert(`Checkout successful! Total: ${formatPrice(totals.finalTotal)}`);
      
      // In a real app, you would redirect to checkout page or payment processor
      // window.location.href = `/checkout?total=${totals.finalTotal}`;
      
    } catch (error) {
      console.error('Checkout error:', error);
      alert('Checkout failed. Please try again.');
    } finally {
      setIsCheckingOut(false);
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop Overlay with Blur */}
      <div 
        className="fixed inset-0 z-50 transition-all duration-300 ease-in-out bg-black bg-opacity-50"
        style={{ 
          backdropFilter: 'blur(8px)',
          WebkitBackdropFilter: 'blur(8px)',
        }}
        onClick={closeCart}
      />

      {/* Cart Sidebar */}
      <div 
        className={`fixed top-0 right-0 h-full w-full max-w-md z-50 transform transition-transform duration-300 ease-in-out shadow-2xl bg-white ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 rounded-full flex items-center justify-center bg-blue-600">
              <svg 
                className="w-5 h-5 text-white" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
              </svg>
            </div>
            <div>
              <h2 className="text-lg font-bold text-gray-900">
                Shopping Cart
              </h2>
              <p className="text-sm text-gray-600">
                {cart.totalItems} {cart.totalItems === 1 ? 'item' : 'items'}
              </p>
            </div>
          </div>
          
          <button
            onClick={closeCart}
            className="p-2 rounded-full transition-colors hover:bg-gray-100"
          >
            <svg 
              className="w-5 h-5 text-gray-600" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Cart Items */}
        <div className="flex-1 overflow-y-auto p-6 space-y-4" style={{ maxHeight: 'calc(100vh - 280px)' }}>
          {cart.items.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4 bg-gray-100">
                <svg 
                  className="w-8 h-8 text-gray-400" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                </svg>
              </div>
              <h3 className="text-lg font-medium mb-2 text-gray-900">
                Your cart is empty
              </h3>
              <p className="text-sm text-gray-600">
                Add some products to get started
              </p>
            </div>
          ) : (
            cart.items.map((item) => (
              <div 
                key={item.id} 
                className="flex space-x-4 p-4 rounded-lg border border-gray-200 transition-all duration-200 hover:shadow-sm bg-white"
              >
                {/* Product Image */}
                <div className="relative w-16 h-16 rounded-lg overflow-hidden flex-shrink-0 bg-gray-100">
                  <Image
                    src={item.image}
                    alt={item.title}
                    fill
                    className="object-cover"
                    onError={(e) => {
                      // Fallback for missing images
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                  {/* Fallback icon if image fails */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                </div>

                {/* Product Details */}
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-sm truncate text-gray-900">
                    {item.title}
                  </h3>
                  
                  {item.variant && (
                    <p className="text-xs mt-1 text-gray-600">
                      {Object.entries(item.variant.options).map(([key, value]) => 
                        `${key}: ${value}`
                      ).join(', ')}
                    </p>
                  )}

                  <div className="flex items-center justify-between mt-2">
                    <span className="font-semibold text-sm text-blue-600">
                      {formatPrice(item.price)}
                    </span>

                    {/* Quantity Controls */}
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => updateQuantity(item.id, item.quantity - 1)}
                        className="w-6 h-6 rounded-full flex items-center justify-center transition-colors bg-gray-100 hover:bg-gray-200 text-gray-700"
                        disabled={isLoading}
                      >
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                        </svg>
                      </button>
                      
                      <span className="text-sm font-medium min-w-[20px] text-center text-gray-900">
                        {item.quantity}
                      </span>
                      
                      <button
                        onClick={() => updateQuantity(item.id, item.quantity + 1)}
                        className="w-6 h-6 rounded-full flex items-center justify-center transition-colors bg-gray-100 hover:bg-gray-200 text-gray-700"
                        disabled={isLoading || item.quantity >= (item.maxQuantity || 99)}
                      >
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>

                {/* Remove Button */}
                <button
                  onClick={() => removeItem(item.id)}
                  className="p-1 rounded-full transition-colors hover:bg-red-50 text-gray-400 hover:text-red-500 flex-shrink-0"
                  disabled={isLoading}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            ))
          )}
        </div>

        {/* Cart Summary & Checkout */}
        {cart.items.length > 0 && (
          <div className="border-t border-gray-200 p-6 space-y-4">
            {/* Totals */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Subtotal:</span>
                <span className="text-gray-900">{formatPrice(totals.subtotal)}</span>
              </div>
              
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Tax:</span>
                <span className="text-gray-900">{formatPrice(totals.tax)}</span>
              </div>
              
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Shipping:</span>
                <span className="text-gray-900">
                  {totals.shipping === 0 ? 'Free' : formatPrice(totals.shipping)}
                </span>
              </div>
              
              <div className="flex justify-between text-lg font-bold pt-2 border-t border-gray-200">
                <span className="text-gray-900">Total:</span>
                <span className="text-blue-600">{formatPrice(totals.finalTotal)}</span>
              </div>
            </div>

            {/* Checkout Button */}
            <button
              className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 ${
                isCheckingOut || isLoading
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 hover:shadow-lg'
              } text-white`}
              disabled={isCheckingOut || isLoading}
              onClick={handleCheckout}
            >
              {isCheckingOut 
                ? 'Processing...' 
                : isLoading 
                  ? 'Loading...' 
                  : `Checkout • ${formatPrice(totals.finalTotal)}`
              }
            </button>

            {/* Continue Shopping */}
            <button
              onClick={closeCart}
              className="w-full py-2 px-4 text-sm font-medium transition-colors text-gray-600 hover:text-gray-800"
            >
              Continue Shopping
            </button>
          </div>
        )}
      </div>
    </>
  );
};