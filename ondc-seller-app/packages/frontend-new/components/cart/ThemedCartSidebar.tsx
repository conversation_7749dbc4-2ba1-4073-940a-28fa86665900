'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { getStoreCartAPI, Cart } from '@/lib/api/cart';
import { useToast } from '@/app/providers/toast-provider';

// Material Design 3 Components
import {
  Drawer,
  Box,
  Typography,
  IconButton,
  Button,
  Card,
  CardContent,
  Stack,
  Divider,
  Badge,
  Chip,
  CircularProgress,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  ButtonGroup,
  Backdrop,
} from '@mui/material';
import {
  Close as CloseIcon,
  ShoppingCart as CartIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  Delete as DeleteIcon,
  ShoppingBag as EmptyCartIcon,
} from '@mui/icons-material';

interface ThemedCartSidebarProps {
  storeHandle: string;
}

export const ThemedCartSidebar: React.FC<ThemedCartSidebarProps> = ({ storeHandle }) => {
  const router = useRouter();
  const { showToast } = useToast();
  const [cart, setCart] = useState<Cart | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isCheckingOut, setIsCheckingOut] = useState(false);
  
  // Get store-specific cart API
  const cartAPI = getStoreCartAPI(storeHandle);
  
  // Calculate totals for API cart
  const formatPrice = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
    }).format(amount); // Assuming amount is in cents
  };
  
  const calculateTotals = () => {
    if (!cart || !cart.items || cart.items.length === 0) {
      return {
        subtotal: 0,
        tax: 0,
        shipping: 0,
        finalTotal: 0,
        totalItems: 0
      };
    }
    
    const subtotal = cart.items.reduce((sum, item) => {
      return sum + (item.unit_price * item.quantity);
    }, 0);
    
    const tax = subtotal * 0.08; // 8% tax
    const shipping = subtotal > 5000 ? 0 : 50; // Free shipping over $50
    const finalTotal = subtotal + tax + shipping;
    const totalItems = cart.items.reduce((sum, item) => sum + item.quantity, 0);
    
    return {
      subtotal,
      tax,
      shipping,
      finalTotal,
      totalItems
    };
  };
  
  const totals = calculateTotals();

  // Load cart data
  const loadCart = async () => {
    const cartId = cartAPI.getStoredCartId();
    if (!cartId) return;
    
    try {
      console.log(`🔄 Loading cart for store ${storeHandle}:`, cartId);
      const cartResponse = await cartAPI.getCart(cartId);
      setCart(cartResponse.cart);
      console.log(`✅ Loaded cart for store ${storeHandle}:`, cartResponse.cart.items.length, 'items');
    } catch (error) {
      console.error(`Failed to load cart for store ${storeHandle}:`, error);
    }
  };

  // Remove item
  const removeItem = async (lineItemId: string) => {
    if (!cart?.id) return;
    
    setIsLoading(true);
    try {
      console.log(`🗑️ Removing item from cart for store ${storeHandle}:`, lineItemId);
      const cartResponse = await cartAPI.removeLineItem(cart.id, lineItemId);
      setCart(cartResponse.cart);
      
      // Dispatch cart update event to notify header and other components
      window.dispatchEvent(new CustomEvent('cartUpdated'));
      
      console.log(`✅ Removed item from cart for store ${storeHandle}`);
      showToast('Item removed from cart', 'success');
    } catch (error) {
      console.error(`Failed to remove item from cart for store ${storeHandle}:`, error);
      showToast('Failed to remove item from cart', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Update quantity
  const updateQuantity = async (lineItemId: string, quantity: number) => {
    if (!cart?.id) return;
    
    if (quantity <= 0) {
      await removeItem(lineItemId);
      return;
    }
    
    setIsLoading(true);
    try {
      console.log(`📝 Updating quantity for store ${storeHandle}:`, lineItemId, 'to', quantity);
      const cartResponse = await cartAPI.updateLineItem(cart.id, lineItemId, { quantity });
      setCart(cartResponse.cart);
      
      // Dispatch cart update event to notify header and other components
      window.dispatchEvent(new CustomEvent('cartUpdated'));
      
      console.log(`✅ Updated quantity for store ${storeHandle}`);
    } catch (error) {
      console.error(`Failed to update quantity for store ${storeHandle}:`, error);
      showToast('Failed to update quantity', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Close cart
  const closeCart = () => {
    setIsOpen(false);
  };

  const handleCheckout = async () => {
    setIsCheckingOut(true);
    
    try {
      console.log('Proceeding to checkout with:', cart);
      console.log('Total amount:', formatPrice(totals.finalTotal, 'INR'));
      
      // Navigate to checkout page
      const checkoutUrl = `/${storeHandle}/checkout`;
      
      // Start navigation
      router.push(checkoutUrl);
      
      // Keep loading state and close cart after a longer delay
      // This ensures users see the loading state
      setTimeout(() => {
        closeCart();
      }, 1000); // 1 second delay to show loading state in cart
      
      // Fallback timeout to clear loading state if navigation takes too long
      setTimeout(() => {
        setIsCheckingOut(false);
      }, 10000); // 10 second fallback timeout
      
    } catch (error) {
      console.error('Checkout error:', error);
      showToast('Checkout failed. Please try again.', 'error');
      setIsCheckingOut(false);
    }
  };

  // Listen for cart updates
  useEffect(() => {
    const handleCartUpdate = () => {
      loadCart();
    };

    // Listen for custom cart update events
    window.addEventListener('cartUpdated', handleCartUpdate);
    
    // Listen for cart open events
    const handleCartOpen = () => {
      setIsOpen(true);
      loadCart();
    };
    window.addEventListener('openCart', handleCartOpen);

    // Load cart on mount
    loadCart();

    return () => {
      window.removeEventListener('cartUpdated', handleCartUpdate);
      window.removeEventListener('openCart', handleCartOpen);
    };
  }, [storeHandle]);

  // Listen for route changes to clear loading state
  useEffect(() => {
    const handleRouteChange = () => {
      // Clear loading state when route changes (navigation completed)
      setIsCheckingOut(false);
    };

    // Listen for browser navigation events
    window.addEventListener('popstate', handleRouteChange);
    
    // Listen for Next.js route changes
    const handleBeforeUnload = () => {
      setIsCheckingOut(false);
    };
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('popstate', handleRouteChange);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  // Cleanup loading state on unmount and add visibility change listener
  useEffect(() => {
    const handleVisibilityChange = () => {
      // Clear loading state when page becomes visible again (after navigation)
      if (!document.hidden && isCheckingOut) {
        setTimeout(() => {
          setIsCheckingOut(false);
        }, 500); // Small delay to ensure page is fully loaded
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      setIsCheckingOut(false);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isCheckingOut]);

  return (
    <>
      <Drawer
        anchor="right"
        open={isOpen}
        onClose={closeCart}
        PaperProps={{
          sx: {
            width: { xs: '100%', sm: 400 },
            maxWidth: '100vw',
          },
        }}
      >
      <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        {/* Header */}
        <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Stack direction="row" alignItems="center" spacing={2}>
              <Avatar sx={{ bgcolor: 'primary.main' }}>
                <CartIcon />
              </Avatar>
              <Box>
                <Typography variant="h6" fontWeight="bold">
                  Shopping Cart
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {totals.totalItems} {totals.totalItems === 1 ? 'item' : 'items'}
                </Typography>
              </Box>
            </Stack>
            <IconButton onClick={closeCart} size="small">
              <CloseIcon />
            </IconButton>
          </Stack>
        </Box>

        {/* Cart Items */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
          {!cart || !cart.items || cart.items.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <Avatar
                sx={{
                  width: 64,
                  height: 64,
                  bgcolor: 'action.hover',
                  mx: 'auto',
                  mb: 2,
                }}
              >
                <EmptyCartIcon sx={{ fontSize: 32, color: 'text.secondary' }} />
              </Avatar>
              <Typography variant="h6" gutterBottom>
                Your cart is empty
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Add some products to get started
              </Typography>
            </Box>
          ) : (
            <List>
              {cart.items.map((item) => (
                <ListItem key={item.id} sx={{ px: 0, py: 1 }}>
                  <Card sx={{ width: '100%', mb: 1 }}>
                    <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                      <Stack direction="row" spacing={2} alignItems="flex-start">
                        <ListItemAvatar>
                          <Avatar
                            variant="rounded"
                            sx={{ width: 56, height: 56 }}
                            src={item.thumbnail}
                          >
                            <EmptyCartIcon />
                          </Avatar>
                        </ListItemAvatar>
                        
                        <Box sx={{ flex: 1, minWidth: 0 }}>
                          <Typography variant="body2" fontWeight="medium" noWrap>
                            {item.title}
                          </Typography>
                          
                          {item.variant && (
                            <Typography variant="caption" color="text.secondary">
                              {item.variant.title || 'Default Variant'}
                            </Typography>
                          )}

                          <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mt: 1 }}>
                            <Typography variant="body2" color="primary.main" fontWeight="bold">
                              {formatPrice(item.unit_price, 'INR')}
                            </Typography>

                            <ButtonGroup size="small" variant="outlined">
                              <IconButton
                                size="small"
                                onClick={() => updateQuantity(item.id, item.quantity - 1)}
                                disabled={isLoading}
                              >
                                <RemoveIcon fontSize="small" />
                              </IconButton>
                              
                              <Button disabled sx={{ minWidth: 40 }}>
                                {item.quantity}
                              </Button>
                              
                              <IconButton
                                size="small"
                                onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                disabled={isLoading}
                              >
                                <AddIcon fontSize="small" />
                              </IconButton>
                            </ButtonGroup>
                          </Stack>
                        </Box>

                        <IconButton
                          size="small"
                          onClick={() => removeItem(item.id)}
                          disabled={isLoading}
                          color="error"
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Stack>
                    </CardContent>
                  </Card>
                </ListItem>
              ))}
            </List>
          )}
        </Box>

        {/* Cart Summary & Checkout */}
        {cart && cart.items && cart.items.length > 0 && (
          <Box sx={{ borderTop: 1, borderColor: 'divider', p: 3 }}>
            <Stack spacing={2}>
              {/* Totals */}
              <Stack spacing={1}>
                <Stack direction="row" justifyContent="space-between">
                  <Typography variant="body2" color="text.secondary">
                    Subtotal:
                  </Typography>
                  <Typography variant="body2">
                    {formatPrice(totals.subtotal, 'INR')}
                  </Typography>
                </Stack>
                
                <Stack direction="row" justifyContent="space-between">
                  <Typography variant="body2" color="text.secondary">
                    Tax:
                  </Typography>
                  <Typography variant="body2">
                    {formatPrice(totals.tax, 'INR')}
                  </Typography>
                </Stack>
                
                <Stack direction="row" justifyContent="space-between">
                  <Typography variant="body2" color="text.secondary">
                    Shipping:
                  </Typography>
                  <Typography variant="body2">
                    {totals.shipping === 0 ? 'Free' : formatPrice(totals.shipping, 'INR')}
                  </Typography>
                </Stack>
                
                <Divider />
                
                <Stack direction="row" justifyContent="space-between">
                  <Typography variant="h6" fontWeight="bold">
                    Total:
                  </Typography>
                  <Typography variant="h6" fontWeight="bold" color="primary.main">
                    {formatPrice(totals.finalTotal, 'INR')}
                  </Typography>
                </Stack>
              </Stack>

              {/* Checkout Button */}
              <Button
                variant="contained"
                size="large"
                fullWidth
                disabled={isCheckingOut || isLoading}
                onClick={handleCheckout}
                startIcon={
                  isCheckingOut || isLoading ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : null
                }
                sx={{
                  position: 'relative',
                  '&.Mui-disabled': {
                    backgroundColor: 'primary.main',
                    color: 'primary.contrastText',
                    opacity: 0.8,
                  },
                }}
              >
                {isCheckingOut 
                  ? 'Redirecting to Checkout...' 
                  : isLoading 
                    ? 'Loading...' 
                    : `Checkout • ${formatPrice(totals.finalTotal, 'INR')}`
                }
              </Button>

              {/* Continue Shopping */}
              <Button
                variant="text"
                fullWidth
                onClick={closeCart}
                color="inherit"
              >
                Continue Shopping
              </Button>
            </Stack>
          </Box>
        )}
      </Box>
    </Drawer>
    
    {/* Checkout Loading Backdrop */}
    <Backdrop
      sx={{ 
        color: '#fff', 
        zIndex: (theme) => theme.zIndex.drawer + 1,
        flexDirection: 'column',
        gap: 2,
      }}
      open={isCheckingOut}
    >
      <CircularProgress color="inherit" size={60} />
      <Typography variant="h6" color="inherit">
        Redirecting to checkout...
      </Typography>
      <Typography variant="body2" color="inherit" sx={{ mt: 1, opacity: 0.8 }}>
        Please wait while we prepare your checkout
      </Typography>
    </Backdrop>
    </>
  );
};