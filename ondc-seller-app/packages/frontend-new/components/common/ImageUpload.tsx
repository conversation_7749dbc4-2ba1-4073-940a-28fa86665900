'use client';

import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { extractColorPalette, uploadImageToCloudinary, ColorPalette } from '@/lib/utils/colorPalette';
import { Button } from '@mui/material';
interface ImageUploadProps {
  onImageUpload: (imageUrl: string, colorPalette: ColorPalette | null) => void;
  onUrlSubmit: (imageUrl: string, colorPalette: ColorPalette | null) => void;
  currentImageUrl?: string;
  currentColorPalette?: ColorPalette | null;
}

export const ImageUpload: React.FC<ImageUploadProps> = ({
  onImageUpload,
  onUrlSubmit,
  currentImageUrl,
  currentColorPalette,
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [isExtractingColors, setIsExtractingColors] = useState(false);
  const [imageUrl, setImageUrl] = useState(currentImageUrl || '');
  const [uploadMode, setUploadMode] = useState<'file' | 'url'>('file');
  const [error, setError] = useState<string | null>(null);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    setIsUploading(true);
    setError(null);

    try {
      // Upload image
      const uploadedUrl = await uploadImageToCloudinary(file);
      if (!uploadedUrl) {
        throw new Error('Failed to upload image');
      }

      setImageUrl(uploadedUrl);
      setIsExtractingColors(true);

      // Extract color palette
      const colorPalette = await extractColorPalette(uploadedUrl);
      
      onImageUpload(uploadedUrl, colorPalette);
      
    } catch (error: any) {
      console.error('Error uploading image:', error);
      setError(error.message || 'Failed to upload image');
    } finally {
      setIsUploading(false);
      setIsExtractingColors(false);
    }
  }, [onImageUpload]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']
    },
    multiple: false,
    maxSize: 5 * 1024 * 1024, // 5MB
  });

  const handleUrlSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    if (!imageUrl.trim()) return;

    setIsExtractingColors(true);
    setError(null);

    try {
      console.log('=== EXTRACTING COLOR PALETTE FROM URL ===');
      console.log('Image URL:', imageUrl);
      
      // Extract color palette from URL
      const colorPalette = await extractColorPalette(imageUrl);
      
      console.log('Color palette extracted:', colorPalette);
      onUrlSubmit(imageUrl, colorPalette);
    } catch (error: any) {
      console.error('Error processing image URL:', error);
      setError(error.message || 'Failed to process image URL');
    } finally {
      setIsExtractingColors(false);
    }
  };

  const renderColorPalette = () => {
    if (!currentColorPalette) return null;

    return (
      <div className="p-4 bg-gray-50 rounded-lg pt-0">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Extracted Color Palette</h4>
        {/* <p className="text-xs text-gray-500 mb-3">
          Colors extracted using node-vibrant library.
        </p> */}
        <div className="grid grid-cols-3 gap-2">
          {[
            { label: 'Vibrant', color: currentColorPalette.vibrant },
            { label: 'Vibrant Light', color: currentColorPalette.vibrantLight },
            { label: 'Vibrant Dark', color: currentColorPalette.vibrantDark },
            { label: 'Muted', color: currentColorPalette.muted },
            { label: 'Muted Light', color: currentColorPalette.mutedLight },
            { label: 'Muted Dark', color: currentColorPalette.mutedDark },
          ].map(({ label, color }) => (
            <div key={label} className="flex items-center space-x-2">
              <div
                className="w-6 h-6 rounded border border-gray-300"
                style={{ backgroundColor: color }}
              />
              <div>
                <div className="text-xs font-medium text-gray-700">{label}</div>
                <div className="text-xs text-gray-500">{color}</div>
              </div>
            </div>
          ))}
        </div>
        {/* <div className="mt-3 text-xs text-gray-500">
          <div className="flex justify-between">
            <span>Total Population: {currentColorPalette.population}</span>
            <span>Extracted: {new Date(currentColorPalette.extractedAt).toLocaleString()}</span>
          </div>
        </div> */}
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* Upload Mode Toggle */}
      <div className="flex space-x-4">
      <Button
          type="button"
          onClick={() => setUploadMode('file')}
          // className={`px-4 py-2 rounded-md text-sm font-medium ${
          //   uploadMode === 'file'
          //     ? 'bg-blue-600 text-white'
          //     : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          // }`}
          variant="contained"
          size="large"
        >
          Upload File
        </Button>
        <Button
          type="button"
          onClick={() => setUploadMode('url')}
          className={`px-4 py-2 rounded-md text-sm font-medium ${
            uploadMode === 'url'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          Image URL
        </Button>
      </div>

      {/* File Upload */}
      {uploadMode === 'file'  && (
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
            isDragActive
              ? 'border-blue-400 bg-blue-50'
              : 'border-gray-300 hover:border-gray-400'
          }`}
        >
          <input {...getInputProps()} />
          {isUploading ? (
            <div className="space-y-2">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-sm text-gray-600">Uploading image...</p>
            </div>
          ) : isExtractingColors ? (
            <div className="space-y-2">
              <div className="animate-pulse h-8 w-8 bg-blue-200 rounded-full mx-auto"></div>
              <p className="text-sm text-gray-600">Extracting color palette...</p>
            </div>
          ) : (
            <div className="space-y-2">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                stroke="currentColor"
                fill="none"
                viewBox="0 0 48 48"
              >
                <path
                  d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                  strokeWidth={2}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <div>
                <p className="text-sm text-gray-600">
                  {isDragActive ? 'Drop the image here' : 'Drag & drop an image here, or click to select'}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  PNG, JPG, GIF up to 5MB
                </p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* URL Input */}
      {uploadMode === 'url' && (
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Image URL
            </label>
            <input
              type="url"
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
              placeholder="https://example.com/image.jpg"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <div className="mt-2 space-x-2">
              <button
                type="button"
                onClick={() => setImageUrl('https://images.unsplash.com/photo-1603967788945-b2215de1d7b7?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D')}
                className="text-xs text-blue-600 hover:text-blue-800 underline"
              >
                Use test image
              </button>
              <button
                type="button"
                onClick={() => setImageUrl('https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400')}
                className="text-xs text-gray-600 hover:text-gray-800 underline"
              >
                Alternative test
              </button>
            </div>
          </div>
          <button
            type="button"
            onClick={handleUrlSubmit}
            disabled={isExtractingColors || !imageUrl.trim()}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isExtractingColors ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Extracting Colors...</span>
              </div>
            ) : (
              'Extract Color Palette'
            )}
          </button>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Current Image Preview */}
      {currentImageUrl && (
        <div className="space-y-3 flex">
          <div className="relative">
            <img
              src={currentImageUrl}
              alt="Store logo preview"
              className="w-32 h-32 object-cover rounded-lg border border-gray-300"
            />
            <div className="absolute top-2 left-0">
              <button
                type="button"
                onClick={() => {
                  setImageUrl('');
                  onImageUpload('', null);
                }}
                className="bg-red-500 text-white rounded-full p-1 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
          <div>
          {/* Color Palette Display */}
          {renderColorPalette()}
          </div>
        </div>
      )}
    </div>
  );
};