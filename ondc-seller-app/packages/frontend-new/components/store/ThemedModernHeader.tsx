'use client';

import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Store } from '@/types';
import { useStoreTheme } from '@/hooks/useStoreTheme';
import { cartAPI, Cart, getStoredCartId } from '@/lib/api/cart';
import { useAuthContext } from '../auth/AuthProvider';
import { UserMenu } from '../auth/UserMenu';
import { AuthModal } from '../auth/AuthModal';
import { useStoreConfigStore } from '@/stores/storeConfigStore';

interface ThemedModernHeaderProps {
  store: Store;
  storeHandle: string;
}

export const ThemedModernHeader: React.FC<ThemedModernHeaderProps> = ({ store, storeHandle }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authModalMode, setAuthModalMode] = useState<'login' | 'signup'>('login');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [cart, setCart] = useState<Cart | null>(null);
  const { isAuthenticated, user } = useAuthContext();
  
  // Calculate total items for API cart
  const totalItems = cart && cart.items ? cart.items.reduce((sum, item) => sum + item.quantity, 0) : 0;
  
  // Toggle cart function
  const toggleCart = () => {
    window.dispatchEvent(new CustomEvent('openCart'));
  };
  
  // Load cart data
  const loadCart = async () => {
    const cartId = getStoredCartId();
    if (!cartId) return;
    
    try {
      const cartResponse = await cartAPI.getCart(cartId, storeHandle);
      setCart(cartResponse.cart);
    } catch (error) {
      console.error('Failed to load cart:', error);
    }
  };
  
  // Listen for cart updates
  useEffect(() => {
    const handleCartUpdate = () => {
      loadCart();
    };

    window.addEventListener('cartUpdated', handleCartUpdate);
    loadCart(); // Load on mount

    return () => {
      window.removeEventListener('cartUpdated', handleCartUpdate);
    };
  }, [storeHandle]);
  const defaultLogo = '/images/default-store-logo.png';
  
  // Get dynamic store data from Zustand
  const { storeData } = useStoreConfigStore(storeHandle);
  
  // Use dynamic data if available, fallback to props
  const displayName = storeData?.name || store.name || storeHandle;
  const displayLogo = storeData?.logo || store.logo || defaultLogo;

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Navigate to products listing page with search
      window.location.href = `/${storeHandle}/products?q=${encodeURIComponent(searchQuery)}`;
    }
  };

  return (
    <div>
      <header 
        className="shadow-sm border-b sticky top-0 z-50 themed-header"
        style={{ 
          backgroundColor: 'var(--header-bg)',
          borderBottomColor: 'var(--header-border)',
        }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-14 sm:h-16 lg:h-20">
            {/* Logo and Store Name */}
            <div className="flex items-center space-x-2 sm:space-x-4 flex-shrink-0">
              <Link href={`/${storeHandle}`} className="flex items-center space-x-2 sm:space-x-3 group">
                <div 
                  className="relative w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 rounded-full overflow-hidden border-2 transition-all duration-200"
                  style={{ borderColor: 'var(--theme-border)' }}
                >
                  <Image
                    src={displayLogo}
                    alt={`${displayName} logo`}
                    fill
                    className="object-cover"
                    sizes="(max-width: 640px) 32px, (max-width: 1024px) 40px, 48px"
                  />
                </div>
                <div className="hidden sm:block">
                  <h1 
                    className="text-sm sm:text-lg lg:text-xl font-bold transition-colors group-hover:opacity-80 truncate max-w-32 sm:max-w-48 lg:max-w-none"
                    style={{ color: 'var(--header-text)' }}
                  >
                    {displayName}
                  </h1>
                </div>
              </Link>
            </div>

            {/* Search Bar - Hidden on mobile, shown in mobile menu */}
            <div className="hidden md:flex flex-1 max-w-xl mx-4 lg:mx-8">
              <form onSubmit={handleSearch} className="w-full relative">
                <div className={`relative transition-all duration-200 ${
                  isSearchFocused ? 'transform scale-105' : ''
                }`}>
                  <input
                    type="text"
                    placeholder="Search products..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onFocus={() => setIsSearchFocused(true)}
                    onBlur={() => setIsSearchFocused(false)}
                    className="bg-white w-full pl-10 pr-4 py-2 lg:py-3 border rounded-full focus:ring-2 focus:border-transparent transition-all duration-200 themed-search-input text-sm lg:text-base"
                    style={{
                      // backgroundColor: isSearchFocused ? 'var(--theme-surface)' : 'var(--header-search-bg)',
                      borderColor: isSearchFocused ? 'var(--header-search-border)' : 'var(--theme-border)',
                      color: 'var(--theme-text)',
                      '--tw-ring-color': 'var(--theme-primary)',
                    } as React.CSSProperties}
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg 
                      className="w-4 h-4 lg:w-5 lg:h-5" 
                      fill="none" 
                      stroke="var(--theme-text-secondary)" 
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  {searchQuery && (
                    <button
                      type="button"
                      onClick={() => setSearchQuery('')}
                      className="absolute inset-y-0 right-10 flex items-center pr-2"
                    >
                      <svg 
                        className="w-4 h-4 hover:opacity-80 transition-opacity" 
                        fill="none" 
                        stroke="var(--theme-text-secondary)" 
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  )}
                  <button
                    type="submit"
                    className="absolute inset-y-0 right-0 flex items-center pr-3"
                  >
                    <div 
                      className="p-1.5 lg:p-2 rounded-full transition-all duration-200 hover:shadow-lg"
                      style={{ 
                        backgroundColor: 'var(--btn-primary)',
                        color: 'var(--btn-text)',
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--btn-primary-hover)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--btn-primary)';
                      }}
                    >
                      <svg className="w-3 h-3 lg:w-4 lg:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </div>
                  </button>
                </div>
              </form>
            </div>

            {/* Desktop Actions */}
            <div className="hidden md:flex items-center space-x-2 lg:space-x-4 flex-shrink-0">
              {/* Cart */}
              <button 
                onClick={toggleCart}
                className="relative group"
              >
                <div 
                  className="flex items-center space-x-2 rounded-full px-2 lg:px-3 py-2 transition-all duration-200 hover:shadow-sm"
                  style={{ backgroundColor: 'var(--theme-background)' }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--theme-hover)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--theme-background)';
                  }}
                >
                  <div className="relative">
                    <svg 
                      className="w-5 h-5 lg:w-6 lg:h-6 transition-colors" 
                      fill="none" 
                      stroke="var(--theme-text)" 
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                    </svg>
                    {totalItems > 0 && (
                      <span 
                        className="absolute -top-2 -right-2 text-white text-xs rounded-full w-4 h-4 lg:w-5 lg:h-5 flex items-center justify-center font-medium animate-pulse"
                        style={{ backgroundColor: 'var(--theme-accent)' }}
                      >
                        {totalItems > 99 ? '99+' : totalItems}
                      </span>
                    )}
                  </div>
                  <span 
                    className="hidden lg:block text-sm font-medium transition-colors"
                    style={{ color: 'var(--theme-text)' }}
                  >
                    Cart
                  </span>
                </div>
              </button>

              {/* Profile / Auth */}
              {isAuthenticated ? (
                <UserMenu user={user} storeHandle={storeHandle} />
              ) : (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => {
                      setAuthModalMode('login');
                      setIsAuthModalOpen(true);
                    }}
                    className="px-3 lg:px-4 py-2 text-sm font-medium transition-colors hover:opacity-80"
                    style={{ color: 'var(--theme-text)' }}
                  >
                    Login
                  </button>
                  <button
                    onClick={() => {
                      setAuthModalMode('signup');
                      setIsAuthModalOpen(true);
                    }}
                    className="px-3 lg:px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 hover:shadow-sm"
                    style={{ 
                      backgroundColor: 'var(--btn-primary)',
                      color: 'var(--btn-text)',
                    }}
                  >
                    Sign Up
                  </button>
                </div>
              )}
            </div>

            {/* Mobile Actions */}
            <div className="flex md:hidden items-center space-x-2">
              {/* Cart */}
              <button 
                onClick={toggleCart}
                className="relative p-2"
              >
                <svg 
                  className="w-6 h-6" 
                  fill="none" 
                  stroke="var(--theme-text)" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                </svg>
                {totalItems > 0 && (
                  <span 
                    className="absolute -top-1 -right-1 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium"
                    style={{ backgroundColor: 'var(--theme-accent)' }}
                  >
                    {totalItems > 9 ? '9+' : totalItems}
                  </span>
                )}
              </button>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="p-2"
                aria-label="Toggle menu"
              >
                <svg 
                  className="w-6 h-6" 
                  fill="none" 
                  stroke="var(--theme-text)" 
                  viewBox="0 0 24 24"
                >
                  {isMobileMenuOpen ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  )}
                </svg>
              </button>
            </div>
          </div>

          {/* Mobile Menu */}
          {isMobileMenuOpen && (
            <div className="md:hidden border-t" style={{ borderTopColor: 'var(--header-border)' }}>
              <div className="px-2 pt-2 pb-3 space-y-3">
                {/* Mobile Search */}
                <form onSubmit={handleSearch} className="relative">
                  <input
                    type="text"
                    placeholder="Search products..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="bg-white w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-all duration-200 text-sm"
                    style={{
                      // backgroundColor: 'var(--theme-surface)',
                      borderColor: 'var(--theme-border)',
                      color: 'var(--theme-text)',
                      '--tw-ring-color': 'var(--theme-primary)',
                    } as React.CSSProperties}
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg 
                      className="w-5 h-5" 
                      fill="none" 
                      stroke="var(--theme-text-secondary)" 
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                </form>

                {/* Mobile Auth */}
                {!isAuthenticated ? (
                  <div className="flex space-x-2">
                    <button
                      onClick={() => {
                        setAuthModalMode('login');
                        setIsAuthModalOpen(true);
                        setIsMobileMenuOpen(false);
                      }}
                      className="flex-1 px-4 py-2 text-sm font-medium border rounded-lg transition-colors"
                      style={{ 
                        borderColor: 'var(--theme-border)',
                        color: 'var(--theme-text)',
                      }}
                    >
                      Login
                    </button>
                    <button
                      onClick={() => {
                        setAuthModalMode('signup');
                        setIsAuthModalOpen(true);
                        setIsMobileMenuOpen(false);
                      }}
                      className="flex-1 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200"
                      style={{ 
                        backgroundColor: 'var(--btn-primary)',
                        color: 'var(--btn-text)',
                      }}
                    >
                      Sign Up
                    </button>
                  </div>
                ) : (
                  <div className="py-2">
                    <UserMenu user={user} storeHandle={storeHandle} />
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </header>
      
      {/* Auth Modal */}
      <AuthModal 
        isOpen={isAuthModalOpen}
        mode={authModalMode}
        onClose={() => setIsAuthModalOpen(false)}
        onSwitchMode={(mode) => setAuthModalMode(mode)}
        storeHandle={storeHandle}
      />
    </div>
  );
};