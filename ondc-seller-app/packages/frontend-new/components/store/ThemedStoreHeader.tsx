'use client';

import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import { ActionButton } from '../ui/ActionButton';
import { Store } from '@/types';
import { useStoreTheme } from '@/hooks/useStoreTheme';
import { getStoreCartAPI, Cart } from '@/lib/api/cart';
import { useStoreAuthContext } from '../auth/StoreAuthProvider';
import { UserMenu } from '../auth/UserMenu';
import { StoreAuthModal } from '../auth/StoreAuthModal';
import { UserMenuDropdown } from '../auth/UserMenuDropdown';
import { useStoreConfigStore } from '@/stores/storeConfigStore';
import { useStoreAuthStore } from '@/stores/storeAuthStore';
import { useToast } from '@/app/providers/toast-provider';
import { SearchInput } from '../search/SearchInput';

interface ThemedStoreHeaderProps {
  store: Store;
  storeHandle: string;
}

export const ThemedStoreHeader: React.FC<ThemedStoreHeaderProps> = ({ store, storeHandle }) => {

  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authModalMode, setAuthModalMode] = useState<'login' | 'signup'>('login');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [cart, setCart] = useState<Cart | null>(null);
  const { isAuthenticated, user } = useStoreAuthContext();
  const { showToast } = useToast();
  const pathname = usePathname();
  
  // Check if we're on checkout page
  const isCheckoutPage = pathname?.includes('/checkout');
  
  // Calculate total items for API cart
  const totalItems = cart && cart.items ? cart.items.reduce((sum, item) => sum + item.quantity, 0) : 0;
  
  // Toggle cart function
  const toggleCart = () => {
    // Don't open cart on checkout page
    if (isCheckoutPage) {
      console.log('🚫 Cart disabled on checkout page');
      return;
    }
    window.dispatchEvent(new CustomEvent('openCart'));
  };
  
  // Load cart data
  const loadCart = async () => {
    const cartId = cartAPI.getStoredCartId();
    if (!cartId) return;
    
    try {
      console.log(`🔄 Loading cart for store ${storeHandle}:`, cartId);
      const cartResponse = await cartAPI.getCart(cartId);
      setCart(cartResponse.cart);
      console.log(`✅ Loaded cart for store ${storeHandle}:`, cartResponse.cart.items.length, 'items');
    } catch (error) {
      console.error(`Failed to load cart for store ${storeHandle}:`, error);
    }
  };
  
  // Listen for cart updates
  useEffect(() => {
    const handleCartUpdate = () => {
      loadCart();
    };

    window.addEventListener('cartUpdated', handleCartUpdate);
    loadCart(); // Load on mount

    return () => {
      window.removeEventListener('cartUpdated', handleCartUpdate);
    };
  }, [storeHandle]);
  const defaultLogo = '/images/default-store-logo.png';
  
  // Get dynamic store data from Zustand (store-specific)
  const { storeData } = useStoreConfigStore(storeHandle);
  const { clearAuth } = useStoreAuthStore(storeHandle);
  
  // Get store-specific cart API
  const cartAPI = getStoreCartAPI(storeHandle);
  
  
  // Use dynamic data if available, fallback to props
  const displayName = storeData?.name || store.name || storeHandle;
  const displayLogo = storeData?.logo || store.logo || defaultLogo;



  return (
    <div>
      <header 
        className="shadow-sm border-b sticky top-0 z-50 themed-header"
        style={{ 
          backgroundColor: 'var(--header-bg)',
          borderBottomColor: 'var(--header-border)',
        }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-14 sm:h-16 lg:h-20">
            {/* Logo and Store Name */}
            <div className="flex items-center space-x-2 sm:space-x-4 flex-shrink-0">
              <ActionButton
                href={`/${storeHandle}`}
                variant="ghost"
                className="flex items-center space-x-2 sm:space-x-3 group p-0 hover:bg-transparent"
                loadingType="navigation"
                actionId="header-home-nav"
              >
                <div 
                  className="relative w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 rounded-full overflow-hidden border-2 transition-all duration-200"
                  style={{ borderColor: 'var(--theme-border)' }}
                >
                  <Image
                    src={displayLogo}
                    alt={`${displayName} logo`}
                    fill
                    className="object-cover"
                    sizes="(max-width: 640px) 32px, (max-width: 1024px) 40px, 48px"
                  />
                </div>
                <div className="hidden sm:block">
                  <h1 
                    className="text-sm sm:text-lg lg:text-xl font-bold transition-colors group-hover:opacity-80 truncate max-w-32 sm:max-w-48 lg:max-w-none"
                    style={{ color: 'var(--header-text)' }}
                  >
                    {displayName}
                  </h1>
                </div>
              </ActionButton>
            </div>

            {/* Search Bar - Hidden on mobile, shown in mobile menu */}
            <div className="hidden md:flex flex-1 max-w-xl mx-4 lg:mx-8">
              <SearchInput
                storeHandle={storeHandle}
                placeholder="Search products..."
                variant="outlined"
                size="small"
                fullWidth
                showSuggestions
                className="w-full"
              />
            </div>

            {/* Desktop Actions */}
            <div className="hidden md:flex items-center space-x-2 lg:space-x-4 flex-shrink-0">
              {/* Cart */}
              <button 
                onClick={toggleCart}
                className={`relative group ${isCheckoutPage ? 'opacity-50 cursor-not-allowed' : ''}`}
                disabled={isCheckoutPage}
                title={isCheckoutPage ? 'Cart is disabled during checkout' : 'View cart'}
              >
                <div 
                  className="flex items-center space-x-2 rounded-full px-2 lg:px-3 py-2 transition-all duration-200 hover:shadow-sm"
                  style={{ backgroundColor: 'var(--theme-background)' }}
                  onMouseEnter={(e) => {
                    if (!isCheckoutPage) {
                      e.currentTarget.style.backgroundColor = 'var(--theme-hover)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isCheckoutPage) {
                      e.currentTarget.style.backgroundColor = 'var(--theme-background)';
                    }
                  }}
                >
                  <div className="relative">
                    <svg 
                      className="w-5 h-5 lg:w-6 lg:h-6 transition-colors" 
                      fill="none" 
                      stroke="var(--theme-text)" 
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                    </svg>
                    {totalItems > 0 && (
                      <span 
                        className="absolute -top-2 -right-2 text-white text-xs rounded-full w-4 h-4 lg:w-5 lg:h-5 flex items-center justify-center font-medium animate-pulse"
                        style={{ backgroundColor: 'var(--theme-accent)' }}
                      >
                        {totalItems > 99 ? '99+' : totalItems}
                      </span>
                    )}
                  </div>
                  <span 
                    className="hidden lg:block text-sm font-medium transition-colors"
                    style={{ color: 'var(--theme-text)' }}
                  >
                    Cart
                  </span>
                </div>
              </button>

              {/* Profile / Auth */}
              {isAuthenticated ? (
                <UserMenuDropdown storeHandle={storeHandle} />
              ) : (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => {
                      setAuthModalMode('login');
                      setIsAuthModalOpen(true);
                    }}
                    className="px-3 lg:px-4 py-2 text-sm font-medium transition-colors hover:opacity-80"
                    style={{ color: 'var(--theme-text)' }}
                  >
                    Login
                  </button>
                  <button
                    onClick={() => {
                      setAuthModalMode('signup');
                      setIsAuthModalOpen(true);
                    }}
                    className="px-3 lg:px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 hover:shadow-sm"
                    style={{ 
                      backgroundColor: 'var(--btn-primary)',
                      color: 'var(--btn-text)',
                    }}
                  >
                    Sign Up
                  </button>
                </div>
              )}
            </div>

            {/* Mobile Actions */}
            <div className="flex md:hidden items-center space-x-2">
              {/* Cart */}
              <button 
                onClick={toggleCart}
                className={`relative p-2 ${isCheckoutPage ? 'opacity-50 cursor-not-allowed' : ''}`}
                disabled={isCheckoutPage}
                title={isCheckoutPage ? 'Cart is disabled during checkout' : 'View cart'}
              >
                <svg 
                  className="w-6 h-6" 
                  fill="none" 
                  stroke="var(--theme-text)" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                </svg>
                {totalItems > 0 && (
                  <span 
                    className="absolute -top-1 -right-1 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium"
                    style={{ backgroundColor: 'var(--theme-accent)' }}
                  >
                    {totalItems > 9 ? '9+' : totalItems}
                  </span>
                )}
              </button>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="p-2"
                aria-label="Toggle menu"
              >
                <svg 
                  className="w-6 h-6" 
                  fill="none" 
                  stroke="var(--theme-text)" 
                  viewBox="0 0 24 24"
                >
                  {isMobileMenuOpen ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  )}
                </svg>
              </button>
            </div>
          </div>

          {/* Mobile Menu */}
          {isMobileMenuOpen && (
            <div className="md:hidden border-t" style={{ borderTopColor: 'var(--header-border)' }}>
              <div className="px-2 pt-2 pb-3 space-y-3">
                {/* Mobile Search */}
                <SearchInput
                  storeHandle={storeHandle}
                  placeholder="Search products..."
                  variant="outlined"
                  size="medium"
                  fullWidth
                  showSuggestions
                  className="w-full"
                />

                {/* Mobile Auth */}
                {!isAuthenticated ? (
                  <div className="flex space-x-2">
                    <button
                      onClick={() => {
                        setAuthModalMode('login');
                        setIsAuthModalOpen(true);
                        setIsMobileMenuOpen(false);
                      }}
                      className="flex-1 px-4 py-2 text-sm font-medium border rounded-lg transition-colors"
                      style={{ 
                        borderColor: 'var(--theme-border)',
                        color: 'var(--theme-text)',
                      }}
                    >
                      Login
                    </button>
                    <button
                      onClick={() => {
                        setAuthModalMode('signup');
                        setIsAuthModalOpen(true);
                        setIsMobileMenuOpen(false);
                      }}
                      className="flex-1 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200"
                      style={{ 
                        backgroundColor: 'var(--btn-primary)',
                        color: 'var(--btn-text)',
                      }}
                    >
                      Sign Up
                    </button>
                  </div>
                ) : (
                  <div className="py-2">
                    <UserMenuDropdown storeHandle={storeHandle} />
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </header>
      
      {/* Store Auth Modal */}
      <StoreAuthModal 
        isOpen={isAuthModalOpen}
        mode={authModalMode}
        onClose={() => setIsAuthModalOpen(false)}
        onSwitchMode={(mode) => setAuthModalMode(mode)}
        storeHandle={storeHandle}
      />
    </div>
  );
};