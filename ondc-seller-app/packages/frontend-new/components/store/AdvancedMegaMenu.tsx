'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { ProductCategory } from '@/types';

interface AdvancedMegaMenuProps {
  categories: ProductCategory[];
  storeHandle: string;
}

export const AdvancedMegaMenu: React.FC<AdvancedMegaMenuProps> = ({ 
  categories, 
  storeHandle 
}) => {
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [clickedCategory, setClickedCategory] = useState<string | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleMouseEnter = (categoryId: string) => {
    if (!isMobile) {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      setHoveredCategory(categoryId);
    }
  };

  const handleMouseLeave = () => {
    if (!isMobile) {
      timeoutRef.current = setTimeout(() => {
        setHoveredCategory(null);
      }, 150); // Small delay to prevent flickering
    }
  };

  const handleCategoryClick = (categoryId: string, hasSubcategories: boolean) => {
    if (isMobile && hasSubcategories) {
      setClickedCategory(clickedCategory === categoryId ? null : categoryId);
    } else {
      window.location.href = `/${storeHandle}/categories/${categories.find(c => c.id === categoryId)?.handle}`;
    }
  };

  const getColumnCount = (subcategoriesCount: number) => {
    if (subcategoriesCount <= 6) return 1;
    if (subcategoriesCount <= 12) return 2;
    return 3;
  };

  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <div className="px-4 sm:px-6 lg:px-8 py-4">
      
        {/* Categories Container */}
        <div className="relative">
          {/* Main Categories Navigation */}
          <div className="bg-gray-50 rounded-xl border border-gray-200 overflow-hidden">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 divide-x divide-gray-100">
            {categories.map((category) => {
              const hasSubcategories = category.category_children && category.category_children.length > 0;
              const isActive = hoveredCategory === category.id || clickedCategory === category.id;
              
              return (
                <div
                  key={category.id}
                  className="relative"
                  onMouseEnter={() => handleMouseEnter(category.id)}
                  onMouseLeave={handleMouseLeave}
                >
                  {/* Category Item */}
                  <div
                    className={`group p-4 text-center cursor-pointer transition-all duration-300 ${
                      isActive ? 'bg-blue-50 text-blue-600' : 'hover:bg-gray-50'
                    }`}
                    onClick={() => handleCategoryClick(category.id, hasSubcategories)}
                  >
                    {/* Category Icon */}
                    <div className={`w-10 h-10 mx-auto mb-2 rounded-full flex items-center justify-center transition-all duration-300 ${
                      isActive 
                        ? 'bg-blue-200 text-blue-700' 
                        : 'bg-gray-100 text-gray-600 group-hover:bg-blue-100 group-hover:text-blue-600'
                    }`}>
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                      </svg>
                    </div>
                    
                    {/* Category Name */}
                    <h3 className={`font-medium text-xs lg:text-sm transition-colors ${
                      isActive ? 'text-blue-600' : 'text-gray-900 group-hover:text-blue-600'
                    }`}>
                      {category.name}
                    </h3>
                    
                    {/* Subcategory Indicator */}
                    {hasSubcategories && (
                      <div className="mt-1 flex items-center justify-center">
                        <span className="text-xs text-gray-500">{category.category_children.length}</span>
                        <svg className={`w-3 h-3 ml-1 transition-transform ${
                          isActive ? 'rotate-180' : ''
                        }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    )}
                  </div>

                  {/* Mega Menu Dropdown */}
                  {isActive && hasSubcategories && (
                    <div className={`absolute top-full left-0 z-50 ${isMobile ? 'relative' : 'w-screen max-w-4xl'}`}
                         style={!isMobile ? { left: '50%', transform: 'translateX(-50%)' } : {}}>
                      <div className="bg-white rounded-xl shadow-2xl border border-gray-200 mt-2 overflow-hidden animate-fadeIn">
                        {/* Mega Menu Header */}
                        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-100">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                </svg>
                              </div>
                              <div>
                                <h4 className="font-semibold text-gray-900">{category.name}</h4>
                                <p className="text-sm text-gray-600">{category.description}</p>
                              </div>
                            </div>
                            <Link
                              href={`/${storeHandle}/categories/${category.handle}`}
                              className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                            >
                              View All →
                            </Link>
                          </div>
                        </div>

                        {/* Subcategories Grid */}
                        <div className="p-6">
                          <div className={`grid gap-4 ${
                            getColumnCount(category.category_children.length) === 1 ? 'grid-cols-1' :
                            getColumnCount(category.category_children.length) === 2 ? 'grid-cols-1 md:grid-cols-2' :
                            'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
                          }`}>
                            {category.category_children.map((subcategory) => (
                              <Link
                                key={subcategory.id}
                                href={`/${storeHandle}/subcategories/${subcategory.handle}`}
                                className="group flex items-start space-x-3 p-3 rounded-lg hover:bg-blue-50 transition-colors"
                              >
                                <div className="w-3 h-3 bg-blue-400 rounded-full mt-1 group-hover:bg-blue-600 transition-colors flex-shrink-0" />
                                <div className="flex-1 min-w-0">
                                  <h5 className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                                    {subcategory.name}
                                  </h5>
                                  {subcategory.description && (
                                    <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                                      {subcategory.description}
                                    </p>
                                  )}
                                </div>
                                <svg className="w-4 h-4 text-gray-400 group-hover:text-blue-600 transition-colors flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                              </Link>
                            ))}
                          </div>
                        </div>

                        {/* Featured Products Preview (Optional) */}
                        <div className="bg-gray-50 px-6 py-4 border-t border-gray-100">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">
                              Explore {category.category_children.length} subcategories
                            </span>
                            <Link
                              href={`/${storeHandle}/categories/${category.handle}`}
                              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                            >
                              Browse All
                            </Link>
                          </div>
                        </div>
                      </div>
                      
                      {/* Arrow pointing up (desktop only) */}
                      {!isMobile && (
                        <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                          <div className="w-4 h-4 bg-white border-l border-t border-gray-200 transform rotate-45"></div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* View All Categories - Compact */}
        <div className="text-center mt-4">
          <Link
            href={`/${storeHandle}/categories`}
            className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors"
          >
            <span>View All</span>
            <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        </div>
      </div>
    </div>
  );
};