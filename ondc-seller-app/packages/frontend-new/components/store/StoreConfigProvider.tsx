'use client';

import React, { useEffect, useRef } from 'react';
import { useStoreConfigStore } from '@/stores/storeConfigStore';
import { useStoreThemeWithAutoLoad } from '@/hooks/useStoreThemeWithAutoLoad';
import { ThemeAutoInitializer } from '@/components/theme/ThemeAutoInitializer';
import { clearAllThemeVariables, debugThemeState } from '@/lib/utils/themeUtils';

interface StoreConfigProviderProps {
  children: React.ReactNode;
  storeHandle: string;
}

export const StoreConfigProvider: React.FC<StoreConfigProviderProps> = ({
  children,
  storeHandle,
}) => {
  const { 
    fetchStoreConfig, 
    storeData, 
    isLoading, 
    error, 
    currentStoreHandle,
    isStoreCacheValid 
  } = useStoreConfigStore(storeHandle);
  
  // Use the new auto-theme loading system
  const {
    currentTheme,
    autoLoader,
    isAnyLoading: isThemeLoading,
    combinedError: themeError
  } = useStoreThemeWithAutoLoad(storeHandle);
  
  const fetchingRef = useRef<string | null>(null); // Track which store handle is being fetched
  const previousStoreHandleRef = useRef<string | null>(null); // Track previous store handle for cleanup
  const lastThemeAppliedRef = useRef<string | null>(null); // Track last applied theme to prevent unnecessary refreshes

  // Auto-fetch store config when component mounts or store handle changes
  useEffect(() => {
    if (storeHandle) {
      // Check if store handle has changed or if we need fresh data
      const storeHandleChanged = currentStoreHandle !== storeHandle;
      const needsFreshData = !isStoreCacheValid(storeHandle);
      const notCurrentlyFetching = fetchingRef.current !== storeHandle;
      
      const shouldFetch = (storeHandleChanged || needsFreshData) && !isLoading && notCurrentlyFetching;
      
      if (shouldFetch) {
        console.log('StoreConfigProvider: Store handle changed or needs fresh data');
        console.log('Previous handle:', currentStoreHandle);
        console.log('New handle:', storeHandle);
        console.log('Store handle changed:', storeHandleChanged);
        console.log('Needs fresh data:', needsFreshData);
        
        fetchingRef.current = storeHandle;
        
        fetchStoreConfig(storeHandle)
          .then(() => {
            fetchingRef.current = null;
            console.log('StoreConfigProvider: Store config loaded for', storeHandle);
          })
          .catch((error) => {
            console.error('Failed to fetch store config in provider:', error);
            fetchingRef.current = null;
            // Don't throw the error, let the component handle it gracefully
          });
      }
    }
  }, [storeHandle, currentStoreHandle, isLoading]);

  // Cleanup effect when store handle changes
  useEffect(() => {
    // If store handle has changed, clear theme variables immediately
    if (previousStoreHandleRef.current && previousStoreHandleRef.current !== storeHandle) {
      console.log('=== STORE HANDLE CHANGED - IMMEDIATE THEME CLEANUP ===');
      console.log('Previous:', previousStoreHandleRef.current);
      console.log('New:', storeHandle);
      
      if (typeof document !== 'undefined') {
        clearAllThemeVariables();
        console.log('Cleared theme variables for store handle change');
      }
      
      // Reset last applied theme reference
      lastThemeAppliedRef.current = null;
    }
    
    // Update the previous store handle reference
    previousStoreHandleRef.current = storeHandle;
  }, [storeHandle]);

  // Log theme status for debugging
  useEffect(() => {
    console.log('=== STORE CONFIG PROVIDER THEME STATUS ===');
    console.log('Store handle:', storeHandle);
    console.log('Current theme:', currentTheme);
    console.log('Auto loader status:', autoLoader);
    console.log('Theme loading:', isThemeLoading);
    console.log('Theme error:', themeError);
    
    // Debug current state
    debugThemeState(storeHandle, storeData);
  }, [storeHandle, currentTheme, autoLoader, isThemeLoading, themeError, storeData]);

  // Update document title if store name is available
  useEffect(() => {
    if (storeData?.name && typeof document !== 'undefined') {
      const originalTitle = document.title;
      document.title = `${storeData.name} - Online Store`;
      
      return () => {
        document.title = originalTitle;
      };
    }
  }, [storeData?.name]);

  // Auto-retry theme loading if it fails and store data becomes available
  useEffect(() => {
    if (storeData && storeData.logo && !currentTheme && !isThemeLoading && themeError) {
      console.log('🔄 Store data available but theme failed - retrying auto-load');
      autoLoader.retryAutoLoad();
    }
  }, [storeData, currentTheme, isThemeLoading, themeError, autoLoader]);

  return (
    <>
      {/* Auto-initialize theme for this store */}
      <ThemeAutoInitializer storeHandle={storeHandle} />
      {children}
    </>
  );
};