'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { ProductCategory } from '@/types';

interface ThemedHeaderMegaMenuProps {
  categories: ProductCategory[];
  storeHandle: string;
}

export const ThemedHeaderMegaMenu: React.FC<ThemedHeaderMegaMenuProps> = ({ 
  categories, 
  storeHandle 
}) => {
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  


  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleMouseEnter = (categoryId: string) => {
    if (!isMobile) {
      setHoveredCategory(categoryId);
    }
  };

  const handleMouseLeave = () => {
    if (!isMobile) {
      setHoveredCategory(null);
    }
  };

  if (!categories || categories.length === 0) {
    console.log('🌭 MEGA MENU: Returning null - no categories');
    return null;
  }
  

  return (
    <div className="w-full themed-nav-container bg-white" style={{ 
      // backgroundColor: 'var(--nav-bg)', 
      borderBottomColor: 'var(--nav-border)' 
    }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-4">
          {/* Categories List */}
          <div className="flex items-center space-x-2">
            {categories.map((category) => {
              const hasSubcategories = category.category_children && category.category_children.length > 0;
              const isHovered = hoveredCategory === category.id;
              
              return (
                <div
                  key={category.id}
                  className="relative"
                  onMouseEnter={() => handleMouseEnter(category.id)}
                  onMouseLeave={handleMouseLeave}
                >
                  {/* Category Button */}
                  <button
                    className="themed-category-button flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200"
                    style={{
                      color: isHovered ? 'var(--nav-active)' : 'var(--nav-text)',
                      backgroundColor: isHovered ? 'var(--nav-hover)' : 'transparent',
                      boxShadow: isHovered ? 'var(--card-shadow)' : 'none',
                    }}
                  >
                    <span>{category.name}</span>
                    {hasSubcategories && (
                      <div className="flex items-center space-x-1">
                        <span 
                          className="text-xs px-2 py-1 rounded-full transition-all duration-200"
                          style={{
                            backgroundColor: isHovered ? 'var(--btn-primary)' : 'var(--theme-border)',
                            color: isHovered ? 'var(--btn-text)' : 'var(--theme-text-secondary)',
                          }}
                        >
                          {category.category_children.length}
                        </span>
                        <svg 
                          className={`w-4 h-4 transition-transform duration-200 ${
                            isHovered ? 'rotate-180' : ''
                          }`} 
                          fill="none" 
                          stroke="currentColor" 
                          viewBox="0 0 24 24"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    )}
                  </button>

                  {/* Themed Dropdown Menu */}
                  {isHovered && hasSubcategories && (
                    <div 
                      className="absolute top-full left-0 w-80 rounded-xl border z-[9999] themed-dropdown"
                      style={{ 
                        backgroundColor: 'var(--dropdown-bg)',
                        borderColor: 'var(--dropdown-border)',
                        boxShadow: 'var(--dropdown-shadow)',
                      }}
                      onMouseEnter={() => setHoveredCategory(category.id)}
                      onMouseLeave={() => setHoveredCategory(null)}
                    >
                      {/* Header */}
                      <div 
                        className="px-6 py-4 border-b rounded-t-xl"
                        style={{ 
                          background: `linear-gradient(135deg, var(--theme-primary)20, var(--theme-secondary)20)`,
                          borderBottomColor: 'var(--dropdown-border)',
                        }}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 
                              className="font-bold text-base"
                              style={{ color: 'var(--theme-text)' }}
                            >
                              {category.name}
                            </h3>
                            <p 
                              className="text-sm mt-1"
                              style={{ color: 'var(--theme-text-secondary)' }}
                            >
                              {category.category_children.length} subcategories available
                            </p>
                          </div>
                          <Link
                            href={`/${storeHandle}/categories/${category.handle}`}
                            className="text-sm font-semibold hover:underline transition-colors"
                            style={{ color: 'var(--btn-primary)' }}
                          >
                            View All →
                          </Link>
                        </div>
                      </div>

                      {/* Subcategories List */}
                      <div className="p-6">
                        <div className="space-y-2">
                          {category.category_children.map((subcategory, index) => (
                            <Link
                              key={subcategory.id}
                              href={`/${storeHandle}/subcategories/${subcategory.handle}`}
                              className="group flex items-center space-x-4 p-3 rounded-lg transition-all duration-200 border border-transparent themed-dropdown-item"
                              style={{
                                '--hover-bg': 'var(--dropdown-item-hover)',
                                '--hover-border': 'var(--theme-primary)',
                              } as React.CSSProperties}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.backgroundColor = 'var(--dropdown-item-hover)';
                                e.currentTarget.style.borderColor = 'var(--theme-primary)';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.backgroundColor = 'transparent';
                                e.currentTarget.style.borderColor = 'transparent';
                              }}
                            >
                              <div 
                                className="w-4 h-4 rounded-full transition-all duration-200 flex-shrink-0"
                                style={{ 
                                  background: `linear-gradient(135deg, var(--theme-primary), var(--theme-accent))`,
                                }}
                              />
                              <div className="flex-1">
                                <h4 
                                  className="font-semibold transition-colors"
                                  style={{ color: 'var(--theme-text)' }}
                                >
                                  {subcategory.name}
                                </h4>
                            
                              </div>
                              <svg 
                                className="w-5 h-5 transition-all duration-200 transform group-hover:translate-x-1" 
                                fill="none" 
                                stroke="var(--theme-text-secondary)"
                                viewBox="0 0 24 24"
                              >
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                              </svg>
                            </Link>
                          ))}
                        </div>
                      </div>

                      {/* Arrow pointing up */}
                      {/* <div className="absolute -top-2 left-6">
                        <div 
                          className="w-4 h-4 border-l border-t transform rotate-45"
                          style={{ 
                            backgroundColor: 'var(--dropdown-bg)',
                            borderColor: 'var(--dropdown-border)',
                          }}
                        ></div>
                      </div> */}
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* View All Categories */}
          {/* <div>
            <Link
              href={`/${storeHandle}/categories`}
              className="flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 hover:shadow-sm"
              style={{ 
                color: 'var(--btn-primary)',
                backgroundColor: 'transparent',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--nav-hover)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <span>All Categories</span>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div> */}
        </div>
      </div>
    </div>
  );
};