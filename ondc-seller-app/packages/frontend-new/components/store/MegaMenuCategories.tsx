'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { ProductCategory } from '@/types';

interface MegaMenuCategoriesProps {
  categories: ProductCategory[];
  storeHandle: string;
}

export const MegaMenuCategories: React.FC<MegaMenuCategoriesProps> = ({ 
  categories, 
  storeHandle 
}) => {
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [clickedCategory, setClickedCategory] = useState<string | null>(null);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleCategoryInteraction = (categoryId: string) => {
    if (isMobile) {
      setClickedCategory(clickedCategory === categoryId ? null : categoryId);
    } else {
      setHoveredCategory(categoryId);
    }
  };

  const handleMouseLeave = () => {
    if (!isMobile) {
      setHoveredCategory(null);
    }
  };

  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <div className="mb-12 px-4 sm:px-6 lg:px-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">Shop by Category</h2>
        <p className="text-gray-600">Explore our wide range of products</p>
      </div>
      
      {/* Mega Menu Container */}
      <div className="relative">
        {/* Main Categories Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {categories.map((category) => (
            <div
              key={category.id}
              className="relative"
              onMouseEnter={() => !isMobile && setHoveredCategory(category.id)}
              onMouseLeave={handleMouseLeave}
            >
              {/* Category Card */}
              <div
                className="group block bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 border border-gray-100 overflow-hidden transform hover:-translate-y-1 cursor-pointer"
                onClick={() => {
                  if (isMobile && category.category_children && category.category_children.length > 0) {
                    handleCategoryInteraction(category.id);
                  } else {
                    window.location.href = `/${storeHandle}/categories/${category.handle}`;
                  }
                }}
              >
                <div className="p-6 text-center">
                  {/* Category Icon */}
                  <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full flex items-center justify-center group-hover:from-blue-200 group-hover:to-indigo-200 transition-all duration-300">
                    <svg 
                      className="w-8 h-8 text-blue-600 group-hover:text-blue-700 transition-colors" 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth={2} 
                        d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" 
                      />
                    </svg>
                  </div>
                  
                  {/* Category Name */}
                  <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors text-sm lg:text-base">
                    {category.name}
                  </h3>
                  
                  {/* Category Description */}
                  {category.description && (
                    <p className="text-xs text-gray-500 mt-2 line-clamp-2">
                      {category.description}
                    </p>
                  )}
                  
                  {/* Subcategory Count */}
                  {category.category_children && category.category_children.length > 0 && (
                    <div className="mt-3 flex items-center justify-center space-x-1 text-xs text-gray-400">
                      <span>{category.category_children.length} items</span>
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  )}
                </div>
              </div>
              
              {/* Mobile: View Category Link */}
              {isMobile && (
                <Link
                  href={`/${storeHandle}/categories/${category.handle}`}
                  className="block mt-2 text-center text-xs text-blue-600 hover:text-blue-700 font-medium"
                >
                  View Category
                </Link>
              )}

              {/* Mega Menu Dropdown */}
              {((hoveredCategory === category.id && !isMobile) || (clickedCategory === category.id && isMobile)) && 
               category.category_children && category.category_children.length > 0 && (
                <div className={`absolute top-full left-0 right-0 z-50 mt-2 ${isMobile ? 'relative mt-4' : ''}`}>
                  <div className={`bg-white rounded-xl shadow-xl border border-gray-200 p-6 min-w-[300px] max-w-[400px] mx-auto animate-fadeIn ${isMobile ? 'shadow-lg' : ''}`}>
                    {/* Dropdown Header */}
                    <div className="flex items-center space-x-3 mb-4 pb-3 border-b border-gray-100">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">{category.name}</h4>
                        <p className="text-xs text-gray-500">{category.category_children.length} subcategories</p>
                      </div>
                    </div>

                    {/* Subcategories Grid */}
                    <div className="grid grid-cols-1 gap-2">
                      {category.category_children.map((subcategory) => (
                        <Link
                          key={subcategory.id}
                          href={`/${storeHandle}/subcategories/${subcategory.handle}`}
                          className="group flex items-center space-x-3 p-3 rounded-lg hover:bg-blue-50 transition-colors"
                        >
                          <div className="w-2 h-2 bg-blue-400 rounded-full group-hover:bg-blue-600 transition-colors flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <h5 className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors text-sm">
                              {subcategory.name}
                            </h5>
                            {subcategory.description && (
                              <p className="text-xs text-gray-500 truncate">
                                {subcategory.description}
                              </p>
                            )}
                          </div>
                          <svg className="w-4 h-4 text-gray-400 group-hover:text-blue-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </Link>
                      ))}
                    </div>

                    {/* View All Link */}
                    <div className="mt-4 pt-3 border-t border-gray-100">
                      <Link
                        href={`/${storeHandle}/categories/${category.handle}`}
                        className="block w-full text-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm"
                      >
                        View All {category.name}
                      </Link>
                    </div>
                  </div>
                  
                  {/* Arrow pointing up */}
                  <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                    <div className="w-4 h-4 bg-white border-l border-t border-gray-200 transform rotate-45"></div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* View All Categories */}
        <div className="text-center mt-8">
          <Link
            href={`/${storeHandle}/categories`}
            className="inline-flex items-center px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors shadow-sm hover:shadow-md"
          >
            <span>View All Categories</span>
            <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        </div>
      </div>
    </div>
  );
};