'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { getPublishedCMSPagesByStoreHandle, type CMSPageData, type StrapiResponse } from '@/lib/api/strapi/pages';
import {
  Box,
  Container,
  Typography,
  Paper,
  Card,
  CardContent,
  CardActions,
  Button,
  Grid,
  Breadcrumbs,
  Link as MuiLink,
  CircularProgress,
  Alert,
  Chip
} from '@mui/material';
import {
  Home as HomeIcon,
  NavigateNext as NavigateNextIcon,
  Article as ArticleIcon,
  ArrowForward as ArrowForwardIcon
} from '@mui/icons-material';
import Link from 'next/link';

interface CMSPage {
  id: string;
  title: string;
  slug: string;
  content: string;
  publishedAt: string;
  updatedAt: string;
}

// Helper function to convert Strapi CMS page data to component format
const convertStrapiCMSPageToComponent = (strapiPage: CMSPageData): CMSPage => {
  return {
    id: strapiPage.documentId || strapiPage.id?.toString() || 'unknown',
    title: strapiPage.pageName || 'Untitled',
    slug: strapiPage.pageSlug || '',
    content: strapiPage.content || '',
    publishedAt: strapiPage.publishedAt || '',
    updatedAt: strapiPage.updatedAt || new Date().toISOString()
  };
};

export const CMSPagesList: React.FC = () => {
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  
  const [pages, setPages] = useState<CMSPage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPages = async () => {
      if (!storeHandle) {
        setError('Missing store handle');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        
        console.log('🌍 Fetching published CMS pages for store view:', storeHandle);
        const response: StrapiResponse<CMSPageData[]> = await getPublishedCMSPagesByStoreHandle(storeHandle);
        
        console.log('✅ Published CMS pages fetched successfully:', response);
        
        if (!response.data || !Array.isArray(response.data)) {
          console.warn('⚠️ Invalid API response structure:', response);
          setPages([]);
          setIsLoading(false);
          return;
        }
        
        const convertedPages = response.data.map((page, index) => {
          try {
            return convertStrapiCMSPageToComponent(page);
          } catch (error) {
            console.error(`❌ Error converting page at index ${index}:`, error, page);
            return null;
          }
        }).filter(Boolean) as CMSPage[];
        
        setPages(convertedPages);
        
      } catch (error: any) {
        console.error('❌ Error fetching CMS pages:', error);
        
        if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
          setError('Access denied');
        } else {
          setError('Failed to load pages');
        }
        
      } finally {
        setIsLoading(false);
      }
    };

    fetchPages();
  }, [storeHandle]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const stripHtml = (html: string) => {
    return html.replace(/<[^>]*>/g, '');
  };

  const getExcerpt = (content: string, maxLength: number = 150) => {
    const text = stripHtml(content);
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  };

  if (isLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" minHeight="400px">
          <CircularProgress size={48} sx={{ mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            Loading pages...
          </Typography>
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs 
        separator={<NavigateNextIcon fontSize="small" />}
        sx={{ mb: 3 }}
      >
        <Link href={`/${storeHandle}`} passHref>
          <MuiLink 
            component="a" 
            color="inherit" 
            sx={{ 
              display: 'flex', 
              alignItems: 'center',
              textDecoration: 'none',
              '&:hover': { textDecoration: 'underline' }
            }}
          >
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Home
          </MuiLink>
        </Link>
        <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
          <ArticleIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Pages
        </Typography>
      </Breadcrumbs>

      {/* Page Header */}
      <Box mb={4}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight={600}>
          Pages
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Browse all available pages and information
        </Typography>
      </Box>

      {/* Pages Grid */}
      {pages.length === 0 ? (
        <Paper elevation={1} sx={{ p: 4, textAlign: 'center', borderRadius: 2 }}>
          <ArticleIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            No Pages Available
          </Typography>
          <Typography variant="body2" color="text.secondary">
            There are currently no published pages to display.
          </Typography>
        </Paper>
      ) : (
        <Grid container spacing={3}>
          {pages.map((page) => (
            <Grid item xs={12} md={6} lg={4} key={page.id}>
              <Card 
                elevation={1} 
                sx={{ 
                  height: '100%', 
                  display: 'flex', 
                  flexDirection: 'column',
                  borderRadius: 2,
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    elevation: 3,
                    transform: 'translateY(-2px)'
                  }
                }}
              >
                <CardContent sx={{ flexGrow: 1, p: 3 }}>
                  <Box display="flex" alignItems="center" gap={1} mb={2}>
                    <ArticleIcon color="primary" fontSize="small" />
                    <Chip 
                      label="Published" 
                      color="success" 
                      size="small" 
                      variant="outlined"
                    />
                  </Box>
                  
                  <Typography variant="h6" component="h2" gutterBottom fontWeight={600}>
                    {page.title}
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" paragraph>
                    {getExcerpt(page.content)}
                  </Typography>
                  
                  <Typography variant="caption" color="text.secondary">
                    Published {formatDate(page.publishedAt)}
                  </Typography>
                </CardContent>
                
                <CardActions sx={{ p: 3, pt: 0 }}>
                  <Button
                    component={Link}
                    href={`/${storeHandle}/pages/${page.slug}`}
                    variant="contained"
                    endIcon={<ArrowForwardIcon />}
                    fullWidth
                    sx={{ borderRadius: 2 }}
                  >
                    Read More
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </Container>
  );
};

export default CMSPagesList;