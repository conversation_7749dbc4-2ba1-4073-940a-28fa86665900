'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { getPublishedCMSPageBySlug, type CMSPageData, type StrapiResponse } from '@/lib/api/strapi/pages';
import {
  Box,
  Container,
  Typography,
  Paper,
  Breadcrumbs,
  Link as MuiLink,
  CircularProgress,
  Alert,
  Chip
} from '@mui/material';
import {
  Home as HomeIcon,
  NavigateNext as NavigateNextIcon,
  Article as ArticleIcon
} from '@mui/icons-material';
import Link from 'next/link';

interface CMSPageViewProps {
  pageSlug: string;
}

interface CMSPage {
  id: string;
  title: string;
  slug: string;
  content: string;
  publishedAt: string;
  updatedAt: string;
}

// Helper function to convert Strapi CMS page data to component format
const convertStrapiCMSPageToComponent = (strapiPage: CMSPageData): CMSPage => {
  return {
    id: strapiPage.documentId || strapiPage.id?.toString() || 'unknown',
    title: strapiPage.pageName || 'Untitled',
    slug: strapiPage.pageSlug || '',
    content: strapiPage.content || '',
    publishedAt: strapiPage.publishedAt || '',
    updatedAt: strapiPage.updatedAt || new Date().toISOString()
  };
};

export const CMSPageView: React.FC<CMSPageViewProps> = ({ pageSlug }) => {
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  
  const [page, setPage] = useState<CMSPage | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPage = async () => {
      if (!storeHandle || !pageSlug) {
        setError('Missing store handle or page slug');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        
        console.log('🌍 Fetching published CMS page for store view:', { storeHandle, pageSlug });
        const response: StrapiResponse<CMSPageData[]> = await getPublishedCMSPageBySlug(storeHandle, pageSlug);
        
        console.log('✅ Published CMS page fetched successfully:', response);
        
        // The API returns an array, so we need to get the first item
        if (!response.data || !Array.isArray(response.data) || response.data.length === 0) {
          setError('Page not found or not published');
          setIsLoading(false);
          return;
        }
        
        const pageData = response.data[0];
        const convertedPage = convertStrapiCMSPageToComponent(pageData);
        setPage(convertedPage);
        
      } catch (error: any) {
        console.error('❌ Error fetching CMS page:', error);
        
        if (error.message?.includes('404') || error.message?.includes('Not Found')) {
          setError('Page not found');
        } else if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
          setError('Access denied');
        } else {
          setError('Failed to load page');
        }
        
      } finally {
        setIsLoading(false);
      }
    };

    fetchPage();
  }, [storeHandle, pageSlug]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" minHeight="400px">
          <CircularProgress size={48} sx={{ mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            Loading page...
          </Typography>
        </Box>
      </Container>
    );
  }

  if (error || !page) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error || 'Page not found'}
        </Alert>
        <Box textAlign="center">
          <Typography variant="h5" gutterBottom>
            Page Not Available
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            The page you're looking for might not exist or is not published yet.
          </Typography>
          <Link href={`/${storeHandle}`} passHref>
            <MuiLink component="a" variant="button">
              Return to Home
            </MuiLink>
          </Link>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs 
        separator={<NavigateNextIcon fontSize="small" />}
        sx={{ mb: 3 }}
      >
        <Link href={`/${storeHandle}`} passHref>
          <MuiLink 
            component="a" 
            color="inherit" 
            sx={{ 
              display: 'flex', 
              alignItems: 'center',
              textDecoration: 'none',
              '&:hover': { textDecoration: 'underline' }
            }}
          >
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Home
          </MuiLink>
        </Link>
        <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
          <ArticleIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          {page.title}
        </Typography>
      </Breadcrumbs>

      {/* Page Content */}
      <Paper elevation={1} sx={{ borderRadius: 2, overflow: 'hidden' }}>
        {/* Page Header */}
        <Box sx={{ p: 4, borderBottom: '1px solid', borderColor: 'divider' }}>
          <Typography variant="h3" component="h1" gutterBottom fontWeight={600}>
            {page.title}
          </Typography>
          
          <Box display="flex" alignItems="center" gap={2} mt={2}>
            <Chip 
              label="Published" 
              color="success" 
              size="small" 
              variant="outlined"
            />
            <Typography variant="body2" color="text.secondary">
              Published on {formatDate(page.publishedAt)}
            </Typography>
            {page.updatedAt !== page.publishedAt && (
              <Typography variant="body2" color="text.secondary">
                • Updated on {formatDate(page.updatedAt)}
              </Typography>
            )}
          </Box>
        </Box>

        {/* Page Content */}
        <Box sx={{ p: 4 }}>
          <Box 
            sx={{ 
              lineHeight: 1.7,
              fontSize: '1rem',
              '& h1': {
                fontSize: '2rem',
                fontWeight: 'bold',
                margin: '24px 0 16px 0',
                lineHeight: 1.2
              },
              '& h2': {
                fontSize: '1.5rem',
                fontWeight: 'bold',
                margin: '20px 0 12px 0',
                lineHeight: 1.3
              },
              '& h3': {
                fontSize: '1.25rem',
                fontWeight: 'bold',
                margin: '16px 0 8px 0',
                lineHeight: 1.4
              },
              '& p': {
                margin: '12px 0',
                lineHeight: 1.7
              },
              '& ul, & ol': {
                margin: '12px 0',
                paddingLeft: '24px'
              },
              '& li': {
                margin: '6px 0',
                lineHeight: 1.6
              },
              '& strong': {
                fontWeight: 'bold'
              },
              '& em': {
                fontStyle: 'italic'
              },
              '& u': {
                textDecoration: 'underline'
              },
              '& blockquote': {
                borderLeft: '4px solid',
                borderColor: 'primary.main',
                paddingLeft: '16px',
                margin: '16px 0',
                fontStyle: 'italic',
                backgroundColor: 'grey.50',
                padding: '16px'
              },
              '& img': {
                maxWidth: '100%',
                height: 'auto',
                borderRadius: 1
              },
              '& a': {
                color: 'primary.main',
                textDecoration: 'none',
                '&:hover': {
                  textDecoration: 'underline'
                }
              }
            }}
            dangerouslySetInnerHTML={{ __html: page.content }}
          />
        </Box>
      </Paper>
    </Container>
  );
};

export default CMSPageView;