'use client';

import React, { useEffect, useState } from 'react';
import { StoreHomeProps } from '@/types';
import { useStoreCategories, useFeaturedProducts, useStoreProducts } from '@/hooks/useStore';
import { useStoreConfig } from '@/hooks/useStoreConfig';
import { useBanners } from '@/hooks/useBanners';
import { ThemedStoreHeader } from './ThemedStoreHeader';
import { HeroBanner } from './HeroBanner';
import { ThemedHeaderMegaMenu } from './ThemedHeaderMegaMenu';
import { ProductCarousel } from './ProductCarousel';
import { ThemedStoreFooter } from './ThemedStoreFooter';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { ErrorMessage } from '../ui/ErrorMessage';
import { DevelopmentBanner } from '../ui/DevelopmentBanner';

import { DynamicStoreInfo } from './DynamicStoreInfo';
import { shouldUseMockData, logApiUsage } from '@/lib/utils/api-fallback';
import { demoBanners, demoCategories } from '@/lib/demo-data';
import { fetchStoreCategories } from '@/lib/api/store';
import { useQueryClient } from '@tanstack/react-query';

export const ModernStoreHomePage: React.FC<StoreHomeProps> = ({ storeHandle }) => {
  // Early return if no store handle provided
  if (!storeHandle) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 bg-gray-50">
        <ErrorMessage 
          message="Store handle is required to load this page."
          className="max-w-md"
        />
      </div>
    );
  }
  // Fetch store configuration from new API
  const {
    storeData,
    isLoading: storeConfigLoading,
    error: storeConfigError,
    storeName,
    storeDescription,
    storeAddress,
    storeContact,
    storeLogo,

    storeTheme,
    fetchStoreConfig
  } = useStoreConfig();

  // Note: Removed legacy store API call - now using only store-configurations endpoint

  // Fetch store categories
  const { 
    data: categories, 
    isLoading: categoriesLoading,
    error: categoriesError,
    refetch: refetchCategories
  } = useStoreCategories(storeHandle);
  
  // Force refetch categories on mount for debugging
  useEffect(() => {
    if (storeHandle && !categoriesLoading) {
      console.log('🔄 Force refetching categories for debugging...');
      refetchCategories();
    }
  }, [storeHandle]);
  
  // Manual API test - bypass React Query
  const [manualCategories, setManualCategories] = useState(null);
  const [manualLoading, setManualLoading] = useState(false);
  const [rawApiData, setRawApiData] = useState(null);
  const queryClient = useQueryClient();
  


  // Fetch featured products
  const { 
    data: featuredProducts, 
    isLoading: featuredLoading 
  } = useFeaturedProducts(storeHandle, 8);

  // Fetch all products
  const { 
    data: allProducts, 
    isLoading: productsLoading 
  } = useStoreProducts(storeHandle, 12);

  // Fetch banners for this specific store
  const { 
    data: banners, 
    isLoading: bannersLoading,
    error: bannersError 
  } = useBanners(storeHandle);


  // Handle loading state
  if (storeConfigLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">
            Loading store configuration...
          </p>
        </div>
      </div>
    );
  }

  // Handle error state - prioritize store config error
  if (storeConfigError && !storeData) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 bg-gray-50">
        <ErrorMessage 
          message={"Unable to load store configuration. Please check your connection or try again later."}
          onRetry={() => fetchStoreConfig(true)}
          className="max-w-md"
        />
      </div>
    );
  }
  
  // Note: Removed legacy store error handling - now using only store-configurations endpoint

  // Create store object from store configuration data
  const unifiedStore = {
    id: storeData?.id || storeHandle,
    name: storeName || storeHandle,
    handle: storeData?.handle || storeHandle,
    description: storeDescription || '',
    logo: storeLogo,
    address: storeAddress,
    contact: storeContact,
    theme: storeTheme,
    ...storeData, // Use store config data as primary source
  };

  // If no store data at all
  if (!unifiedStore.name && !storeData) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 bg-gray-50">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">Loading store data...</p>
        </div>
      </div>
    );
  }


  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--theme-background)' }}>
      {/* Development Banner */}
      {/* <DevelopmentBanner /> */}

      {/* Themed Store Header */}
      <ThemedStoreHeader store={unifiedStore} storeHandle={storeHandle} />
      
      {/* Categories Mega Menu - Below Header, Above Banner - Prioritize real API categories */}
      {categoriesLoading ? (
        <div className="w-full bg-gray-100 border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between py-4">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="h-8 bg-gray-300 rounded animate-pulse w-24"></div>
              ))}
            </div>
          </div>
        </div>
      ) : (
        <>
          {/* Debug info */}
        
          
          {/* Show real categories if available */}
          {categories && Array.isArray(categories) && categories.length > 0 ? (
            <>
              <ThemedHeaderMegaMenu 
                categories={categories} 
                storeHandle={storeHandle}
              />
            </>
          ) : manualCategories && manualCategories.length > 0 ? (
            <>
              <div className="w-full bg-blue-50 border-b border-blue-200">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                  <div className="py-2 text-center">
                    <p className="text-sm text-blue-700">
                      🔧 Using Manual API Categories ({manualCategories.length} categories) - React Query Failed
                    </p>
                  </div>
                </div>
              </div>
              
              {/* Simple test display of manual categories */}
              <div className="w-full bg-gray-100 border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                  <div className="py-4">
                    <h3 className="text-lg font-semibold mb-2">Manual Categories Test:</h3>
                    <div className="flex flex-wrap gap-4">
                      {manualCategories.map((cat, index) => (
                        <div key={cat.id || index} className="bg-white p-3 rounded border">
                          <div className="font-medium">{cat.name}</div>
                          <div className="text-sm text-gray-600">
                            {cat.category_children?.length || 0} subcategories
                          </div>
                          {cat.category_children && cat.category_children.length > 0 && (
                            <div className="mt-2 text-xs">
                              {cat.category_children.map((sub, subIndex) => (
                                <div key={sub.id || subIndex} className="text-gray-500">
                                  • {sub.name}
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
              
              <ThemedHeaderMegaMenu 
                categories={manualCategories} 
                storeHandle={storeHandle}
              />
            </>
          ) : (
            <>
              <div className="w-full bg-yellow-50 border-b border-yellow-200">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                  <div className="py-2 text-center">
                    <p className="text-sm text-yellow-700">
                      📝 Demo Categories (Both React Query and Manual API failed)
                    </p>
                  </div>
                </div>
              </div>
              <ThemedHeaderMegaMenu 
                categories={demoCategories} 
                storeHandle={storeHandle}
              />
            </>
          )}
        </>
      )}
      

      
      {/* Hero Banner Section - Use real API banners */}
      {banners && banners.length > 0 ? (
        <HeroBanner banners={banners} storeHandle={storeHandle} />
      ) : (
        <HeroBanner banners={demoBanners} storeHandle={storeHandle} />
      )}
      
      {/* Main Content */}
      <div className="py-8">
        {/* Loading State for Products */}
        {(productsLoading || featuredLoading) && (
          <div className="flex justify-center items-center py-12">
            <LoadingSpinner size="lg" />
            <p className="ml-4 text-gray-600">Loading products...</p>
          </div>
        )}

        {/* Latest Arrivals Carousel - Use real API products */}
        {!productsLoading && allProducts && allProducts.length > 0 && (
          <ProductCarousel
            title="Latest Arrivals"
            products={allProducts.slice(0, 8)}
            storeHandle={storeHandle}
            viewAllLink={`/${storeHandle}/products?filter=latest`}
          />
        )}

        {/* Top Selling Products Carousel - Use real API products */}
        {!productsLoading && allProducts && allProducts.length > 8 && (
          <ProductCarousel
            title="Top Selling Products"
            products={allProducts.slice(8, 16)}
            storeHandle={storeHandle}
            viewAllLink={`/${storeHandle}/products?filter=bestsellers`}
          />
        )}

        {/* Featured Products Section - Use real API featured products */}
        {!featuredLoading && featuredProducts && featuredProducts.length > 0 && (
          <ProductCarousel
            title="Featured Products"
            products={featuredProducts}
            storeHandle={storeHandle}
            viewAllLink={`/${storeHandle}/products?featured=true`}
          />
        )}

        {/* All Products Section - Show if no featured products but have regular products */}
        {!productsLoading && !featuredLoading && (!featuredProducts || featuredProducts.length === 0) && allProducts && allProducts.length > 0 && (
          <ProductCarousel
            title="Our Products"
            products={allProducts.slice(0, 8)}
            storeHandle={storeHandle}
            viewAllLink={`/${storeHandle}/products`}
          />
        )}

        {/* No Products Message */}
        {!productsLoading && !featuredLoading && (!allProducts || allProducts.length === 0) && (!featuredProducts || featuredProducts.length === 0) && (
          <div className="text-center py-12">
            <div className="text-gray-500 mb-4">
              <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-700 mb-2">No Products Available</h3>
            <p className="text-gray-500">Products will appear here once they are added to the store.</p>
          </div>
        )}
      </div>

      {/* Themed Store Footer */}
      <ThemedStoreFooter store={unifiedStore} storeHandle={storeHandle} />
    </div>
  );
};