import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  Box,
  Typography,
  TextField,
  Button,
  Grid,
  Stack,
  Container,
  Divider,
  IconButton,
  InputAdornment,
} from '@mui/material';
import {
  Email,
  Phone,
  LocationOn,
  Facebook,
  Twitter,
  Instagram,
  YouTube,
  Send,
} from '@mui/icons-material';
import { Store } from '@/types';

interface StoreFooterProps {
  store: Store;
  storeHandle: string;
}

export const StoreFooter: React.FC<StoreFooterProps> = ({ store, storeHandle }) => {
  const defaultLogo = '/images/default-store-logo.png';

  const cmsPages = [
    { name: 'About Us', href: `/${storeHandle}/about-us` },
    { name: 'Contact Us', href: `/${storeHandle}/contact-us` },
    { name: 'Privacy Policy', href: `/${storeHandle}/privacy-policy` },
    { name: 'Terms & Conditions', href: `/${storeHandle}/terms-and-conditions` },
    { name: 'Refund Policy', href: `/${storeHandle}/refund-policy` },
    { name: 'Shipping Policy', href: `/${storeHandle}/shipping-policy` },
  ];

  const quickLinks = [
    { name: 'All Products', href: `/${storeHandle}/products` },
    { name: 'Categories', href: `/${storeHandle}/categories` },
    { name: 'Latest Arrivals', href: `/${storeHandle}/products?filter=latest` },
    { name: 'Top Selling', href: `/${storeHandle}/products?filter=bestsellers` },
    { name: 'Sale Items', href: `/${storeHandle}/products?filter=sale` },
    { name: 'Gift Cards', href: `/${storeHandle}/gift-cards` },
  ];

  const customerService = [
    { name: 'Help Center', href: `/${storeHandle}/help` },
    { name: 'Track Your Order', href: `/${storeHandle}/track-order` },
    { name: 'Returns & Exchanges', href: `/${storeHandle}/returns` },
    { name: 'Size Guide', href: `/${storeHandle}/size-guide` },
    { name: 'FAQ', href: `/${storeHandle}/faq` },
    { name: 'Live Chat', href: `/${storeHandle}/chat` },
  ];

  return (
    <Box component="footer" sx={{ bgcolor: 'grey.900', color: 'white' }}>
      {/* Newsletter Signup */}
      <Box sx={{ bgcolor: 'grey.800', py: 6 }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              Stay Updated
            </Typography>
            <Typography variant="body1" sx={{ color: 'grey.300', mb: 3 }}>
              Subscribe to get special offers, free giveaways, and updates.
            </Typography>
            <Stack direction="row" spacing={0} sx={{ maxWidth: 400, mx: 'auto' }}>
              <TextField
                fullWidth
                placeholder="Enter your email"
                type="email"
                variant="outlined"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    bgcolor: 'white',
                    borderRadius: '8px 0 0 8px',
                    '& fieldset': { border: 'none' },
                    color: 'text.primary'
                  }
                }}
              />
              <Button
                variant="contained"
                sx={{
                  borderRadius: '0 8px 8px 0',
                  px: 3,
                  py: 1.5,
                  minWidth: 'auto'
                }}
                endIcon={<Send />}
              >
                Subscribe
              </Button>
            </Stack>
          </Box>
        </Container>
      </Box>

      {/* Main Footer Content */}
      <Box sx={{ py: 6 }}>
        <Container maxWidth="lg">
          <Grid container spacing={4}>
            {/* Store Information */}
            <Grid item xs={12} lg={3}>
              <Stack spacing={2}>
                <Stack direction="row" spacing={2} alignItems="center">
                  <Box sx={{
                    position: 'relative',
                    width: 40,
                    height: 40,
                    borderRadius: '50%',
                    overflow: 'hidden'
                  }}>
                    <Image
                      src={store.logo || defaultLogo}
                      alt={`${store.name} logo`}
                      fill
                      style={{ objectFit: 'cover' }}
                    />
                  </Box>
                  <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                    {store.name}
                  </Typography>
                </Stack>
                <Typography variant="body2" sx={{ color: 'grey.300', lineHeight: 1.6 }}>
                  {store.description || `Welcome to ${store.name} - your trusted online store for quality products.`}
                </Typography>

                {/* Contact Information */}
                {store.contact_info && (
                  <Stack spacing={1}>
                    {store.contact_info.email && (
                      <Stack direction="row" spacing={1} alignItems="center">
                        <Email sx={{ fontSize: 16, color: 'grey.400' }} />
                        <Typography
                          component="a"
                          href={`mailto:${store.contact_info.email}`}
                          variant="body2"
                          sx={{
                            color: 'grey.300',
                            textDecoration: 'none',
                            '&:hover': { color: 'white' },
                            transition: 'color 0.2s'
                          }}
                        >
                          {store.contact_info.email}
                        </Typography>
                      </Stack>
                    )}
                  {store.contact_info.phone && (
                    <div className="flex items-center space-x-2">
                      <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                      <a href={`tel:${store.contact_info.phone}`} className="text-gray-300 hover:text-white transition-colors">
                        {store.contact_info.phone}
                      </a>
                    </div>
                  )}
                  {store.contact_info.address && (
                    <div className="flex items-start space-x-2">
                      <svg className="w-4 h-4 text-gray-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <span className="text-gray-300">{store.contact_info.address}</span>
                    </div>
                  )}
                </div>
              )}

              {/* Social Media Links */}
              {store.social_links && (
                <div className="flex space-x-4 mt-6">
                  {store.social_links.facebook && (
                    <a href={store.social_links.facebook} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                      </svg>
                    </a>
                  )}
                  {store.social_links.instagram && (
                    <a href={store.social_links.instagram} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.876.876 1.366 2.027 1.366 3.324s-.49 2.448-1.366 3.323c-.875.876-2.026 1.366-3.323 1.366z"/>
                      </svg>
                    </a>
                  )}
                  {store.social_links.twitter && (
                    <a href={store.social_links.twitter} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                      </svg>
                    </a>
                  )}
                </div>
              )}
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
              <ul className="space-y-2">
                {quickLinks.map((link) => (
                  <li key={link.name}>
                    <Link href={link.href} className="text-gray-300 hover:text-white transition-colors text-sm">
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Customer Service */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Customer Service</h3>
              <ul className="space-y-2">
                {customerService.map((link) => (
                  <li key={link.name}>
                    <Link href={link.href} className="text-gray-300 hover:text-white transition-colors text-sm">
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Legal & Policies */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Legal & Policies</h3>
              <ul className="space-y-2">
                {cmsPages.map((page) => (
                  <li key={page.name}>
                    <Link href={page.href} className="text-gray-300 hover:text-white transition-colors text-sm">
                      {page.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800 py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-sm text-gray-400 mb-4 md:mb-0">
              © {new Date().getFullYear()} {store.name}. All rights reserved.
            </div>
            <div className="flex items-center space-x-6 text-sm text-gray-400">
              <span>Powered by Multi-Tenant E-commerce</span>
              <div className="flex items-center space-x-2">
                <span>We accept:</span>
                <div className="flex space-x-2">
                  <div className="w-8 h-5 bg-blue-600 rounded text-xs flex items-center justify-center text-white font-bold">
                    VISA
                  </div>
                  <div className="w-8 h-5 bg-red-600 rounded text-xs flex items-center justify-center text-white font-bold">
                    MC
                  </div>
                  <div className="w-8 h-5 bg-blue-500 rounded text-xs flex items-center justify-center text-white font-bold">
                    PP
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};