import React from 'react';
import Image from 'next/image';
import {
  Box,
  Typography,
  Avatar,
  Chip,
  Stack,
  Card,
  CardContent,
} from '@mui/material';
import {
  Store as StoreIcon,
  LocationOn,
  Phone,
  Email,
  Language,
} from '@mui/icons-material';
import { Store } from '@/types';

interface StoreHeaderProps {
  store: Store;
}

export const StoreHeader: React.FC<StoreHeaderProps> = ({ store }) => {
  const defaultLogo = '/images/default-store-logo.png';
  const defaultBanner = '/images/default-store-banner.jpg';

  return (
    <Box sx={{ position: 'relative' }}>
      {/* Store Banner */}
      <Box sx={{
        position: 'relative',
        height: { xs: 256, md: 320, lg: 384 },
        overflow: 'hidden',
        borderRadius: 2,
        mb: 3
      }}>
        <Image
          src={store.banner || defaultBanner}
          alt={`${store.name} banner`}
          fill
          style={{ objectFit: 'cover' }}
          priority
        />
        <Box sx={{
          position: 'absolute',
          inset: 0,
          bgcolor: 'rgba(0, 0, 0, 0.3)'
        }} />

        {/* Store Info Overlay */}
        <Box sx={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          p: 3,
          color: 'white'
        }}>
          <Stack direction="row" spacing={2} alignItems="flex-end">
            {/* Store Logo */}
            <Avatar
              src={store.logo || defaultLogo}
              alt={`${store.name} logo`}
              sx={{
                width: { xs: 80, md: 96 },
                height: { xs: 80, md: 96 },
                border: 4,
                borderColor: 'white',
                boxShadow: 3,
                flexShrink: 0
              }}
            />

            {/* Store Details */}
            <Box sx={{ flex: 1, minWidth: 0 }}>
              <Typography
                variant="h3"
                component="h1"
                sx={{
                  fontSize: { xs: '1.5rem', md: '2rem', lg: '2.5rem' },
                  fontWeight: 'bold',
                  mb: 1,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
              >
                {store.name}
              </Typography>
              {store.description && (
                <Typography
                  variant="body1"
                  sx={{
                    fontSize: { xs: '0.875rem', md: '1rem' },
                    color: 'grey.200',
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden'
                  }}
                >
                  {store.description}
                </Typography>
              )}

              {/* Store Category & Subcategory */}
              <Stack direction="row" spacing={1} sx={{ mt: 2, flexWrap: 'wrap' }}>
                <Chip
                  label={store.category.name}
                  size="small"
                  sx={{
                    bgcolor: 'rgba(255, 255, 255, 0.2)',
                    backdropFilter: 'blur(4px)',
                    color: 'white',
                    fontSize: { xs: '0.75rem', md: '0.875rem' },
                    fontWeight: 600
                  }}
                />
                {store.subcategory && (
                  <Chip
                    label={store.subcategory.name}
                    size="small"
                    sx={{
                      bgcolor: 'rgba(255, 255, 255, 0.15)',
                      backdropFilter: 'blur(4px)',
                      color: 'white',
                      fontSize: { xs: '0.75rem', md: '0.875rem' }
                    }}
                  />
                )}
              </Stack>
            </Box>
          </Stack>
        </Box>
      </Box>

      {/* Store Stats & Actions */}
      <Stack
        direction={{ xs: 'column', md: 'row' }}
        justifyContent="space-between"
        alignItems={{ xs: 'flex-start', md: 'center' }}
        spacing={2}
        sx={{ mb: 4 }}
      >
        <Stack direction="row" spacing={3}>
          {/* Contact Info */}
          {store.contact_info && (
            <Stack direction="row" spacing={2} sx={{ fontSize: '0.875rem', color: 'text.secondary' }}>
              {store.contact_info.email && (
                <Box
                  component="a"
                  href={`mailto:${store.contact_info.email}`}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 0.5,
                    color: 'inherit',
                    textDecoration: 'none',
                    '&:hover': { color: 'primary.main' },
                    transition: 'color 0.2s'
                  }}
                >
                  <Email sx={{ fontSize: 16 }} />
                  <Typography variant="body2">{store.contact_info.email}</Typography>
                </Box>
              )}
              {store.contact_info.phone && (
                <Box
                  component="a"
                  href={`tel:${store.contact_info.phone}`}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 0.5,
                    color: 'inherit',
                    textDecoration: 'none',
                    '&:hover': { color: 'primary.main' },
                    transition: 'color 0.2s'
                  }}
                >
                  <Phone sx={{ fontSize: 16 }} />
                  <Typography variant="body2">{store.contact_info.phone}</Typography>
                </Box>
              )}
            </Stack>
          )}
        </Stack>

        {/* Social Links */}
        {store.social_links && (
          <Stack direction="row" spacing={1.5}>
            {store.social_links.facebook && (
              <Box
                component="a"
                href={store.social_links.facebook}
                target="_blank"
                rel="noopener noreferrer"
                sx={{
                  color: 'text.secondary',
                  '&:hover': { color: '#1877f2' },
                  transition: 'color 0.2s'
                }}
              >
                <Language sx={{ fontSize: 20 }} />
              </Box>
            )}
            {store.social_links.instagram && (
              <Box
                component="a"
                href={store.social_links.instagram}
                target="_blank"
                rel="noopener noreferrer"
                sx={{
                  color: 'text.secondary',
                  '&:hover': { color: '#e4405f' },
                  transition: 'color 0.2s'
                }}
              >
                <Language sx={{ fontSize: 20 }} />
              </Box>
            )}
            {store.social_links.twitter && (
              <Box
                component="a"
                href={store.social_links.twitter}
                target="_blank"
                rel="noopener noreferrer"
                sx={{
                  color: 'text.secondary',
                  '&:hover': { color: '#1da1f2' },
                  transition: 'color 0.2s'
                }}
              >
                <Language sx={{ fontSize: 20 }} />
              </Box>
            )}
          </Stack>
        )}
      </Stack>
    </Box>
  );
};