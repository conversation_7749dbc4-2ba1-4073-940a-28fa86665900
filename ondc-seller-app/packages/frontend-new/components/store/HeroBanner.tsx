'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  Box,
  Typography,
  Button,
  Container,
  IconButton,
  Stack,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  ArrowBackIos,
  ArrowForwardIos,
  FiberManualRecord,
} from '@mui/icons-material';

interface Banner {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  image: string;
  ctaText: string;
  ctaLink: string;
  backgroundColor: string;
}

interface HeroBannerProps {
  banners: Banner[];
  storeHandle: string;
}

export const HeroBanner: React.FC<HeroBannerProps> = ({ banners, storeHandle }) => {
  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    if (banners.length > 1) {
      const timer = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % banners.length);
      }, 5000); // Change slide every 5 seconds

      return () => clearInterval(timer);
    }
  }, [banners.length]);

  if (!banners || banners.length === 0) {
    return null;
  }

  const currentBanner = banners[currentSlide];
  
  // Safety check for currentBanner
  if (!currentBanner) {
    return null;
  }

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Container maxWidth="xl" sx={{ px: { xs: 2, sm: 3, lg: 4 }, mt: 2 }}>
      <Box sx={{
        position: 'relative',
        height: { xs: 192, sm: 256, md: 320, lg: 384 },
        overflow: 'hidden',
        borderRadius: 2,
        bgcolor: currentBanner?.backgroundColor || 'primary.main'
      }}>
        {/* Background Image */}
        <Box sx={{ position: 'absolute', inset: 0 }}>
          <Image
            src={currentBanner?.image || '/images/default-banner.jpg'}
            alt={currentBanner?.title || 'Banner'}
            fill
            style={{ objectFit: 'cover' }}
            priority
            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 100vw, 100vw"
          />
          <Box sx={{ position: 'absolute', inset: 0, bgcolor: 'rgba(0, 0, 0, 0.3)' }} />
        </Box>

        {/* Content Overlay */}
        <Box sx={{
          position: 'relative',
          zIndex: 10,
          height: '100%',
          display: 'flex',
          alignItems: 'end',
          justifyContent: 'center',
          textAlign: 'center',
          color: 'white',
          px: { xs: 2, sm: 3 },
          maxWidth: 800,
          mx: 'auto',
          paddingBottom:'10px'
        }}>
          <Stack spacing={{ xs: 1, sm: 2 }} alignItems="end">
            <Button
              component={Link}
              href={currentBanner?.ctaLink || '/products'}
              variant="contained"
              size={isMobile ? "medium" : "large"}
              sx={{
                bgcolor: 'white',
                color: 'text.primary',
                borderRadius: 28,
                px: { xs: 2, sm: 3, lg: 4 },
                py: { xs: 1, sm: 1.5 },
                fontWeight: 600,
                fontSize: { xs: '0.875rem', sm: '1rem' },
                '&:hover': {
                  bgcolor: 'grey.100',
                  transform: 'scale(1.05)',
                },
                transition: 'all 0.2s'
              }}
            >
              {currentBanner?.ctaText || 'Shop Now'}
            </Button>
          </Stack>
        </Box>

        {/* Slide Indicators */}
        {banners.length > 1 && (
          <Stack
            direction="row"
            spacing={1}
            sx={{
              position: 'absolute',
              bottom: { xs: 8, sm: 16 },
              left: '50%',
              transform: 'translateX(-50%)',
            }}
          >
            {banners.map((_, index) => (
              <IconButton
                key={index}
                onClick={() => setCurrentSlide(index)}
                size="small"
                sx={{
                  p: 0,
                  minWidth: 'auto',
                  width: { xs: 8, sm: 12 },
                  height: { xs: 8, sm: 12 },
                }}
              >
                <FiberManualRecord
                  sx={{
                    fontSize: { xs: 8, sm: 12 },
                    color: index === currentSlide ? 'white' : 'rgba(255, 255, 255, 0.5)',
                    transform: index === currentSlide ? 'scale(1.1)' : 'scale(1)',
                    transition: 'all 0.2s',
                    '&:hover': {
                      color: 'rgba(255, 255, 255, 0.75)',
                    }
                  }}
                />
              </IconButton>
            ))}
          </Stack>
        )}

        {/* Navigation Arrows - Hidden on mobile */}
        {banners.length > 1 && (
          <>
            <IconButton
              onClick={() => setCurrentSlide((prev) => (prev - 1 + banners.length) % banners.length)}
              sx={{
                display: { xs: 'none', sm: 'flex' },
                position: 'absolute',
                left: { sm: 8, lg: 16 },
                top: '50%',
                transform: 'translateY(-50%)',
                bgcolor: 'rgba(255, 255, 255, 0.2)',
                color: 'white',
                p: { sm: 1, lg: 1.5 },
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.3)',
                },
                transition: 'all 0.2s'
              }}
              aria-label="Previous slide"
            >
              <ArrowBackIos sx={{ fontSize: { sm: 16, lg: 24 } }} />
            </IconButton>
            <IconButton
              onClick={() => setCurrentSlide((prev) => (prev + 1) % banners.length)}
              sx={{
                display: { xs: 'none', sm: 'flex' },
                position: 'absolute',
                right: { sm: 8, lg: 16 },
                top: '50%',
                transform: 'translateY(-50%)',
                bgcolor: 'rgba(255, 255, 255, 0.2)',
                color: 'white',
                p: { sm: 1, lg: 1.5 },
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.3)',
                },
                transition: 'all 0.2s'
              }}
              aria-label="Next slide"
            >
              <ArrowForwardIos sx={{ fontSize: { sm: 16, lg: 24 } }} />
            </IconButton>
          </>
        )}

        {/* Touch/Swipe indicators for mobile */}
        {banners.length > 1 && (
          <Typography
            variant="caption"
            sx={{
              display: { xs: 'block', sm: 'none' },
              position: 'absolute',
              bottom: 32,
              left: '50%',
              transform: 'translateX(-50%)',
              color: 'white',
              opacity: 0.75,
              fontSize: '0.75rem'
            }}
          >
            Swipe for more
          </Typography>
        )}
      </Box>
    </Container>
  );
};