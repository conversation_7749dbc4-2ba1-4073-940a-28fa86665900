import React from 'react';
import Link from 'next/link';
import { ProductCategory } from '@/types';

interface CategorySectionProps {
  categories: ProductCategory[];
  storeHandle: string;
}

export const CategorySection: React.FC<CategorySectionProps> = ({ 
  categories, 
  storeHandle 
}) => {
  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <div className="mb-12">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Shop by Category</h2>
        <Link 
          href={`/${storeHandle}/categories`}
          className="text-blue-600 hover:text-blue-700 font-medium text-sm"
        >
          View All Categories →
        </Link>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
        {categories.slice(0, 6).map((category) => (
          <Link
            key={category.id}
            href={`/${storeHandle}/categories/${category.handle}`}
            className="group"
          >
            <div className="bg-white rounded-lg p-6 text-center shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-100">
              {/* Category Icon/Image */}
              <div className="w-12 h-12 mx-auto mb-3 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                <svg 
                  className="w-6 h-6 text-blue-600" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" 
                  />
                </svg>
              </div>
              
              {/* Category Name */}
              <h3 className="font-medium text-gray-900 text-sm group-hover:text-blue-600 transition-colors">
                {category.name}
              </h3>
              
              {/* Category Description */}
              {category.description && (
                <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                  {category.description}
                </p>
              )}
              
              {/* Subcategories Count */}
              {category.category_children && category.category_children.length > 0 && (
                <span className="text-xs text-gray-400 mt-2 block">
                  {category.category_children.length} subcategories
                </span>
              )}
            </div>
          </Link>
        ))}
      </div>
      
      {/* Show more categories if there are more than 6 */}
      {categories.length > 6 && (
        <div className="text-center mt-6">
          <Link
            href={`/${storeHandle}/categories`}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
          >
            View {categories.length - 6} More Categories
          </Link>
        </div>
      )}
    </div>
  );
};