import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  Card,
  CardContent,
  Typography,
  Button,
  Box,
  Chip,
} from '@mui/material';
import {
  Visibility,
} from '@mui/icons-material';
import { Product } from '@/types';
import { getStoreCartAPI, calculateCartMetadata } from '@/lib/api/cart';
import { useToast } from '@/app/providers/toast-provider';

interface ProductCardProps {
  product: any;
  storeHandle: string;
}

export const ProductCard: React.FC<ProductCardProps> = ({ product, storeHandle }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { showToast } = useToast();
  const defaultImage =  `https://picsum.photos/300/350?random=${product.id}`  ;
  const productImage = product.thumbnail || product.images?.[0]?.url || defaultImage;
  
  // Get store-specific cart API
  const cartAPI = getStoreCartAPI(storeHandle);
  
  // Get the lowest price from variants
  const getLowestPrice = () => {
    if (!product.variants || product.variants.length === 0) return null;
    
    const prices = product.variants.flatMap(variant => 
      variant.metadata.sale_price  || 0
    );
    
    return Math.min(...prices);
  };

  const lowestPrice = getLowestPrice();
  const currency = product.variants?.[0]?.prices?.[0]?.currency_code || 'USD';

  const formatPrice = (amount: number, currencyCode: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode.toUpperCase(),
    }).format(amount);
  };
  
  // Check if product is in stock
  const isInStock = product?.variants?.some(v => v?.metadata?.product_quantity > 0);
  
  // Handle add to cart - DIRECT API CALL
  const handleAddToCart = async (e: React.MouseEvent) => {
    if (!isInStock || isLoading) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Get the first available variant
      const availableVariant = product?.variants?.find(v => v?.metadata?.product_quantity > 0);
      
      if (!availableVariant) {
        showToast('No available variant found', 'error');
        return;
      }
      
      // Get or create cart
      let cartId = cartAPI.getStoredCartId();
      
      if (!cartId) {
        // Create new cart if none exists
        const regionsResponse = await cartAPI.getRegions('Asia');
        const region = regionsResponse.regions.find(r => 
          r.name.toLowerCase().includes('asia')
        ) || regionsResponse.regions[0];
        
        if (!region) {
          throw new Error('No region available');
        }
        
        const cartResponse = await cartAPI.createCart({ region_id: region.id });
        cartId = cartResponse.cart.id;
        // Cart ID is automatically stored by IsolatedCartAPI
      }
      
      // Calculate metadata
      const metadata = calculateCartMetadata(
        lowestPrice || 0,
        1,
        0.08 // 8% tax
      );
      
      // Add item to cart
      await cartAPI.addLineItem(cartId, {
        variant_id: availableVariant.id,
        quantity: 1,
        metadata,
      });
      
      showToast('Product added to cart successfully!', 'success');
      
      // Optionally trigger a custom event to update cart count in header
      window.dispatchEvent(new CustomEvent('cartUpdated'));
      
    } catch (error) {
      console.error('Error adding to cart:', error);
      showToast('Failed to add product to cart', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        transition: 'all 0.2s',
        '&:hover': {
          boxShadow: 3,
          '& .product-image': {
            transform: 'scale(1.05)',
          },
          '& .quick-view-overlay': {
            opacity: 1,
          }
        }
      }}
    >
      {/* Product Image */}
      <Box
        component={Link}
        href={`/${storeHandle}/products/${product.id}`}
        sx={{ position: 'relative', aspectRatio: '1', overflow: 'hidden', textDecoration: 'none' }}
      >
        <Image
          src={productImage}
          alt={product.title}
          fill
          style={{ objectFit: 'cover' }}
          className="product-image"
          sizes="(max-width: 480px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1536px) 25vw, 20vw"
        />

        {/* Product Status Badge */}
        {product.status === 'draft' && (
          <Chip
            label="Draft"
            size="small"
            sx={{
              position: 'absolute',
              top: 8,
              left: 8,
              bgcolor: 'warning.main',
              color: 'white',
              fontSize: '0.75rem',
              fontWeight: 600
            }}
          />
        )}

        {/* Quick View Button - Hidden on mobile */}
        <Box
          className="quick-view-overlay"
          sx={{
            position: 'absolute',
            inset: 0,
            bgcolor: 'rgba(0, 0, 0, 0.2)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            opacity: 0,
            transition: 'opacity 0.2s',
          }}
        >
          <Button
            variant="contained"
            size="small"
            startIcon={<Visibility />}
            sx={{
              display: { xs: 'none', sm: 'flex' },
              bgcolor: 'white',
              color: 'text.primary',
              '&:hover': { bgcolor: 'grey.100' }
            }}
          >
            Quick View
          </Button>
        </Box>
      </Box>

      {/* Product Content */}
      <CardContent sx={{ p: { xs: 1.5, sm: 2 }, flex: 1, display: 'flex', flexDirection: 'column' }}>
        {/* Product Title */}
        <Typography
          component={Link}
          href={`/${storeHandle}/products/${product.id}`}
          variant="h6"
          sx={{
            fontWeight: 600,
            mb: 1,
            fontSize: { xs: '0.875rem', sm: '1rem' },
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            textDecoration: 'none',
            color: 'text.primary',
            '&:hover': { opacity: 0.8 },
            transition: 'opacity 0.2s'
          }}
        >
          {product.title}
        </Typography>

        {/* Product Description - Hidden on mobile */}
        {product.description && (
          <Typography
            variant="body2"
            sx={{
              display: { xs: 'none', sm: '-webkit-box' },
              color: 'text.secondary',
              mb: 1.5,
              fontSize: { xs: '0.75rem', sm: '0.875rem' },
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden'
            }}
          >
            {product.description}
          </Typography>
        )}
          
          {/* Product Tags - Limited on mobile */}
          {product?.tags && product?.tags?.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-3">
              {product?.tags.slice(0, 1).map((tag) => (
                <span 
                  key={tag?.id}
                  className="text-xs px-2 py-1 rounded-md"
                  style={{ 
                    backgroundColor: 'var(--theme-surface, #f3f4f6)',
                    color: 'var(--theme-text-secondary, #6b7280)',
                  }}
                >
                  {tag?.value}
                </span>
              ))}
              {product?.tags.length > 1 && (
                <span 
                  className="text-xs"
                  style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                >
                  +{product?.tags.length - 1}
                </span>
              )}
            </div>
          )}
          
          {/* Spacer to push price and button to bottom */}
          <div className="flex-1"></div>
          
          {/* Price and Actions */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <div className="flex flex-col">
              {product?.metadata?.additional_data?.product_prices[0]?.original_price && (
                <span 
                  className="text-base sm:text-lg font-bold"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  {formatPrice(product?.metadata?.additional_data?.product_prices[0]?.sale_price, 'inr')}
                </span>
              )}
              {product?.variants && product?.variants.length > 0 && (
                <span 
                  className="text-xs"
                  style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                >
                  {product?.variants.length} variant{product?.variants.length !== 1 ? 's' : ''}
                </span>
              )}
            </div>
            
            <button 
              className={`w-full sm:w-auto px-3 py-2 rounded-md text-xs sm:text-sm font-medium transition-all duration-200 hover:shadow-sm ${
                !isInStock || isLoading ? 'opacity-50 cursor-not-allowed' : ''
              }`}
              style={{ 
                backgroundColor: (isInStock && !isLoading) ? 'var(--btn-primary, #3b82f6)' : 'var(--theme-text-secondary, #6b7280)',
                color: 'var(--btn-text, white)',
              }}
              onMouseEnter={(e) => {
                if (isInStock && !isLoading) {
                  e.currentTarget.style.backgroundColor = 'var(--btn-primary-hover, #2563eb)';
                }
              }}
              onMouseLeave={(e) => {
                if (isInStock && !isLoading) {
                  e.currentTarget.style.backgroundColor = 'var(--btn-primary, #3b82f6)';
                }
              }}
              onClick={handleAddToCart}
              disabled={!isInStock || isLoading}
            >
              {isLoading ? 'Adding...' : 'Add to Cart'}
            </button>
          </div>
          
          {/* Inventory Status */}
          {product?.variants && product?.variants?.length > 0 && (
            <div className="mt-2">
              {product?.variants?.some(v => v?.metadata?.product_quantity > 0) ? (
                <span 
                  className="text-xs flex items-center"
                  style={{ color: 'var(--theme-accent, #10b981)' }}
                >
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                  In Stock
                </span>
              ) : (
                <span 
                  className="text-xs flex items-center"
                  style={{ color: '#ef4444' }}
                >
                  <div className="w-2 h-2 bg-red-500 rounded-full mr-1"></div>
                  Out of Stock
                </span>
              )}
            </div>
          )}
      </CardContent>
    </Card>
  );
};