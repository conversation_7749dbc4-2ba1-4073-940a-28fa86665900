import React from 'react';
import Link from 'next/link';
import { ProductCategory } from '@/types';

interface CategoriesWithSubcategoriesProps {
  categories: ProductCategory[];
  storeHandle: string;
}

export const CategoriesWithSubcategories: React.FC<CategoriesWithSubcategoriesProps> = ({ 
  categories, 
  storeHandle 
}) => {
  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <div className="mb-12 px-4 sm:px-6 lg:px-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">Shop by Category</h2>
        <p className="text-gray-600">Explore our wide range of products</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {categories.map((category) => (
          <div
            key={category.id}
            className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-100 overflow-hidden"
          >
            {/* Category Header */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 border-b border-gray-100">
              <Link
                href={`/${storeHandle}/categories/${category.handle}`}
                className="group flex items-center space-x-4"
              >
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                  <svg 
                    className="w-6 h-6 text-blue-600" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      strokeWidth={2} 
                      d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" 
                    />
                  </svg>
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                    {category.name}
                  </h3>
                  {category.description && (
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                      {category.description}
                    </p>
                  )}
                </div>
                <svg className="w-5 h-5 text-gray-400 group-hover:text-blue-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            </div>

            {/* Subcategories */}
            {category.category_children && category.category_children.length > 0 && (
              <div className="p-6">
                <h4 className="text-sm font-medium text-gray-700 mb-3">Subcategories:</h4>
                <div className="grid grid-cols-2 gap-2">
                  {category.category_children.slice(0, 6).map((subcategory) => (
                    <Link
                      key={subcategory.id}
                      href={`/${storeHandle}/subcategories/${subcategory.handle}`}
                      className="text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-lg transition-colors flex items-center space-x-2"
                    >
                      <div className="w-2 h-2 bg-blue-400 rounded-full flex-shrink-0" />
                      <span className="truncate">{subcategory.name}</span>
                    </Link>
                  ))}
                  {category.category_children.length > 6 && (
                    <Link
                      href={`/${storeHandle}/categories/${category.handle}`}
                      className="text-sm text-blue-600 hover:text-blue-700 px-3 py-2 rounded-lg transition-colors flex items-center space-x-2 font-medium"
                    >
                      <span>+{category.category_children.length - 6} more</span>
                    </Link>
                  )}
                </div>
              </div>
            )}

            {/* No Subcategories Message */}
            {(!category.category_children || category.category_children.length === 0) && (
              <div className="p-6">
                <p className="text-sm text-gray-500 italic">No subcategories available</p>
              </div>
            )}

            {/* View All Button */}
            <div className="px-6 pb-6">
              <Link
                href={`/${storeHandle}/categories/${category.handle}`}
                className="w-full bg-gray-50 hover:bg-gray-100 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors text-center block"
              >
                View All Products
              </Link>
            </div>
          </div>
        ))}
      </div>
      
      {/* View All Categories */}
      <div className="text-center mt-8">
        <Link
          href={`/${storeHandle}/categories`}
          className="inline-flex items-center px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
        >
          <span>View All Categories</span>
          <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </Link>
      </div>
    </div>
  );
};