'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { ProductCategory } from '@/types';

interface FixedHeaderMegaMenuProps {
  categories: ProductCategory[];
  storeHandle: string;
}

export const FixedHeaderMegaMenu: React.FC<FixedHeaderMegaMenuProps> = ({ 
  categories, 
  storeHandle 
}) => {
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleMouseEnter = (categoryId: string) => {
    if (!isMobile) {
      setHoveredCategory(categoryId);
    }
  };

  const handleMouseLeave = () => {
    if (!isMobile) {
      setHoveredCategory(null);
    }
  };

  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <div className="w-full bg-white border-b border-gray-200 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-4">
          {/* Categories List */}
          <div className="flex items-center space-x-2">
            {categories.map((category) => {
              const hasSubcategories = category.category_children && category.category_children.length > 0;
              const isHovered = hoveredCategory === category.id;
              
              return (
                <div
                  key={category.id}
                  className="relative"
                  onMouseEnter={() => handleMouseEnter(category.id)}
                  onMouseLeave={handleMouseLeave}
                >
                  {/* Category Button */}
                  <button
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                      isHovered 
                        ? 'bg-blue-100 text-blue-700 shadow-lg' 
                        : 'text-gray-700 hover:bg-gray-100 hover:text-blue-600'
                    }`}
                  >
                    <span>{category.name}</span>
                    {hasSubcategories && (
                      <div className="flex items-center space-x-1">
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          isHovered ? 'bg-blue-200 text-blue-800' : 'bg-gray-200 text-gray-600'
                        }`}>
                          {category.category_children.length}
                        </span>
                        <svg 
                          className={`w-4 h-4 transition-transform duration-200 ${
                            isHovered ? 'rotate-180' : ''
                          }`} 
                          fill="none" 
                          stroke="currentColor" 
                          viewBox="0 0 24 24"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    )}
                  </button>

                  {/* Dropdown Menu - Fixed positioning and z-index */}
                  {isHovered && hasSubcategories && (
                    <div 
                      className="absolute top-full left-0 mt-2 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-[9999]"
                      style={{ 
                        position: 'absolute',
                        zIndex: 9999,
                        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
                      }}
                      onMouseEnter={() => setHoveredCategory(category.id)}
                      onMouseLeave={() => setHoveredCategory(null)}
                    >
                      {/* Header */}
                      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-100 rounded-t-xl">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="font-bold text-gray-900 text-base">{category.name}</h3>
                            <p className="text-sm text-gray-600 mt-1">
                              {category.category_children.length} subcategories available
                            </p>
                          </div>
                          <Link
                            href={`/${storeHandle}/categories/${category.handle}`}
                            className="text-sm text-blue-600 hover:text-blue-700 font-semibold"
                          >
                            View All →
                          </Link>
                        </div>
                      </div>

                      {/* Subcategories List */}
                      <div className="p-6">
                        <div className="space-y-2">
                          {category.category_children.map((subcategory, index) => (
                              <Link
                                key={subcategory.id}
                                href={`/${storeHandle}/subcategories/${subcategory.handle}`}
                              className="group flex items-center space-x-4 p-3 rounded-lg hover:bg-blue-50 transition-all duration-200 border border-transparent hover:border-blue-200"
                            >
                              <div className="w-4 h-4 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full group-hover:from-blue-600 group-hover:to-blue-800 transition-all duration-200 flex-shrink-0" />
                              <div className="flex-1">
                                <h4 className="font-semibold text-gray-900 group-hover:text-blue-700 transition-colors">
                                  {subcategory.name}
                                </h4>
                                {subcategory.description && (
                                  <p className="text-sm text-gray-500 group-hover:text-gray-600 transition-colors mt-1">
                                    {subcategory.description}
                                  </p>
                                )}
                              </div>
                              <svg 
                                className="w-5 h-5 text-gray-400 group-hover:text-blue-600 transition-all duration-200 transform group-hover:translate-x-1" 
                                fill="none" 
                                stroke="currentColor" 
                                viewBox="0 0 24 24"
                              >
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                              </svg>
                            </Link>
                          ))}
                        </div>
                      </div>

                      {/* Footer */}
                      <div className="bg-gray-50 px-6 py-4 border-t border-gray-100 rounded-b-xl">
                        <Link
                          href={`/${storeHandle}/categories/${category.handle}`}
                          className="block w-full text-center bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 rounded-lg transition-colors"
                        >
                          Browse All {category.name}
                        </Link>
                      </div>

                      {/* Arrow pointing up */}
                      <div className="absolute -top-2 left-6">
                        <div className="w-4 h-4 bg-white border-l border-t border-gray-200 transform rotate-45"></div>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* View All Categories */}
          <div>
            <Link
              href={`/${storeHandle}/categories`}
              className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"
            >
              <span>All Categories</span>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>
      </div>


    </div>
  );
};