'use client';

import React from 'react';
import Carousel from 'react-multi-carousel';
import 'react-multi-carousel/lib/styles.css';
import { Product } from '@/types';
import { ProductCard } from './ProductCard';

interface ProductCarouselProps {
  title: string;
  products: Product[];
  storeHandle: string;
  viewAllLink?: string;
}

export const ProductCarousel: React.FC<ProductCarouselProps> = ({
  title,
  products,
  storeHandle,
  viewAllLink
}) => {
  if (!products || products.length === 0) {
    return null;
  }

  // Responsive breakpoints for react-multi-carousel
  const responsive = {
    superLargeDesktop: {
      breakpoint: { max: 4000, min: 1536 },
      items: 5,
      slidesToSlide: 2,
    },
    desktop: {
      breakpoint: { max: 1536, min: 1024 },
      items: 4,
      slidesToSlide: 2,
    },
    tablet: {
      breakpoint: { max: 1024, min: 768 },
      items: 3,
      slidesToSlide: 1,
    },
    mobile: {
      breakpoint: { max: 768, min: 480 },
      items: 2,
      slidesToSlide: 1,
    },
    smallMobile: {
      breakpoint: { max: 480, min: 0 },
      items: 1,
      slidesToSlide: 1,
    },
  };

  // Custom arrow components
  const CustomLeftArrow = ({ onClick }: { onClick?: () => void }) => (
    <button
      onClick={onClick}
      className="absolute left-2 top-1/2 transform -translate-y-1/2 z-10 p-2 rounded-full transition-all duration-200 hover:shadow-lg"
      style={{ 
        backgroundColor: 'var(--btn-primary, #3b82f6)',
        color: 'var(--btn-text, white)',
      }}
      aria-label="Previous products"
    >
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
      </svg>
    </button>
  );

  const CustomRightArrow = ({ onClick }: { onClick?: () => void }) => (
    <button
      onClick={onClick}
      className="absolute right-2 top-1/2 transform -translate-y-1/2 z-10 p-2 rounded-full transition-all duration-200 hover:shadow-lg"
      style={{ 
        backgroundColor: 'var(--btn-primary, #3b82f6)',
        color: 'var(--btn-text, white)',
      }}
      aria-label="Next products"
    >
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
      </svg>
    </button>
  );

  return (
    <div className="mb-8 lg:mb-12">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 lg:mb-6 px-4 sm:px-6 lg:px-8">
        <div className="mb-4 sm:mb-0">
          <h2 
            className="text-xl sm:text-2xl lg:text-3xl font-bold mb-1"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            {title}
          </h2>
          <p 
            className="text-sm sm:text-base"
            style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
          >
            Discover our {title.toLowerCase()}
          </p>
        </div>
        
        {/* View All Link */}
        {/* {viewAllLink && (
          <a
            href={viewAllLink}
            className="inline-flex items-center space-x-1 text-sm sm:text-base font-medium transition-colors hover:opacity-80"
            style={{ color: 'var(--theme-primary, #3b82f6)' }}
          >
            <span>View All</span>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </a>
        )} */}
      </div>

      {/* Carousel */}
      <div className="px-4 sm:px-6 lg:px-8">
        <Carousel
          responsive={responsive}
          infinite={false}
          autoPlay={false}
          keyBoardControl={true}
          customTransition="transform 300ms ease-in-out"
          transitionDuration={300}
          containerClass="carousel-container"
          removeArrowOnDeviceType={["smallMobile"]}
          dotListClass="custom-dot-list-style"
          itemClass="px-2"
          customLeftArrow={<CustomLeftArrow />}
          customRightArrow={<CustomRightArrow />}
          renderDotsOutside={false}
          showDots={false}
          swipeable={true}
          draggable={true}
          partialVisible={false}
          centerMode={false}
        >
          {products.map((product) => (
            <div key={product.id} className="h-full">
              <ProductCard product={product} storeHandle={storeHandle} />
            </div>
          ))}
        </Carousel>
      </div>

      {/* Custom Styles */}
      
    </div>
  );
};