'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { ProductCategory } from '@/types';

interface HeaderMegaMenuProps {
  categories: ProductCategory[];
  storeHandle: string;
}

export const HeaderMegaMenu: React.FC<HeaderMegaMenuProps> = ({ 
  categories, 
  storeHandle 
}) => {
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [clickedCategory, setClickedCategory] = useState<string | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleMouseEnter = (categoryId: string) => {
    if (!isMobile) {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      const category = categories.find(c => c.id === categoryId);
      console.log(`Hovering over category: ${category?.name} with ${category?.category_children?.length || 0} subcategories`);
      setHoveredCategory(categoryId);
    }
  };

  const handleMouseLeave = () => {
    if (!isMobile) {
      console.log('Mouse left category area');
      timeoutRef.current = setTimeout(() => {
        console.log('Hiding mega menu dropdown');
        setHoveredCategory(null);
      }, 300); // Increased delay for better UX
    }
  };

  const handleDropdownMouseEnter = () => {
    if (!isMobile && timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  const handleDropdownMouseLeave = () => {
    if (!isMobile) {
      timeoutRef.current = setTimeout(() => {
        setHoveredCategory(null);
      }, 150);
    }
  };

  const handleCategoryClick = (categoryId: string, hasSubcategories: boolean) => {
    if (isMobile && hasSubcategories) {
      setClickedCategory(clickedCategory === categoryId ? null : categoryId);
    } else {
      window.location.href = `/${storeHandle}/categories/${categories.find(c => c.id === categoryId)?.handle}`;
    }
  };

  if (!categories || categories.length === 0) {
    console.log('No categories available for mega menu');
    return null;
  }

  // Debug: Log categories and their subcategories
  useEffect(() => {
    if (categories && categories.length > 0) {
      console.log('HeaderMegaMenu loaded with categories:', categories.map(cat => ({
        name: cat.name,
        subcategories: cat.category_children?.length || 0,
        subcategoryNames: cat.category_children?.map(sub => sub.name) || []
      })));
    }
  }, [categories]);

  return (
    <div className="w-full">
      {/* Categories Navigation Bar */}
      <div className="bg-white border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-3">
            {/* Categories List */}
            <div className="flex items-center space-x-1 overflow-x-auto scrollbar-hide">
              {categories.map((category) => {
                const hasSubcategories = category.category_children && category.category_children.length > 0;
                const isActive = hoveredCategory === category.id || clickedCategory === category.id;
                
                return (
                  <div
                    key={category.id}
                    className="relative flex-shrink-0"
                    onMouseEnter={() => handleMouseEnter(category.id)}
                    onMouseLeave={handleMouseLeave}
                  >
                    {/* Category Button */}
                    <button
                      className={`flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 whitespace-nowrap ${
                        isActive 
                          ? 'bg-blue-100 text-blue-700 shadow-md border border-blue-200' 
                          : 'text-gray-700 hover:bg-blue-50 hover:text-blue-600 hover:shadow-sm'
                      }`}
                      onClick={() => handleCategoryClick(category.id, hasSubcategories)}
                    >
                      <span>{category.name}</span>
                      {hasSubcategories && (
                        <div className="flex items-center space-x-1">
                          <span className={`text-xs px-1.5 py-0.5 rounded-full transition-all duration-200 ${
                            isActive 
                              ? 'bg-blue-200 text-blue-800' 
                              : 'bg-gray-200 text-gray-600'
                          }`}>
                            {category.category_children.length}
                          </span>
                          <svg className={`w-4 h-4 transition-transform duration-200 ${
                            isActive ? 'rotate-180 text-blue-700' : 'text-gray-500'
                          }`} 
                               fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </div>
                      )}
                    </button>

                    {/* Mega Menu Dropdown */}
                    {isActive && hasSubcategories && (
                      <div 
                        className={`absolute top-full left-0 z-50 mt-1 ${isMobile ? 'relative w-full' : 'w-80'}`}
                        onMouseEnter={handleDropdownMouseEnter}
                        onMouseLeave={handleDropdownMouseLeave}
                      >
                        <div className="bg-white rounded-lg shadow-xl border border-gray-200 overflow-hidden animate-fadeIn">
                          {/* Header */}
                          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-3 border-b border-gray-100">
                            <div className="flex items-center justify-between">
                              <div>
                                <h4 className="font-semibold text-gray-900 text-sm">{category.name}</h4>
                                <p className="text-xs text-gray-600">{category.category_children.length} subcategories</p>
                              </div>
                              <Link
                                href={`/${storeHandle}/categories/${category.handle}`}
                                className="text-xs text-blue-600 hover:text-blue-700 font-medium"
                              >
                                View All →
                              </Link>
                            </div>
                          </div>

                          {/* Subcategories */}
                          <div className="p-4">
                            <div className="space-y-1">
                              {category.category_children.map((subcategory, index) => (
                                <Link
                                  key={subcategory.id}
                                  href={`/${storeHandle}/subcategories/${subcategory.handle}`}
                                  className="group flex items-center space-x-3 p-3 rounded-lg hover:bg-blue-50 transition-all duration-200 border border-transparent hover:border-blue-100"
                                  style={{ animationDelay: `${index * 50}ms` }}
                                >
                                  <div className="w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full group-hover:from-blue-600 group-hover:to-blue-700 transition-all duration-200 flex-shrink-0 shadow-sm" />
                                  <div className="flex-1 min-w-0">
                                    <h5 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors text-sm">
                                      {subcategory.name}
                                    </h5>
                                    {subcategory.description && (
                                      <p className="text-xs text-gray-500 group-hover:text-gray-600 transition-colors mt-0.5 line-clamp-1">
                                        {subcategory.description}
                                      </p>
                                    )}
                                  </div>
                                  <svg className="w-4 h-4 text-gray-400 group-hover:text-blue-600 transition-all duration-200 transform group-hover:translate-x-1" 
                                       fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                  </svg>
                                </Link>
                              ))}
                            </div>
                          </div>

                          {/* Footer */}
                          <div className="bg-gray-50 px-4 py-3 border-t border-gray-100">
                            <Link
                              href={`/${storeHandle}/categories/${category.handle}`}
                              className="block w-full text-center bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 rounded-md transition-colors"
                            >
                              Browse All {category.name}
                            </Link>
                          </div>
                        </div>
                        
                        {/* Arrow pointing up (desktop only) */}
                        {!isMobile && (
                          <div className="absolute -top-1 left-4">
                            <div className="w-3 h-3 bg-white border-l border-t border-gray-200 transform rotate-45"></div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            {/* View All Categories Link */}
            <div className="flex-shrink-0 ml-4">
              <Link
                href={`/${storeHandle}/categories`}
                className="flex items-center space-x-1 px-3 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"
              >
                <span className="hidden sm:inline">All Categories</span>
                <span className="sm:hidden">All</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Subcategories (when clicked) */}
      {isMobile && clickedCategory && (
        <div className="bg-gray-50 border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            {(() => {
              const activeCategory = categories.find(c => c.id === clickedCategory);
              if (!activeCategory?.category_children) return null;
              
              return (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3 text-sm">
                    {activeCategory.name} Categories
                  </h4>
                  <div className="grid grid-cols-2 gap-2">
                    {activeCategory.category_children.map((subcategory) => (
                      <Link
                        key={subcategory.id}
                        href={`/${storeHandle}/subcategories/${subcategory.handle}`}
                        className="flex items-center space-x-2 p-2 bg-white rounded-lg hover:bg-blue-50 transition-colors"
                        onClick={() => setClickedCategory(null)}
                      >
                        <div className="w-2 h-2 bg-blue-400 rounded-full flex-shrink-0" />
                        <span className="text-sm text-gray-700 truncate">{subcategory.name}</span>
                      </Link>
                    ))}
                  </div>
                </div>
              );
            })()} 
          </div>
        </div>
      )}
    </div>
  );
};