'use client';

import React, { useEffect } from 'react';
import { StoreHomeProps } from '@/types';
import { useStore, useStoreCategories, useFeaturedProducts, useStoreProducts } from '@/hooks/useStore';
import { StoreHeader } from './StoreHeader';
import { StoreNavigation } from './StoreNavigation';
import { CategorySection } from './CategorySection';
import { ProductsGrid } from './ProductsGrid';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { ErrorMessage } from '../ui/ErrorMessage';
import { DevelopmentBanner } from '../ui/DevelopmentBanner';
import { shouldUseMockData, logApiUsage } from '@/lib/utils/api-fallback';

export const StoreHomePage: React.FC<StoreHomeProps> = ({ storeHandle }) => {
  // Fetch store data
  const { 
    data: store, 
    isLoading: storeLoading, 
    error: storeError,
    refetch: refetchStore 
  } = useStore(storeHandle);

  // Fetch store categories
  const { 
    data: categories, 
    isLoading: categoriesLoading 
  } = useStoreCategories(storeHandle);

  // Fetch featured products
  const { 
    data: featuredProducts, 
    isLoading: featuredLoading 
  } = useFeaturedProducts(storeHandle, 8);

  // Fetch all products
  const { 
    data: allProducts, 
    isLoading: productsLoading 
  } = useStoreProducts(storeHandle, 12);

  // Log API usage for debugging
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Store Home Page - Mock Data Mode:', shouldUseMockData());
      if (store) logApiUsage('store', 'api', store);
      if (categories) logApiUsage('categories', 'api', categories);
      if (allProducts) logApiUsage('products', 'api', allProducts);
    }
  }, [store, categories, allProducts]);

  // Handle loading state
  if (storeLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">Loading store...</p>
        </div>
      </div>
    );
  }

  // Handle error state - but don't show error if we have mock data
  if (storeError && !store) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <ErrorMessage 
          message={"Unable to load store data. Please check your connection or try again later."}
          onRetry={refetchStore}
          className="max-w-md"
        />
      </div>
    );
  }

  // If no store data at all (shouldn't happen with mock fallback)
  if (!store) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">Loading store data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Development Banner */}
      <DevelopmentBanner />
      
      {/* Store Navigation */}
      <StoreNavigation storeHandle={storeHandle} storeName={store.name} />
      
      {/* Store Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <StoreHeader store={store} />
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Categories Section */}
        {categories && categories.length > 0 && (
          <CategorySection 
            categories={categories} 
            storeHandle={storeHandle}
          />
        )}

        {/* Featured Products Section */}
        {featuredProducts && featuredProducts.length > 0 && (
          <ProductsGrid
            products={featuredProducts}
            storeHandle={storeHandle}
            title="Featured Products"
            isLoading={featuredLoading}
            viewAllLink={`/${storeHandle}/products?featured=true`}
            emptyMessage="No featured products available"
          />
        )}

        {/* All Products Section */}
        <ProductsGrid
          products={allProducts || []}
          storeHandle={storeHandle}
          title={featuredProducts && featuredProducts.length > 0 ? "All Products" : "Our Products"}
          isLoading={productsLoading}
          viewAllLink={`/${storeHandle}/products`}
          emptyMessage="No products available in this store"
        />

        {/* Store Information Footer */}
        <div className="bg-white rounded-lg shadow-sm p-6 mt-12">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Store Details */}
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">About {store.name}</h3>
              {store.description ? (
                <p className="text-gray-600 text-sm leading-relaxed">
                  {store.description}
                </p>
              ) : (
                <p className="text-gray-500 text-sm italic">
                  No description available
                </p>
              )}
            </div>

            {/* Contact Information */}
            {store.contact_info && (
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Contact Information</h3>
                <div className="space-y-2 text-sm">
                  {store.contact_info.email && (
                    <div className="flex items-center space-x-2">
                      <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                      <a href={`mailto:${store.contact_info.email}`} className="text-blue-600 hover:text-blue-700">
                        {store.contact_info.email}
                      </a>
                    </div>
                  )}
                  {store.contact_info.phone && (
                    <div className="flex items-center space-x-2">
                      <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                      <a href={`tel:${store.contact_info.phone}`} className="text-blue-600 hover:text-blue-700">
                        {store.contact_info.phone}
                      </a>
                    </div>
                  )}
                  {store.contact_info.address && (
                    <div className="flex items-start space-x-2">
                      <svg className="w-4 h-4 text-gray-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <span className="text-gray-600">{store.contact_info.address}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Store Stats */}
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Store Statistics</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Products:</span>
                  <span className="font-medium">{allProducts?.length || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Categories:</span>
                  <span className="font-medium">{categories?.length || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Store Type:</span>
                  <span className="font-medium">{store.category.name}</span>
                </div>
                {store.subcategory && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Specialty:</span>
                    <span className="font-medium">{store.subcategory.name}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};