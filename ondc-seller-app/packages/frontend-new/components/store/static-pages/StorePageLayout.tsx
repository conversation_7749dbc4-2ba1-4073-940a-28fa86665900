'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import { useStoreConfig } from '@/hooks/useStoreConfig';
import { ThemedStoreHeader } from '../ThemedStoreHeader';
import { ThemedStoreFooter } from '../ThemedStoreFooter';
import { LoadingSpinner } from '../../ui/LoadingSpinner';
import { ErrorMessage } from '../../ui/ErrorMessage';
import { BreadcrumbNavigation, BreadcrumbItem } from '../../navigation/BreadcrumbNavigation';

interface StorePageLayoutProps {
  title: string;
  lastUpdated?: string;
  children: React.ReactNode;
}

export const StorePageLayout: React.FC<StorePageLayoutProps> = ({ 
  title, 
  lastUpdated = new Date().toISOString(),
  children 
}) => {
  const params = useParams();
  const storeHandle = params.storeHandle as string;

  // Fetch store configuration
  const {
    storeData,
    isLoading: storeConfigLoading,
    error: storeConfigError,
    storeName,
    storeDescription,
    storeAddress,
    storeContact,
    storeLogo,
    storeTheme,
    fetchStoreConfig
  } = useStoreConfig();

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getBreadcrumbs = (): BreadcrumbItem[] => {
    return [
      { label: 'Home', href: `/${storeHandle}` },
      { label: title, href: `/${storeHandle}/${title.toLowerCase().replace(/\s+/g, '-')}`, isActive: true },
    ];
  };

  // Handle loading state
  if (storeConfigLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">
            Loading store configuration...
          </p>
        </div>
      </div>
    );
  }

  // Handle error state
  if (storeConfigError && !storeData) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 bg-gray-50">
        <ErrorMessage 
          message="Unable to load store configuration. Please check your connection or try again later."
          onRetry={() => fetchStoreConfig(true)}
          className="max-w-md"
        />
      </div>
    );
  }

  // Create store object from store configuration data
  const unifiedStore = {
    id: storeData?.id || storeHandle,
    name: storeName || storeHandle,
    handle: storeData?.handle || storeHandle,
    description: storeDescription || '',
    logo: storeLogo,
    address: storeAddress,
    contact: storeContact,
    theme: storeTheme,
    ...storeData,
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}>
      {/* Themed Store Header */}
      <ThemedStoreHeader store={unifiedStore} storeHandle={storeHandle} />
      
      {/* Main Content */}
      <div style={{ 
        maxWidth: '1200px', 
        margin: '0 auto', 
        padding: '32px 16px',
        fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif'
      }}>
        {/* Breadcrumbs */}
        <BreadcrumbNavigation items={getBreadcrumbs()} />

        {/* Page Content */}
        <div style={{ 
          backgroundColor: '#ffffff',
          borderRadius: '12px',
          border: '1px solid #e5e7eb',
          overflow: 'hidden',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          {/* Page Header */}
          <div style={{ 
            padding: '32px',
            borderBottom: '1px solid #e5e7eb'
          }}>
            <h1 style={{ 
              fontSize: '2.5rem',
              fontWeight: '600',
              margin: '0 0 16px 0',
              color: '#111827',
              lineHeight: '1.2'
            }}>
              {title}
            </h1>
            
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: '16px',
              marginTop: '16px'
            }}>
              <span style={{
                backgroundColor: '#3b82f6',
                color: '#ffffff',
                padding: '4px 12px',
                borderRadius: '6px',
                fontSize: '12px',
                fontWeight: '500',
                border: '1px solid #3b82f6'
              }}>
                Updated
              </span>
              <span style={{ 
                fontSize: '14px',
                color: '#6b7280'
              }}>
                Last updated on {formatDate(lastUpdated)}
              </span>
            </div>
          </div>

          {/* Page Content */}
          <div style={{ padding: '32px' }}>
            <div style={{ 
              lineHeight: '1.7',
              fontSize: '16px',
              color: '#111827'
            }}>
              {children}
            </div>
          </div>
        </div>
      </div>

      {/* Themed Store Footer */}
      <ThemedStoreFooter store={unifiedStore} storeHandle={storeHandle} />
    </div>
  );
};

export default StorePageLayout;