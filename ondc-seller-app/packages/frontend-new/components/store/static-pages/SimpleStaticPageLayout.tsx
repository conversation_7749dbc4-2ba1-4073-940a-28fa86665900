'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';

interface SimpleStaticPageLayoutProps {
  title: string;
  lastUpdated?: string;
  children: React.ReactNode;
}

export const SimpleStaticPageLayout: React.FC<SimpleStaticPageLayoutProps> = ({ 
  title, 
  lastUpdated = new Date().toISOString(),
  children 
}) => {
  const params = useParams();
  const storeHandle = params.storeHandle as string;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div style={{ 
      maxWidth: '1200px', 
      margin: '0 auto', 
      padding: '32px 16px',
      fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif'
    }}>
      {/* Breadcrumbs */}
      <nav style={{ marginBottom: '24px' }}>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          fontSize: '14px',
          color: '#6b7280'
        }}>
          <Link 
            href={`/${storeHandle}`}
            style={{ 
              color: '#6b7280',
              textDecoration: 'none',
              display: 'flex',
              alignItems: 'center'
            }}
          >
            🏠 Home
          </Link>
          <span style={{ margin: '0 8px' }}>›</span>
          <span style={{ color: '#111827', fontWeight: '500' }}>
            📄 {title}
          </span>
        </div>
      </nav>

      {/* Page Content */}
      <div style={{ 
        backgroundColor: '#ffffff',
        borderRadius: '12px',
        border: '1px solid #e5e7eb',
        overflow: 'hidden',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      }}>
        {/* Page Header */}
        <div style={{ 
          padding: '32px',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <h1 style={{ 
            fontSize: '2.5rem',
            fontWeight: '600',
            margin: '0 0 16px 0',
            color: '#111827',
            lineHeight: '1.2'
          }}>
            {title}
          </h1>
          
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '16px',
            marginTop: '16px'
          }}>
            <span style={{
              backgroundColor: '#3b82f6',
              color: '#ffffff',
              padding: '4px 12px',
              borderRadius: '6px',
              fontSize: '12px',
              fontWeight: '500',
              border: '1px solid #3b82f6'
            }}>
              Updated
            </span>
            <span style={{ 
              fontSize: '14px',
              color: '#6b7280'
            }}>
              Last updated on {formatDate(lastUpdated)}
            </span>
          </div>
        </div>

        {/* Page Content */}
        <div style={{ padding: '32px' }}>
          <div style={{ 
            lineHeight: '1.7',
            fontSize: '16px',
            color: '#111827'
          }}>
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleStaticPageLayout;