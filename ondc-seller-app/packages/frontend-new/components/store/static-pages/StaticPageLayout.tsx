'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import {
  Box,
  Container,
  Typography,
  Paper,
  Breadcrumbs,
  Link as MuiLink,
  Chip
} from '@mui/material';
import {
  Home as HomeIcon,
  NavigateNext as NavigateNextIcon,
  Article as ArticleIcon
} from '@mui/icons-material';
import Link from 'next/link';

interface StaticPageLayoutProps {
  title: string;
  lastUpdated?: string;
  children: React.ReactNode;
}

export const StaticPageLayout: React.FC<StaticPageLayoutProps> = ({ 
  title, 
  lastUpdated = new Date().toISOString(),
  children 
}) => {
  const params = useParams();
  const storeHandle = params.storeHandle as string;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs 
        separator={<NavigateNextIcon fontSize="small" />}
        sx={{ mb: 3 }}
      >
        <Link href={`/${storeHandle}`} passHref>
          <MuiLink 
            component="a" 
            color="inherit" 
            sx={{ 
              display: 'flex', 
              alignItems: 'center',
              textDecoration: 'none',
              '&:hover': { textDecoration: 'underline' }
            }}
          >
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Home
          </MuiLink>
        </Link>
        <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
          <ArticleIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          {title}
        </Typography>
      </Breadcrumbs>

      {/* Page Content */}
      <Paper elevation={1} sx={{ borderRadius: 2, overflow: 'hidden' }}>
        {/* Page Header */}
        <Box sx={{ p: 4, borderBottom: '1px solid', borderColor: 'divider' }}>
          <Typography variant="h3" component="h1" gutterBottom fontWeight={600}>
            {title}
          </Typography>
          
          <Box display="flex" alignItems="center" gap={2} mt={2}>
            <Chip 
              label="Updated" 
              color="primary" 
              size="small" 
              variant="outlined"
            />
            <Typography variant="body2" color="text.secondary">
              Last updated on {formatDate(lastUpdated)}
            </Typography>
          </Box>
        </Box>

        {/* Page Content */}
        <Box sx={{ p: 4 }}>
          <Box 
            sx={{ 
              lineHeight: 1.7,
              fontSize: '1rem',
              '& h1': {
                fontSize: '2rem',
                fontWeight: 'bold',
                margin: '24px 0 16px 0',
                lineHeight: 1.2
              },
              '& h2': {
                fontSize: '1.5rem',
                fontWeight: 'bold',
                margin: '20px 0 12px 0',
                lineHeight: 1.3
              },
              '& h3': {
                fontSize: '1.25rem',
                fontWeight: 'bold',
                margin: '16px 0 8px 0',
                lineHeight: 1.4
              },
              '& p': {
                margin: '12px 0',
                lineHeight: 1.7
              },
              '& ul, & ol': {
                margin: '12px 0',
                paddingLeft: '24px'
              },
              '& li': {
                margin: '6px 0',
                lineHeight: 1.6
              },
              '& strong': {
                fontWeight: 'bold'
              },
              '& em': {
                fontStyle: 'italic'
              },
              '& blockquote': {
                borderLeft: '4px solid',
                borderColor: 'primary.main',
                paddingLeft: '16px',
                margin: '16px 0',
                fontStyle: 'italic',
                backgroundColor: 'grey.50',
                padding: '16px',
                borderRadius: 1
              },
              '& .highlight-box': {
                backgroundColor: 'primary.50',
                border: '1px solid',
                borderColor: 'primary.200',
                borderRadius: 1,
                padding: 2,
                margin: '16px 0'
              },
              '& .warning-box': {
                backgroundColor: 'warning.50',
                border: '1px solid',
                borderColor: 'warning.200',
                borderRadius: 1,
                padding: 2,
                margin: '16px 0'
              },
              '& .info-box': {
                backgroundColor: 'info.50',
                border: '1px solid',
                borderColor: 'info.200',
                borderRadius: 1,
                padding: 2,
                margin: '16px 0'
              }
            }}
          >
            {children}
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default StaticPageLayout;