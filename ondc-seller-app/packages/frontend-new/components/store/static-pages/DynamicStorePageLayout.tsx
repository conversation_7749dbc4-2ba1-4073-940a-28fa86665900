'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import { useStoreConfig } from '@/hooks/useStoreConfig';
import { useCMSPage } from '@/hooks/useCMSPage';
import { ThemedStoreHeader } from '../ThemedStoreHeader';
import { ThemedStoreFooter } from '../ThemedStoreFooter';
import { LoadingSpinner } from '../../ui/LoadingSpinner';
import { ErrorMessage } from '../../ui/ErrorMessage';
import { BreadcrumbNavigation, BreadcrumbItem } from '../../navigation/BreadcrumbNavigation';

interface DynamicStorePageLayoutProps {
  pageName: string;
  fallbackTitle?: string;
  fallbackContent?: React.ReactNode;
}

export const DynamicStorePageLayout: React.FC<DynamicStorePageLayoutProps> = ({ 
  pageName,
  fallbackTitle,
  fallbackContent
}) => {
  const params = useParams();
  const storeHandle = params.storeHandle as string;

  // Fetch store configuration
  const {
    storeData,
    isLoading: storeConfigLoading,
    error: storeConfigError,
    storeName,
    storeDescription,
    storeAddress,
    storeContact,
    storeLogo,
    storeTheme,
    fetchStoreConfig
  } = useStoreConfig();

  // Fetch CMS page content
  const {
    data: cmsPage,
    isLoading: cmsLoading,
    error: cmsError,
    refetch: refetchCMS
  } = useCMSPage(storeHandle, pageName);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getBreadcrumbs = (): BreadcrumbItem[] => {
    const title = cmsPage?.title || cmsPage?.pageName || fallbackTitle || pageName;
    return [
      { label: 'Home', href: `/${storeHandle}` },
      { label: title, href: `/${storeHandle}/${title.toLowerCase().replace(/\s+/g, '-')}`, isActive: true },
    ];
  };

  // Handle loading state
  if (storeConfigLoading || cmsLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">
            {storeConfigLoading ? 'Loading store configuration...' : 'Loading page content...'}
          </p>
        </div>
      </div>
    );
  }

  // Handle error state
  if (storeConfigError && !storeData) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 bg-gray-50">
        <ErrorMessage 
          message="Unable to load store configuration. Please check your connection or try again later."
          onRetry={() => fetchStoreConfig(true)}
          className="max-w-md"
        />
      </div>
    );
  }

  // Create store object from store configuration data
  const unifiedStore = {
    id: storeData?.id || storeHandle,
    name: storeName || storeHandle,
    handle: storeData?.handle || storeHandle,
    description: storeDescription || '',
    logo: storeLogo,
    address: storeAddress,
    contact: storeContact,
    theme: storeTheme,
    ...storeData,
  };

  // Determine content to display
  const pageTitle = cmsPage?.title || cmsPage?.pageName || fallbackTitle || pageName;
  const pageContent = cmsPage?.content;
  const lastUpdated = cmsPage?.updatedAt || new Date().toISOString();

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}>
      {/* Themed Store Header */}
      <ThemedStoreHeader store={unifiedStore} storeHandle={storeHandle} />
      
      {/* Main Content */}
      <div style={{ 
        maxWidth: '1200px', 
        margin: '0 auto', 
        padding: '32px 16px',
        fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif'
      }}>
        {/* Breadcrumbs */}
        <BreadcrumbNavigation items={getBreadcrumbs()} />

        {/* Page Content */}
        <div style={{ 
          backgroundColor: '#ffffff',
          borderRadius: '12px',
          border: '1px solid #e5e7eb',
          overflow: 'hidden',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          {/* Page Header */}
          <div style={{ 
            padding: '32px',
            borderBottom: '1px solid #e5e7eb'
          }}>
            <h1 style={{ 
              fontSize: '2.5rem',
              fontWeight: '600',
              margin: '0 0 16px 0',
              color: '#111827',
              lineHeight: '1.2'
            }}>
              {pageTitle}
            </h1>
            
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: '16px',
              marginTop: '16px'
            }}>
              <span style={{
                backgroundColor: cmsPage ? '#10b981' : '#6b7280',
                color: '#ffffff',
                padding: '4px 12px',
                borderRadius: '6px',
                fontSize: '12px',
                fontWeight: '500'
              }}>
                {cmsPage ? 'CMS Content' : 'Fallback Content'}
              </span>
              <span style={{ 
                fontSize: '14px',
                color: '#6b7280'
              }}>
                Last updated on {formatDate(lastUpdated)}
              </span>
            </div>
          </div>

          {/* Page Content */}
          <div style={{ padding: '32px' }}>
            {cmsError && !cmsPage && (
              <div style={{
                backgroundColor: '#fef3c7',
                border: '1px solid #fbbf24',
                borderRadius: '8px',
                padding: '16px',
                marginBottom: '24px'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <span style={{ fontSize: '16px' }}>⚠️</span>
                  <div>
                    <p style={{ margin: '0', fontWeight: '600', color: '#92400e' }}>
                      Unable to load CMS content
                    </p>
                    <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#92400e' }}>
                      {cmsError.message || 'Failed to fetch page content from CMS'}
                    </p>
                    <button
                      onClick={() => refetchCMS()}
                      style={{
                        marginTop: '8px',
                        padding: '4px 8px',
                        backgroundColor: '#f59e0b',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        fontSize: '12px',
                        cursor: 'pointer'
                      }}
                    >
                      Retry
                    </button>
                  </div>
                </div>
              </div>
            )}

            <div style={{ 
              lineHeight: '1.7',
              fontSize: '16px',
              color: '#111827'
            }}>
              {pageContent ? (
                // Render CMS content as HTML
                <div 
                  dangerouslySetInnerHTML={{ __html: pageContent }}
                  style={{
                    '& h1': {
                      fontSize: '2rem',
                      fontWeight: 'bold',
                      margin: '24px 0 16px 0',
                      lineHeight: '1.2'
                    },
                    '& h2': {
                      fontSize: '1.5rem',
                      fontWeight: 'bold',
                      margin: '20px 0 12px 0',
                      lineHeight: '1.3'
                    },
                    '& h3': {
                      fontSize: '1.25rem',
                      fontWeight: 'bold',
                      margin: '16px 0 8px 0',
                      lineHeight: '1.4'
                    },
                    '& p': {
                      margin: '12px 0',
                      lineHeight: '1.7'
                    },
                    '& ul, & ol': {
                      margin: '12px 0',
                      paddingLeft: '24px'
                    },
                    '& li': {
                      margin: '6px 0',
                      lineHeight: '1.6'
                    }
                  }}
                />
              ) : fallbackContent ? (
                // Render fallback content
                fallbackContent
              ) : (
                // Default message when no content is available
                <div style={{ textAlign: 'center', padding: '48px 0' }}>
                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>📄</div>
                  <h3 style={{ fontSize: '1.5rem', fontWeight: '600', marginBottom: '8px', color: '#374151' }}>
                    No Content Available
                  </h3>
                  <p style={{ color: '#6b7280', marginBottom: '16px' }}>
                    This page doesn't have any content yet. Please check back later or contact the store administrator.
                  </p>
                  <button
                    onClick={() => refetchCMS()}
                    style={{
                      padding: '8px 16px',
                      backgroundColor: '#3b82f6',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      cursor: 'pointer'
                    }}
                  >
                    Refresh Content
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Themed Store Footer */}
      <ThemedStoreFooter store={unifiedStore} storeHandle={storeHandle} />
    </div>
  );
};

export default DynamicStorePageLayout;