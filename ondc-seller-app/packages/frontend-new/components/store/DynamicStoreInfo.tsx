'use client';

import React, { useEffect } from 'react';
import Image from 'next/image';
import { useStoreConfigStore } from '@/stores/storeConfigStore';
import { Store } from '@/types';

interface DynamicStoreInfoProps {
  storeHandle: string;
  fallbackStore?: Store;
}

export const DynamicStoreInfo: React.FC<DynamicStoreInfoProps> = ({ 
  storeHandle, 
  fallbackStore 
}) => {
  const {
    storeData,
    isLoading,
    error,
    fetchStoreConfig,
    clearStoreData,
    setError,
    storeName,
    storeDescription,
    storeAddress,
    storeContact,
    storeLogo,
    storeTheme,
    storeSettings,
    socialMedia,
    businessHours,
  } = useStoreConfigStore(storeHandle);

  // Fetch store data on mount
  useEffect(() => {
    if (storeHandle && !storeData) {
      fetchStoreConfig(storeHandle).catch(console.error);
    }
  }, [storeHandle, storeData, fetchStoreConfig]);
  
  // Apply dynamic theme styles
  useEffect(() => {
    if (storeTheme && typeof document !== 'undefined') {
      const root = document.documentElement;
      Object.entries({
        '--store-primary': storeTheme.primary_color,
        '--store-secondary': storeTheme.secondary_color,
        '--store-accent': storeTheme.accent_color,
        '--store-background': storeTheme.background_color,
        '--store-text': storeTheme.text_color,
      }).forEach(([property, value]) => {
        if (value) {
          root.style.setProperty(property, value);
        }
      });
    }
  }, [storeTheme]);

  // Update document title
  useEffect(() => {
    if (storeName && typeof document !== 'undefined') {
      document.title = `${storeName} - Online Store`;
    }
  }, [storeName]);

  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="h-8 bg-gray-200 rounded mb-4"></div>
        <div className="h-4 bg-gray-200 rounded mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
      </div>
    );
  }

  if (error || !storeData) {
    return (
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-800 mb-4">
          {storeHandle.charAt(0).toUpperCase() + storeHandle.slice(1)} Store
        </h1>
        <p className="text-gray-600">Welcome to our online store</p>
      </div>
    );
  }

  return (
    <div
      style={{
        backgroundColor: storeTheme?.background_color || 'transparent',
        color: storeTheme?.text_color || 'inherit'
      }}
    >
      {/* Store Header */}
      <div className="text-center mb-8">
        {/* Store Logo */}
        {storeLogo && (
          <div className="mb-6">
            <Image
              src={storeLogo}
              alt={`${storeName} Logo`}
              width={120}
              height={120}
              className="mx-auto rounded-lg shadow-md"
              onError={(e) => {
                // Hide image if it fails to load
                e.currentTarget.style.display = 'none';
              }}
            />
          </div>
        )}

        {/* Store Name */}
        <h1 
          className="text-4xl md:text-5xl font-bold mb-4"
          style={{ color: storeTheme?.primary_color || 'inherit' }}
        >
          {storeName}
        </h1>

        {/* Store Description */}
        {storeDescription && (
          <p className="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto mb-6">
            {storeDescription}
          </p>
        )}
      </div>

      {/* Store Contact Information */}
      {(storeContact || storeAddress) && (
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-2xl font-semibold mb-4 text-center">Store Information</h2>
          
          <div className="grid md:grid-cols-2 gap-6">
            {/* Contact Information */}
            {storeContact && (
              <div>
                <h3 className="text-lg font-medium mb-3 flex items-center">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                  Contact Us
                </h3>
                <div className="space-y-2 text-gray-600">
                  {storeContact.email && (
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                      <a href={`mailto:${storeContact.email}`} className="hover:text-blue-600">
                        {storeContact.email}
                      </a>
                    </div>
                  )}
                  {storeContact.phone && (
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                      <a href={`tel:${storeContact.phone}`} className="hover:text-blue-600">
                        {storeContact.phone}
                      </a>
                    </div>
                  )}
                  {storeContact.website && (
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9" />
                      </svg>
                      <a 
                        href={storeContact.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="hover:text-blue-600"
                      >
                        Visit Website
                      </a>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Address Information */}
            {storeAddress && (
              <div>
                <h3 className="text-lg font-medium mb-3 flex items-center">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  Our Location
                </h3>
                <div className="text-gray-600">
                  {storeAddress.street && <div>{storeAddress.street}</div>}
                  <div>
                    {[storeAddress.city, storeAddress.state, storeAddress.postal_code]
                      .filter(Boolean)
                      .join(', ')}
                  </div>
                  {storeAddress.country && <div>{storeAddress.country}</div>}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Theme Preview (Development Only) */}
      {process.env.NODE_ENV === 'development' && storeTheme && (
        <div className="bg-gray-100 rounded-lg p-4 mb-8">
          <h3 className="text-lg font-medium mb-3">Theme Colors (Dev Only)</h3>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
            {Object.entries(storeTheme).map(([key, value]) => (
              value && (
                <div key={key} className="text-center">
                  <div 
                    className="w-12 h-12 rounded mx-auto mb-1 border"
                    style={{ backgroundColor: value }}
                  ></div>
                  <div className="text-xs text-gray-600">{key.replace('_', ' ')}</div>
                  <div className="text-xs font-mono">{value}</div>
                </div>
              )
            ))}
          </div>
        </div>
      )}
    </div>
  );
};