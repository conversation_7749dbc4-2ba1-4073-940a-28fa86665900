'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { WishlistItem } from '@/types/auth';
import { formatPrice } from '@/lib/cart/mockCartData';
import { saveWishlistToStorage } from '@/lib/auth/mockAuthData';

interface WishlistItemsProps {
  items: WishlistItem[];
  storeHandle: string;
  onUpdateItems: (items: WishlistItem[]) => void;
}

export const WishlistItems: React.FC<WishlistItemsProps> = ({ 
  items, 
  storeHandle, 
  onUpdateItems 
}) => {
  const removeFromWishlist = (itemId: string) => {
    const updatedItems = items.filter(item => item.id !== itemId);
    onUpdateItems(updatedItems);
    saveWishlistToStorage(updatedItems);
  };

  const addToCart = (item: WishlistItem) => {
    // In a real app, this would add to cart
    alert(`Added "${item.title}" to cart!`);
  };

  if (items.length === 0) {
    return (
      <div 
        className="bg-white rounded-lg shadow-sm border p-12 text-center"
        style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
      >
        <div 
          className="w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-6"
          style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}
        >
          <svg 
            className="w-8 h-8" 
            fill="none" 
            stroke="var(--theme-text-secondary, #6b7280)" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </div>
        
        <h3 
          className="text-lg font-medium mb-2"
          style={{ color: 'var(--theme-text, #111827)' }}
        >
          Your wishlist is empty
        </h3>
        <p 
          className="text-sm mb-6"
          style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
        >
          Save items you love to your wishlist and shop them later
        </p>
        
        <Link
          href={`/${storeHandle}`}
          className="inline-flex items-center px-6 py-3 rounded-lg font-semibold transition-all duration-200 hover:shadow-lg"
          style={{ 
            backgroundColor: 'var(--btn-primary, #3b82f6)',
            color: 'var(--btn-text, #ffffff)',
          }}
        >
          Start Shopping
        </Link>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {items.map((item) => (
        <div 
          key={item.id}
          className="bg-white rounded-lg shadow-sm border transition-all duration-200 hover:shadow-md group"
          style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
        >
          {/* Product Image */}
          <div className="relative aspect-square overflow-hidden rounded-t-lg">
            <Image
              src={item.image}
              alt={item.title}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-200"
              onError={(e) => {
                e.currentTarget.style.display = 'none';
              }}
            />
            
            {/* Fallback */}
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
              <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>

            {/* Stock Status */}
            {!item.isInStock && (
              <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                <span className="text-white text-sm font-medium px-3 py-1 bg-red-600 rounded-full">
                  Out of Stock
                </span>
              </div>
            )}

            {/* Remove Button */}
            <button
              onClick={() => removeFromWishlist(item.id)}
              className="absolute top-2 right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-md transition-all duration-200 hover:bg-red-50 hover:text-red-600 opacity-0 group-hover:opacity-100"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* Sale Badge */}
            {item.originalPrice && item.originalPrice > item.price && (
              <div className="absolute top-2 left-2">
                <span 
                  className="text-white text-xs font-bold px-2 py-1 rounded-full"
                  style={{ backgroundColor: 'var(--theme-accent, #ef4444)' }}
                >
                  SALE
                </span>
              </div>
            )}
          </div>

          {/* Product Details */}
          <div className="p-4">
            <h3 
              className="font-medium text-sm mb-2 line-clamp-2"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              {item.title}
            </h3>

            {/* Variant */}
            {item.variant && (
              <p 
                className="text-xs mb-2"
                style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
              >
                {Object.entries(item.variant.options).map(([key, value]) => 
                  `${key}: ${value}`
                ).join(', ')}
              </p>
            )}

            {/* Price */}
            <div className="flex items-center space-x-2 mb-3">
              <span 
                className="font-bold"
                style={{ color: 'var(--theme-primary, #3b82f6)' }}
              >
                {formatPrice(item.price)}
              </span>
              {item.originalPrice && item.originalPrice > item.price && (
                <span 
                  className="text-sm line-through"
                  style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                >
                  {formatPrice(item.originalPrice)}
                </span>
              )}
            </div>

            {/* Added Date */}
            <p 
              className="text-xs mb-4"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
            >
              Added {new Date(item.addedAt).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric'
              })}
            </p>

            {/* Actions */}
            <div className="space-y-2">
              <button
                onClick={() => addToCart(item)}
                disabled={!item.isInStock}
                className={`w-full py-2 px-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                  item.isInStock 
                    ? 'hover:shadow-lg' 
                    : 'opacity-50 cursor-not-allowed'
                }`}
                style={{ 
                  backgroundColor: item.isInStock 
                    ? 'var(--btn-primary, #3b82f6)' 
                    : 'var(--theme-text-secondary, #6b7280)',
                  color: 'var(--btn-text, #ffffff)',
                }}
              >
                {item.isInStock ? 'Add to Cart' : 'Out of Stock'}
              </button>

              <Link
                href={`/${storeHandle}/products/${item.productId}`}
                className="block w-full py-2 px-4 text-sm font-medium text-center rounded-lg border transition-colors hover:opacity-80"
                style={{ 
                  borderColor: 'var(--theme-border, #d1d5db)',
                  color: 'var(--theme-text, #111827)',
                }}
              >
                View Details
              </Link>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};