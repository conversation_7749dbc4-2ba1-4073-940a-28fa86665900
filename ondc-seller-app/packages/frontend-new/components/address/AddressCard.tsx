'use client';

import React, { useState } from 'react';
import { UserAddress } from '@/types/auth';

interface AddressCardProps {
  address: UserAddress;
  onEdit: () => void;
  onDelete: () => void;
  onSetDefault: () => void;
}

export const AddressCard: React.FC<AddressCardProps> = ({ 
  address, 
  onEdit, 
  onDelete, 
  onSetDefault 
}) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const getAddressTypeIcon = (type: string) => {
    switch (type) {
      case 'home':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
        );
      case 'work':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        );
    }
  };

  const getAddressTypeColor = (type: string) => {
    switch (type) {
      case 'home':
        return '#10b981'; // Green
      case 'work':
        return '#3b82f6'; // Blue
      default:
        return '#6b7280'; // Gray
    }
  };

  const handleDelete = () => {
    if (showDeleteConfirm) {
      onDelete();
      setShowDeleteConfirm(false);
    } else {
      setShowDeleteConfirm(true);
    }
  };

  return (
    <div 
      className={`bg-white rounded-lg shadow-sm border transition-all duration-200 hover:shadow-md ${
        address.isDefault ? 'ring-2' : ''
      }`}
      style={{ 
        borderColor: 'var(--theme-border, #e5e7eb)',
        '--tw-ring-color': address.isDefault ? 'var(--theme-primary, #3b82f6)' : 'transparent',
      }}
    >
      {/* Header */}
      <div 
        className="p-4 border-b"
        style={{ borderBottomColor: 'var(--theme-border, #e5e7eb)' }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div 
              className="p-2 rounded-full"
              style={{ 
                backgroundColor: `${getAddressTypeColor(address.type)}20`,
                color: getAddressTypeColor(address.type),
              }}
            >
              {getAddressTypeIcon(address.type)}
            </div>
            <div>
              <h3 
                className="font-semibold capitalize"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                {address.type}
              </h3>
              {address.isDefault && (
                <span 
                  className="text-xs px-2 py-1 rounded-full font-medium"
                  style={{ 
                    backgroundColor: 'var(--theme-primary, #3b82f6)',
                    color: 'var(--btn-text, #ffffff)',
                  }}
                >
                  Default
                </span>
              )}
            </div>
          </div>
          
          {/* Actions Dropdown */}
          <div className="relative group">
            <button className="p-2 rounded-full transition-colors hover:opacity-80">
              <svg 
                className="w-5 h-5" 
                fill="none" 
                stroke="var(--theme-text-secondary, #6b7280)" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
              </svg>
            </button>
            
            {/* Dropdown Menu */}
            <div className="absolute right-0 mt-2 w-48 rounded-lg shadow-lg border z-10 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200"
              style={{ 
                backgroundColor: 'var(--theme-surface, #ffffff)',
                borderColor: 'var(--theme-border, #e5e7eb)',
              }}
            >
              <div className="py-2">
                <button
                  onClick={onEdit}
                  className="flex items-center w-full px-4 py-2 text-sm transition-colors hover:opacity-80"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Edit Address
                </button>
                
                {!address.isDefault && (
                  <button
                    onClick={onSetDefault}
                    className="flex items-center w-full px-4 py-2 text-sm transition-colors hover:opacity-80"
                    style={{ color: 'var(--theme-text, #111827)' }}
                  >
                    <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Set as Default
                  </button>
                )}
                
                <div 
                  className="border-t my-2"
                  style={{ borderTopColor: 'var(--theme-border, #e5e7eb)' }}
                />
                
                <button
                  onClick={handleDelete}
                  className={`flex items-center w-full px-4 py-2 text-sm transition-colors hover:opacity-80 ${
                    showDeleteConfirm ? 'text-red-600' : ''
                  }`}
                  style={{ color: showDeleteConfirm ? '#dc2626' : 'var(--theme-text, #111827)' }}
                >
                  <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  {showDeleteConfirm ? 'Confirm Delete' : 'Delete Address'}
                </button>
                
                {showDeleteConfirm && (
                  <button
                    onClick={() => setShowDeleteConfirm(false)}
                    className="flex items-center w-full px-4 py-2 text-sm transition-colors hover:opacity-80"
                    style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                  >
                    <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Cancel
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Address Details */}
      <div className="p-4">
        <div 
          className="text-sm space-y-1"
          style={{ color: 'var(--theme-text, #111827)' }}
        >
          <p className="font-medium">
            {address.firstName} {address.lastName}
          </p>
          
          {address.company && (
            <p style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>
              {address.company}
            </p>
          )}
          
          <p>{address.address}</p>
          {address.address2 && <p>{address.address2}</p>}
          
          <p>
            {address.city}, {address.state} {address.zipCode}
          </p>
          
          <p>{address.country}</p>
          
          {address.phone && (
            <p 
              className="mt-2 pt-2 border-t"
              style={{ 
                borderTopColor: 'var(--theme-border, #e5e7eb)',
                color: 'var(--theme-text-secondary, #6b7280)',
              }}
            >
              📞 {address.phone}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};