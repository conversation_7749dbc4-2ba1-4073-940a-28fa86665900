'use client';

import React, { useState } from 'react';
import { UserAddress } from '@/types/auth';
import { AddressCard } from './AddressCard';
import { AddressForm } from './AddressForm';

interface AddressListProps {
  addresses: UserAddress[];
  storeHandle: string;
  onUpdateAddresses: (addresses: UserAddress[]) => void;
}

export const AddressList: React.FC<AddressListProps> = ({ 
  addresses, 
  storeHandle, 
  onUpdateAddresses 
}) => {
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [editingAddress, setEditingAddress] = useState<UserAddress | null>(null);

  const handleAddAddress = (newAddress: Omit<UserAddress, 'id'>) => {
    const address: UserAddress = {
      ...newAddress,
      id: `addr-${Date.now()}`,
    };
    
    // If this is the first address or marked as default, make it default
    if (addresses.length === 0 || address.isDefault) {
      // Remove default from other addresses
      const updatedAddresses = addresses.map(addr => ({ ...addr, isDefault: false }));
      onUpdateAddresses([...updatedAddresses, address]);
    } else {
      onUpdateAddresses([...addresses, address]);
    }
    
    setIsAddingNew(false);
  };

  const handleEditAddress = (updatedAddress: UserAddress) => {
    const updatedAddresses = addresses.map(addr => {
      if (addr.id === updatedAddress.id) {
        // If this address is being set as default, remove default from others
        if (updatedAddress.isDefault) {
          return updatedAddress;
        }
        return updatedAddress;
      }
      // If the updated address is being set as default, remove default from this one
      if (updatedAddress.isDefault) {
        return { ...addr, isDefault: false };
      }
      return addr;
    });
    
    onUpdateAddresses(updatedAddresses);
    setEditingAddress(null);
  };

  const handleDeleteAddress = (addressId: string) => {
    const addressToDelete = addresses.find(addr => addr.id === addressId);
    const updatedAddresses = addresses.filter(addr => addr.id !== addressId);
    
    // If we deleted the default address and there are other addresses, make the first one default
    if (addressToDelete?.isDefault && updatedAddresses.length > 0) {
      updatedAddresses[0].isDefault = true;
    }
    
    onUpdateAddresses(updatedAddresses);
  };

  const handleSetDefault = (addressId: string) => {
    const updatedAddresses = addresses.map(addr => ({
      ...addr,
      isDefault: addr.id === addressId,
    }));
    onUpdateAddresses(updatedAddresses);
  };

  return (
    <div className="space-y-6">
      {/* Add New Address Button */}
      <div className="flex justify-between items-center">
        <div>
          <h2 
            className="text-xl font-semibold"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            Saved Addresses ({addresses.length})
          </h2>
        </div>
        
        {!isAddingNew && !editingAddress && (
          <button
            onClick={() => setIsAddingNew(true)}
            className="px-6 py-3 rounded-lg font-semibold transition-all duration-200 hover:shadow-lg"
            style={{ 
              backgroundColor: 'var(--btn-primary, #3b82f6)',
              color: 'var(--btn-text, #ffffff)',
            }}
          >
            Add New Address
          </button>
        )}
      </div>

      {/* Add New Address Form */}
      {isAddingNew && (
        <div 
          className="bg-white rounded-lg shadow-sm border p-6"
          style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
        >
          <div className="flex items-center justify-between mb-6">
            <h3 
              className="text-lg font-semibold"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              Add New Address
            </h3>
            <button
              onClick={() => setIsAddingNew(false)}
              className="p-2 rounded-full transition-colors hover:opacity-80"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <AddressForm
            onSubmit={handleAddAddress}
            onCancel={() => setIsAddingNew(false)}
            isDefault={addresses.length === 0}
          />
        </div>
      )}

      {/* Edit Address Form */}
      {editingAddress && (
        <div 
          className="bg-white rounded-lg shadow-sm border p-6"
          style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
        >
          <div className="flex items-center justify-between mb-6">
            <h3 
              className="text-lg font-semibold"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              Edit Address
            </h3>
            <button
              onClick={() => setEditingAddress(null)}
              className="p-2 rounded-full transition-colors hover:opacity-80"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <AddressForm
            address={editingAddress}
            onSubmit={handleEditAddress}
            onCancel={() => setEditingAddress(null)}
          />
        </div>
      )}

      {/* Address Cards */}
      {addresses.length === 0 && !isAddingNew ? (
        <div 
          className="bg-white rounded-lg shadow-sm border p-12 text-center"
          style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
        >
          <div 
            className="w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-6"
            style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}
          >
            <svg 
              className="w-8 h-8" 
              fill="none" 
              stroke="var(--theme-text-secondary, #6b7280)" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
          
          <h3 
            className="text-lg font-medium mb-2"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            No addresses saved
          </h3>
          <p 
            className="text-sm mb-6"
            style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
          >
            Add your first address to make checkout faster
          </p>
          
          <button
            onClick={() => setIsAddingNew(true)}
            className="inline-flex items-center px-6 py-3 rounded-lg font-semibold transition-all duration-200 hover:shadow-lg"
            style={{ 
              backgroundColor: 'var(--btn-primary, #3b82f6)',
              color: 'var(--btn-text, #ffffff)',
            }}
          >
            Add Your First Address
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {addresses.map((address) => (
            <AddressCard
              key={address.id}
              address={address}
              onEdit={() => setEditingAddress(address)}
              onDelete={() => handleDeleteAddress(address.id)}
              onSetDefault={() => handleSetDefault(address.id)}
            />
          ))}
        </div>
      )}
    </div>
  );
};