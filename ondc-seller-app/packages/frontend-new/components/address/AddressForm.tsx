'use client';

import React, { useState } from 'react';
import { UserAddress } from '@/types/auth';

interface AddressFormProps {
  address?: UserAddress;
  onSubmit: (address: UserAddress | Omit<UserAddress, 'id'>) => void;
  onCancel: () => void;
  isDefault?: boolean;
}

export const AddressForm: React.FC<AddressFormProps> = ({ 
  address, 
  onSubmit, 
  onCancel,
  isDefault = false 
}) => {
  const [formData, setFormData] = useState({
    type: address?.type || 'home',
    firstName: address?.firstName || '',
    lastName: address?.lastName || '',
    company: address?.company || '',
    address: address?.address || '',
    address2: address?.address2 || '',
    city: address?.city || '',
    state: address?.state || '',
    zipCode: address?.zipCode || '',
    country: address?.country || 'US',
    phone: address?.phone || '',
    isDefault: address?.isDefault || isDefault,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }
    if (!formData.address.trim()) {
      newErrors.address = 'Address is required';
    }
    if (!formData.city.trim()) {
      newErrors.city = 'City is required';
    }
    if (!formData.state.trim()) {
      newErrors.state = 'State is required';
    }
    if (!formData.zipCode.trim()) {
      newErrors.zipCode = 'ZIP code is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const addressData = {
      ...formData,
      id: address?.id,
    };

    if (address) {
      onSubmit(addressData as UserAddress);
    } else {
      const { id, ...newAddressData } = addressData;
      onSubmit(newAddressData);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Address Type */}
      <div>
        <label 
          className="block text-sm font-medium mb-2"
          style={{ color: 'var(--theme-text, #111827)' }}
        >
          Address Type
        </label>
        <select
          name="type"
          value={formData.type}
          onChange={handleInputChange}
          className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
          style={{ 
            borderColor: 'var(--theme-border, #d1d5db)',
            '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
          }}
        >
          <option value="home">Home</option>
          <option value="work">Work</option>
          <option value="other">Other</option>
        </select>
      </div>

      {/* Name Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label 
            className="block text-sm font-medium mb-2"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            First Name *
          </label>
          <input
            type="text"
            name="firstName"
            value={formData.firstName}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors ${
              errors.firstName ? 'border-red-500' : ''
            }`}
            style={{ 
              borderColor: errors.firstName ? '#ef4444' : 'var(--theme-border, #d1d5db)',
              '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
            }}
            placeholder="John"
          />
          {errors.firstName && (
            <p className="text-red-500 text-xs mt-1">{errors.firstName}</p>
          )}
        </div>

        <div>
          <label 
            className="block text-sm font-medium mb-2"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            Last Name *
          </label>
          <input
            type="text"
            name="lastName"
            value={formData.lastName}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors ${
              errors.lastName ? 'border-red-500' : ''
            }`}
            style={{ 
              borderColor: errors.lastName ? '#ef4444' : 'var(--theme-border, #d1d5db)',
              '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
            }}
            placeholder="Doe"
          />
          {errors.lastName && (
            <p className="text-red-500 text-xs mt-1">{errors.lastName}</p>
          )}
        </div>
      </div>

      {/* Company */}
      <div>
        <label 
          className="block text-sm font-medium mb-2"
          style={{ color: 'var(--theme-text, #111827)' }}
        >
          Company (Optional)
        </label>
        <input
          type="text"
          name="company"
          value={formData.company}
          onChange={handleInputChange}
          className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
          style={{ 
            borderColor: 'var(--theme-border, #d1d5db)',
            '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
          }}
          placeholder="Company Name"
        />
      </div>

      {/* Address */}
      <div>
        <label 
          className="block text-sm font-medium mb-2"
          style={{ color: 'var(--theme-text, #111827)' }}
        >
          Street Address *
        </label>
        <input
          type="text"
          name="address"
          value={formData.address}
          onChange={handleInputChange}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors ${
            errors.address ? 'border-red-500' : ''
          }`}
          style={{ 
            borderColor: errors.address ? '#ef4444' : 'var(--theme-border, #d1d5db)',
            '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
          }}
          placeholder="123 Main Street"
        />
        {errors.address && (
          <p className="text-red-500 text-xs mt-1">{errors.address}</p>
        )}
      </div>

      {/* Address Line 2 */}
      <div>
        <label 
          className="block text-sm font-medium mb-2"
          style={{ color: 'var(--theme-text, #111827)' }}
        >
          Apartment, Suite, etc. (Optional)
        </label>
        <input
          type="text"
          name="address2"
          value={formData.address2}
          onChange={handleInputChange}
          className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
          style={{ 
            borderColor: 'var(--theme-border, #d1d5db)',
            '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
          }}
          placeholder="Apt 4B, Suite 200, etc."
        />
      </div>

      {/* City, State, ZIP */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label 
            className="block text-sm font-medium mb-2"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            City *
          </label>
          <input
            type="text"
            name="city"
            value={formData.city}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors ${
              errors.city ? 'border-red-500' : ''
            }`}
            style={{ 
              borderColor: errors.city ? '#ef4444' : 'var(--theme-border, #d1d5db)',
              '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
            }}
            placeholder="New York"
          />
          {errors.city && (
            <p className="text-red-500 text-xs mt-1">{errors.city}</p>
          )}
        </div>

        <div>
          <label 
            className="block text-sm font-medium mb-2"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            State *
          </label>
          <input
            type="text"
            name="state"
            value={formData.state}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors ${
              errors.state ? 'border-red-500' : ''
            }`}
            style={{ 
              borderColor: errors.state ? '#ef4444' : 'var(--theme-border, #d1d5db)',
              '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
            }}
            placeholder="NY"
          />
          {errors.state && (
            <p className="text-red-500 text-xs mt-1">{errors.state}</p>
          )}
        </div>

        <div>
          <label 
            className="block text-sm font-medium mb-2"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            ZIP Code *
          </label>
          <input
            type="text"
            name="zipCode"
            value={formData.zipCode}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors ${
              errors.zipCode ? 'border-red-500' : ''
            }`}
            style={{ 
              borderColor: errors.zipCode ? '#ef4444' : 'var(--theme-border, #d1d5db)',
              '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
            }}
            placeholder="10001"
          />
          {errors.zipCode && (
            <p className="text-red-500 text-xs mt-1">{errors.zipCode}</p>
          )}
        </div>
      </div>

      {/* Country */}
      <div>
        <label 
          className="block text-sm font-medium mb-2"
          style={{ color: 'var(--theme-text, #111827)' }}
        >
          Country
        </label>
        <select
          name="country"
          value={formData.country}
          onChange={handleInputChange}
          className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
          style={{ 
            borderColor: 'var(--theme-border, #d1d5db)',
            '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
          }}
        >
          <option value="US">United States</option>
          <option value="CA">Canada</option>
          <option value="UK">United Kingdom</option>
          <option value="AU">Australia</option>
        </select>
      </div>

      {/* Phone */}
      <div>
        <label 
          className="block text-sm font-medium mb-2"
          style={{ color: 'var(--theme-text, #111827)' }}
        >
          Phone Number (Optional)
        </label>
        <input
          type="tel"
          name="phone"
          value={formData.phone}
          onChange={handleInputChange}
          className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
          style={{ 
            borderColor: 'var(--theme-border, #d1d5db)',
            '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
          }}
          placeholder="+****************"
        />
      </div>

      {/* Default Address Checkbox */}
      <div className="flex items-center">
        <input
          type="checkbox"
          name="isDefault"
          checked={formData.isDefault}
          onChange={handleInputChange}
          className="mr-2"
          style={{ accentColor: 'var(--theme-primary, #3b82f6)' }}
        />
        <label 
          className="text-sm"
          style={{ color: 'var(--theme-text, #111827)' }}
        >
          Set as default address
        </label>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4 pt-6">
        <button
          type="button"
          onClick={onCancel}
          className="px-6 py-3 rounded-lg font-medium transition-colors border"
          style={{ 
            borderColor: 'var(--theme-border, #d1d5db)',
            color: 'var(--theme-text, #111827)',
          }}
        >
          Cancel
        </button>
        
        <button
          type="submit"
          className="px-6 py-3 rounded-lg font-semibold transition-all duration-200 hover:shadow-lg"
          style={{ 
            backgroundColor: 'var(--btn-primary, #3b82f6)',
            color: 'var(--btn-text, #ffffff)',
          }}
        >
          {address ? 'Update Address' : 'Save Address'}
        </button>
      </div>
    </form>
  );
};