'use client';

import React, { ReactNode } from 'react';
import { StoreConfigProvider } from '../store/StoreConfigProvider';
import { NavigationProvider } from '../navigation/NavigationProvider';
import { AuthProvider } from '../auth/AuthProvider';
import { StoreAuthProvider } from '../auth/StoreAuthProvider';
// import { CartProvider } from '../cart/CartProvider';
// import { ApiCartProvider } from '../cart/ApiCartProvider';
import { ThemedCartSidebar } from '../cart/ThemedCartSidebar';
import { NavigationLoader } from '../navigation/NavigationLoader';
import { SimpleStoreThemeProvider } from '../providers/SimpleStoreThemeProvider';

interface GlobalProvidersProps {
  children: ReactNode;
  storeHandle: string;
  includeAuth?: boolean;
  includeStoreAuth?: boolean;
  includeCart?: boolean;
}

export const GlobalProviders: React.FC<GlobalProvidersProps> = ({ 
  children, 
  storeHandle,
  includeAuth = false, // Default to false for admin auth
  includeStoreAuth = true, // Default to true for store auth
  includeCart = true,
}) => {
  let content = children;

  // Add cart sidebar without provider (direct API calls)
  if (includeCart) {
    content = (
      <>
        {content}
        <ThemedCartSidebar storeHandle={storeHandle} />
      </>
    );
  }

  // Wrap with StoreAuthProvider if needed (for store customers)
  if (includeStoreAuth) {
    content = <StoreAuthProvider storeHandle={storeHandle}>{content}</StoreAuthProvider>;
  }

  // Wrap with AuthProvider if needed (for admin users)
  if (includeAuth) {
    content = <AuthProvider>{content}</AuthProvider>;
  }

  return (
    <StoreConfigProvider storeHandle={storeHandle}>
      <SimpleStoreThemeProvider storeHandle={storeHandle}>
        <NavigationProvider>
          {content}
          <NavigationLoader />
        </NavigationProvider>
      </SimpleStoreThemeProvider>
    </StoreConfigProvider>
  );
};