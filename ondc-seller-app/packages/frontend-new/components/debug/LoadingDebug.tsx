'use client';

import React from 'react';
import { useGlobalLoading } from '../loading/GlobalLoadingProvider';

export const LoadingDebug: React.FC = () => {
  const { loadingState, stopLoading } = useGlobalLoading();

  if (!loadingState.isLoading) {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      top: 10,
      left: 10,
      background: 'rgba(255, 0, 0, 0.9)',
      color: 'white',
      padding: '12px',
      borderRadius: '8px',
      fontSize: '12px',
      zIndex: 99999,
      fontFamily: 'monospace',
      maxWidth: '300px',
    }}>
      <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>🐛 LOADING DEBUG</div>
      <div>Type: {loadingState.loadingType}</div>
      <div>Message: {loadingState.message}</div>
      <div>SubMessage: {loadingState.subMessage}</div>
      <div>ActionId: {loadingState.actionId}</div>
      <div>Progress: {loadingState.progress}%</div>
      <button 
        onClick={() => stopLoading()}
        style={{
          marginTop: '8px',
          padding: '4px 8px',
          background: 'white',
          color: 'red',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '10px'
        }}
      >
        Force Stop Loading
      </button>
    </div>
  );
};

export default LoadingDebug;