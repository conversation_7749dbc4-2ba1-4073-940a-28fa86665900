'use client';

import React, { useCallback } from 'react';
import { ProductImage } from '@/lib/validations/product';

interface ImageUploadProps {
  images: ProductImage[];
  onImagesChange: (images: ProductImage[]) => void;
  thumbnailId?: string;
  onThumbnailChange?: (imageId: string) => void;
  maxImages?: number;
  label?: string;
  error?: string;
  helperText?: string;
}

export const ImageUpload: React.FC<ImageUploadProps> = ({
  images,
  onImagesChange,
  thumbnailId,
  onThumbnailChange,
  maxImages = 10,
  label,
  error,
  helperText,
}) => {
  const [showUrlInput, setShowUrlInput] = React.useState(false);
  const [imageUrl, setImageUrl] = React.useState('');
  const [urlError, setUrlError] = React.useState('');
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    
    const newImages: ProductImage[] = files.map((file) => ({
      id: Math.random().toString(36).substr(2, 9),
      url: URL.createObjectURL(file),
      alt: file.name,
      file,
    }));

    const updatedImages = [...images, ...newImages].slice(0, maxImages);
    onImagesChange(updatedImages);

    // Set first image as thumbnail if none selected
    if (!thumbnailId && updatedImages.length > 0 && onThumbnailChange) {
      onThumbnailChange(updatedImages[0].id);
    }

    // Reset input
    event.target.value = '';
  }, [images, maxImages, onImagesChange, thumbnailId, onThumbnailChange]);

  const removeImage = (imageId: string) => {
    const updatedImages = images.filter((img) => img.id !== imageId);
    onImagesChange(updatedImages);

    // If removed image was thumbnail, set first remaining image as thumbnail
    if (thumbnailId === imageId && updatedImages.length > 0 && onThumbnailChange) {
      onThumbnailChange(updatedImages[0].id);
    } else if (thumbnailId === imageId && onThumbnailChange) {
      onThumbnailChange('');
    }
  };

  const setThumbnail = (imageId: string) => {
    if (onThumbnailChange) {
      onThumbnailChange(imageId);
    }
  };

  const handleUrlUpload = () => {
    setUrlError('');
    
    if (!imageUrl.trim()) {
      setUrlError('Please enter an image URL');
      return;
    }

    // Basic URL validation
    try {
      new URL(imageUrl);
    } catch {
      setUrlError('Please enter a valid URL');
      return;
    }

    // Check if it's likely an image URL
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'];
    const isImageUrl = imageExtensions.some(ext => 
      imageUrl.toLowerCase().includes(ext)
    ) || imageUrl.includes('unsplash.com') || imageUrl.includes('imgur.com') || imageUrl.includes('cloudinary.com');

    if (!isImageUrl) {
      setUrlError('URL does not appear to be an image. Please check the URL.');
      return;
    }

    const newImage: ProductImage = {
      id: Math.random().toString(36).substr(2, 9),
      url: imageUrl,
      alt: 'Product image from URL',
    };

    const updatedImages = [...images, newImage].slice(0, maxImages);
    onImagesChange(updatedImages);

    // Set first image as thumbnail if none selected
    if (!thumbnailId && updatedImages.length > 0 && onThumbnailChange) {
      onThumbnailChange(updatedImages[0].id);
    }

    // Reset form
    setImageUrl('');
    setShowUrlInput(false);
  };

  const cancelUrlUpload = () => {
    setImageUrl('');
    setUrlError('');
    setShowUrlInput(false);
  };

  return (
    <div>
      {label && (
        <label className={`block text-sm font-medium mb-2 ${
          error ? 'text-red-700' : 'text-gray-700'
        }`}>
          {label}
        </label>
      )}

      {/* Upload Area */}
      {images.length < maxImages && (
        <div className="space-y-4 mb-6">
          {/* File Upload */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center ${
              error ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            <input
              accept="image/*"
              style={{ display: 'none' }}
              id="image-upload-input"
              type="file"
              multiple
              onChange={handleFileUpload}
            />
            <label htmlFor="image-upload-input" className="cursor-pointer">
              <div>
                {/* Cloud Upload Icon */}
                <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Click to upload images
                </h3>
                <p className="text-gray-600 mb-2">
                  Select multiple image files from your device
                </p>
                <p className="text-sm text-gray-500">
                  Supports: JPG, PNG, GIF, WebP (Max {maxImages} images)
                </p>
              </div>
            </label>
          </div>

          {/* Divider */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">or</span>
            </div>
          </div>

          {/* URL Upload */}
          {!showUrlInput ? (
            <div className="text-center">
              <button
                onClick={() => setShowUrlInput(true)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
              >
                {/* Link Icon */}
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
                Add image from URL
              </button>
            </div>
          ) : (
            <div className="border border-gray-300 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-900 mb-3">Add image from URL</h4>
              <div className="space-y-3">
                <div>
                  <input
                    type="url"
                    value={imageUrl}
                    onChange={(e) => setImageUrl(e.target.value)}
                    placeholder="https://example.com/image.jpg"
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      urlError ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  {urlError && (
                    <p className="text-red-600 text-sm mt-1">{urlError}</p>
                  )}
                </div>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={handleUrlUpload}
                    disabled={!imageUrl.trim()}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Add Image
                  </button>
                  <button
                    onClick={cancelUrlUpload}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
                <p className="text-xs text-gray-500">
                  Enter a direct link to an image file (JPG, PNG, GIF, WebP)
                </p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Image Grid */}
      {images.length > 0 && (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
          {images.map((image) => (
            <div
              key={image.id}
              className="relative group bg-white border border-gray-200 rounded-lg overflow-hidden"
            >
              <img
                src={image.url}
                alt={image.alt || 'Product image'}
                className="w-full h-32 object-cover"
              />
              
              {/* Thumbnail indicator */}
              {thumbnailId === image.id && (
                <div className="absolute top-2 left-2 bg-blue-600 text-white rounded-full p-1">
                  {/* Star Icon */}
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                  </svg>
                </div>
              )}

              {/* Action buttons */}
              <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center space-x-2">
                {onThumbnailChange && (
                  <button
                    onClick={() => setThumbnail(image.id)}
                    className={`p-2 rounded-full text-white ${
                      thumbnailId === image.id
                        ? 'bg-blue-600'
                        : 'bg-white bg-opacity-20 hover:bg-white hover:bg-opacity-30'
                    }`}
                  >
                    {thumbnailId === image.id ? (
                      /* Filled Star */
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                      </svg>
                    ) : (
                      /* Star Border */
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                      </svg>
                    )}
                  </button>
                )}
                <button
                  onClick={() => removeImage(image.id)}
                  className="p-2 rounded-full bg-red-600 text-white hover:bg-red-700"
                >
                  {/* Delete Icon */}
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Empty state */}
      {images.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          {/* Image Icon */}
          <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <p className="text-sm">
            No images uploaded yet
          </p>
        </div>
      )}

      {/* Info message */}
      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
        <p className="text-sm text-gray-600">
          💡 You can upload images from your device or add them via URL. Drag & drop functionality will be available after installing react-dropzone.
        </p>
      </div>

      {(error || helperText) && (
        <p className={`text-sm mt-2 ${
          error ? 'text-red-600' : 'text-gray-500'
        }`}>
          {error || helperText}
        </p>
      )}
    </div>
  );
};