'use client';

import React from 'react';

interface RichTextRendererProps {
  content: string | null | undefined;
  className?: string;
  fallback?: React.ReactNode;
  style?: React.CSSProperties;
}

export const RichTextRenderer: React.FC<RichTextRendererProps> = ({
  content,
  className = '',
  fallback = null,
  style = {},
}) => {
  // Return fallback if no content
  if (!content || content.trim() === '') {
    return <>{fallback}</>;
  }

  // Check if content contains HTML tags
  const isRichText = /<[^>]*>/g.test(content);

  if (isRichText) {
    // Basic HTML sanitization - remove script tags and dangerous attributes
    const sanitizedContent = content
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');

    return (
      <div
        className={`rich-text-content ${className}`}
        dangerouslySetInnerHTML={{ __html: sanitizedContent }}
        style={{
          // Rich text styling
          lineHeight: '1.6',
          color: 'var(--theme-text, #111827)',
          ...style,
        }}
      />
    );
  } else {
    // Render as plain text with line breaks preserved
    return (
      <div
        className={`plain-text-content ${className}`}
        style={{
          whiteSpace: 'pre-wrap',
          lineHeight: '1.6',
          color: 'var(--theme-text, #111827)',
          ...style,
        }}
      >
        {content}
      </div>
    );
  }
};

export default RichTextRenderer;