'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useGlobalLoading } from '../loading/GlobalLoadingProvider';

interface ActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void | Promise<void>;
  href?: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  loadingText?: string;
  loadingType?: 'button' | 'page' | 'navigation' | 'backdrop';
  className?: string;
  style?: React.CSSProperties;
  type?: 'button' | 'submit' | 'reset';
  actionId?: string;
  showGlobalLoader?: boolean;
}

export const ActionButton: React.FC<ActionButtonProps> = ({
  children,
  onClick,
  href,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  loadingText,
  loadingType = 'button',
  className = '',
  style = {},
  type = 'button',
  actionId,
  showGlobalLoader = true,
  ...props
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const { startLoading, stopLoading, isActionLoading } = useGlobalLoading();
  const [internalLoading, setInternalLoading] = useState(false);
  const [lastNavigationActionId, setLastNavigationActionId] = useState<string | null>(null);

  // Stop navigation loading when pathname changes
  useEffect(() => {
    if (lastNavigationActionId) {
      console.log('🎯 Pathname changed, stopping navigation loading:', lastNavigationActionId);
      stopLoading(lastNavigationActionId);
      setLastNavigationActionId(null);
    }
  }, [pathname, lastNavigationActionId, stopLoading]);

  const isLoading = loading || internalLoading || (actionId ? isActionLoading(actionId) : false);

  const getVariantStyles = () => {
    const baseStyles = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
    
    switch (variant) {
      case 'primary':
        return `${baseStyles} text-white shadow-sm hover:shadow-md focus:ring-blue-500 ${
          isLoading || disabled 
            ? 'bg-gray-400 cursor-not-allowed' 
            : 'bg-blue-600 hover:bg-blue-700'
        }`;
      case 'secondary':
        return `${baseStyles} text-gray-700 bg-gray-100 border border-gray-300 shadow-sm hover:bg-gray-200 focus:ring-gray-500 ${
          isLoading || disabled 
            ? 'opacity-50 cursor-not-allowed' 
            : ''
        }`;
      case 'outline':
        return `${baseStyles} text-blue-600 bg-transparent border border-blue-600 hover:bg-blue-50 focus:ring-blue-500 ${
          isLoading || disabled 
            ? 'opacity-50 cursor-not-allowed' 
            : ''
        }`;
      case 'ghost':
        return `${baseStyles} text-gray-600 bg-transparent hover:bg-gray-100 focus:ring-gray-500 ${
          isLoading || disabled 
            ? 'opacity-50 cursor-not-allowed' 
            : ''
        }`;
      default:
        return baseStyles;
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-2 text-sm';
      case 'md':
        return 'px-4 py-2 text-sm';
      case 'lg':
        return 'px-6 py-3 text-base';
      default:
        return 'px-4 py-2 text-sm';
    }
  };

  const handleClick = async () => {
    if (isLoading || disabled) return;

    const currentActionId = actionId || `action-${Date.now()}`;

    try {
      // Handle navigation
      if (href) {
        if (showGlobalLoader && loadingType === 'navigation') {
          startLoading('navigation', loadingText || 'Navigating...', {
            actionId: currentActionId,
            subMessage: 'Loading page content'
          });
          
          // Add a small delay to show the loading state
          await new Promise(resolve => setTimeout(resolve, 100));
          
          // Navigate and stop loading after a reasonable time
          router.push(href);
          
          // Track this navigation action ID for cleanup on pathname change
          setLastNavigationActionId(currentActionId);
          
          // Fallback: Stop loading after a reasonable time if pathname doesn't change
          setTimeout(() => {
            if (lastNavigationActionId === currentActionId) {
              console.log('⏰ Navigation timeout, stopping loading:', currentActionId);
              stopLoading(currentActionId);
              setLastNavigationActionId(null);
            }
          }, 2000); // 2 second fallback timeout
          
          return;
        } else {
          // For non-navigation loading types or when global loader is disabled
          router.push(href);
          return;
        }
      }

      // Start loading state for non-navigation actions
      if (showGlobalLoader && loadingType !== 'button') {
        startLoading(loadingType, loadingText || 'Processing...', {
          actionId: currentActionId,
          subMessage: 'Please wait while we process your request'
        });
      } else {
        setInternalLoading(true);
      }

      // Handle onClick function
      if (onClick) {
        await onClick();
      }

    } catch (error) {
      console.error('Action button error:', error);
      // Always stop loading on error
      if (showGlobalLoader) {
        stopLoading(currentActionId);
      } else {
        setInternalLoading(false);
      }
    } finally {
      // Stop loading state for non-navigation actions
      if (!href && showGlobalLoader && loadingType !== 'button') {
        setTimeout(() => stopLoading(currentActionId), 100);
      } else if (!href) {
        setInternalLoading(false);
      }
    }
  };

  const buttonContent = (
    <>
      {isLoading && (
        <svg 
          className="animate-spin -ml-1 mr-2 h-4 w-4" 
          xmlns="http://www.w3.org/2000/svg" 
          fill="none" 
          viewBox="0 0 24 24"
        >
          <circle 
            className="opacity-25" 
            cx="12" 
            cy="12" 
            r="10" 
            stroke="currentColor" 
            strokeWidth="4"
          />
          <path 
            className="opacity-75" 
            fill="currentColor" 
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      {isLoading && loadingText ? loadingText : children}
    </>
  );

  return (
    <button
      type={type}
      onClick={handleClick}
      disabled={isLoading || disabled}
      className={`${getVariantStyles()} ${getSizeStyles()} ${className}`}
      style={style}
      aria-busy={isLoading}
      aria-disabled={isLoading || disabled}
      {...props}
    >
      {buttonContent}
    </button>
  );
};