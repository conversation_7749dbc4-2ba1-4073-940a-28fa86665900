'use client';

import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Paper,
  Toolbar,
  IconButton,
  Divider,
  FormHelperText,
  ButtonGroup,
  Tooltip
} from '@mui/material';
import {
  FormatBold,
  FormatItalic,
  FormatUnderlined,
  FormatListBulleted,
  FormatListNumbered,
  Undo,
  Redo,
  Title,
  Subject
} from '@mui/icons-material';

interface SimpleLexicalEditorProps {
  value: string;
  onChange: (value: string) => void;
  error?: boolean;
  helperText?: string;
  placeholder?: string;
}

export const SimpleLexicalEditor: React.FC<SimpleLexicalEditorProps> = ({
  value,
  onChange,
  error = false,
  helperText,
  placeholder = 'Start writing your content here...'
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isEditorFocused, setIsEditorFocused] = useState(false);

  useEffect(() => {
    if (editorRef.current && editorRef.current.innerHTML !== value) {
      editorRef.current.innerHTML = value;
    }
  }, [value]);

  const handleInput = () => {
    if (editorRef.current) {
      const content = editorRef.current.innerHTML;
      onChange(content);
    }
  };

  const execCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    if (editorRef.current) {
      editorRef.current.focus();
      handleInput();
    }
  };

  const insertHeading = (level: number) => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const heading = document.createElement(`h${level}`);
      heading.textContent = 'Heading';
      
      try {
        range.deleteContents();
        range.insertNode(heading);
        
        const newRange = document.createRange();
        newRange.setStartAfter(heading);
        newRange.collapse(true);
        selection.removeAllRanges();
        selection.addRange(newRange);
        
        handleInput();
      } catch (e) {
        console.warn('Could not insert heading:', e);
      }
    }
  };

  const insertList = (type: 'ul' | 'ol') => {
    execCommand(type === 'ul' ? 'insertUnorderedList' : 'insertOrderedList');
  };

  const formatText = (command: string) => {
    execCommand(command);
  };

  const showPlaceholder = !value || value.trim() === '';

  return (
    <Box>
      <Paper
        variant="outlined"
        sx={{
          borderColor: error ? 'error.main' : 'divider',
          borderRadius: 1,
          overflow: 'hidden'
        }}
      >
        <Toolbar
          sx={{
            borderBottom: '1px solid',
            borderColor: 'divider',
            p: 1,
            gap: 1,
            flexWrap: 'wrap',
            minHeight: 'auto',
            backgroundColor: 'grey.50'
          }}
        >
          <ButtonGroup size="small">
            <Tooltip title="Heading 1">
              <IconButton
                onClick={() => insertHeading(1)}
                size="small"
              >
                <Title />
              </IconButton>
            </Tooltip>
            <Tooltip title="Heading 2">
              <IconButton
                onClick={() => insertHeading(2)}
                size="small"
              >
                <Subject />
              </IconButton>
            </Tooltip>
          </ButtonGroup>

          <Divider orientation="vertical" flexItem />

          <ButtonGroup size="small">
            <Tooltip title="Bold">
              <IconButton
                onClick={() => formatText('bold')}
                size="small"
              >
                <FormatBold />
              </IconButton>
            </Tooltip>
            <Tooltip title="Italic">
              <IconButton
                onClick={() => formatText('italic')}
                size="small"
              >
                <FormatItalic />
              </IconButton>
            </Tooltip>
            <Tooltip title="Underline">
              <IconButton
                onClick={() => formatText('underline')}
                size="small"
              >
                <FormatUnderlined />
              </IconButton>
            </Tooltip>
          </ButtonGroup>

          <Divider orientation="vertical" flexItem />

          <ButtonGroup size="small">
            <Tooltip title="Bullet List">
              <IconButton
                onClick={() => insertList('ul')}
                size="small"
              >
                <FormatListBulleted />
              </IconButton>
            </Tooltip>
            <Tooltip title="Numbered List">
              <IconButton
                onClick={() => insertList('ol')}
                size="small"
              >
                <FormatListNumbered />
              </IconButton>
            </Tooltip>
          </ButtonGroup>

          <Divider orientation="vertical" flexItem />

          <ButtonGroup size="small">
            <Tooltip title="Undo">
              <IconButton
                onClick={() => execCommand('undo')}
                size="small"
              >
                <Undo />
              </IconButton>
            </Tooltip>
            <Tooltip title="Redo">
              <IconButton
                onClick={() => execCommand('redo')}
                size="small"
              >
                <Redo />
              </IconButton>
            </Tooltip>
          </ButtonGroup>
        </Toolbar>

        <Box sx={{ position: 'relative' }}>
          {showPlaceholder && (
            <Box
              sx={{
                position: 'absolute',
                top: 16,
                left: 16,
                color: 'text.secondary',
                fontStyle: 'italic',
                pointerEvents: 'none',
                fontSize: '14px',
                zIndex: 1
              }}
            >
              {placeholder}
            </Box>
          )}
          
          <Box
            ref={editorRef}
            contentEditable
            suppressContentEditableWarning
            onInput={handleInput}
            onFocus={() => setIsEditorFocused(true)}
            onBlur={() => setIsEditorFocused(false)}
            sx={{
              minHeight: '200px',
              padding: '16px',
              outline: 'none',
              fontSize: '14px',
              lineHeight: '1.6',
              fontFamily: 'inherit',
              position: 'relative',
              zIndex: 2,
              '& h1': {
                fontSize: '2rem',
                fontWeight: 'bold',
                margin: '16px 0 8px 0',
                lineHeight: 1.2
              },
              '& h2': {
                fontSize: '1.5rem',
                fontWeight: 'bold',
                margin: '14px 0 6px 0',
                lineHeight: 1.3
              },
              '& h3': {
                fontSize: '1.25rem',
                fontWeight: 'bold',
                margin: '12px 0 4px 0',
                lineHeight: 1.4
              },
              '& p': {
                margin: '8px 0',
                lineHeight: 1.6
              },
              '& ul, & ol': {
                margin: '8px 0',
                paddingLeft: '24px'
              },
              '& li': {
                margin: '4px 0',
                lineHeight: 1.5
              },
              '& strong': {
                fontWeight: 'bold'
              },
              '& em': {
                fontStyle: 'italic'
              },
              '& u': {
                textDecoration: 'underline'
              }
            }}
          />
        </Box>
      </Paper>
      
      {helperText && (
        <FormHelperText error={error} sx={{ mt: 1 }}>
          {helperText}
        </FormHelperText>
      )}
      
      <Box sx={{ mt: 1 }}>
        <FormHelperText>
          Tip: Use the toolbar above to format your text with headings, bold, italic, underline, and lists
        </FormHelperText>
      </Box>
    </Box>
  );
};

export default SimpleLexicalEditor;