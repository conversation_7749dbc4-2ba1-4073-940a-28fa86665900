'use client';

import React, { useMemo, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { Box, Typography, FormHelperText } from '@mui/material';

// Dynamically import ReactQuill to avoid SSR issues
const ReactQuill = dynamic(
  async () => {
    try {
      // Import Quill CSS
      await import('react-quill/dist/quill.snow.css');
      const module = await import('react-quill');
      
      // Check if module and default export exist
      if (!module || !module.default) {
        console.error('ReactQuill module or default export is null');
        throw new Error('ReactQuill module failed to load');
      }
      
      return module.default;
    } catch (error) {
      console.error('Error loading ReactQuill:', error);
      // Return a fallback component
      return ({ value, onChange, placeholder }: any) => (
        <textarea
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className="w-full h-32 p-3 border border-gray-300 rounded-md resize-none"
        />
      );
    }
  },
  { 
    ssr: false,
    loading: () => <div className="h-32 bg-gray-100 animate-pulse rounded" />
  }
);

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  label?: string;
  error?: string;
  helperText?: string;
  height?: number;
}

export const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = 'Enter text...',
  label,
  error,
  helperText,
  height = 200,
}) => {
  const modules = useMemo(() => ({
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      ['link'],
      [{ 'align': [] }],
      [{ 'color': [] }, { 'background': [] }],
      ['clean']
    ],
  }), []);

  const formats = [
    'header',
    'bold', 'italic', 'underline', 'strike',
    'list', 'bullet', 'indent',
    'link', 'align', 'color', 'background'
  ];

  return (
    <Box>
      {label && (
        <Typography 
          variant="body2" 
          component="label" 
          sx={{ 
            display: 'block', 
            mb: 1, 
            fontWeight: 500,
            color: error ? 'error.main' : 'text.primary'
          }}
        >
          {label}
        </Typography>
      )}
      
      <Box
        sx={{
          '& .ql-container': {
            minHeight: `${height}px`,
            fontSize: '14px',
            fontFamily: 'inherit',
          },
          '& .ql-toolbar': {
            borderTop: error ? '1px solid' : '1px solid #e0e0e0',
            borderLeft: error ? '1px solid' : '1px solid #e0e0e0',
            borderRight: error ? '1px solid' : '1px solid #e0e0e0',
            borderTopLeftRadius: '4px',
            borderTopRightRadius: '4px',
            borderColor: error ? 'error.main' : '#e0e0e0',
          },
          '& .ql-container': {
            borderBottom: error ? '1px solid' : '1px solid #e0e0e0',
            borderLeft: error ? '1px solid' : '1px solid #e0e0e0',
            borderRight: error ? '1px solid' : '1px solid #e0e0e0',
            borderBottomLeftRadius: '4px',
            borderBottomRightRadius: '4px',
            borderColor: error ? 'error.main' : '#e0e0e0',
          },
          '& .ql-editor': {
            minHeight: `${height}px`,
            fontSize: '14px',
            lineHeight: 1.5,
          },
          '& .ql-editor.ql-blank::before': {
            color: '#9e9e9e',
            fontStyle: 'normal',
            content: `"${placeholder}"`,
          },
        }}
      >
        <ReactQuill
          value={value}
          onChange={onChange}
          modules={modules}
          formats={formats}
          theme="snow"
        />
      </Box>
      
      {(error || helperText) && (
        <FormHelperText error={!!error} sx={{ mt: 1 }}>
          {error || helperText}
        </FormHelperText>
      )}
    </Box>
  );
};