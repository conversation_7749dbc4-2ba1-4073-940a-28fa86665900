'use client';

import React, { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import {
  Box,
  Typography,
  IconButton,
  Grid,
  Card,
  CardMedia,
  CardActions,
  Button,
  FormHelperText,
} from '@mui/material';
import {
  CloudUpload,
  Delete,
  Image as ImageIcon,
  Star,
  StarBorder,
} from '@mui/icons-material';
import { ProductImage } from '@/lib/validations/product';

interface ImageUploadProps {
  images: ProductImage[];
  onImagesChange: (images: ProductImage[]) => void;
  thumbnailId?: string;
  onThumbnailChange?: (imageId: string) => void;
  maxImages?: number;
  label?: string;
  error?: string;
  helperText?: string;
}

export const ImageUpload: React.FC<ImageUploadProps> = ({
  images,
  onImagesChange,
  thumbnailId,
  onThumbnailChange,
  maxImages = 10,
  label,
  error,
  helperText,
}) => {
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newImages: ProductImage[] = acceptedFiles.map((file) => ({
      id: Math.random().toString(36).substr(2, 9),
      url: URL.createObjectURL(file),
      alt: file.name,
      file,
    }));

    const updatedImages = [...images, ...newImages].slice(0, maxImages);
    onImagesChange(updatedImages);

    // Set first image as thumbnail if none selected
    if (!thumbnailId && updatedImages.length > 0 && onThumbnailChange) {
      onThumbnailChange(updatedImages[0].id);
    }
  }, [images, maxImages, onImagesChange, thumbnailId, onThumbnailChange]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp'],
    },
    maxFiles: maxImages - images.length,
    disabled: images.length >= maxImages,
  });

  const removeImage = (imageId: string) => {
    const updatedImages = images.filter((img) => img.id !== imageId);
    onImagesChange(updatedImages);

    // If removed image was thumbnail, set first remaining image as thumbnail
    if (thumbnailId === imageId && updatedImages.length > 0 && onThumbnailChange) {
      onThumbnailChange(updatedImages[0].id);
    } else if (thumbnailId === imageId && onThumbnailChange) {
      onThumbnailChange('');
    }
  };

  const setThumbnail = (imageId: string) => {
    if (onThumbnailChange) {
      onThumbnailChange(imageId);
    }
  };

  return (
    <Box>
      {label && (
        <Typography 
          variant="body2" 
          component="label" 
          sx={{ 
            display: 'block', 
            mb: 2, 
            fontWeight: 500,
            color: error ? 'error.main' : 'text.primary'
          }}
        >
          {label}
        </Typography>
      )}

      {/* Upload Area */}
      {images.length < maxImages && (
        <Box
          {...getRootProps()}
          sx={{
            border: `2px dashed ${error ? 'error.main' : isDragActive ? 'primary.main' : '#e0e0e0'}`,
            borderRadius: 2,
            p: 4,
            textAlign: 'center',
            cursor: 'pointer',
            backgroundColor: isDragActive ? 'action.hover' : 'background.paper',
            transition: 'all 0.2s ease',
            mb: 2,
            '&:hover': {
              backgroundColor: 'action.hover',
              borderColor: 'primary.main',
            },
          }}
        >
          <input {...getInputProps()} />
          <CloudUpload sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            {isDragActive ? 'Drop images here' : 'Drag & drop images here'}
          </Typography>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            or click to select files
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Supports: JPG, PNG, GIF, WebP (Max {maxImages} images)
          </Typography>
        </Box>
      )}

      {/* Image Grid */}
      {images.length > 0 && (
        <Grid container spacing={2}>
          {images.map((image) => (
            <Grid item xs={6} sm={4} md={3} key={image.id}>
              <Card
                sx={{
                  position: 'relative',
                  '&:hover .image-actions': {
                    opacity: 1,
                  },
                }}
              >
                <CardMedia
                  component="img"
                  height="120"
                  image={image.url}
                  alt={image.alt || 'Product image'}
                  sx={{ objectFit: 'cover' }}
                />
                
                {/* Thumbnail indicator */}
                {thumbnailId === image.id && (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 8,
                      left: 8,
                      backgroundColor: 'primary.main',
                      color: 'white',
                      borderRadius: '50%',
                      p: 0.5,
                    }}
                  >
                    <Star sx={{ fontSize: 16 }} />
                  </Box>
                )}

                <CardActions
                  className="image-actions"
                  sx={{
                    position: 'absolute',
                    top: 0,
                    right: 0,
                    left: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    opacity: 0,
                    transition: 'opacity 0.2s ease',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  {onThumbnailChange && (
                    <IconButton
                      size="small"
                      onClick={() => setThumbnail(image.id)}
                      sx={{ 
                        color: 'white',
                        backgroundColor: thumbnailId === image.id ? 'primary.main' : 'rgba(255, 255, 255, 0.2)',
                      }}
                    >
                      {thumbnailId === image.id ? <Star /> : <StarBorder />}
                    </IconButton>
                  )}
                  <IconButton
                    size="small"
                    onClick={() => removeImage(image.id)}
                    sx={{ color: 'white', backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
                  >
                    <Delete />
                  </IconButton>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Empty state */}
      {images.length === 0 && (
        <Box
          sx={{
            textAlign: 'center',
            py: 4,
            color: 'text.secondary',
          }}
        >
          <ImageIcon sx={{ fontSize: 48, mb: 2 }} />
          <Typography variant="body2">
            No images uploaded yet
          </Typography>
        </Box>
      )}

      {(error || helperText) && (
        <FormHelperText error={!!error} sx={{ mt: 1 }}>
          {error || helperText}
        </FormHelperText>
      )}
    </Box>
  );
};