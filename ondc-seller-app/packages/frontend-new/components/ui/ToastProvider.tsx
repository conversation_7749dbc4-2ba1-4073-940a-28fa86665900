"use client";

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  ReactNode,
} from "react";
import { Snackbar, Alert, AlertColor, SnackbarOrigin } from "@mui/material";

type ToastOptions = {
  duration?: number;
  position?: SnackbarOrigin; // { vertical: 'top' | 'bottom'; horizontal: 'left' | 'right' | 'center' }
};

type ToastContextType = {
  success: (msg: string, options?: ToastOptions) => void;
  error: (msg: string, options?: ToastOptions) => void;
  warning: (msg: string, options?: ToastOptions) => void;
  info: (msg: string, options?: ToastOptions) => void;
};

const ToastContext = createContext<ToastContextType | undefined>(undefined);

type ToastState = {
  open: boolean;
  message: string;
  severity: AlertColor;
  duration: number;
  position: SnackbarOrigin;
};

export const ToastProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [toast, setToast] = useState<ToastState>({
    open: false,
    message: "",
    severity: "success",
    duration: 3000,
    position: { vertical: "top", horizontal: "right" },
  });

  const show = useCallback(
    (message: string, severity: AlertColor, options?: ToastOptions) => {
      setToast({
        open: true,
        message,
        severity,
        duration: options?.duration ?? 3000,
        position: options?.position ?? {
          vertical: "top",
          horizontal: "right",
        },
      });
    },
    [],
  );

  const handleClose = () => {
    setToast((prev) => ({ ...prev, open: false }));
  };

  const toastApi: ToastContextType = {
    success: (msg, opts) => show(msg, "success", opts),
    error: (msg, opts) => show(msg, "error", opts),
    warning: (msg, opts) => show(msg, "warning", opts),
    info: (msg, opts) => show(msg, "info", opts),
  };

  return (
    <ToastContext.Provider value={toastApi}>
      {children}
      <Snackbar
        open={toast.open}
        autoHideDuration={toast.duration}
        onClose={handleClose}
        anchorOrigin={toast.position}
      >
        <Alert
          onClose={handleClose}
          severity={toast.severity}
          variant="filled"
          sx={{
            width: "100%",
            backgroundColor:
              toast.severity === "success"
                ? "#28a745" // green
                : toast.severity === "error"
                  ? "#dc3545" // red
                  : toast.severity === "warning"
                    ? "#ffc107" // yellow
                    : toast.severity === "info"
                      ? "#17a2b8" // blue
                      : undefined,
            color: "#fff", // text color
          }}
        >
          {toast.message}
        </Alert>
      </Snackbar>
    </ToastContext.Provider>
  );
};

export const useToast = (): ToastContextType => {
  const context = useContext(ToastContext);
  if (!context) {
    // console.error('useToast must be used within a ToastProvider');
    throw new Error("useToast must be used within a ToastProvider");
  }
  return context;
};
