'use client';

import React from 'react';
import { Box, Typography, FormHelperText, TextField } from '@mui/material';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  label?: string;
  error?: string;
  helperText?: string;
  height?: number;
}

export const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = 'Enter text...',
  label,
  error,
  helperText,
  height = 200,
}) => {
  return (
    <Box>
      {label && (
        <Typography 
          variant="body2" 
          component="label" 
          sx={{ 
            display: 'block', 
            mb: 1, 
            fontWeight: 500,
            color: error ? 'error.main' : 'text.primary'
          }}
        >
          {label}
        </Typography>
      )}
      
      <TextField
        fullWidth
        multiline
        rows={Math.floor(height / 25)} // Approximate rows based on height
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        error={!!error}
        helperText={error || helperText}
        sx={{
          '& .MuiInputBase-root': {
            minHeight: `${height}px`,
            alignItems: 'flex-start',
          },
          '& .MuiInputBase-input': {
            fontSize: '14px',
            lineHeight: 1.5,
          },
        }}
      />
      
      <Box sx={{ mt: 1, p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>
        <Typography variant="caption" color="text.secondary">
          💡 Rich text editor will be available after installing dependencies. Currently using basic text area.
        </Typography>
      </Box>
    </Box>
  );
};