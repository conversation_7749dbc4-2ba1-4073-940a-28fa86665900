'use client';

import React from 'react';
import { useNavigationLoader } from '@/hooks/useNavigationLoader';
import { LoadingSpinner } from './LoadingSpinner';

export const NavigationLoader: React.FC = () => {
  const isLoading = useNavigationLoader();

  if (!isLoading) return null;

  return (
    <div className="fixed inset-0 z-[99999] flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-opacity-80 backdrop-blur-sm transition-opacity duration-200"
        style={{ backgroundColor: 'var(--theme-background, #ffffff)' }}
      />
      
      {/* Loading Content */}
      <div className="relative z-10 flex flex-col items-center space-y-4 animate-fade-in">
        <div 
          className="p-6 rounded-lg shadow-lg border"
          style={{ 
            backgroundColor: 'var(--theme-surface, #ffffff)',
            borderColor: 'var(--theme-border, #e5e7eb)',
          }}
        >
          <div className="flex flex-col items-center space-y-4">
            <LoadingSpinner size="lg" />
            <div className="text-center">
              <p 
                className="text-sm font-medium"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                Loading...
              </p>
              <p 
                className="text-xs mt-1 animate-pulse"
                style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
              >
                Please wait while we load the page
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};