'use client';

import React from 'react';

interface LoadingButtonProps {
  children: React.ReactNode;
  onClick?: () => void | Promise<void>;
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  style?: React.CSSProperties;
  type?: 'button' | 'submit' | 'reset';
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
}

export const LoadingButton: React.FC<LoadingButtonProps> = ({
  children,
  onClick,
  disabled = false,
  loading = false,
  className = '',
  style = {},
  type = 'button',
  variant = 'primary',
  size = 'md',
}) => {
  const [isLoading, setIsLoading] = React.useState(false);

  const handleClick = async () => {
    if (onClick && !isLoading && !disabled) {
      setIsLoading(true);
      try {
        await onClick();
      } catch (error) {
        console.error('Button action failed:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const isButtonLoading = loading || isLoading;
  const isButtonDisabled = disabled || isButtonLoading;

  // Size classes
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
  };

  // Variant classes
  const variantClasses = {
    primary: 'text-white font-semibold',
    secondary: 'border font-medium',
    outline: 'border-2 font-medium bg-transparent',
  };

  // Base button styles
  const baseStyles: React.CSSProperties = {
    backgroundColor: variant === 'primary' ? 'var(--btn-primary, #3b82f6)' : 
                    variant === 'secondary' ? 'var(--theme-surface, #f3f4f6)' : 
                    'transparent',
    color: variant === 'primary' ? 'var(--btn-text, #ffffff)' : 
           variant === 'secondary' ? 'var(--theme-text, #111827)' : 
           'var(--theme-primary, #3b82f6)',
    borderColor: variant === 'outline' ? 'var(--theme-primary, #3b82f6)' : 
                 variant === 'secondary' ? 'var(--theme-border, #d1d5db)' : 
                 'transparent',
    opacity: isButtonDisabled ? 0.6 : 1,
    cursor: isButtonDisabled ? 'not-allowed' : 'pointer',
    ...style,
  };

  return (
    <button
      type={type}
      onClick={handleClick}
      disabled={isButtonDisabled}
      className={`
        inline-flex items-center justify-center
        rounded-lg transition-all duration-200
        focus:outline-none focus:ring-2 focus:ring-offset-2
        ${sizeClasses[size]}
        ${variantClasses[variant]}
        ${!isButtonDisabled && variant === 'primary' ? 'hover:shadow-lg hover:scale-105' : ''}
        ${!isButtonDisabled && variant !== 'primary' ? 'hover:opacity-80' : ''}
        ${className}
      `}
      style={baseStyles}
    >
      {isButtonLoading && (
        <svg
          className={`animate-spin -ml-1 mr-2 h-4 w-4 ${size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4'}`}
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      {isButtonLoading ? 'Loading...' : children}
    </button>
  );
};