'use client';

import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Grid,
  Typography,
  Divider,
  Alert,
  Paper,
  Stack,
  InputAdornment,
} from '@mui/material';
import {
  Inventory as InventoryIcon,
  AttachMoney as AttachMoneyIcon,
  Category as CategoryIcon,
  Description as DescriptionIcon,
  Tag as TagIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import Mui<PERSON><PERSON><PERSON><PERSON> from '../admin/MuiFormField';

interface ProductData {
  name: string;
  price: string;
  description: string;
  category: string;
  sku?: string;
  inventory_quantity: string;
}

interface AddProductStepProps {
  data: ProductData;
  updateData: (field: keyof ProductData, value: any) => void;
}

const productCategories = [
  { value: 'fashion', label: 'Fashion & Apparel' },
  { value: 'electronics', label: 'Electronics' },
  { value: 'home_garden', label: 'Home & Garden' },
  { value: 'health_beauty', label: 'Health & Beauty' },
  { value: 'sports_outdoors', label: 'Sports & Outdoors' },
  { value: 'books_media', label: 'Books & Media' },
  { value: 'food_beverages', label: 'Food & Beverages' },
  { value: 'automotive', label: 'Automotive' },
  { value: 'toys_games', label: 'Toys & Games' },
  { value: 'jewelry_accessories', label: 'Jewelry & Accessories' },
  { value: 'other', label: 'Other' },
];

const AddProductStep: React.FC<AddProductStepProps> = ({
  data,
  updateData,
}) => {
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    updateData(name as keyof ProductData, value);
  };

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Header */}
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <InventoryIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
        <Typography variant='h4' component='h2' fontWeight='bold' gutterBottom>
          Add Your First Product
        </Typography>
        <Typography
          variant='body1'
          color='text.secondary'
          sx={{ maxWidth: 600, mx: 'auto' }}
        >
          Start by adding one product to your store. You can add more products
          later or use bulk upload in the next step.
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Basic Product Information */}
        <Grid size={12}>
          <Card sx={{ borderRadius: 3, boxShadow: 4 }}>
            <CardContent sx={{ p: 3 }}>
              <Stack
                direction='row'
                alignItems='center'
                spacing={1}
                sx={{ mb: 3 }}
              >
                <DescriptionIcon color='primary' />
                <Typography variant='h6' fontWeight='bold'>
                  Product Details
                </Typography>
              </Stack>
              <Divider sx={{ mb: 3 }} />

              <Grid container spacing={3}>
                <Grid size={{ xs: 12, md: 6 }}>
                  <MuiFormField
                    label='Product Name'
                    name='name'
                    value={data.name}
                    onChange={handleInputChange}
                    placeholder='Enter product name'
                    required
                    help='This will be displayed to customers'
                  />
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <MuiFormField
                    label='SKU'
                    name='sku'
                    value={data.sku || ''}
                    onChange={handleInputChange}
                    placeholder='Enter SKU (e.g., PROD-001)'
                    help='Stock Keeping Unit - unique identifier'
                    startAdornment={
                      <InputAdornment position='start'>
                        <TagIcon />
                      </InputAdornment>
                    }
                  />
                </Grid>
                <Grid size={12}>
                  <MuiFormField
                    label='Product Description'
                    name='description'
                    type='textarea'
                    rows={4}
                    value={data.description}
                    onChange={handleInputChange}
                    placeholder='Describe your product, its features, and benefits'
                    required
                    help='This will appear in product listings'
                  />
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <MuiFormField
                    label='Category'
                    name='category'
                    type='select'
                    value={data.category}
                    onChange={handleInputChange}
                    options={productCategories}
                    required
                    help='Select the most appropriate category'
                    startAdornment={
                      <InputAdornment position='start'>
                        <CategoryIcon />
                      </InputAdornment>
                    }
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Pricing and Inventory */}
        <Grid size={12}>
          <Card sx={{ borderRadius: 3, boxShadow: 4 }}>
            <CardContent sx={{ p: 3 }}>
              <Stack
                direction='row'
                alignItems='center'
                spacing={1}
                sx={{ mb: 3 }}
              >
                <AttachMoneyIcon color='primary' />
                <Typography variant='h6' fontWeight='bold'>
                  Pricing & Inventory
                </Typography>
              </Stack>
              <Divider sx={{ mb: 3 }} />

              <Grid container spacing={3}>
                <Grid size={{ xs: 12, md: 6 }}>
                  <MuiFormField
                    label='Price (₹)'
                    name='price'
                    type='number'
                    value={data.price}
                    onChange={handleInputChange}
                    placeholder='0.00'
                    required
                    help='Selling price for customers'
                    startAdornment={
                      <InputAdornment position='start'>
                        <AttachMoneyIcon />
                      </InputAdornment>
                    }
                  />
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <MuiFormField
                    label='Inventory Quantity'
                    name='inventory_quantity'
                    type='number'
                    value={data.inventory_quantity}
                    onChange={handleInputChange}
                    placeholder='0'
                    required
                    help='Available stock quantity'
                    startAdornment={
                      <InputAdornment position='start'>
                        <InventoryIcon />
                      </InputAdornment>
                    }
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Product Preview */}
        <Grid size={12}>
          <Card sx={{ borderRadius: 3, boxShadow: 4, bgcolor: 'primary.50' }}>
            <CardContent sx={{ p: 3 }}>
              <Stack
                direction='row'
                alignItems='center'
                spacing={1}
                sx={{ mb: 3 }}
              >
                <InfoIcon color='primary' />
                <Typography variant='h6' fontWeight='bold'>
                  Product Preview
                </Typography>
              </Stack>
              <Divider sx={{ mb: 3 }} />

              <Paper sx={{ p: 3, bgcolor: 'background.paper' }}>
                <Stack direction='row' spacing={2}>
                  <Box
                    sx={{
                      width: 64,
                      height: 64,
                      bgcolor: 'grey.200',
                      borderRadius: 2,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <InventoryIcon sx={{ fontSize: 32, color: 'grey.400' }} />
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant='h6' fontWeight='bold' gutterBottom>
                      {data.name || 'Product Name'}
                    </Typography>
                    <Typography
                      variant='body2'
                      color='text.secondary'
                      sx={{ mb: 2 }}
                    >
                      {data.description ||
                        'Product description will appear here...'}
                    </Typography>
                    <Stack
                      direction='row'
                      justifyContent='space-between'
                      alignItems='center'
                      sx={{ mb: 2 }}
                    >
                      <Typography
                        variant='h6'
                        color='success.main'
                        fontWeight='bold'
                      >
                        ₹{data.price || '0.00'}
                      </Typography>
                      <Typography variant='body2' color='text.secondary'>
                        {data.inventory_quantity || '0'} in stock
                      </Typography>
                    </Stack>
                    {data.category && (
                      <Box sx={{ mb: 1 }}>
                        <Typography
                          variant='caption'
                          sx={{
                            px: 1,
                            py: 0.5,
                            bgcolor: 'primary.100',
                            color: 'primary.800',
                            borderRadius: 1,
                            fontWeight: 'medium',
                          }}
                        >
                          {productCategories.find(
                            cat => cat.value === data.category
                          )?.label || data.category}
                        </Typography>
                      </Box>
                    )}
                    {data.sku && (
                      <Typography variant='caption' color='text.secondary'>
                        SKU: {data.sku}
                      </Typography>
                    )}
                  </Box>
                </Stack>
              </Paper>
            </CardContent>
          </Card>
        </Grid>

        {/* Helpful Tip */}
        <Grid size={12}>
          <Alert severity='info' sx={{ borderRadius: 2 }}>
            <Typography variant='subtitle2' fontWeight='bold' gutterBottom>
              Tip: Start Simple
            </Typography>
            <Typography variant='body2'>
              Don't worry about making this perfect. You can always edit product
              details, add images, and update pricing later from your admin
              dashboard.
            </Typography>
          </Alert>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AddProductStep;
