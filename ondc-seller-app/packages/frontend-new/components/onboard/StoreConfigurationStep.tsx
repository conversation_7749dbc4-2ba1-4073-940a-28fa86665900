'use client';

import React, { useState } from 'react';
import {
  Box,
  Grid,
  TextField,
  Typography,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  OutlinedInput,
  FormControlLabel,
  Switch,
  Avatar,
  Button,
  IconButton,
  Divider,
  Paper,
  Stack,
} from '@mui/material';
import {
  Business,
  LocationOn,
  Phone,
  Email,
  Description,
  Palette,
  CloudUpload,
  Payment,
  CheckCircle,
} from '@mui/icons-material';
import StoreLogoDropzone from '../StoreLogoDropzone';

interface StoreConfigurationData {
  store_name: string;
  store_description: string;
  gst_number: string;
  business_type: string;
  business_category: string;
  email: string;
  phone: string;
  website?: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  pincode: string;
  country: string;
  store_theme: string;
  store_logo?: File;
  payment_methods?: { [key: string]: boolean };
  store_handle?: string;
}

interface PaymentMode {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  enabled: boolean;
}

interface StoreConfigurationStepProps {
  data: StoreConfigurationData;
  updateData: (field: keyof StoreConfigurationData, value: any) => void;
}

const businessTypes = [
  { value: 'individual', label: 'Individual' },
  { value: 'partnership', label: 'Partnership' },
  { value: 'private_limited', label: 'Private Limited' },
  { value: 'public_limited', label: 'Public Limited' },
  { value: 'llp', label: 'Limited Liability Partnership (LLP)' },
  { value: 'proprietorship', label: 'Proprietorship' },
];

const businessCategories = [
  { value: 'fashion', label: 'Fashion & Apparel' },
  { value: 'electronics', label: 'Electronics' },
  { value: 'home_garden', label: 'Home & Garden' },
  { value: 'health_beauty', label: 'Health & Beauty' },
  { value: 'sports_outdoors', label: 'Sports & Outdoors' },
  { value: 'books_media', label: 'Books & Media' },
  { value: 'food_beverages', label: 'Food & Beverages' },
  { value: 'automotive', label: 'Automotive' },
  { value: 'toys_games', label: 'Toys & Games' },
  { value: 'jewelry_accessories', label: 'Jewelry & Accessories' },
  { value: 'other', label: 'Other' },
];

const storeThemes = [
  { value: 'modern', label: 'Modern', color: '#3B82F6' },
  { value: 'classic', label: 'Classic', color: '#6B7280' },
  { value: 'minimal', label: 'Minimal', color: '#000000' },
  { value: 'vibrant', label: 'Vibrant', color: '#F59E0B' },
  { value: 'elegant', label: 'Elegant', color: '#8B5CF6' },
  { value: 'bold', label: 'Bold', color: '#EF4444' },
];

// Default payment modes available in the system
const defaultPaymentModes: PaymentMode[] = [
  {
    id: 'cash_on_delivery',
    name: 'Cash on Delivery',
    description: 'Accept cash payments upon delivery',
    icon: <Payment />,
    enabled: true,
  },
  {
    id: 'upi',
    name: 'UPI',
    description: 'Accept UPI payments (PhonePe, GPay, Paytm)',
    icon: <Payment />,
    enabled: true,
  },
  {
    id: 'credit_card',
    name: 'Credit Card',
    description: 'Accept Visa, MasterCard, American Express',
    icon: <Payment />,
    enabled: true,
  },
  {
    id: 'debit_card',
    name: 'Debit Card',
    description: 'Accept debit card payments',
    icon: <Payment />,
    enabled: true,
  },
  {
    id: 'net_banking',
    name: 'Net Banking',
    description: 'Accept payments via internet banking',
    icon: <Payment />,
    enabled: false,
  },
  {
    id: 'wallet',
    name: 'Digital Wallets',
    description: 'Accept payments from digital wallets',
    icon: <Payment />,
    enabled: false,
  },
  {
    id: 'bnpl',
    name: 'Buy Now Pay Later',
    description: 'Accept BNPL payments (Simpl, LazyPay)',
    icon: <Payment />,
    enabled: false,
  },
];

const StoreConfigurationStep: React.FC<StoreConfigurationStepProps> = ({
  data,
  updateData,
}) => {
  const [storeLogo, setStoreLogo] = useState<File | null>(null);
  const [storeLogoUrl, setStoreLogoUrl] = useState<string>('');
  const [paymentModes, setPaymentModes] = useState<PaymentMode[]>(
    defaultPaymentModes.map(mode => ({
      ...mode,
      enabled: data.payment_methods?.[mode.id] ?? mode.enabled,
    }))
  );

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      updateData('store_logo', file);
    }
  };

  const handlePaymentModeToggle = (modeId: string, enabled: boolean) => {
    const updatedModes = paymentModes.map(mode =>
      mode.id === modeId ? { ...mode, enabled } : mode
    );
    setPaymentModes(updatedModes);

    // Update the data with the new payment modes configuration
    const paymentModesConfig = updatedModes.reduce(
      (acc, mode) => {
        acc[mode.id] = mode.enabled;
        return acc;
      },
      {} as { [key: string]: boolean }
    );

    updateData('payment_methods', paymentModesConfig);
  };

  const createHandle = (name: string) =>
    name
      .toLowerCase()
      .trim()
      .replace(/[\s\W-]+/g, '-') // Replace spaces and non-word chars with hyphens
      .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens

  return (
    <Box sx={{ maxWidth: 1000, mx: 'auto' }}>
      <Stack spacing={6}>
        {/* Basic Store Information */}
        <Card
          elevation={8}
          sx={{
            borderRadius: 3,
            border: '1px solid #e2e8f0',
            backgroundColor: '#ffffff',
            '&:hover': {
              boxShadow:
                '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          <CardContent sx={{ p: 4 }}>
            <Box display='flex' alignItems='center' mb={3}>
              <Business color='primary' sx={{ mr: 2 }} />
              <Typography variant='h6' fontWeight={600} color='primary'>
                Basic Store Information
              </Typography>
            </Box>

            <Grid container spacing={3}>
              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='Store Name'
                  value={data.store_name}
                  onChange={e => {
                    updateData('store_name', e.target.value);
                    updateData('store_handle', createHandle(e.target.value));
                  }}
                  placeholder='Enter your store name'
                  required
                  variant='outlined'
                />
              </Grid>

              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='GST Number'
                  value={data.gst_number}
                  onChange={e => updateData('gst_number', e.target.value)}
                  placeholder='Enter 15-digit GST number'
                  inputProps={{ maxLength: 15 }}
                  required
                  variant='outlined'
                />
              </Grid>

              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='Store Description'
                  value={data.store_description}
                  onChange={e =>
                    updateData('store_description', e.target.value)
                  }
                  placeholder='Describe what your store sells and what makes it unique'
                  multiline
                  rows={4}
                  required
                  variant='outlined'
                />
              </Grid>

              <Grid item size={{ xs: 12, md: 4 }}>
                <FormControl fullWidth required>
                  <InputLabel>Business Type</InputLabel>
                  <Select
                    value={data.business_type}
                    onChange={e => updateData('business_type', e.target.value)}
                    label='Business Type'
                  >
                    {businessTypes.map(type => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item size={{ xs: 12, md: 4 }}>
                <FormControl fullWidth required>
                  <InputLabel>Business Category</InputLabel>
                  <Select
                    value={data.business_category}
                    onChange={e =>
                      updateData('business_category', e.target.value)
                    }
                    label='Business Category'
                  >
                    {businessCategories.map(category => (
                      <MenuItem key={category.value} value={category.value}>
                        {category.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Payment Modes */}
        <Card
          elevation={8}
          sx={{
            borderRadius: 3,
            border: '1px solid #e2e8f0',
            backgroundColor: '#ffffff',
            '&:hover': {
              boxShadow:
                '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          <CardContent sx={{ p: 4 }}>
            <Box display='flex' alignItems='center' mb={3}>
              <Payment color='primary' sx={{ mr: 2 }} />
              <Typography variant='h6' fontWeight={600} color='primary'>
                Payment Modes
              </Typography>
            </Box>

            <Typography variant='body2' color='text.secondary' mb={3}>
              Select the payment methods you want to accept from your customers
            </Typography>

            <Grid container spacing={2}>
              {paymentModes.map(mode => (
                <Grid item xs={12} sm={6} md={4} key={mode.id}>
                  <Paper
                    elevation={mode.enabled ? 3 : 1}
                    sx={{
                      p: 2,
                      border: mode.enabled ? '2px solid' : '1px solid',
                      borderColor: mode.enabled ? 'primary.main' : 'grey.300',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        elevation: 3,
                        borderColor: 'primary.main',
                      },
                    }}
                    onClick={() =>
                      handlePaymentModeToggle(mode.id, !mode.enabled)
                    }
                  >
                    <Box
                      display='flex'
                      alignItems='center'
                      justifyContent='space-between'
                    >
                      <Box display='flex' alignItems='center'>
                        <Box
                          sx={{
                            mr: 2,
                            color: mode.enabled ? 'primary.main' : 'grey.500',
                          }}
                        >
                          {mode.icon}
                        </Box>
                        <Box>
                          <Typography variant='subtitle2' fontWeight={600}>
                            {mode.name}
                          </Typography>
                          <Typography variant='caption' color='text.secondary'>
                            {mode.description}
                          </Typography>
                        </Box>
                      </Box>
                      <Switch
                        checked={mode.enabled}
                        onChange={e =>
                          handlePaymentModeToggle(mode.id, e.target.checked)
                        }
                        color='primary'
                        onClick={e => e.stopPropagation()}
                      />
                    </Box>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card
          elevation={8}
          sx={{
            borderRadius: 3,
            border: '1px solid #e2e8f0',
            backgroundColor: '#ffffff',
            '&:hover': {
              boxShadow:
                '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          <CardContent sx={{ p: 4 }}>
            <Box display='flex' alignItems='center' mb={3}>
              <Phone color='primary' sx={{ mr: 2 }} />
              <Typography variant='h6' fontWeight={600} color='primary'>
                Contact Information
              </Typography>
            </Box>

            <Grid container spacing={3}>
              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='Email Address'
                  type='email'
                  value={data.email}
                  onChange={e => updateData('email', e.target.value)}
                  placeholder='Enter your email address'
                  required
                  variant='outlined'
                />
              </Grid>

              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='Phone Number'
                  type='tel'
                  value={data.phone}
                  onChange={e => updateData('phone', e.target.value)}
                  placeholder='Enter your phone number'
                  required
                  variant='outlined'
                />
              </Grid>

              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='Website (Optional)'
                  type='url'
                  value={data.website || ''}
                  onChange={e => updateData('website', e.target.value)}
                  placeholder='https://your-website.com'
                  variant='outlined'
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Address Information */}
        <Card
          elevation={8}
          sx={{
            borderRadius: 3,
            border: '1px solid #e2e8f0',
            backgroundColor: '#ffffff',
            '&:hover': {
              boxShadow:
                '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          <CardContent sx={{ p: 4 }}>
            <Box display='flex' alignItems='center' mb={3}>
              <LocationOn color='primary' sx={{ mr: 2 }} />
              <Typography variant='h6' fontWeight={600} color='primary'>
                Store Address
              </Typography>
            </Box>

            <Grid container spacing={3}>
              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='Address Line 1'
                  value={data.address_line_1}
                  onChange={e => updateData('address_line_1', e.target.value)}
                  placeholder='Street address, building name, etc.'
                  required
                  variant='outlined'
                />
              </Grid>

              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='Address Line 2 (Optional)'
                  value={data.address_line_2 || ''}
                  onChange={e => updateData('address_line_2', e.target.value)}
                  placeholder='Apartment, suite, unit, etc.'
                  variant='outlined'
                />
              </Grid>

              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='City'
                  value={data.city}
                  onChange={e => updateData('city', e.target.value)}
                  placeholder='Enter city'
                  required
                  variant='outlined'
                />
              </Grid>

              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='State'
                  value={data.state}
                  onChange={e => updateData('state', e.target.value)}
                  placeholder='Enter state'
                  required
                  variant='outlined'
                />
              </Grid>

              <Grid item size={{ xs: 12, md: 4 }}>
                <TextField
                  fullWidth
                  label='Pincode'
                  value={data.pincode}
                  onChange={e => updateData('pincode', e.target.value)}
                  placeholder='Enter pincode'
                  required
                  variant='outlined'
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Store Branding */}
        <Card
          elevation={8}
          sx={{
            borderRadius: 3,
            border: '1px solid #e2e8f0',
            backgroundColor: '#ffffff',
            '&:hover': {
              boxShadow:
                '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          <CardContent sx={{ p: 4 }}>
            <Box display='flex' alignItems='center' mb={3}>
              <Palette color='primary' sx={{ mr: 2 }} />
              <Typography variant='h6' fontWeight={600} color='primary'>
                Store Branding
              </Typography>
            </Box>

            <Grid container spacing={3} mb={3}>
              <Grid item size={{ xs: 12, md: 4 }}>
                <FormControl fullWidth>
                  <InputLabel>Select Theme</InputLabel>
                  <Select
                    value={data.store_theme}
                    onChange={e => updateData('store_theme', e.target.value)}
                    label='Select Theme'
                  >
                    {storeThemes.map(theme => (
                      <MenuItem key={theme.value} value={theme.value}>
                        <Box display='flex' alignItems='center'>
                          <Box
                            sx={{
                              width: 24,
                              height: 24,
                              borderRadius: '50%',
                              backgroundColor: theme.color,
                              mr: 2,
                            }}
                          />
                          {theme.label}
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
            <Grid container spacing={3} mb={3}>
              <Grid item size={{ xs: 12, md: 4 }}>
                <StoreLogoDropzone
                  storeLogo={storeLogo}
                  setStoreLogo={setStoreLogo}
                  storeLogoUrl={storeLogoUrl}
                  setStoreLogoUrl={setStoreLogoUrl}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Stack>
    </Box>
  );
};

export default StoreConfigurationStep;
