'use client';

import React from 'react';
import { Upload, Download, FileSpreadsheet, CheckCircle, AlertCircle, Info } from 'lucide-react';

interface BulkUploadStepProps {
  file: File | null;
  setFile: (file: File | null) => void;
}

const BulkUploadStep: React.FC<BulkUploadStepProps> = ({ file, setFile }) => {
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      // Validate file type
      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel', // .xls
        'text/csv', // .csv
      ];
      
      if (allowedTypes.includes(selectedFile.type)) {
        setFile(selectedFile);
      } else {
        alert('Please select a valid Excel (.xlsx, .xls) or CSV file.');
      }
    }
  };

  const handleRemoveFile = () => {
    setFile(null);
  };

  const downloadTemplate = () => {
    // This would typically download a pre-created template file
    const templateData = [
      ['Product Name', 'Description', 'Price', 'Category', 'SKU', 'Inventory Quantity'],
      ['Sample Product 1', 'This is a sample product description', '99.99', 'electronics', 'PROD-001', '50'],
      ['Sample Product 2', 'Another sample product description', '149.99', 'fashion', 'PROD-002', '25'],
    ];
    
    const csvContent = templateData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'product-upload-template.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <Upload className="w-12 h-12 text-blue-600 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          Bulk Upload Products (Optional)
        </h3>
        <p className="text-gray-600">
          Upload multiple products at once using an Excel or CSV file. This step is optional - you can skip it and add products later.
        </p>
      </div>

      {/* Template Download */}
      <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
        <div className="flex items-start">
          <Info className="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
          <div className="flex-1">
            <h4 className="text-lg font-semibold text-blue-900 mb-2">
              Download Template First
            </h4>
            <p className="text-blue-800 mb-4">
              To ensure your products are uploaded correctly, please download and use our template file. 
              Fill in your product information following the exact format provided.
            </p>
            <button
              onClick={downloadTemplate}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Download className="w-4 h-4 mr-2" />
              Download Template
            </button>
          </div>
        </div>
      </div>

      {/* File Upload Area */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">Upload Your Products File</h4>
        
        {!file ? (
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors">
            <FileSpreadsheet className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-lg font-medium text-gray-700 mb-2">
              Drop your file here or click to browse
            </p>
            <p className="text-sm text-gray-500 mb-4">
              Supports Excel (.xlsx, .xls) and CSV files up to 10MB
            </p>
            <input
              type="file"
              accept=".xlsx,.xls,.csv"
              onChange={handleFileUpload}
              className="hidden"
              id="bulk-upload"
            />
            <label
              htmlFor="bulk-upload"
              className="inline-flex items-center px-6 py-3 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer transition-colors"
            >
              <Upload className="w-4 h-4 mr-2" />
              Choose File
            </label>
          </div>
        ) : (
          <div className="border border-green-300 rounded-lg p-6 bg-green-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <CheckCircle className="w-6 h-6 text-green-600 mr-3" />
                <div>
                  <p className="font-medium text-green-900">{file.name}</p>
                  <p className="text-sm text-green-700">
                    {(file.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>
              <button
                onClick={handleRemoveFile}
                className="text-red-600 hover:text-red-800 transition-colors"
              >
                Remove
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Upload Guidelines */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">Upload Guidelines</h4>
        <div className="space-y-3">
          <div className="flex items-start">
            <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
            <p className="text-gray-700">Use the provided template to ensure correct formatting</p>
          </div>
          <div className="flex items-start">
            <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
            <p className="text-gray-700">Include all required fields: Product Name, Description, Price, Category</p>
          </div>
          <div className="flex items-start">
            <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
            <p className="text-gray-700">Prices should be in numbers only (without currency symbols)</p>
          </div>
          <div className="flex items-start">
            <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
            <p className="text-gray-700">Maximum file size: 10MB</p>
          </div>
          <div className="flex items-start">
            <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
            <p className="text-gray-700">Supported formats: Excel (.xlsx, .xls) and CSV</p>
          </div>
        </div>
      </div>

      {/* Skip Option */}
      <div className="bg-yellow-50 p-6 rounded-lg border border-yellow-200">
        <div className="flex items-start">
          <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
          <div>
            <h4 className="text-lg font-semibold text-yellow-900 mb-2">
              Skip This Step?
            </h4>
            <p className="text-yellow-800 mb-4">
              You can skip bulk upload and add products individually later from your admin dashboard. 
              This won't affect your store setup.
            </p>
            <div className="space-y-2 text-sm text-yellow-700">
              <p>• You already added one product in the previous step</p>
              <p>• You can always bulk upload products later</p>
              <p>• Individual product management gives you more control</p>
            </div>
          </div>
        </div>
      </div>

      {/* File Format Examples */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">Expected File Format</h4>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200 rounded-lg">
            <thead className="bg-gray-100">
              <tr>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 border-b">Product Name</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 border-b">Description</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 border-b">Price</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 border-b">Category</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 border-b">SKU</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 border-b">Inventory</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="px-4 py-2 text-sm text-gray-600 border-b">Wireless Headphones</td>
                <td className="px-4 py-2 text-sm text-gray-600 border-b">High-quality wireless headphones</td>
                <td className="px-4 py-2 text-sm text-gray-600 border-b">2999</td>
                <td className="px-4 py-2 text-sm text-gray-600 border-b">electronics</td>
                <td className="px-4 py-2 text-sm text-gray-600 border-b">WH-001</td>
                <td className="px-4 py-2 text-sm text-gray-600 border-b">50</td>
              </tr>
              <tr>
                <td className="px-4 py-2 text-sm text-gray-600 border-b">Cotton T-Shirt</td>
                <td className="px-4 py-2 text-sm text-gray-600 border-b">Comfortable cotton t-shirt</td>
                <td className="px-4 py-2 text-sm text-gray-600 border-b">599</td>
                <td className="px-4 py-2 text-sm text-gray-600 border-b">fashion</td>
                <td className="px-4 py-2 text-sm text-gray-600 border-b">TS-001</td>
                <td className="px-4 py-2 text-sm text-gray-600 border-b">100</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default BulkUploadStep;
