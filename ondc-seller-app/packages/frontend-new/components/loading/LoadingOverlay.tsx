'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import { useGlobalLoading } from './GlobalLoadingProvider';
import { LoadingSpinner } from '../ui/LoadingSpinner';

export const LoadingOverlay: React.FC = () => {
  const { loadingState } = useGlobalLoading();
  const pathname = usePathname();

  // Don't show global loading overlay in admin routes - use AdminNavigationLoader instead
  // Exception: Show loading for login-related navigation to admin routes
  // Don't show logout loading on login page - let login page handle stopping it
  const isAdminRoute = pathname?.includes('/admin');
  const isLoginPage = pathname === '/login';
  const isLoginLoading = loadingState.actionId === 'user-login';
  const isLogoutLoading = loadingState.actionId === 'user-logout';
  
  // Don't show loading if:
  // 1. Not loading at all
  // 2. On admin routes without login loading
  // 3. On login page with logout loading (let login page handle it)
  if (!loadingState.isLoading || 
      (isAdminRoute && !isLoginLoading) ||
      (isLoginPage && isLogoutLoading)) {
    return null;
  }

  const getLoadingContent = () => {
    switch (loadingState.loadingType) {
      case 'page':
        return (
          <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}>
            <div className="text-center max-w-md mx-auto px-4">
              <div className="mb-6">
                <LoadingSpinner size="lg" text="" />
              </div>
              <h2 className="text-2xl font-semibold mb-3" style={{ color: 'var(--theme-text, #111827)' }}>
                {loadingState.message}
              </h2>
              {loadingState.subMessage && (
                <p className="text-sm leading-relaxed" style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>
                  {loadingState.subMessage}
                </p>
              )}
              {loadingState.progress !== undefined && loadingState.progress > 0 && (
                <div className="mt-4">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="h-2 rounded-full transition-all duration-300"
                      style={{ 
                        backgroundColor: 'var(--theme-primary, #3b82f6)',
                        width: `${loadingState.progress}%`
                      }}
                    />
                  </div>
                  <p className="text-xs mt-2" style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>
                    {Math.round(loadingState.progress)}% complete
                  </p>
                </div>
              )}
            </div>
          </div>
        );

      case 'navigation':
        return (
          <div className="fixed inset-0 z-[99999] flex items-center justify-center">
            {/* Backdrop */}
            <div 
              className="absolute inset-0 bg-opacity-80 backdrop-blur-sm transition-opacity duration-200"
              style={{ backgroundColor: 'var(--theme-background, #ffffff)' }}
            />
            
            {/* Loading Content */}
            <div className="relative z-10 flex flex-col items-center space-y-4 animate-fade-in">
              <div 
                className="p-6 rounded-lg shadow-lg border"
                style={{ 
                  backgroundColor: 'var(--theme-surface, #ffffff)',
                  borderColor: 'var(--theme-border, #e5e7eb)',
                }}
              >
                <div className="flex flex-col items-center space-y-4">
                  <LoadingSpinner size="lg" text="" />
                  <div className="text-center">
                    <p 
                      className="text-sm font-medium"
                      style={{ color: 'var(--theme-text, #111827)' }}
                    >
                      {loadingState.message}
                    </p>
                    {loadingState.subMessage && (
                      <p 
                        className="text-xs mt-1"
                        style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                      >
                        {loadingState.subMessage}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'backdrop':
        return (
          <div 
            className="fixed inset-0 z-40 flex items-center justify-center"
            style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
          >
            <div 
              className="bg-white rounded-lg p-6 max-w-sm mx-4 text-center shadow-xl"
              style={{ backgroundColor: 'var(--theme-surface, #ffffff)' }}
            >
              <div className="mb-4">
                <LoadingSpinner size="lg" text="" />
              </div>
              <h3 className="text-lg font-semibold mb-2" style={{ color: 'var(--theme-text, #111827)' }}>
                {loadingState.message}
              </h3>
              {loadingState.subMessage && (
                <p className="text-sm" style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>
                  {loadingState.subMessage}
                </p>
              )}
              {loadingState.progress !== undefined && loadingState.progress > 0 && (
                <div className="mt-4">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="h-2 rounded-full transition-all duration-300"
                      style={{ 
                        backgroundColor: 'var(--theme-primary, #3b82f6)',
                        width: `${loadingState.progress}%`
                      }}
                    />
                  </div>
                  <p className="text-xs mt-2" style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>
                    {Math.round(loadingState.progress)}% complete
                  </p>
                </div>
              )}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      {getLoadingContent()}
    </>
  );
};