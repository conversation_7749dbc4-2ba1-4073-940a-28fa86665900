'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { User } from '@/types/auth';
import { useAuthContext } from './AuthProvider';

interface UserMenuProps {
  user: User | null;
  storeHandle: string;
}

export const UserMenu: React.FC<UserMenuProps> = ({ user, storeHandle }) => {
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const { logout } = useAuthContext();

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    logout();
    setIsOpen(false);
  };

  if (!user) return null;

  return (
    <div className="relative" ref={menuRef}>
      {/* User Avatar Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 rounded-full px-3 py-2 transition-all duration-200 hover:shadow-sm"
        style={{ backgroundColor: 'var(--theme-background)' }}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = 'var(--theme-hover)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = 'var(--theme-background)';
        }}
      >
        <div className="relative w-8 h-8 rounded-full overflow-hidden">
          {user.avatar ? (
            <Image
              src={user.avatar}
              alt={`${user.firstName} ${user.lastName}`}
              fill
              className="object-cover"
            />
          ) : (
            <div 
              className="w-full h-full flex items-center justify-center"
              style={{ backgroundColor: 'var(--theme-primary)' }}
            >
              <span 
                className="text-sm font-semibold"
                style={{ color: 'var(--btn-text)' }}
              >
                {user.firstName.charAt(0)}{user.lastName.charAt(0)}
              </span>
            </div>
          )}
        </div>
        <span 
          className="hidden lg:block text-sm font-medium transition-colors"
          style={{ color: 'var(--theme-text)' }}
        >
          {user.firstName}
        </span>
        <svg 
          className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          fill="none" 
          stroke="var(--theme-text)" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div 
          className="absolute right-0 mt-2 w-64 rounded-lg shadow-lg border z-50"
          style={{ 
            backgroundColor: 'var(--theme-surface, #ffffff)',
            borderColor: 'var(--theme-border, #e5e7eb)',
          }}
        >
          {/* User Info Header */}
          <div 
            className="px-4 py-3 border-b"
            style={{ borderBottomColor: 'var(--theme-border, #e5e7eb)' }}
          >
            <p 
              className="text-sm font-medium"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              {user.firstName} {user.lastName}
            </p>
            <p 
              className="text-xs"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
            >
              {user.email}
            </p>
          </div>

          {/* Menu Items */}
          <div className="py-2">
            <Link
              href={`/${storeHandle}/profile`}
              className="flex items-center px-4 py-2 text-sm transition-colors hover:opacity-80"
              style={{ color: 'var(--theme-text, #111827)' }}
              onClick={() => setIsOpen(false)}
            >
              <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              My Profile
            </Link>

            <Link
              href={`/${storeHandle}/orders`}
              className="flex items-center px-4 py-2 text-sm transition-colors hover:opacity-80"
              style={{ color: 'var(--theme-text, #111827)' }}
              onClick={() => setIsOpen(false)}
            >
              <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              My Orders
            </Link>

            <Link
              href={`/${storeHandle}/wishlist`}
              className="flex items-center px-4 py-2 text-sm transition-colors hover:opacity-80"
              style={{ color: 'var(--theme-text, #111827)' }}
              onClick={() => setIsOpen(false)}
            >
              <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
              Wishlist
            </Link>

            <Link
              href={`/${storeHandle}/addresses`}
              className="flex items-center px-4 py-2 text-sm transition-colors hover:opacity-80"
              style={{ color: 'var(--theme-text, #111827)' }}
              onClick={() => setIsOpen(false)}
            >
              <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              Addresses
            </Link>

            <div 
              className="border-t my-2"
              style={{ borderTopColor: 'var(--theme-border, #e5e7eb)' }}
            />

            <button
              onClick={handleLogout}
              className="flex items-center w-full px-4 py-2 text-sm transition-colors hover:opacity-80"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              Logout
            </button>
          </div>
        </div>
      )}
    </div>
  );
};