'use client';

import React, { useEffect, useState } from 'react';
import { useStoreAuthStore } from '@/stores/storeAuthStore';

interface AuthHydrationBoundaryProps {
  children: React.ReactNode;
  storeHandle: string;
}

/**
 * Component that ensures auth state is properly hydrated before rendering children
 * Prevents hydration mismatches between server and client
 */
export const AuthHydrationBoundary: React.FC<AuthHydrationBoundaryProps> = ({ 
  children, 
  storeHandle 
}) => {
  const [isClientSide, setIsClientSide] = useState(false);
  const [hasWaitedForHydration, setHasWaitedForHydration] = useState(false);
  const authStore = useStoreAuthStore(storeHandle);

  // First, ensure we're on the client side
  useEffect(() => {
    setIsClientSide(true);
  }, []);

  // Then wait for Zustand hydration
  useEffect(() => {
    if (isClientSide) {
      console.log(`🔄 AuthHydrationBoundary: Waiting for hydration for store: ${storeHandle}`);
      console.log('Current auth state:', {
        token: authStore.token ? 'exists' : 'null',
        user: authStore.user ? 'exists' : 'null',
        isAuthenticated: authStore.isAuthenticated,
        hasHydrated: authStore.hasHydrated
      });
      
      // Wait a bit longer for Zustand to hydrate from localStorage
      const timer = setTimeout(() => {
        setHasWaitedForHydration(true);
        console.log(`✅ AuthHydrationBoundary: Hydration complete for store: ${storeHandle}`);
        console.log('Final auth state:', {
          token: authStore.token ? 'exists' : 'null',
          user: authStore.user ? 'exists' : 'null',
          isAuthenticated: authStore.isAuthenticated,
          hasHydrated: authStore.hasHydrated
        });
      }, 200); // Increased delay to ensure localStorage is read
      
      return () => clearTimeout(timer);
    }
  }, [isClientSide, storeHandle, authStore]);

  // Show loading state until both client-side and hydration are complete
  if (!isClientSide || !hasWaitedForHydration) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-sm text-gray-600">Loading...</span>
      </div>
    );
  }

  return <>{children}</>;
};