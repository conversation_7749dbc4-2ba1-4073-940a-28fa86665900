'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { useStoreAuth } from '@/hooks/useStoreAuth';
import { StoreLoginCredentials } from '@/lib/api/storeAuth';
import { AuthHydrationBoundary } from './AuthHydrationBoundary';

interface StoreUser {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  has_account?: boolean;
  created_at?: string;
  updated_at?: string;
  metadata?: {
    [key: string]: any;
  };
}

interface StoreAuthContextType {
  token: string | null;
  user: StoreUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  loadingMessage: string | null;
  error: string | null;
  login: (credentials: StoreLoginCredentials, storeHandle: string) => Promise<StoreUser>;
  logout: () => void;
  clearError: () => void;
  refreshUserData: (storeHandle: string) => Promise<StoreUser>;
}

const StoreAuthContext = createContext<StoreAuthContextType | undefined>(undefined);

interface StoreAuthProviderProps {
  children: ReactNode;
  storeHandle: string;
}

export const StoreAuthProvider: React.FC<StoreAuthProviderProps> = ({ children, storeHandle }) => {
  const storeAuthData = useStoreAuth(storeHandle);

  return (
    <AuthHydrationBoundary storeHandle={storeHandle}>
      <StoreAuthContext.Provider value={storeAuthData}>
        {children}
      </StoreAuthContext.Provider>
    </AuthHydrationBoundary>
  );
};

export const useStoreAuthContext = (): StoreAuthContextType => {
  const context = useContext(StoreAuthContext);
  if (context === undefined) {
    throw new Error('useStoreAuthContext must be used within a StoreAuthProvider');
  }
  return context;
};