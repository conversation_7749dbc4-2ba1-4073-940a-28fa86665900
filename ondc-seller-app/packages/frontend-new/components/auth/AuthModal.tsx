'use client';

import React, { useState } from 'react';
import { useAuthContext } from './AuthProvider';
import { LoginCredentials, SignupData } from '@/types/auth';

interface AuthModalProps {
  isOpen: boolean;
  mode: 'login' | 'signup';
  onClose: () => void;
  onSwitchMode: (mode: 'login' | 'signup') => void;
  storeHandle: string;
}

export const AuthModal: React.FC<AuthModalProps> = ({ 
  isOpen, 
  mode, 
  onClose, 
  onSwitchMode, 
  storeHandle 
}) => {
  const { login, signup, isLoading, error, clearError } = useAuthContext();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    rememberMe: false,
    agreeToTerms: false,
    subscribeNewsletter: false,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
    
    // Clear error when user starts typing
    if (error) {
      clearError();
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (mode === 'login') {
        const credentials: LoginCredentials = {
          email: formData.email,
          password: formData.password,
          rememberMe: formData.rememberMe,
        };
        await login(credentials);
      } else {
        if (formData.password !== formData.confirmPassword) {
          throw new Error('Passwords do not match');
        }
        
        const signupData: SignupData = {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          password: formData.password,
          confirmPassword: formData.confirmPassword,
          agreeToTerms: formData.agreeToTerms,
          subscribeNewsletter: formData.subscribeNewsletter,
        };
        await signup(signupData);
      }
      
      // Close modal on success
      onClose();
      
      // Reset form
      setFormData({
        email: '',
        password: '',
        confirmPassword: '',
        firstName: '',
        lastName: '',
        rememberMe: false,
        agreeToTerms: false,
        subscribeNewsletter: false,
      });
    } catch (error) {
      // Error is handled by the auth context
      console.error('Auth error:', error);
    }
  };

  const switchMode = () => {
    clearError();
    onSwitchMode(mode === 'login' ? 'signup' : 'login');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div 
        className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto"
        style={{ 
          backgroundColor: 'var(--theme-surface, #ffffff)',
          borderColor: 'var(--theme-border, #e5e7eb)',
        }}
      >
        {/* Header */}
        <div 
          className="flex items-center justify-between p-6 border-b"
          style={{ borderBottomColor: 'var(--theme-border, #e5e7eb)' }}
        >
          <h2 
            className="text-xl font-bold"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            {mode === 'login' ? 'Welcome Back' : 'Create Account'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 rounded-full transition-colors hover:opacity-80"
            style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Error Message */}
          {error && (
            <div 
              className="p-3 rounded-lg border text-sm"
              style={{ 
                backgroundColor: '#fef2f2',
                borderColor: '#fecaca',
                color: '#dc2626',
              }}
            >
              {error}
            </div>
          )}

          {/* Signup Fields */}
          {mode === 'signup' && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label 
                  className="block text-sm font-medium mb-2"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  First Name *
                </label>
                <input
                  type="text"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                  style={{ 
                    borderColor: 'var(--theme-border, #d1d5db)',
                    '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                  }}
                  placeholder="John"
                />
              </div>
              <div>
                <label 
                  className="block text-sm font-medium mb-2"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  Last Name *
                </label>
                <input
                  type="text"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                  style={{ 
                    borderColor: 'var(--theme-border, #d1d5db)',
                    '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                  }}
                  placeholder="Doe"
                />
              </div>
            </div>
          )}

          {/* Email */}
          <div>
            <label 
              className="block text-sm font-medium mb-2"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              Email Address *
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
              style={{ 
                borderColor: 'var(--theme-border, #d1d5db)',
                '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
              }}
              placeholder="<EMAIL>"
            />
          </div>

          {/* Password */}
          <div>
            <label 
              className="block text-sm font-medium mb-2"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              Password *
            </label>
            <input
              type="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
              style={{ 
                borderColor: 'var(--theme-border, #d1d5db)',
                '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
              }}
              placeholder="••••••••"
            />
          </div>

          {/* Confirm Password (Signup only) */}
          {mode === 'signup' && (
            <div>
              <label 
                className="block text-sm font-medium mb-2"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                Confirm Password *
              </label>
              <input
                type="password"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
                style={{ 
                  borderColor: 'var(--theme-border, #d1d5db)',
                  '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                }}
                placeholder="••••••••"
              />
            </div>
          )}

          {/* Remember Me (Login only) */}
          {mode === 'login' && (
            <div className="flex items-center">
              <input
                type="checkbox"
                name="rememberMe"
                checked={formData.rememberMe}
                onChange={handleInputChange}
                className="mr-2"
                style={{ accentColor: 'var(--theme-primary, #3b82f6)' }}
              />
              <label 
                className="text-sm"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                Remember me
              </label>
            </div>
          )}

          {/* Terms Agreement (Signup only) */}
          {mode === 'signup' && (
            <div className="space-y-3">
              <div className="flex items-start">
                <input
                  type="checkbox"
                  name="agreeToTerms"
                  checked={formData.agreeToTerms}
                  onChange={handleInputChange}
                  required
                  className="mr-2 mt-1"
                  style={{ accentColor: 'var(--theme-primary, #3b82f6)' }}
                />
                <label 
                  className="text-sm"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  I agree to the{' '}
                  <a 
                    href={`/${storeHandle}/terms-and-conditions`}
                    className="underline transition-colors hover:opacity-80"
                    style={{ color: 'var(--theme-primary, #3b82f6)' }}
                  >
                    Terms of Service
                  </a>{' '}
                  and{' '}
                  <a 
                    href={`/${storeHandle}/privacy-policy`}
                    className="underline transition-colors hover:opacity-80"
                    style={{ color: 'var(--theme-primary, #3b82f6)' }}
                  >
                    Privacy Policy
                  </a>
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="subscribeNewsletter"
                  checked={formData.subscribeNewsletter}
                  onChange={handleInputChange}
                  className="mr-2"
                  style={{ accentColor: 'var(--theme-primary, #3b82f6)' }}
                />
                <label 
                  className="text-sm"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  Subscribe to newsletter for updates and offers
                </label>
              </div>
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 ${
              isLoading 
                ? 'opacity-50 cursor-not-allowed' 
                : 'hover:shadow-lg'
            }`}
            style={{ 
              backgroundColor: 'var(--btn-primary, #3b82f6)',
              color: 'var(--btn-text, #ffffff)',
            }}
          >
            {isLoading 
              ? (mode === 'login' ? 'Signing In...' : 'Creating Account...') 
              : (mode === 'login' ? 'Sign In' : 'Create Account')
            }
          </button>

          {/* Switch Mode */}
          <div className="text-center">
            <p 
              className="text-sm"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
            >
              {mode === 'login' ? "Don't have an account?" : 'Already have an account?'}{' '}
              <button
                type="button"
                onClick={switchMode}
                className="font-medium transition-colors hover:opacity-80"
                style={{ color: 'var(--theme-primary, #3b82f6)' }}
              >
                {mode === 'login' ? 'Sign up' : 'Sign in'}
              </button>
            </p>
          </div>

          {/* Demo Credentials (Login only) */}
          {mode === 'login' && (
            <div 
              className="p-3 rounded-lg text-xs"
              style={{ 
                backgroundColor: 'var(--theme-background, #f9fafb)',
                color: 'var(--theme-text-secondary, #6b7280)',
              }}
            >
              <p className="font-medium mb-1">Demo Credentials:</p>
              <p>Email: <EMAIL></p>
              <p>Password: password123</p>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};