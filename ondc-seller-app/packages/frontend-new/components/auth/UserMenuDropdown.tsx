'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useStoreAuthContext } from './StoreAuthProvider';
import { useStoreAuthStore } from '@/stores/storeAuthStore';
import { useToast } from '@/app/providers/toast-provider';

interface UserMenuDropdownProps {
  storeHandle: string;
}

export const UserMenuDropdown: React.FC<UserMenuDropdownProps> = ({ storeHandle }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const { user, isAuthenticated } = useStoreAuthContext();
  const { clearAuth } = useStoreAuthStore(storeHandle);
  const { showToast } = useToast();
  const router = useRouter();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current && 
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Close dropdown on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => {
        document.removeEventListener('keydown', handleEscape);
      };
    }
  }, [isOpen]);

  const handleLogout = () => {
    clearAuth();
    showToast('Logged out successfully', 'success');
    setIsOpen(false);
    router.push(`/${storeHandle}`);
  };

  const handleMyAccount = () => {
    setIsOpen(false);
    router.push(`/${storeHandle}/profile`);
  };

  const handleMyOrders = () => {
    setIsOpen(false);
    router.push(`/${storeHandle}/orders`);
  };

  if (!isAuthenticated || !user) {
    return null;
  }

  const displayName = user.first_name || user.email?.split('@')[0] || 'User';

  return (
    <div className="relative">
      {/* User Button */}
      <button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 rounded-full transition-all duration-200 hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2"
        style={{ 
          backgroundColor: isOpen ? 'var(--theme-hover)' : 'var(--theme-background)',
          '--tw-ring-color': 'var(--theme-primary)',
          '--tw-ring-offset-color': 'var(--theme-surface)',
        }}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        {/* User Avatar */}
        <div 
          className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
          style={{ 
            backgroundColor: 'var(--theme-primary)',
            color: 'var(--btn-text)',
          }}
        >
          {displayName.charAt(0).toUpperCase()}
        </div>
        
        {/* User Name */}
        <span 
          className="hidden sm:block text-sm font-medium max-w-24 truncate"
          style={{ color: 'var(--theme-text)' }}
        >
          {displayName}
        </span>
        
        {/* Dropdown Arrow */}
        <svg 
          className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          fill="none" 
          stroke="var(--theme-text-secondary)" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div
          ref={dropdownRef}
          className="absolute right-0 mt-2 w-56 rounded-xl shadow-lg border backdrop-blur-sm z-50 animate-in fade-in slide-in-from-top-2 duration-200"
          style={{ 
            backgroundColor: 'var(--theme-surface)',
            borderColor: 'var(--theme-border)',
            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
          }}
          role="menu"
          aria-orientation="vertical"
        >
          {/* User Info Header */}
          <div 
            className="px-4 py-3 border-b"
            style={{ borderBottomColor: 'var(--theme-border)' }}
          >
            <div className="flex items-center space-x-3">
              <div 
                className="w-10 h-10 rounded-full flex items-center justify-center text-base font-medium"
                style={{ 
                  backgroundColor: 'var(--theme-primary)',
                  color: 'var(--btn-text)',
                }}
              >
                {displayName.charAt(0).toUpperCase()}
              </div>
              <div className="flex-1 min-w-0">
                <p 
                  className="text-sm font-medium truncate"
                  style={{ color: 'var(--theme-text)' }}
                >
                  {user.first_name && user.last_name 
                    ? `${user.first_name} ${user.last_name}`
                    : displayName
                  }
                </p>
                <p 
                  className="text-xs truncate"
                  style={{ color: 'var(--theme-text-secondary)' }}
                >
                  {user.email}
                </p>
              </div>
            </div>
          </div>

          {/* Menu Items */}
          <div className="py-2">
            {/* My Account */}
            <button
              onClick={handleMyAccount}
              className="w-full flex items-center px-4 py-3 text-sm transition-colors hover:bg-opacity-80 focus:outline-none focus:bg-opacity-80"
              style={{ 
                color: 'var(--theme-text)',
                '--hover-bg': 'var(--theme-hover)',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--theme-hover)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
              role="menuitem"
            >
              <svg 
                className="w-5 h-5 mr-3" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <span className="font-medium">My Account</span>
            </button>

            {/* My Orders */}
            <button
              onClick={handleMyOrders}
              className="w-full flex items-center px-4 py-3 text-sm transition-colors hover:bg-opacity-80 focus:outline-none focus:bg-opacity-80"
              style={{ 
                color: 'var(--theme-text)',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--theme-hover)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
              role="menuitem"
            >
              <svg 
                className="w-5 h-5 mr-3" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
              </svg>
              <span className="font-medium">My Orders</span>
            </button>

            {/* Divider */}
            <div 
              className="my-2 border-t"
              style={{ borderTopColor: 'var(--theme-border)' }}
            />

            {/* Logout */}
            <button
              onClick={handleLogout}
              className="w-full flex items-center px-4 py-3 text-sm transition-colors hover:bg-opacity-80 focus:outline-none focus:bg-opacity-80"
              style={{ 
                color: 'var(--theme-text-secondary)',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--theme-hover)';
                e.currentTarget.style.color = 'var(--theme-accent)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = 'var(--theme-text-secondary)';
              }}
              role="menuitem"
            >
              <svg 
                className="w-5 h-5 mr-3" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              <span className="font-medium">Logout</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};