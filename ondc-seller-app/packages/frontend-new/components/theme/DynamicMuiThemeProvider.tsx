'use client';

import React, { ReactNode, useMemo } from 'react';
import { ThemeProvider, createTheme, alpha } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { useThemeColors } from '@/hooks/useTheme';

interface DynamicMuiThemeProviderProps {
  children: ReactNode;
}

export const DynamicMuiThemeProvider: React.FC<DynamicMuiThemeProviderProps> = ({ children }) => {
  const themeColors = useThemeColors();

  // Create dynamic Material UI theme based on CSS custom properties
  const dynamicTheme = useMemo(() => {
    // Fallback colors if CSS custom properties are not available
    const fallbackColors = {
      primary: '#6750A4',
      secondary: '#625B71',
      background: '#FFFBFE',
      surface: '#FFFFFF',
      text: '#1C1B1F',
      textSecondary: '#49454F',
      border: '#CAC4D0',
    };

    // Use theme colors from CSS custom properties or fallback
    const colors = {
      primary: themeColors.primary || fallbackColors.primary,
      secondary: themeColors.secondary || fallbackColors.secondary,
      background: themeColors.background || fallbackColors.background,
      surface: themeColors.surface || fallbackColors.surface,
      text: themeColors.text || fallbackColors.text,
      textSecondary: themeColors.textSecondary || fallbackColors.textSecondary,
      border: themeColors.border || fallbackColors.border,
    };

    return createTheme({
      palette: {
        mode: 'light',
        primary: {
          main: colors.primary,
          light: alpha(colors.primary, 0.7),
          dark: alpha(colors.primary, 0.9),
          contrastText: '#FFFFFF',
        },
        secondary: {
          main: colors.secondary,
          light: alpha(colors.secondary, 0.7),
          dark: alpha(colors.secondary, 0.9),
          contrastText: '#FFFFFF',
        },
        error: {
          main: '#BA1A1A',
          light: '#DE3730',
          dark: '#93000A',
          contrastText: '#FFFFFF',
        },
        warning: {
          main: '#F57C00',
          light: '#FFB74D',
          dark: '#E65100',
          contrastText: '#000000',
        },
        info: {
          main: '#0288D1',
          light: '#03DAC6',
          dark: '#018786',
          contrastText: '#FFFFFF',
        },
        success: {
          main: '#2E7D32',
          light: '#4CAF50',
          dark: '#1B5E20',
          contrastText: '#FFFFFF',
        },
        background: {
          default: colors.background,
          paper: colors.surface,
        },
        text: {
          primary: colors.text,
          secondary: colors.textSecondary,
        },
        divider: colors.border,
      },
      
      // Material Design 3 Typography Scale
      typography: {
        fontFamily: '"Roboto", "Inter", "Helvetica", "Arial", sans-serif',
        h1: {
          fontSize: '3.5rem',
          fontWeight: 400,
          lineHeight: 1.12,
          letterSpacing: '-0.25px',
        },
        h2: {
          fontSize: '2.8125rem',
          fontWeight: 400,
          lineHeight: 1.16,
          letterSpacing: '0px',
        },
        h3: {
          fontSize: '2.25rem',
          fontWeight: 400,
          lineHeight: 1.22,
          letterSpacing: '0px',
        },
        h4: {
          fontSize: '2rem',
          fontWeight: 400,
          lineHeight: 1.25,
          letterSpacing: '0px',
        },
        h5: {
          fontSize: '1.75rem',
          fontWeight: 400,
          lineHeight: 1.29,
          letterSpacing: '0px',
        },
        h6: {
          fontSize: '1.5rem',
          fontWeight: 400,
          lineHeight: 1.33,
          letterSpacing: '0px',
        },
        body1: {
          fontSize: '1rem',
          fontWeight: 400,
          lineHeight: 1.5,
          letterSpacing: '0.5px',
        },
        body2: {
          fontSize: '0.875rem',
          fontWeight: 400,
          lineHeight: 1.43,
          letterSpacing: '0.25px',
        },
        button: {
          fontSize: '0.875rem',
          fontWeight: 500,
          lineHeight: 1.43,
          letterSpacing: '0.1px',
          textTransform: 'none',
        },
        caption: {
          fontSize: '0.75rem',
          fontWeight: 500,
          lineHeight: 1.33,
          letterSpacing: '0.5px',
        },
        overline: {
          fontSize: '0.6875rem',
          fontWeight: 500,
          lineHeight: 1.45,
          letterSpacing: '0.5px',
          textTransform: 'uppercase',
        },
      },
      
      // Material Design 3 Shape System
      shape: {
        borderRadius: 12,
      },
      
      // Material Design 3 Elevation System
      shadows: [
        'none',
        '0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15)',
        '0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15)',
        '0px 1px 3px 0px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15)',
        '0px 2px 3px 0px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15)',
        '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
        '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
        '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
        '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
        '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
        '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
        '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
        '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
        '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
        '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
        '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
        '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
        '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
        '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
        '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
        '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
        '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
        '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
        '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
        '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
      ],
      
      // Material Design 3 Component Customizations
      components: {
        MuiButton: {
          styleOverrides: {
            root: ({ theme, ownerState }) => ({
              textTransform: 'none',
              fontWeight: 500,
              borderRadius: 20,
              paddingX: 24,
              paddingY: 10,
              minHeight: 40,
              ...(ownerState.variant === 'contained' && {
                boxShadow: theme.shadows[1],
                '&:hover': {
                  boxShadow: theme.shadows[2],
                },
                '&:active': {
                  boxShadow: theme.shadows[1],
                },
              }),
              ...(ownerState.variant === 'outlined' && {
                borderWidth: 1,
                '&:hover': {
                  borderWidth: 1,
                },
              }),
            }),
          },
        },
        
        MuiFab: {
          styleOverrides: {
            root: ({ theme }) => ({
              borderRadius: 16,
              boxShadow: theme.shadows[3],
              '&:hover': {
                boxShadow: theme.shadows[4],
              },
            }),
          },
        },
        
        MuiTextField: {
          styleOverrides: {
            root: {
              '& .MuiOutlinedInput-root': {
                borderRadius: 4,
                '& fieldset': {
                  borderWidth: 1,
                },
                '&:hover fieldset': {
                  borderWidth: 1,
                },
                '&.Mui-focused fieldset': {
                  borderWidth: 2,
                },
              },
            },
          },
        },
        
        MuiCard: {
          styleOverrides: {
            root: ({ theme }) => ({
              borderRadius: 12,
              boxShadow: theme.shadows[1],
              '&:hover': {
                boxShadow: theme.shadows[2],
              },
            }),
          },
        },
        
        MuiPaper: {
          styleOverrides: {
            root: ({ theme }) => ({
              borderRadius: 12,
              boxShadow: theme.shadows[1],
            }),
          },
        },
        
        MuiChip: {
          styleOverrides: {
            root: {
              borderRadius: 8,
              fontWeight: 500,
            },
          },
        },
        
        MuiDialog: {
          styleOverrides: {
            paper: {
              borderRadius: 28,
            },
          },
        },
        
        MuiMenu: {
          styleOverrides: {
            paper: {
              borderRadius: 4,
            },
          },
        },
        
        MuiSnackbar: {
          styleOverrides: {
            root: {
              '& .MuiSnackbarContent-root': {
                borderRadius: 4,
              },
            },
          },
        },
        
        MuiSwitch: {
          styleOverrides: {
            root: {
              '& .MuiSwitch-thumb': {
                borderRadius: '50%',
              },
              '& .MuiSwitch-track': {
                borderRadius: 16,
              },
            },
          },
        },
        
        MuiSlider: {
          styleOverrides: {
            thumb: {
              borderRadius: '50%',
            },
            track: {
              borderRadius: 4,
            },
            rail: {
              borderRadius: 4,
            },
          },
        },
      },
    });
  }, [themeColors]);

  return (
    <ThemeProvider theme={dynamicTheme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  );
};