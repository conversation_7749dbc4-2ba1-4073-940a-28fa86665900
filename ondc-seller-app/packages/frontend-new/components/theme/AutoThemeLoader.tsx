/**
 * Auto Theme Loader Component
 * Displays the status of automatic theme loading and provides manual controls
 */

'use client';

import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  <PERSON>ton,
  Stack,
  Alert,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Palette,
  Refresh,
  CheckCircle,
  Warning,
  Error as ErrorIcon,
  Info,
} from '@mui/icons-material';
import { useStoreThemeWithAutoLoad } from '@/hooks/useStoreThemeWithAutoLoad';

interface AutoThemeLoaderProps {
  storeHandle: string;
  showDetails?: boolean;
  compact?: boolean;
}

export const AutoThemeLoader: React.FC<AutoThemeLoaderProps> = ({
  storeHandle,
  showDetails = true,
  compact = false,
}) => {
  const {
    currentTheme,
    autoLoader,
    isAnyLoading,
    combinedError,
  } = useStoreThemeWithAutoLoad(storeHandle);

  if (compact) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        {isAnyLoading && (
          <LinearProgress sx={{ width: 100, height: 4 }} />
        )}
        
        {currentTheme && !isAnyLoading && (
          <Chip
            icon={<CheckCircle />}
            label="Theme Applied"
            color="success"
            size="small"
          />
        )}
        
        {combinedError && !isAnyLoading && (
          <Chip
            icon={<ErrorIcon />}
            label="Theme Error"
            color="error"
            size="small"
          />
        )}
        
        <Tooltip title="Retry Auto-Load">
          <IconButton
            size="small"
            onClick={autoLoader.retryAutoLoad}
            disabled={isAnyLoading}
          >
            <Refresh />
          </IconButton>
        </Tooltip>
      </Box>
    );
  }

  return (
    <Card elevation={1}>
      <CardContent>
        <Stack spacing={2}>
          {/* Header */}
          <Stack direction="row" alignItems="center" spacing={2}>
            <Palette color="primary" />
            <Box>
              <Typography variant="h6" fontWeight="bold">
                Auto Theme Loader
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Store: {storeHandle}
              </Typography>
            </Box>
          </Stack>

          {/* Loading State */}
          {isAnyLoading && (
            <Box>
              <LinearProgress sx={{ mb: 1 }} />
              <Typography variant="body2" color="text.secondary">
                {autoLoader.isLoading ? 'Loading theme from store data...' : 'Generating theme...'}
              </Typography>
            </Box>
          )}

          {/* Success State */}
          {currentTheme && autoLoader.hasAutoApplied && !isAnyLoading && (
            <Alert severity="success" icon={<CheckCircle />}>
              <Typography variant="body2">
                <strong>Theme auto-applied successfully!</strong>
                {autoLoader.logoSource && (
                  <span> Source: {autoLoader.logoSource}</span>
                )}
              </Typography>
            </Alert>
          )}

          {/* Manual Theme State */}
          {currentTheme && !autoLoader.hasAutoApplied && !isAnyLoading && (
            <Alert severity="info" icon={<Info />}>
              <Typography variant="body2">
                <strong>Theme manually applied.</strong> Auto-loading was not needed.
              </Typography>
            </Alert>
          )}

          {/* Error State */}
          {combinedError && !isAnyLoading && (
            <Alert severity="error" icon={<ErrorIcon />}>
              <Typography variant="body2">
                <strong>Error:</strong> {combinedError}
              </Typography>
            </Alert>
          )}

          {/* No Theme State */}
          {!currentTheme && !isAnyLoading && !combinedError && (
            <Alert severity="warning" icon={<Warning />}>
              <Typography variant="body2">
                <strong>No theme applied.</strong> No logo found for automatic theme generation.
              </Typography>
            </Alert>
          )}

          {/* Theme Details */}
          {showDetails && currentTheme && (
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Current Theme Colors:
              </Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                <Chip
                  label="Primary"
                  size="small"
                  sx={{
                    bgcolor: currentTheme.primary,
                    color: 'white',
                    fontWeight: 'bold',
                  }}
                />
                <Chip
                  label="Secondary"
                  size="small"
                  sx={{
                    bgcolor: currentTheme.secondary,
                    color: 'white',
                    fontWeight: 'bold',
                  }}
                />
                <Chip
                  label="Accent"
                  size="small"
                  sx={{
                    bgcolor: currentTheme.accent,
                    color: 'white',
                    fontWeight: 'bold',
                  }}
                />
              </Stack>
            </Box>
          )}

          {/* Controls */}
          <Stack direction="row" spacing={2}>
            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={autoLoader.retryAutoLoad}
              disabled={isAnyLoading}
              size="small"
            >
              Retry Auto-Load
            </Button>
            
            {showDetails && (
              <Button
                variant="text"
                size="small"
                onClick={() => {
                  console.log('🔍 Auto Theme Loader Debug Info:');
                  console.log('Store Handle:', storeHandle);
                  console.log('Current Theme:', currentTheme);
                  console.log('Auto Loader State:', autoLoader);
                  console.log('Combined Error:', combinedError);
                }}
              >
                Debug Info
              </Button>
            )}
          </Stack>
        </Stack>
      </CardContent>
    </Card>
  );
};