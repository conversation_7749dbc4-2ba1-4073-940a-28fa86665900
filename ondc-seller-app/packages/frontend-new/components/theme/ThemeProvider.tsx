'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { useStoreThemeWithAutoLoad } from '@/hooks/useStoreThemeWithAutoLoad';
import { AutoTheme } from '@/lib/utils/simpleTheme';

interface ThemeContextType {
  currentTheme: AutoTheme | null;
  isGenerating: boolean;
  isAnyLoading: boolean;
  error: string | null;
  autoLoader: {
    isLoading: boolean;
    hasAutoApplied: boolean;
    logoSource: 'zustand' | 'localStorage' | 'strapi' | null;
    error: string | null;
    retryAutoLoad: () => Promise<void>;
  };
  generateThemeFromLogo: (logoUrl: string) => Promise<AutoTheme | null>;
  applyTheme: (theme: AutoTheme) => void;
  resetToDefault: () => void;
  clearTheme: () => void;
  accessibility: {
    isValid: boolean;
    issues: string[];
  } | null;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
  storeHandle?: string;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ 
  children, 
  storeHandle 
}) => {
  const themeData = useStoreThemeWithAutoLoad(storeHandle || 'default');

  return (
    <ThemeContext.Provider value={themeData}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useThemeContext = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useThemeContext must be used within a ThemeProvider');
  }
  return context;
};