/**
 * Theme Auto Initializer Component
 * Automatically initializes themes when mounted in layouts or pages
 */

'use client';

import { useEffect, useState } from 'react';
import { autoInitializeOnPageLoad, getThemeStatus } from '@/lib/utils/autoThemeInitializer';

interface ThemeAutoInitializerProps {
  storeHandle: string;
  showDebug?: boolean;
}

export const ThemeAutoInitializer: React.FC<ThemeAutoInitializerProps> = ({
  storeHandle,
  showDebug = false,
}) => {
  const [initStatus, setInitStatus] = useState<{
    isInitialized: boolean;
    hasTheme: boolean;
    error: string | null;
  }>({
    isInitialized: false,
    hasTheme: false,
    error: null,
  });

  useEffect(() => {
    const initializeTheme = async () => {
      if (!storeHandle) return;

      try {
        console.log('🎨 ThemeAutoInitializer: Starting initialization for:', storeHandle);
        
        // Check current status
        const currentStatus = getThemeStatus(storeHandle);
        
        if (currentStatus.isInitialized && currentStatus.hasTheme) {
          console.log('✅ Theme already initialized for:', storeHandle);
          setInitStatus({
            isInitialized: true,
            hasTheme: true,
            error: null,
          });
          return;
        }

        // Auto-initialize
        await autoInitializeOnPageLoad(storeHandle);
        
        // Check status after initialization
        const newStatus = getThemeStatus(storeHandle);
        setInitStatus({
          isInitialized: newStatus.isInitialized,
          hasTheme: newStatus.hasTheme,
          error: null,
        });

        if (newStatus.hasTheme) {
          console.log('✅ Theme auto-initialized successfully for:', storeHandle);
        } else {
          console.log('ℹ️ No theme auto-initialized for:', storeHandle);
        }

      } catch (error: any) {
        console.error('❌ Error in ThemeAutoInitializer:', error);
        setInitStatus({
          isInitialized: false,
          hasTheme: false,
          error: error.message || 'Unknown error',
        });
      }
    };

    initializeTheme();
  }, [storeHandle]);

  // This component doesn't render anything visible by default
  if (!showDebug) {
    return null;
  }

  // Debug view
  return (
    <div style={{
      position: 'fixed',
      top: 10,
      right: 10,
      background: 'rgba(0,0,0,0.8)',
      color: 'white',
      padding: '8px 12px',
      borderRadius: '4px',
      fontSize: '12px',
      zIndex: 9999,
      fontFamily: 'monospace',
    }}>
      <div>🎨 Theme Status: {storeHandle}</div>
      <div>Initialized: {initStatus.isInitialized ? '✅' : '❌'}</div>
      <div>Has Theme: {initStatus.hasTheme ? '✅' : '❌'}</div>
      {initStatus.error && <div>Error: {initStatus.error}</div>}
    </div>
  );
};