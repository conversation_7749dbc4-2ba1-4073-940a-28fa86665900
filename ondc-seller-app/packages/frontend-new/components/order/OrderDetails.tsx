'use client';

import React from 'react';
import Image from 'next/image';
import { 
  Box, 
  Card, 
  CardContent, 
  Typography, 
  Chip, 
  Grid, 
  Divider, 
  Avatar,
  Stack,
  Paper,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Badge
} from '@mui/material';
import { 
  CheckCircle, 
  Schedule, 
  Cancel, 
  Warning,
  LocalShipping,
  Payment,
  Person,
  LocationOn,
  Receipt
} from '@mui/icons-material';
import { Order } from '@/lib/api/medusa/orders';
import { formatPrice } from '@/lib/cart/mockCartData';

interface OrderDetailsProps {
  order: Order;
  storeHandle: string;
}

export const OrderDetails: React.FC<OrderDetailsProps> = ({ order, storeHandle }) => {
  const getStatusColor = (status?: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    if (!status) return 'default';
    
    switch (status.toLowerCase()) {
      case 'pending':
        return 'warning';
      case 'completed':
        return 'success';
      case 'archived':
        return 'secondary';
      case 'canceled':
      case 'cancelled':
        return 'error';
      case 'requires_action':
        return 'info';
      default:
        return 'default';
    }
  };

  const getStatusText = (status?: string): string => {
    if (!status) return 'Unknown';
    
    switch (status.toLowerCase()) {
      case 'pending':
        return 'Pending';
      case 'completed':
        return 'Completed';
      case 'archived':
        return 'Archived';
      case 'canceled':
      case 'cancelled':
        return 'Cancelled';
      case 'requires_action':
        return 'Requires Action';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  const getFulfillmentStatusColor = (status?: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    if (!status) return 'default';
    
    switch (status.toLowerCase()) {
      case 'not_fulfilled':
        return 'warning';
      case 'partially_fulfilled':
        return 'info';
      case 'fulfilled':
        return 'success';
      case 'partially_shipped':
        return 'primary';
      case 'shipped':
        return 'primary';
      case 'partially_returned':
        return 'warning';
      case 'returned':
        return 'error';
      case 'canceled':
      case 'cancelled':
        return 'secondary';
      case 'requires_action':
        return 'error';
      default:
        return 'default';
    }
  };

  const getFulfillmentStatusText = (status?: string): string => {
    if (!status) return 'Unknown';
    
    switch (status.toLowerCase()) {
      case 'not_fulfilled':
        return 'Not Fulfilled';
      case 'partially_fulfilled':
        return 'Partially Fulfilled';
      case 'fulfilled':
        return 'Fulfilled';
      case 'partially_shipped':
        return 'Partially Shipped';
      case 'shipped':
        return 'Shipped';
      case 'partially_returned':
        return 'Partially Returned';
      case 'returned':
        return 'Returned';
      case 'canceled':
      case 'cancelled':
        return 'Cancelled';
      case 'requires_action':
        return 'Requires Action';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  const getPaymentStatusColor = (status?: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    if (!status) return 'default';
    
    switch (status.toLowerCase()) {
      case 'not_paid':
        return 'warning';
      case 'awaiting':
        return 'info';
      case 'captured':
        return 'success';
      case 'partially_refunded':
        return 'warning';
      case 'refunded':
        return 'error';
      case 'canceled':
      case 'cancelled':
        return 'secondary';
      case 'requires_action':
        return 'error';
      default:
        return 'default';
    }
  };

  const getPaymentStatusText = (status?: string): string => {
    if (!status) return 'Unknown';
    
    switch (status.toLowerCase()) {
      case 'not_paid':
        return 'Not Paid';
      case 'awaiting':
        return 'Awaiting Payment';
      case 'captured':
        return 'Paid';
      case 'partially_refunded':
        return 'Partially Refunded';
      case 'refunded':
        return 'Refunded';
      case 'canceled':
      case 'cancelled':
        return 'Cancelled';
      case 'requires_action':
        return 'Requires Action';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  const getStatusIcon = (status?: string) => {
    if (!status) return <Warning />;
    
    switch (status.toLowerCase()) {
      case 'completed':
      case 'fulfilled':
      case 'captured':
        return <CheckCircle />;
      case 'pending':
      case 'awaiting':
        return <Schedule />;
      case 'canceled':
      case 'cancelled':
        return <Cancel />;
      default:
        return <Warning />;
    }
  };

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Order Header */}
      <Card elevation={2} sx={{ mb: 3 }}>
        <CardContent sx={{ p: 4 }}>
          <Grid container spacing={3} alignItems="center">
            <Grid item size={{xs:12, md:8}}>
              <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
                Order #{order.display_id}
              </Typography>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Placed on {new Date(order.created_at).toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap" sx={{ mt: 2 }}>
                <Chip 
                  icon={getStatusIcon(order.status)}
                  label={getStatusText(order.status)}
                  color={getStatusColor(order.status)}
                  variant="filled"
                />
                <Chip 
                  icon={<LocalShipping />}
                  label={getFulfillmentStatusText(order.fulfillment_status)}
                  color={getFulfillmentStatusColor(order.fulfillment_status)}
                  variant="outlined"
                />
                <Chip 
                  icon={<Payment />}
                  label={getPaymentStatusText(order.payment_status)}
                  color={getPaymentStatusColor(order.payment_status)}
                  variant="outlined"
                />
              </Stack>
            </Grid>
            
            <Grid item size={{xs:12, md:4}}>
              <Box textAlign={{ xs: 'left', md: 'right' }}>
                <Typography variant="body2" color="text.secondary">
                  Total Amount
                </Typography>
                <Typography variant="h3" color="primary" fontWeight="bold">
                  {formatPrice(order?.totals?.original_order_total || 0)}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Grid container spacing={3}>
        {/* Order Items */}
        <Grid item size={12 }>
          <Card elevation={2}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Receipt />
                Order Items ({order.items?.length || 0})
              </Typography>
              
              <List>
                {order.items?.map((item, index) => (
                  <React.Fragment key={item.id}>
                    <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                      <ListItemAvatar>
                        <Badge 
                          badgeContent={item.quantity} 
                          color="primary"
                          anchorOrigin={{
                            vertical: 'top',
                            horizontal: 'right',
                          }}
                        >
                          <Avatar
                            variant="rounded"
                            sx={{ width: 80, height: 80, mr: 2 }}
                            src={item.thumbnail}
                          >
                            {!item.thumbnail && (
                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                                <Typography variant="h6" color="text.secondary">
                                  {item.title?.charAt(0) || '?'}
                                </Typography>
                              </Box>
                            )}
                          </Avatar>
                        </Badge>
                      </ListItemAvatar>
                      
                      <ListItemText
                        primary={
                          <Typography variant="h6" fontWeight="medium">
                            {item.title}
                          </Typography>
                        }
                        secondary={
                          <Box>
                            {item.description && (
                              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                {item.description}
                              </Typography>
                            )}
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                {formatPrice(item.unit_price)} × {item.quantity}
                              </Typography>
                              <Typography variant="h6" color="primary" fontWeight="bold">
                                {formatPrice(item.unit_price * item.quantity)}
                              </Typography>
                            </Box>
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < (order.items?.length || 0) - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Order Summary & Details */}
        <Grid item size={12}>
          <Stack spacing={3}>
            {/* Order Summary */}
            <Card elevation={2}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Receipt />
                  Order Summary
                </Typography>
                
                <Stack spacing={2}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2" color="text.secondary">
                      Subtotal:
                    </Typography>
                    <Typography variant="body2">
                      {formatPrice(order.subtotal || 0)}
                    </Typography>
                  </Box>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2" color="text.secondary">
                      Shipping:
                    </Typography>
                    <Typography variant="body2">
                      {(order.shipping_total || 0) === 0 ? 'Free' : formatPrice(order.shipping_total || 0)}
                    </Typography>
                  </Box>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2" color="text.secondary">
                      Tax:
                    </Typography>
                    <Typography variant="body2">
                      {formatPrice(order.tax_total || 0)}
                    </Typography>
                  </Box>
                  
                  <Divider />
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="h6" fontWeight="bold">
                      Total:
                    </Typography>
                    <Typography variant="h6" color="primary" fontWeight="bold">
                      {formatPrice(order.total || 0)}
                    </Typography>
                  </Box>
                </Stack>
              </CardContent>
            </Card>

            {/* Customer Information */}
            <Card elevation={2}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Person />
                  Customer Information
                </Typography>
                
                <Stack spacing={1}>
                  <Typography variant="body1" fontWeight="medium">
                    {order?.customer 
                      ? `${order.customer.first_name} ${order.customer.last_name}`
                      : order.shipping_address 
                        ? `${order.shipping_address.first_name} ${order.shipping_address.last_name}`
                        : 'Unknown Customer'
                    }
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {order?.customer?.email || 'No email provided'}
                  </Typography>
                  {(order?.customer?.phone || order?.shipping_address?.phone) && (
                    <Typography variant="body2" color="text.secondary">
                      {order?.customer?.phone || order?.shipping_address?.phone}
                    </Typography>
                  )}
                </Stack>
              </CardContent>
            </Card>

            {/* Shipping Address */}
            {order.shipping_address && (
              <Card elevation={2}>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <LocationOn />
                    Shipping Address
                  </Typography>
                  
                  <Stack spacing={0.5}>
                    <Typography variant="body1" fontWeight="medium">
                      {order.shipping_address.first_name} {order.shipping_address.last_name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {order.shipping_address.address_1}
                    </Typography>
                    {order.shipping_address.address_2 && (
                      <Typography variant="body2" color="text.secondary">
                        {order.shipping_address.address_2}
                      </Typography>
                    )}
                    <Typography variant="body2" color="text.secondary">
                      {order.shipping_address.city}, {order.shipping_address.province} {order.shipping_address.postal_code}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {order.shipping_address.country_code}
                    </Typography>
                    {order.shipping_address.phone && (
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                        {order.shipping_address.phone}
                      </Typography>
                    )}
                  </Stack>
                </CardContent>
              </Card>
            )}

            {/* Billing Address */}
            {order.billing_address && (
              <Card elevation={2}>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Payment />
                    Billing Address
                  </Typography>
                  
                  <Stack spacing={0.5}>
                    <Typography variant="body1" fontWeight="medium">
                      {order.billing_address.first_name} {order.billing_address.last_name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {order.billing_address.address_1}
                    </Typography>
                    {order.billing_address.address_2 && (
                      <Typography variant="body2" color="text.secondary">
                        {order.billing_address.address_2}
                      </Typography>
                    )}
                    <Typography variant="body2" color="text.secondary">
                      {order.billing_address.city}, {order.billing_address.province} {order.billing_address.postal_code}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {order.billing_address.country_code}
                    </Typography>
                    {order.billing_address.phone && (
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                        {order.billing_address.phone}
                      </Typography>
                    )}
                  </Stack>
                </CardContent>
              </Card>
            )}
          </Stack>
        </Grid>
      </Grid>
    </Box>
  );
};