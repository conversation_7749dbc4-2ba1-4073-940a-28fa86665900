'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Order } from '@/types/order';
import { formatPrice } from '@/lib/cart/mockCartData';

interface OrdersListProps {
  orders: Order[];
  storeHandle: string;
}

export const OrdersList: React.FC<OrdersListProps> = ({ orders, storeHandle }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'var(--theme-primary, #3b82f6)';
      case 'processing':
        return '#f59e0b';
      case 'shipped':
        return '#8b5cf6';
      case 'delivered':
        return '#10b981';
      case 'cancelled':
        return '#ef4444';
      default:
        return 'var(--theme-text-secondary, #6b7280)';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'Order Confirmed';
      case 'processing':
        return 'Processing';
      case 'shipped':
        return 'Shipped';
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Pending';
    }
  };

  if (orders.length === 0) {
    return (
      <div 
        className="bg-white rounded-lg shadow-sm border p-12 text-center"
        style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
      >
        <div 
          className="w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-6"
          style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}
        >
          <svg 
            className="w-8 h-8" 
            fill="none" 
            stroke="var(--theme-text-secondary, #6b7280)" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        
        <h3 
          className="text-lg font-medium mb-2"
          style={{ color: 'var(--theme-text, #111827)' }}
        >
          No orders yet
        </h3>
        <p 
          className="text-sm mb-6"
          style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
        >
          When you place orders, they'll appear here
        </p>
        
        <Link
          href={`/${storeHandle}`}
          className="inline-flex items-center px-6 py-3 rounded-lg font-semibold transition-all duration-200 hover:shadow-lg"
          style={{ 
            backgroundColor: 'var(--btn-primary, #3b82f6)',
            color: 'var(--btn-text, #ffffff)',
          }}
        >
          Start Shopping
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {orders.map((order) => (
        <div 
          key={order.id}
          className="bg-white rounded-lg shadow-sm border transition-all duration-200 hover:shadow-md"
          style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
        >
          {/* Order Header */}
          <div className="p-6 border-b" style={{ borderBottomColor: 'var(--theme-border, #e5e7eb)' }}>
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-6">
                <div>
                  <h3 
                    className="font-semibold"
                    style={{ color: 'var(--theme-text, #111827)' }}
                  >
                    Order #{order.orderNumber}
                  </h3>
                  <p 
                    className="text-sm"
                    style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                  >
                    {new Date(order.createdAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>
                
                <div 
                  className="px-3 py-1 rounded-full text-sm font-medium text-white mt-2 sm:mt-0 inline-block"
                  style={{ backgroundColor: getStatusColor(order.status) }}
                >
                  {getStatusText(order.status)}
                </div>
              </div>
              
              <div className="mt-4 lg:mt-0 text-right">
                <p 
                  className="text-lg font-bold"
                  style={{ color: 'var(--theme-primary, #3b82f6)' }}
                >
                  {formatPrice(order.totals.total)}
                </p>
                <p 
                  className="text-sm"
                  style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                >
                  {order.items.length} {order.items.length === 1 ? 'item' : 'items'}
                </p>
              </div>
            </div>
          </div>

          {/* Order Items Preview */}
          <div className="p-6">
            <div className="flex items-center space-x-4 mb-4">
              {order.items.slice(0, 3).map((item, index) => (
                <div key={item.id} className="relative">
                  <div className="relative w-12 h-12 rounded-lg overflow-hidden bg-gray-100">
                    <Image
                      src={item.image}
                      alt={item.title}
                      fill
                      className="object-cover"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                  </div>
                  {item.quantity > 1 && (
                    <div 
                      className="absolute -top-1 -right-1 w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold text-white"
                      style={{ backgroundColor: 'var(--theme-primary, #3b82f6)' }}
                    >
                      {item.quantity}
                    </div>
                  )}
                </div>
              ))}
              
              {order.items.length > 3 && (
                <div 
                  className="w-12 h-12 rounded-lg flex items-center justify-center text-xs font-medium"
                  style={{ 
                    backgroundColor: 'var(--theme-background, #f9fafb)',
                    color: 'var(--theme-text-secondary, #6b7280)',
                  }}
                >
                  +{order.items.length - 3}
                </div>
              )}
            </div>

            {/* Order Actions */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
              <div className="flex items-center space-x-4">
                <Link
                  href={`/${storeHandle}/orders/${order.orderNumber}`}
                  className="text-sm font-medium transition-colors hover:opacity-80"
                  style={{ color: 'var(--theme-primary, #3b82f6)' }}
                >
                  View Details
                </Link>
                
                {order.trackingNumber && (
                  <button
                    className="text-sm font-medium transition-colors hover:opacity-80"
                    style={{ color: 'var(--theme-primary, #3b82f6)' }}
                    onClick={() => {
                      // In a real app, this would open tracking page
                      alert(`Tracking: ${order.trackingNumber}`);
                    }}
                  >
                    Track Package
                  </button>
                )}
              </div>
              
              <div className="flex items-center space-x-3">
                {order.status === 'delivered' && (
                  <button
                    className="px-4 py-2 text-sm font-medium rounded-lg border transition-colors hover:opacity-80"
                    style={{ 
                      borderColor: 'var(--theme-border, #d1d5db)',
                      color: 'var(--theme-text, #111827)',
                    }}
                    onClick={() => {
                      // In a real app, this would open reorder flow
                      alert('Reorder functionality coming soon!');
                    }}
                  >
                    Reorder
                  </button>
                )}
                
                <button
                  className="px-4 py-2 text-sm font-medium rounded-lg border transition-colors hover:opacity-80"
                  style={{ 
                    borderColor: 'var(--theme-border, #d1d5db)',
                    color: 'var(--theme-text, #111827)',
                  }}
                  onClick={() => {
                    // In a real app, this would open support
                    alert('Need help? Contact our support team!');
                  }}
                >
                  Get Help
                </button>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};