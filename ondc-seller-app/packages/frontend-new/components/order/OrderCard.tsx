'use client';

import React from 'react';
import Link from 'next/link';
import { 
  Card, 
  CardContent, 
  CardActions, 
  Typography, 
  Chip, 
  Box, 
  Button, 
  Avatar, 
  Stack,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider
} from '@mui/material';
import { 
  Visibility, 
  Refresh, 
  CheckCircle, 
  Schedule, 
  Cancel, 
  Warning,
  LocalShipping,
  Payment
} from '@mui/icons-material';
import { Order } from '@/lib/api/medusa/orders';

interface OrderCardProps {
  order: Order;
  storeHandle: string;
}

export const OrderCard: React.FC<OrderCardProps> = ({ order, storeHandle }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatPrice = (amount: number | undefined | null, currencyCode: string | undefined | null) => {
    if (amount === undefined || amount === null) {
      return '$0.00';
    }
    
    try {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currencyCode || 'USD',
      }).format(amount);
    } catch (error) {
      console.warn('Error formatting price:', error);
      return `${amount} ${currencyCode || 'USD'}`;
    }
  };

  const getStatusColor = (status?: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    if (!status) return 'default';
    
    switch (status.toLowerCase()) {
      case 'completed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'cancelled':
      case 'canceled':
        return 'error';
      default:
        return 'default';
    }
  };

  const getFulfillmentStatusColor = (status?: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    if (!status) return 'default';
    
    switch (status.toLowerCase()) {
      case 'fulfilled':
        return 'success';
      case 'shipped':
        return 'primary';
      case 'partially_fulfilled':
        return 'warning';
      case 'not_fulfilled':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const getStatusText = (status?: string): string => {
    if (!status) return 'Unknown';
    return status.charAt(0).toUpperCase() + status.slice(1).replace(/_/g, ' ');
  };

  const getStatusIcon = (status?: string) => {
    if (!status) return <Warning />;
    
    switch (status.toLowerCase()) {
      case 'completed':
      case 'fulfilled':
        return <CheckCircle />;
      case 'pending':
        return <Schedule />;
      case 'cancelled':
      case 'canceled':
        return <Cancel />;
      default:
        return <Warning />;
    }
  };

  return (
    <Card 
      elevation={2} 
      sx={{ 
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          elevation: 4,
          transform: 'translateY(-2px)'
        }
      }}
    >
      <CardContent sx={{ pb: 2 }}>
        {/* Order Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box>
            <Typography variant="h6" component="h3" fontWeight="bold">
              Order #{order.display_id}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Placed on {formatDate(order.created_at)}
            </Typography>
          </Box>
          <Box sx={{ textAlign: 'right' }}>
            <Typography variant="h6" fontWeight="bold" color="primary">
              {formatPrice(order.total, order.currency_code)}
            </Typography>
            <Stack direction="row" spacing={0.5} sx={{ mt: 1 }} justifyContent="flex-end">
              <Chip 
                icon={getStatusIcon(order.status)}
                label={getStatusText(order.status)}
                color={getStatusColor(order.status)}
                size="small"
                variant="filled"
              />
              <Chip 
                icon={<LocalShipping />}
                label={getStatusText(order.fulfillment_status)}
                color={getFulfillmentStatusColor(order.fulfillment_status)}
                size="small"
                variant="outlined"
              />
            </Stack>
          </Box>
        </Box>

        {/* Order Items */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Items ({order.items?.length || 0})
          </Typography>
          
          {order.items && order.items.length > 0 ? (
            <List dense sx={{ py: 0 }}>
              {order.items.slice(0, 3).map((item, index) => (
                <React.Fragment key={item.id}>
                  <ListItem sx={{ px: 0, py: 0.5 }}>
                    <ListItemAvatar>
                      <Avatar
                        variant="rounded"
                        src={item.thumbnail}
                        sx={{ width: 40, height: 40 }}
                      >
                        {!item.thumbnail && (
                          <Typography variant="caption">
                            {item.title?.charAt(0) || '?'}
                          </Typography>
                        )}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Typography variant="body2" noWrap>
                          {item.title}
                        </Typography>
                      }
                      secondary={
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="caption" color="text.secondary">
                            Qty: {item.quantity} × {formatPrice(item.unit_price, order.currency_code)}
                          </Typography>
                          <Typography variant="caption" fontWeight="medium">
                            {formatPrice(item.unit_price * item.quantity, order.currency_code)}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < Math.min((order.items?.length || 0), 3) - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          ) : (
            <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
              No items found
            </Typography>
          )}
          
          {order.items && order.items.length > 3 && (
            <Typography variant="caption" color="text.secondary">
              +{order.items.length - 3} more items
            </Typography>
          )}
        </Box>
      </CardContent>

      {/* Order Actions */}
      <CardActions sx={{ px: 2, pb: 2, pt: 0 }}>
        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={1} sx={{ width: '100%' }}>
          <Button
            component={Link}
            href={`/${storeHandle}/orders/${order.id}`}
            variant="outlined"
            startIcon={<Visibility />}
            fullWidth
            sx={{ flex: 1 }}
          >
            View Details
          </Button>
          
          {order.status === 'completed' && order.fulfillment_status === 'fulfilled' && (
            <Button
              variant="contained"
              startIcon={<Refresh />}
              fullWidth
              sx={{ flex: 1 }}
            >
              Reorder
            </Button>
          )}
        </Stack>
      </CardActions>
    </Card>
  );
};