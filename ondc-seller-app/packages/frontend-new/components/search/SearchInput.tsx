'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { 
  TextField, 
  InputAdornment, 
  IconButton, 
  Paper, 
  List, 
  ListItem, 
  ListItemText, 
  ListItemAvatar,
  Avatar,
  Typography,
  Box,
  Chip,
  CircularProgress,
  Fade
} from '@mui/material';
import { 
  Search as SearchIcon, 
  Clear as ClearIcon,
  History as HistoryIcon,
  TrendingUp as TrendingIcon
} from '@mui/icons-material';
import { useSearchStore } from '@/stores/searchStore';
import { createSearchAPI } from '@/lib/api/search';
import { debounce } from 'lodash';

interface SearchInputProps {
  storeHandle: string;
  placeholder?: string;
  variant?: 'outlined' | 'filled' | 'standard';
  size?: 'small' | 'medium';
  fullWidth?: boolean;
  showSuggestions?: boolean;
  onSearch?: (query: string) => void;
  className?: string;
}

export const SearchInput: React.FC<SearchInputProps> = ({
  storeHandle,
  placeholder = 'Search products...',
  variant = 'outlined',
  size = 'medium',
  fullWidth = true,
  showSuggestions = true,
  onSearch,
  className,
}) => {
  const router = useRouter();
  const searchAPI = createSearchAPI(storeHandle);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  
  const {
    query,
    suggestions,
    searchHistory,
    isLoadingSuggestions,
    showSuggestions: showSuggestionsState,
    setQuery,
    setSuggestions,
    setLoadingSuggestions,
    addToHistory,
    setShowSuggestions,
  } = useSearchStore();

  const [isFocused, setIsFocused] = useState(false);
  const [localQuery, setLocalQuery] = useState(query);

  // Debounced function to fetch suggestions
  const debouncedGetSuggestions = useCallback(
    debounce(async (searchQuery: string) => {
      if (!searchQuery.trim() || searchQuery.length < 2) {
        setSuggestions([]);
        return;
      }

      try {
        setLoadingSuggestions(true);
        const response = await searchAPI.getSuggestions(storeHandle, searchQuery, 5);
        setSuggestions(response.suggestions);
      } catch (error) {
        console.error('Failed to get suggestions:', error);
        setSuggestions([]);
      } finally {
        setLoadingSuggestions(false);
      }
    }, 300),
    [storeHandle, searchAPI, setSuggestions, setLoadingSuggestions]
  );

  // Handle input change
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setLocalQuery(value);
    setQuery(value);

    if (showSuggestions && value.trim()) {
      setShowSuggestions(true);
      debouncedGetSuggestions(value);
    } else {
      setShowSuggestions(false);
      setSuggestions([]);
    }
  };

  // Handle search submission
  const handleSearch = (searchQuery?: string) => {
    const queryToSearch = searchQuery || localQuery.trim();
    
    if (!queryToSearch) return;

    // Add to search history
    addToHistory(queryToSearch);
    
    // Hide suggestions
    setShowSuggestions(false);
    
    // Blur input
    inputRef.current?.blur();

    // Call custom onSearch handler or navigate to products listing page
    if (onSearch) {
      onSearch(queryToSearch);
    } else {
      router.push(`/${storeHandle}/products?q=${encodeURIComponent(queryToSearch)}`);
    }
  };

  // Handle key press
  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleSearch();
    } else if (event.key === 'Escape') {
      setShowSuggestions(false);
      inputRef.current?.blur();
    }
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion: any) => {
    setLocalQuery(suggestion.title);
    setQuery(suggestion.title);
    handleSearch(suggestion.title);
  };

  // Handle history item click
  const handleHistoryClick = (historyQuery: string) => {
    setLocalQuery(historyQuery);
    setQuery(historyQuery);
    handleSearch(historyQuery);
  };

  // Handle clear
  const handleClear = () => {
    setLocalQuery('');
    setQuery('');
    setSuggestions([]);
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  // Handle focus
  const handleFocus = () => {
    setIsFocused(true);
    if (showSuggestions && (suggestions.length > 0 || searchHistory.length > 0)) {
      setShowSuggestions(true);
    }
  };

  // Handle blur
  const handleBlur = () => {
    setIsFocused(false);
    // Delay hiding suggestions to allow clicking on them
    setTimeout(() => {
      setShowSuggestions(false);
    }, 200);
  };

  // Click outside handler
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [setShowSuggestions]);

  // Sync local query with store query
  useEffect(() => {
    setLocalQuery(query);
  }, [query]);

  const shouldShowSuggestions = showSuggestions && showSuggestionsState && isFocused;
  const hasSuggestions = suggestions.length > 0;
  const hasHistory = searchHistory.length > 0;

  return (
    <Box className={className} sx={{ position: 'relative', width: fullWidth ? '100%' : 'auto' }}>
      <TextField
        ref={inputRef}
        value={localQuery}
        onChange={handleInputChange}
        onKeyPress={handleKeyPress}
        onFocus={handleFocus}
        onBlur={handleBlur}
        placeholder={placeholder}
        variant={variant}
        size={size}
        fullWidth={fullWidth}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon color="action" />
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment position="end">
              {isLoadingSuggestions && (
                <CircularProgress size={20} sx={{ mr: 1 }} />
              )}
              {localQuery && (
                <IconButton
                  size="small"
                  onClick={handleClear}
                  edge="end"
                >
                  <ClearIcon />
                </IconButton>
              )}
            </InputAdornment>
          ),
        }}
        sx={{
          '& .MuiOutlinedInput-root': {
            borderRadius: 2,
          },
        }}
      />

      {/* Suggestions Dropdown */}
      <Fade in={shouldShowSuggestions}>
        <Paper
          ref={suggestionsRef}
          elevation={8}
          sx={{
            position: 'absolute',
            top: '100%',
            left: 0,
            right: 0,
            zIndex: 1300,
            maxHeight: 400,
            overflow: 'auto',
            mt: 1,
            display: shouldShowSuggestions ? 'block' : 'none',
          }}
        >
          <List disablePadding>
            {/* Search Suggestions */}
            {hasSuggestions && (
              <>
                <ListItem>
                  <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 600 }}>
                    Products
                  </Typography>
                </ListItem>
                {suggestions.map((suggestion) => (
                  <ListItem
                    key={suggestion.id}
                    button
                    onClick={() => handleSuggestionClick(suggestion)}
                    sx={{ py: 1 }}
                  >
                    <ListItemAvatar>
                      <Avatar
                        src={suggestion.thumbnail}
                        variant="rounded"
                        sx={{ width: 40, height: 40 }}
                      >
                        <SearchIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Typography
                          dangerouslySetInnerHTML={{ __html: suggestion.highlighted }}
                        />
                      }
                      secondary={suggestion.handle}
                    />
                  </ListItem>
                ))}
              </>
            )}

            {/* Search History */}
            {hasHistory && !hasSuggestions && (
              <>
                <ListItem>
                  <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 600 }}>
                    Recent Searches
                  </Typography>
                </ListItem>
                {searchHistory.slice(0, 5).map((historyQuery, index) => (
                  <ListItem
                    key={index}
                    button
                    onClick={() => handleHistoryClick(historyQuery)}
                    sx={{ py: 1 }}
                  >
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'grey.100' }}>
                        <HistoryIcon color="action" />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText primary={historyQuery} />
                  </ListItem>
                ))}
              </>
            )}

            {/* No suggestions */}
            {!hasSuggestions && !hasHistory && localQuery.length >= 2 && !isLoadingSuggestions && (
              <ListItem>
                <ListItemText
                  primary="No suggestions found"
                  secondary="Try a different search term"
                />
              </ListItem>
            )}
          </List>
        </Paper>
      </Fade>
    </Box>
  );
};
