'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Button,
  Chip,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Skeleton,
  Alert,
  Divider,
} from '@mui/material';
import {
  Search as SearchIcon,
  ShoppingCart as CartIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import { useSearchStore } from '@/stores/searchStore';
import { createSearchAPI } from '@/lib/api/search';
import { SearchInput } from './SearchInput';

interface SearchResultsProps {
  storeHandle: string;
}

export const SearchResults: React.FC<SearchResultsProps> = ({ storeHandle }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const searchAPI = createSearchAPI(storeHandle);

  const {
    query,
    results,
    isLoading,
    error,
    totalHits,
    processingTimeMs,
    currentPage,
    limit,
    filters,
    sortBy,
    setQuery,
    setResults,
    setLoading,
    setError,
    setPage,
    setSortBy,
    addToHistory,
  } = useSearchStore();

  const [initialized, setInitialized] = useState(false);

  // Initialize search from URL params
  useEffect(() => {
    const urlQuery = searchParams.get('q') || '';
    const urlPage = parseInt(searchParams.get('page') || '1');
    const urlSort = searchParams.get('sort') || 'created_at:desc';

    if (urlQuery !== query) {
      setQuery(urlQuery);
    }
    if (urlPage !== currentPage) {
      setPage(urlPage);
    }
    if (urlSort !== sortBy) {
      setSortBy(urlSort);
    }

    setInitialized(true);
  }, [searchParams, setQuery, setPage, setSortBy, query, currentPage, sortBy]);

  // Perform search when query, page, or sort changes
  useEffect(() => {
    if (!initialized || !query.trim()) return;

    const performSearch = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await searchAPI.searchProducts(
          storeHandle,
          query,
          filters,
          {
            limit,
            offset: (currentPage - 1) * limit,
            sort: sortBy,
          }
        );

        setResults(response.hits, response.estimatedTotalHits, response.processingTimeMs);
        addToHistory(query);
      } catch (err) {
        console.error('Search failed:', err);
        setError(err instanceof Error ? err.message : 'Search failed');
      }
    };

    performSearch();
  }, [
    initialized,
    query,
    currentPage,
    limit,
    sortBy,
    filters,
    storeHandle,
    searchAPI,
    setLoading,
    setError,
    setResults,
    addToHistory,
  ]);

  // Update URL when search parameters change
  const updateURL = (newQuery?: string, newPage?: number, newSort?: string) => {
    const params = new URLSearchParams();
    
    if (newQuery || query) {
      params.set('q', newQuery || query);
    }
    if (newPage && newPage > 1) {
      params.set('page', newPage.toString());
    }
    if (newSort && newSort !== 'created_at:desc') {
      params.set('sort', newSort);
    }

    const newUrl = `/${storeHandle}/search?${params.toString()}`;
    router.push(newUrl, { scroll: false });
  };

  // Handle page change
  const handlePageChange = (event: React.ChangeEvent<unknown>, page: number) => {
    setPage(page);
    updateURL(undefined, page);
  };

  // Handle sort change
  const handleSortChange = (event: any) => {
    const newSort = event.target.value;
    setSortBy(newSort);
    setPage(1);
    updateURL(undefined, 1, newSort);
  };

  // Handle new search
  const handleNewSearch = (newQuery: string) => {
    setQuery(newQuery);
    setPage(1);
    updateURL(newQuery, 1);
  };

  // Handle product view
  const handleViewProduct = (product: any) => {
    router.push(`/${storeHandle}/products/${product.handle}`);
  };

  // Calculate pagination
  const totalPages = Math.ceil(totalHits / limit);

  if (!initialized) {
    return (
      <Box sx={{ p: 3 }}>
        <Skeleton variant="rectangular" height={60} sx={{ mb: 3 }} />
        <Grid container spacing={3}>
          {[...Array(6)].map((_, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Card>
                <Skeleton variant="rectangular" height={200} />
                <CardContent>
                  <Skeleton variant="text" height={30} />
                  <Skeleton variant="text" height={20} />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Search Header */}
      <Paper elevation={1} sx={{ p: 3, mb: 3 }}>
        <SearchInput
          storeHandle={storeHandle}
          onSearch={handleNewSearch}
          placeholder="Search products..."
        />
        
        {query && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="h6" gutterBottom>
              Search Results for "{query}"
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
              <Typography variant="body2" color="text.secondary">
                {isLoading ? 'Searching...' : `${totalHits} results found`}
                {processingTimeMs > 0 && ` in ${processingTimeMs}ms`}
              </Typography>
              
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Sort by</InputLabel>
                <Select
                  value={sortBy}
                  label="Sort by"
                  onChange={handleSortChange}
                  disabled={isLoading}
                >
                  <MenuItem value="created_at:desc">Newest First</MenuItem>
                  <MenuItem value="created_at:asc">Oldest First</MenuItem>
                  <MenuItem value="title:asc">Name A-Z</MenuItem>
                  <MenuItem value="title:desc">Name Z-A</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Box>
        )}
      </Paper>

      {/* Error State */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Loading State */}
      {isLoading && (
        <Grid container spacing={3}>
          {[...Array(6)].map((_, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Card>
                <Skeleton variant="rectangular" height={200} />
                <CardContent>
                  <Skeleton variant="text" height={30} />
                  <Skeleton variant="text" height={20} />
                  <Skeleton variant="text" height={20} />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Results */}
      {!isLoading && !error && results.length > 0 && (
        <>
          <Grid container spacing={3}>
            {results.map((product) => (
              <Grid item xs={12} sm={6} md={4} key={product.id}>
                <Card 
                  sx={{ 
                    height: '100%', 
                    display: 'flex', 
                    flexDirection: 'column',
                    transition: 'transform 0.2s, box-shadow 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4,
                    },
                  }}
                >
                  <CardMedia
                    component="img"
                    height="200"
                    image={product.thumbnail || '/placeholder-product.jpg'}
                    alt={product.title}
                    sx={{ objectFit: 'cover' }}
                  />
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Typography 
                      variant="h6" 
                      component="h3" 
                      gutterBottom
                      sx={{ 
                        fontSize: '1rem',
                        fontWeight: 600,
                        lineHeight: 1.3,
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                      }}
                    >
                      {product._formatted?.title || product.title}
                    </Typography>
                    
                    {product.description && (
                      <Typography 
                        variant="body2" 
                        color="text.secondary"
                        sx={{
                          display: '-webkit-box',
                          WebkitLineClamp: 3,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          mb: 1,
                        }}
                      >
                        {product._formatted?.description || product.description}
                      </Typography>
                    )}

                    {product.tags && product.tags.length > 0 && (
                      <Box sx={{ mt: 1 }}>
                        {product.tags.slice(0, 3).map((tag, index) => (
                          <Chip
                            key={index}
                            label={tag}
                            size="small"
                            variant="outlined"
                            sx={{ mr: 0.5, mb: 0.5 }}
                          />
                        ))}
                      </Box>
                    )}
                  </CardContent>
                  
                  <Divider />
                  
                  <CardActions sx={{ p: 2, justifyContent: 'space-between' }}>
                    <Button
                      size="small"
                      startIcon={<ViewIcon />}
                      onClick={() => handleViewProduct(product)}
                    >
                      View Details
                    </Button>
                    <Button
                      size="small"
                      variant="contained"
                      startIcon={<CartIcon />}
                      onClick={() => handleViewProduct(product)}
                    >
                      Add to Cart
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Pagination */}
          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Pagination
                count={totalPages}
                page={currentPage}
                onChange={handlePageChange}
                color="primary"
                size="large"
                showFirstButton
                showLastButton
              />
            </Box>
          )}
        </>
      )}

      {/* No Results */}
      {!isLoading && !error && results.length === 0 && query && (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <SearchIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            No results found for "{query}"
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            Try adjusting your search terms or browse our categories
          </Typography>
          <Button
            variant="contained"
            onClick={() => router.push(`/${storeHandle}`)}
          >
            Browse All Products
          </Button>
        </Paper>
      )}
    </Box>
  );
};
