'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getCMSPageById, updateCMSPageById, createCMSPageById, type CMSPageData, type StrapiResponse } from '@/lib/api/strapi/pages';
import { useToast } from '@/app/providers/toast-provider';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Stack,
  Paper,
  Divider,
  Container,
  Breadcrumbs,
  Link as MuiLink,
  InputAdornment,
  CircularProgress
} from '@mui/material';
import {
  Save as SaveIcon,
  Cancel as CancelIcon,
  Title as TitleIcon,
  Link as LinkIcon,
  NavigateNext as NavigateNextIcon,
  Preview as PreviewIcon,
  Article as ArticleIcon,
  ArrowBack as ArrowBackIcon
} from '@mui/icons-material';
import Link from 'next/link';
import { SimpleLexicalEditor } from '@/components/ui/SimpleLexicalEditor';

interface CMSPageFormData {
  title: string;
  slug: string;
  content: string;
}

interface AdminCMSPageFormProps {
  pageId?: string;
  mode: 'create' | 'edit';
  storeHandle: string;
}



export const AdminCMSPageForm: React.FC<AdminCMSPageFormProps> = ({ 
  pageId, 
  mode, 
  storeHandle 
}) => {
  const router = useRouter();
  const { showToast } = useToast();
  
  const [formData, setFormData] = useState<CMSPageFormData>({
    title: '',
    slug: '',
    content: ''
  });

  const [errors, setErrors] = useState<Partial<CMSPageFormData>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(mode === 'edit');
  const [showPreview, setShowPreview] = useState(false);

  // Fetch page data when in edit mode
  useEffect(() => {
    const fetchPageData = async () => {
      if (mode === 'edit' && pageId) {
        try {
          setIsLoadingData(true);
          
          console.log('📝 Fetching CMS page data for editing:', pageId);
          const response: StrapiResponse<CMSPageData> = await getCMSPageById(pageId);
          
          console.log('✅ CMS page data fetched successfully:', response);
          
          // Convert Strapi data to form format
          const pageData = response.data;
          setFormData({
            title: pageData.pageName || pageData.title || '',
            slug: pageData.pageSlug || pageData.slug || '',
            content: pageData.content || ''
          });
          
          showToast('Page data loaded successfully', 'success');
          
        } catch (error: any) {
          console.error('❌ Error fetching CMS page data:', error);
          
          // Check if it's an authentication error
          if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
            showToast('Authentication failed. Please check STRAPI_ACCESS_TOKEN configuration.', 'error');
          } else if (error.message?.includes('404') || error.message?.includes('Not Found')) {
            showToast('Page not found. It may have been deleted.', 'error');
            // Redirect back to list after a short delay
            setTimeout(() => {
              router.push(`/${storeHandle}/admin/cms`);
            }, 2000);
          } else {
            showToast(`Failed to load page data: ${error.message || 'Unknown error'}`, 'error');
          }
          
        } finally {
          setIsLoadingData(false);
        }
      } else {
        setIsLoadingData(false);
      }
    };

    fetchPageData();
  }, [mode, pageId, storeHandle]);

  const handleInputChange = (field: keyof CMSPageFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Auto-generate slug from title
    if (field === 'title' && typeof value === 'string') {
      const slug = value
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setFormData(prev => ({ ...prev, slug }));
    }
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<CMSPageFormData> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Page title is required';
    }

    if (!formData.slug.trim()) {
      newErrors.slug = 'Page slug is required';
    } else if (!/^[a-z0-9-]+$/.test(formData.slug)) {
      newErrors.slug = 'Slug can only contain lowercase letters, numbers, and hyphens';
    }

    if (!formData.content.trim()) {
      newErrors.content = 'Page content is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    
    try {
      if (mode === 'edit' && pageId) {
        // Update existing page
        console.log('💾 Updating CMS page:', pageId, formData);
        
        const updatePayload = {
          pageName: formData.title,
          pageSlug: formData.slug,
          content: formData.content,
          pageStoreName: storeHandle
        };
        
        const response = await updateCMSPageById(pageId, updatePayload);
        
        console.log('✅ CMS page updated successfully:', response);
        if(response?.data?.id) showToast('Page updated successfully!', 'success');
        
      } else {
        // Create new page
        console.log('🆕 Creating new CMS page:', formData);
        
        const createPayload = {
          pageName: formData.title,
          pageSlug: formData.slug,
          content: formData.content,
          pageStoreName: storeHandle
        };
        
        const response = await createCMSPageById(createPayload);
        
        console.log('✅ CMS page created successfully:', response);
        if(response?.data?.id){

          showToast('Page created successfully!', 'success');
          router.push(`/${storeHandle}/admin/cms`);
        }

      }
      
      // Redirect back to CMS pages list after a short delay
 
      
    } catch (error: any) {
      console.error('❌ Error saving CMS page:', error);
      
      // Check if it's an authentication error
      if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
        showToast('Authentication failed. Please check STRAPI_ACCESS_TOKEN configuration.', 'error');
      } else if (error.message?.includes('404') || error.message?.includes('Not Found')) {
        showToast('Page not found. It may have been deleted.', 'error');
      } else {
        showToast(`Failed to save page: ${error.message || 'Unknown error'}`, 'error');
      }
      
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push(`/${storeHandle}/admin/cms`);
  };

  const stripHtml = (html: string) => {
    return html.replace(/<[^>]*>/g, '');
  };

  // Show loading state while fetching page data
  if (isLoadingData) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" minHeight="400px">
          <CircularProgress size={48} sx={{ mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            Loading page data...
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Fetching page information for editing
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs 
        separator={<NavigateNextIcon fontSize="small" />}
        sx={{ mb: 3 }}
      >
         <Button
            component={Link}
            href={`/${storeHandle}/admin/cms`}
            startIcon={<ArrowBackIcon />}
            variant="text"
            color="inherit"
            sx={{ 
              textTransform: 'none',
              color: 'text.secondary',
              '&:hover': {
                color: 'primary.main',
                bgcolor: 'transparent'
              }
            }}
          >
            Back to CMS Pages
          </Button>

        
      
      </Breadcrumbs>

      {/* Page Header */}
      <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={4}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight={600}>
            {mode === 'create' ? 'Create New Page' : 'Edit Page'}
          </Typography>
          <Typography variant="body1" color="text.secondary">
            {mode === 'create' 
              ? 'Create a new content page for your store'
              : 'Update page content and settings'
            }
          </Typography>
        </Box>
        
        
      </Box>

      {/* Action Buttons */}
      <Box display="flex" justifyContent="flex-end" gap={2} mb={4}>
        <Button
          variant="outlined"
          startIcon={<CancelIcon />}
          onClick={handleCancel}
          disabled={isLoading}
          size="large"
          sx={{
            borderRadius: 2,
            textTransform: 'none',
            px: 3
          }}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="contained"
          onClick={handleSubmit}
          startIcon={isLoading ? null : <SaveIcon />}
          disabled={isLoading}
          size="large"
          sx={{
            borderRadius: 2,
            textTransform: 'none',
            px: 3
          }}
        >
          {isLoading ? 'Saving...' : (mode === 'create' ? 'Create Page' : 'Update Page')}
        </Button>
      </Box>

      <form >
        <Grid container spacing={4}>
          {/* Main Form */}
          <Grid item size={12}>
            <Stack spacing={3}>
              {/* Basic Information */}
              <Card elevation={1} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <TitleIcon color="primary" />
                    Basic Information
                  </Typography>
                  <Divider sx={{ mb: 3 }} />
                  
                  <Grid container spacing={3} size={12}>
                    <Grid item size={{ xs: 12, md: 4 }}>
                      <TextField
                        fullWidth
                        label="Page Title"
                        value={formData.title}
                        onChange={(e) => handleInputChange('title', e.target.value)}
                        error={!!errors.title}
                        helperText={errors.title || 'Enter a descriptive title for your page'}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <TitleIcon color="action" />
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Grid>
                    
                    <Grid item size={{ xs: 12, md: 4 }}>
                      <TextField
                        fullWidth
                        label="Page Slug"
                        value={formData.slug}
                        onChange={(e) => handleInputChange('slug', e.target.value)}
                        error={!!errors.slug}
                        helperText={errors.slug || 'URL-friendly version of the title (auto-generated)'}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <LinkIcon color="action" />
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Page Content */}
              <Card elevation={1} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <ArticleIcon color="primary" />
                    Page Content
                  </Typography>
                  <Divider sx={{ mb: 3 }} />
                  
                  <SimpleLexicalEditor
                    value={formData.content}
                    onChange={(value) => handleInputChange('content', value)}
                    error={!!errors.content}
                    helperText={errors.content || 'Use the toolbar above to format your content with headings, lists, and text styling'}
                    placeholder="Start writing your page content here..."
                  />
                </CardContent>
              </Card>



              {/* Preview */}
              {showPreview && formData.content && (
                <Card elevation={1} sx={{ borderRadius: 2 }}>
                  <CardContent sx={{ p: 4 }}>
                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PreviewIcon color="primary" />
                      Content Preview
                    </Typography>
                    <Divider sx={{ mb: 3 }} />
                    
                    <Paper 
                      elevation={0} 
                      sx={{ 
                        p: 3, 
                        borderRadius: 2,
                        border: '1px solid',
                        borderColor: 'divider',
                        bgcolor: 'grey.50'
                      }}
                    >
                      <Typography variant="h4" gutterBottom>
                        {formData.title || 'Page Title'}
                      </Typography>
                      <Box 
                        sx={{ 
                          lineHeight: 1.7,
                          '& h1': {
                            fontSize: '2rem',
                            fontWeight: 'bold',
                            margin: '16px 0 8px 0'
                          },
                          '& h2': {
                            fontSize: '1.5rem',
                            fontWeight: 'bold',
                            margin: '14px 0 6px 0'
                          },
                          '& h3': {
                            fontSize: '1.25rem',
                            fontWeight: 'bold',
                            margin: '12px 0 4px 0'
                          },
                          '& p': {
                            margin: '8px 0'
                          },
                          '& ul, & ol': {
                            margin: '8px 0',
                            paddingLeft: '24px'
                          },
                          '& li': {
                            margin: '4px 0'
                          },
                          '& strong': {
                            fontWeight: 'bold'
                          },
                          '& em': {
                            fontStyle: 'italic'
                          },
                          '& u': {
                            textDecoration: 'underline'
                          }
                        }}
                        dangerouslySetInnerHTML={{ __html: formData.content || '<p>No content to preview</p>' }}
                      />
                    </Paper>
                  </CardContent>
                </Card>
              )}
            </Stack>
          </Grid>
        </Grid>
      </form>
    </Container>
  );
};