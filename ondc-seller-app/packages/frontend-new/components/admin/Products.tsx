"use client";

import { useMedusaProducts } from "@/hooks/medusa/useProduct";

export default function Products() {
  const { data, isLoading, error } = useMedusaProducts();

  if (isLoading) return <p>Loading products...</p>;
  if (error) return <p>Error loading products</p>;

  return (
    <div>
      <h2>Products</h2>
      <ul>
        {data?.products?.map((product: any) => (
          <li key={product.id}>{product.title}</li>
        ))}
      </ul>
    </div>
  );
}
