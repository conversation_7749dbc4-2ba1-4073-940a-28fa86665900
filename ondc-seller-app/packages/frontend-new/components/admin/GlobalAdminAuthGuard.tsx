'use client';

import React, { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import { Box, CircularProgress, Typography } from '@mui/material';

/**
 * Enhanced GlobalAdminAuthGuard with reactive authentication
 * - Waits for proper hydration before checking auth
 * - Uses reactive authentication (responds to 401 API errors)
 * - No periodic token checking - more efficient
 */

interface GlobalAdminAuthGuardProps {
  children: React.ReactNode;
}

// Utility function to handle 401 responses for global auth
const handleGlobalUnauthorizedResponse = () => {
  console.log('🚨 401 Unauthorized detected - clearing global auth and redirecting');
  
  try {
    // Clear main auth store
    const { clearAuth } = useAuthStore.getState();
    clearAuth();
    
    // Clear additional localStorage items
    if (typeof window !== 'undefined') {
      try {
        localStorage.removeItem('ondc_auth_token');
        console.log('🧹 Additional auth tokens cleared');
      } catch (error) {
        console.error('Error clearing additional localStorage items:', error);
      }
    }
    
    // Redirect to login
    if (typeof window !== 'undefined') {
      window.location.href = '/login?tab=login';
    }
    
  } catch (error) {
    console.error('❌ Error handling unauthorized response:', error);
    // Fallback: force redirect
    if (typeof window !== 'undefined') {
      window.location.href = '/login?tab=login';
    }
  }
};

// Enhanced fetch wrapper for global auth
const setupGlobalApiInterceptor = () => {
  // Override fetch globally to handle 401s
  const originalFetch = window.fetch;
  window.fetch = async (...args) => {
    try {
      const response = await originalFetch(...args);
      
      // Check for 401 Unauthorized
      if (response.status === 401) {
        console.log('🚨 Global API returned 401 Unauthorized:', args[0]);
        handleGlobalUnauthorizedResponse();
      }
      
      return response;
    } catch (error) {
      throw error;
    }
  };
};

export const GlobalAdminAuthGuard: React.FC<GlobalAdminAuthGuardProps> = ({ children }) => {
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const redirectAttempted = useRef(false);
  
  const mainAuth = useAuthStore();

  useEffect(() => {
    // Setup global API interceptor for reactive auth
    setupGlobalApiInterceptor();
  }, []);

  useEffect(() => {
    const checkAuthentication = async () => {
      console.log('🔍 GlobalAdminAuthGuard: Checking authentication state');
      
      try {
        // Wait for hydration to complete
        if (!mainAuth.hasHydrated) {
          console.log('⏳ Waiting for hydration to complete...');
          return; // Don't check auth yet
        }

        console.log('✅ Hydration complete, checking global auth...');
        
        // Check authentication state
        const hasMainAuth = mainAuth.isAuthenticated && mainAuth.token && mainAuth.user;
        
        console.log('Global auth status:', {
          isAuthenticated: mainAuth.isAuthenticated,
          hasToken: !!mainAuth.token,
          hasUser: !!mainAuth.user
        });

        // If no authentication found after hydration, redirect
        if (!hasMainAuth && !redirectAttempted.current) {
          redirectAttempted.current = true;
          console.log('❌ No authentication found after hydration - redirecting');
          
          // Use Promise.resolve to avoid setTimeout
          Promise.resolve().then(() => {
            router.replace('/login?tab=login');
          });
          return;
        }

        // If we have auth, allow access
        if (hasMainAuth) {
          console.log('✅ Global authentication valid - allowing access');
          setIsAuthorized(true);
        }

      } catch (error) {
        console.error('Error during authentication check:', error);
        if (!redirectAttempted.current) {
          redirectAttempted.current = true;
          router.replace('/login?tab=login');
        }
      } finally {
        setIsChecking(false);
      }
    };

    checkAuthentication();
  }, [router, mainAuth.hasHydrated, mainAuth.isAuthenticated, mainAuth.token, mainAuth.user]);

  // Reset redirect flag when authentication state changes to authenticated
  useEffect(() => {
    if (mainAuth.isAuthenticated && redirectAttempted.current) {
      redirectAttempted.current = false;
    }
  }, [mainAuth.isAuthenticated]);

  // Show loading state while checking authentication
  if (isChecking || !mainAuth.hasHydrated) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          gap: 2,
          backgroundColor: 'background.default'
        }}
      >
        <CircularProgress size={40} />
        <Typography variant="body2" color="text.secondary">
          {!mainAuth.hasHydrated ? 'Loading authentication...' : 'Verifying access...'}
        </Typography>
        {process.env.NODE_ENV === 'development' && (
          <Typography variant="caption" color="text.disabled" sx={{ mt: 2 }}>
            Debug: Hydrated={mainAuth.hasHydrated.toString()}, Auth={mainAuth.isAuthenticated.toString()}
          </Typography>
        )}
      </Box>
    );
  }

  // Show nothing if redirect is in progress
  if (!isAuthorized) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          gap: 2,
        }}
      >
        <CircularProgress size={40} />
        <Typography variant="body2" color="text.secondary">
          Redirecting to login...
        </Typography>
      </Box>
    );
  }

  // Render children if authenticated and hydrated
  console.log('✅ GlobalAdminAuthGuard: Rendering protected content');
  return <>{children}</>;
};