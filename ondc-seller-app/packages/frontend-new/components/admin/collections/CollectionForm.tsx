'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  Box,
  Typography,
  TextField,
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  Stack,
  CircularProgress,
  Divider,
} from '@mui/material';
import {
  Save,
  ArrowBack,
  Delete,
  Collections,
} from '@mui/icons-material';
import { useToast } from '@/app/providers/toast-provider';
import { medusaAdminService } from '@/lib/api/medusa-admin';
import { ConfirmationModal } from '@/components/ui/ConfirmationModal';

interface CollectionFormData {
  title: string;
  handle: string;
}

export const CollectionForm: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const { showToast } = useToast();
  
  const collectionId = params?.id as string;
  const dynamicStoreHandle = params?.storeHandle as string;
  const isEdit = Boolean(collectionId && collectionId !== 'new');

  const [formData, setFormData] = useState<CollectionFormData>({
    title: '',
    handle: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const loadCollection = useCallback(async () => {
    try {
      setIsLoading(true);
      const collection = await medusaAdminService.getCollection(dynamicStoreHandle, collectionId);
      setFormData({
        title: collection.title || '',
        handle: collection.handle || '',
      });
    } catch (error) {
      console.error('Error loading collection:', error);
      showToast('Failed to load collection data', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [dynamicStoreHandle, collectionId, showToast]);

  useEffect(() => {
    if (isEdit && collectionId) {
      loadCollection();
    }
  }, [isEdit, collectionId, loadCollection]);

  const handleInputChange = (field: keyof CollectionFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Collection title is required';
    }

    if (!formData.handle.trim()) {
      newErrors.handle = 'Collection handle is required';
    } else if (!/^[a-z0-9-]+$/.test(formData.handle)) {
      newErrors.handle = 'Handle must contain only lowercase letters, numbers, and hyphens';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);
      
      if (isEdit) {
        await medusaAdminService.updateCollection(dynamicStoreHandle, collectionId, formData.title, formData.handle);
        showToast('Collection updated successfully!', 'success');
      } else {
        await medusaAdminService.createCollection(dynamicStoreHandle, formData.title, formData.handle);
        showToast('Collection created successfully!', 'success');
      }
      
      router.push(`/${dynamicStoreHandle}/admin/collections`);
    } catch (error) {
      console.error('Error saving collection:', error);
      showToast(`Failed to ${isEdit ? 'update' : 'create'} collection`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      await medusaAdminService.deleteCollection(dynamicStoreHandle, collectionId);
      showToast('Collection deleted successfully!', 'success');
      router.push(`/${dynamicStoreHandle}/admin/collections`);
    } catch (error) {
      console.error('Error deleting collection:', error);
      showToast('Failed to delete collection', 'error');
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  const handleCancel = () => {
    router.push(`/${dynamicStoreHandle}/admin/collections`);
  };

  if (isEdit && isLoading && !formData.title) {
    return (
      <Box sx={{ maxWidth: 600, mx: 'auto', p: 3, textAlign: 'center' }}>
        <CircularProgress size={48} />
        <Typography variant="body1" sx={{ mt: 2 }}>
          Loading collection data...
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{  p: 3 }}>
      {/* Header */}
      <Stack spacing={3} sx={{ mb: 4 }}>
        <Button
          onClick={handleCancel}
          startIcon={<ArrowBack />}
          variant="text"
          sx={{ alignSelf: 'flex-start' }}
        >
          Back to Collections
        </Button>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
            {isEdit ? 'Edit Collection' : 'Add New Collection'}
          </Typography>
          <Typography variant="body1" color="text.secondary">
            {isEdit ? 'Update collection information and settings.' : 'Create a new collection for organizing your products.'}
          </Typography>
        </Box>
      </Stack>

      {/* Form */}
      <Card>
        <CardHeader 
          title={isEdit ? 'Collection Details' : 'New Collection'}
          avatar={<Collections />}
        />
        <CardContent>
          <Stack component="form" onSubmit={handleSubmit} spacing={3}>
            {/* Collection Title */}
            <TextField
              label="Collection Title"
              value={formData.title || ''}
              onChange={(e) => handleInputChange('title', e.target.value)}
              error={!!errors.title}
              helperText={errors.title || 'A descriptive title for your collection (e.g., "Summer Sale", "Featured Products")'}
              placeholder="Enter collection title"
              required
              fullWidth
            />

            {/* Collection Handle */}
            <TextField
              label="Collection Handle"
              value={formData.handle || ''}
              onChange={(e) => handleInputChange('handle', e.target.value)}
              error={!!errors.handle}
              helperText={errors.handle || 'URL-friendly version of the collection name. Only lowercase letters, numbers, and hyphens allowed.'}
              placeholder="collection-handle"
              required
              fullWidth
            />

            <Divider />

            {/* Form Actions */}
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              {isEdit && (
                <Button
                  type="button"
                  onClick={() => setShowDeleteConfirm(true)}
                  disabled={isDeleting || isLoading}
                  variant="contained"
                  color="error"
                  startIcon={isDeleting ? <CircularProgress size={16} /> : <Delete />}
                >
                  {isDeleting ? 'Deleting...' : 'Delete Collection'}
                </Button>
              )}
              
              <Stack direction="row" spacing={2} justifyContent="space-between" alignItems="center" sx={{width:'100%'}}>
                <Button
                  type="button"
                  onClick={handleCancel}
                  variant="outlined"
                  size="large"
                >
                  {isEdit ? 'Back to Collections' : 'Cancel'}
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading || isDeleting}
                  variant="contained"
                  size="large"
                  startIcon={isLoading ? <CircularProgress size={16} /> : <Save />}
                >
                  {isLoading 
                    ? (isEdit ? 'Updating...' : 'Creating...') 
                    : (isEdit ? 'Update Collection' : 'Create Collection')
                  }
                </Button>
              </Stack>
            </Stack>
          </Stack>
        </CardContent>
      </Card>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={handleDelete}
        title="Delete Collection"
        message={`Are you sure you want to delete the collection "${formData.title}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        confirmButtonColor="red"
        icon="danger"
        isLoading={isDeleting}
      />
    </Box>
  );
};
