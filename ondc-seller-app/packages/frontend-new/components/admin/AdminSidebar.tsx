'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Collapse,
  Badge,
  Divider,
  IconButton,
  Button,
} from '@mui/material';
import {
  Dashboard,
  Inventory,
  Category,
  Collections,
  ShoppingCart,
  People,
  Analytics,
  Settings,
  Store,
  Logout,
  ExpandLess,
  ExpandMore,
  Menu,
  Tag,
  ViewCarousel,
  Article
} from '@mui/icons-material';
import { useAdminNavigation } from '@/hooks/useAdminNavigation';
import { useAuthStore } from '@/stores/authStore';
import { useGlobalLoading } from '@/components/loading/GlobalLoadingProvider';

interface SidebarItem {
  name: string;
  href: string;
  icon: React.ReactNode;
  badge?: string;
  children?: SidebarItem[];
}

const getSidebarItems = (storeHandle: string): SidebarItem[] => [
  {
    name: 'Dashboard',
    href: `/${storeHandle}/admin`,
    icon: <Dashboard />,
  },
  {
    name: 'Products',
    href: `/${storeHandle}/admin/products`,
    icon: <Inventory />,
  },
  {
    name: 'Categories',
    href: `/${storeHandle}/admin/categories`,
    icon: <Category />,
  },
  {
    name: 'Collections',
    href: `/${storeHandle}/admin/collections`,
    icon: <Collections />,
  },
  {
    name: 'Tags',
    href: `/${storeHandle}/admin/tags`,
    icon: <Tag />,
  },
  {
    name: 'Banners',
    href: `/${storeHandle}/admin/banners`,
    icon: <ViewCarousel />,
  },
  {
    name: 'CMS Pages',
    href: `/${storeHandle}/admin/cms`,
    icon: <Article />,
  },
  {
    name: 'Orders',
    href: `/${storeHandle}/admin/orders`,
    icon: <ShoppingCart />,
    // badge: '12',
  },
  {
    name: 'Customers',
    href: `/${storeHandle}/admin/customers`,
    icon: <People />,
  },
  // {
  //   name: 'Analytics',
  //   href: `/${storeHandle}/admin/analytics`,
  //   icon: <Analytics />,
  // },
  {
    name: 'Store Settings',
    href: `/${storeHandle}/admin/store-settings`,
    icon: <Settings />,
  },
];

interface AdminSidebarProps {
  storeHandle: string;
}

export const AdminSidebar: React.FC<AdminSidebarProps> = ({ storeHandle }) => {
  const pathname = usePathname();
  const router = useRouter();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const { isNavigating, setIsNavigating } = useAdminNavigation();
  const { user, getStoreHandle } = useAuthStore();
  const { loadingState } = useGlobalLoading();
  
  // Handle null user state during logout process
  const isUserNull = !user;
  
  // Extract dynamic data from auth store
  const dynamicStoreHandle = getStoreHandle() || storeHandle;
  const storeName = user?.user?.metadata?.store_name ||
                   user?.metadata?.store_name ||
                   (storeHandle ? storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'My Store');
  const storeUrl = `${dynamicStoreHandle}.com`; // You can customize this based on your domain structure
  
  const sidebarItems = getSidebarItems(dynamicStoreHandle);

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    );
  };

  const isActive = (href: string) => {
    if (href === `/${dynamicStoreHandle}/admin`) {
      return pathname === `/${dynamicStoreHandle}/admin`;
    }
    return pathname.startsWith(href);
  };

  // Check if we're in logout loading state
  const isLogoutLoading = loadingState.isLoading && loadingState.actionId === 'user-logout';
  
  // Hide sidebar completely during logout to avoid fragmented UI
  if (isLogoutLoading || isUserNull) {
    return null;
  }

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: 256,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: 256,
          boxSizing: 'border-box',
          top: 80, // Account for header height
          height: 'calc(100vh - 80px)',
          borderRight: 1,
          borderColor: 'divider',
        },
      }}
    >
      <Box sx={{ p: 2 }}>
        <List>
          {sidebarItems.map((item) => (
            <ListItem key={item.name} disablePadding>
              <ListItemButton
                component={Link}
                href={item.href}
                selected={isActive(item.href)}
                onClick={(e) => {
                  if (!isActive(item.href)) {
                    setIsNavigating(true);
                    // Trigger global navigation event
                    window.dispatchEvent(new CustomEvent('admin-navigation-start'));
                  }
                }}
                sx={{
                  borderRadius: 1,
                  mb: 0.5,
                  opacity: isNavigating && !isActive(item.href) ? 0.5 : 1,
                  pointerEvents: isNavigating && !isActive(item.href) ? 'none' : 'auto',
                }}
              >
                <ListItemIcon sx={{ color: isActive(item.href) ? 'primary.main' : 'text.secondary' }}>
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.name}
                  sx={{
                    '& .MuiListItemText-primary': {
                      fontSize: '0.875rem',
                      fontWeight: isActive(item.href) ? 600 : 400,
                    }
                  }}
                />

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {item.badge && (
                    <Badge
                      badgeContent={item.badge}
                      color="error"
                      sx={{ '& .MuiBadge-badge': { fontSize: '0.75rem' } }}
                    />
                  )}
                  {item.children && (
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.preventDefault();
                        toggleExpanded(item.name);
                      }}
                    >
                      {expandedItems.includes(item.name) ? <ExpandLess /> : <ExpandMore />}
                    </IconButton>
                  )}
                </Box>
              </ListItemButton>

              {/* Submenu */}
              {item.children && (
                <Collapse in={expandedItems.includes(item.name)} timeout="auto" unmountOnExit>
                  <List component="div" disablePadding>
                    {item.children.map((child) => (
                      <ListItemButton
                        key={child.name}
                        component={Link}
                        href={child.href}
                        selected={isActive(child.href)}
                        sx={{ pl: 4, borderRadius: 1, mb: 0.5 }}
                      >
                        <ListItemIcon sx={{ color: isActive(child.href) ? 'primary.main' : 'text.secondary' }}>
                          {child.icon}
                        </ListItemIcon>
                        <ListItemText
                          primary={child.name}
                          sx={{
                            '& .MuiListItemText-primary': {
                              fontSize: '0.875rem',
                              fontWeight: isActive(child.href) ? 600 : 400,
                            }
                          }}
                        />
                      </ListItemButton>
                    ))}
                  </List>
                </Collapse>
              )}
            </ListItem>
          ))}
        </List>

        {/* Store Info */}
        <Divider sx={{ my: 2 }} />
        <Box >
          <Box sx={{
            background: 'linear-gradient(to right, #eff6ff, #f3e8ff)',
            borderRadius: 2,
            p: 2,
            border: 1,
            borderColor: '#0000001f'
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box sx={{ flex: 1, minWidth: 0 }}>
                <Typography variant="body2" sx={{ fontWeight: 600, color: 'text.primary' }} noWrap>
                  {storeName}
                </Typography>
                <Typography variant="caption" sx={{ color: 'primary.main', display: 'block' }} noWrap>
                  @{dynamicStoreHandle}
                </Typography>
              </Box>
            </Box>
            <Divider sx={{ my: 1, borderColor: 'primary.100' }} />
            <Button
              component={Link}
              href={`/${dynamicStoreHandle}`}
              target="_blank"
              size="small"
              startIcon={<Store />}
              sx={{ fontSize: '0.75rem', color: 'primary.main' }}
            >
              View Store
            </Button>
          </Box>
        </Box>
      </Box>
    </Drawer>
  );
};