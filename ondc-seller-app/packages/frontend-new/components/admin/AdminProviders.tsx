'use client';

import React, { ReactNode } from 'react';
import { AdminNavigationLoader } from './AdminNavigationLoader';

interface AdminProvidersProps {
  children: ReactNode;
}

export const AdminProviders: React.FC<AdminProvidersProps> = ({ children }) => {
  // Removed duplicate QueryClientProvider - using the one from main app providers
  return (
    <>
      {children}
      <AdminNavigationLoader />
    </>
  );
};