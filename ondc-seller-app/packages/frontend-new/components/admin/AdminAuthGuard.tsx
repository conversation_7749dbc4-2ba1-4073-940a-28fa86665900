'use client';

import React, { useEffect, useState, useRef } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
// Removed useStoreAuthStore - Admin auth guard should only use main auth
import { Box, CircularProgress, Typography } from '@mui/material';

/**
 * Enhanced AdminAuthGuard with reactive authentication
 * - Waits for proper hydration before checking auth
 * - Uses reactive authentication (responds to 401 API errors)
 * - No periodic token checking - more efficient
 */

interface AdminAuthGuardProps {
  children: React.ReactNode;
}

// Utility function to handle 401 responses
const handleUnauthorizedResponse = (storeHandle?: string) => {
  console.log('🚨 401 Unauthorized detected - clearing auth and redirecting');
  
  try {
    // Clear main auth store
    const { clearAuth } = useAuthStore.getState();
    clearAuth();
    
    // Clear store-specific auth if storeHandle provided
    if (storeHandle && typeof window !== 'undefined') {
      try {
        // Clear store-specific localStorage items
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && key.includes(storeHandle)) {
            keysToRemove.push(key);
          }
        }
        
        keysToRemove.forEach(key => {
          localStorage.removeItem(key);
          console.log(`🧹 Removed localStorage key: ${key}`);
        });
      } catch (error) {
        console.error('Error clearing store-specific localStorage:', error);
      }
    }
    
    // Redirect to login
    if (typeof window !== 'undefined') {
      window.location.href = '/login?tab=login';
    }
    
  } catch (error) {
    console.error('❌ Error handling unauthorized response:', error);
    // Fallback: force redirect
    if (typeof window !== 'undefined') {
      window.location.href = '/login?tab=login';
    }
  }
};

// Enhanced fetch wrapper that handles 401 responses automatically
const setupApiInterceptor = (storeHandle?: string) => {
  // Override fetch globally to handle 401s
  const originalFetch = window.fetch;
  window.fetch = async (...args) => {
    try {
      const response = await originalFetch(...args);
      
      // Check for 401 Unauthorized
      if (response.status === 401) {
        console.log('🚨 API returned 401 Unauthorized:', args[0]);
        handleUnauthorizedResponse(storeHandle);
      }
      
      return response;
    } catch (error) {
      throw error;
    }
  };
};

export const AdminAuthGuard: React.FC<AdminAuthGuardProps> = ({ children }) => {
  const router = useRouter();
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  const [isChecking, setIsChecking] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const redirectAttempted = useRef(false);
  
  // Only use main auth for admin authentication
  const mainAuth = useAuthStore();

  useEffect(() => {
    // Setup API interceptor for reactive auth
    setupApiInterceptor(storeHandle);
  }, [storeHandle]);

  useEffect(() => {
    const checkAuthentication = async () => {
      console.log('🔍 AdminAuthGuard: Checking admin authentication state');
      console.log('Store handle:', storeHandle);
      console.log('Current URL:', typeof window !== 'undefined' ? window.location.href : 'N/A');
      
      try {
        // Wait for main auth hydration to complete
        const mainHydrated = mainAuth.hasHydrated;
        
        if (!mainHydrated) {
          console.log('⏳ Waiting for main auth hydration to complete...');
          console.log('Main hydrated:', mainHydrated);
          return; // Don't check auth yet
        }

        console.log('✅ Main auth hydration complete, checking admin auth...');
        
        // Add a small delay to ensure all auth state is properly set
        // await new Promise(resolve => setTimeout(resolve, 100));
        
        // Re-get the auth state after the delay
        const currentAuth = useAuthStore.getState();
        
        // Check main authentication state (admin only)
        const hasMainAuth = currentAuth.isAuthenticated && currentAuth.token && currentAuth.user;
        
        console.log('Admin auth status (after delay):', {
          isAuthenticated: currentAuth.isAuthenticated,
          hasToken: !!currentAuth.token,
          hasUser: !!currentAuth.user,
          hasMainAuth: hasMainAuth,
          userEmail: currentAuth.user?.email,
          tokenLength: currentAuth.token?.length
        });

        // If no admin authentication found after hydration, redirect
        if (!hasMainAuth && !redirectAttempted.current) {
          redirectAttempted.current = true;
          console.log('❌ No admin authentication found after hydration - redirecting to login');
          console.log('Full auth state for debugging:', {
            token: currentAuth.token,
            user: currentAuth.user,
            isAuthenticated: currentAuth.isAuthenticated,
            hasHydrated: currentAuth.hasHydrated
          });
          
          // Use Promise.resolve to avoid setTimeout
          Promise.resolve().then(() => {
            router.replace('/login?tab=login');
          });
          return;
        }

        // If we have admin auth, allow access
        if (hasMainAuth) {
          console.log('✅ Admin authentication valid - allowing access');
          setIsAuthorized(true);
        }

      } catch (error) {
        console.error('Error during authentication check:', error);
        if (!redirectAttempted.current) {
          redirectAttempted.current = true;
          router.replace('/login?tab=login');
        }
      } finally {
        setIsChecking(false);
      }
    };

    checkAuthentication();
  }, [
    router, 
    storeHandle, 
    mainAuth.hasHydrated,
    mainAuth.isAuthenticated, 
    mainAuth.token, 
    mainAuth.user
  ]);

  // Reset redirect flag when authentication state changes to authenticated
  useEffect(() => {
    if (mainAuth.isAuthenticated && redirectAttempted.current) {
      redirectAttempted.current = false;
    }
  }, [mainAuth.isAuthenticated]);

  // Show loading state while checking authentication
  if (isChecking || !mainAuth.hasHydrated) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          gap: 2,
          backgroundColor: 'background.default'
        }}
      >
        <CircularProgress size={40} />
        <Typography variant="body2" color="text.secondary">
          {!mainAuth.hasHydrated ? 'Loading admin authentication...' : 'Verifying admin access...'}
        </Typography>

      </Box>
    );
  }

  // Show nothing if redirect is in progress
  if (!isAuthorized) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          gap: 2,
        }}
      >
        <CircularProgress size={40} />
        <Typography variant="body2" color="text.secondary">
          Redirecting to login...
        </Typography>
      </Box>
    );
  }

  // Render children if authenticated and hydrated
  console.log('✅ AdminAuthGuard: Rendering protected content');
  return <>{children}</>;
};