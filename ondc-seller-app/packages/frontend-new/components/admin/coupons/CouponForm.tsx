'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface CouponRule {
  id: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'starts_with' | 'ends_with';
  attribute: 'cart_total' | 'product_category' | 'product_tag' | 'customer_email' | 'customer_group' | 'quantity' | 'product_sku';
  value: string;
  description: string;
}

interface Coupon {
  id: string;
  code: string;
  name: string;
  description: string;
  rules: CouponRule[];
  status: 'draft' | 'active' | 'inactive';
  discountType: 'percentage' | 'fixed_amount';
  discountValue: number;
  usageLimit: number;
  usedCount: number;
  validFrom: string;
  validTo: string;
  createdAt: string;
}

interface CouponFormProps {
  isEdit?: boolean;
  couponId?: string;
}

// Mock coupons for edit mode
const mockCoupons: Coupon[] = [
  {
    id: '1',
    code: 'SAVE20',
    name: '20% Off Summer Sale',
    description: 'Get 20% off on all summer collection items',
    rules: [
      {
        id: '1',
        operator: 'greater_than',
        attribute: 'cart_total',
        value: '1000',
        description: 'Cart total must be greater than ₹1000'
      },
      {
        id: '2',
        operator: 'contains',
        attribute: 'product_category',
        value: 'summer',
        description: 'Product category must contain "summer"'
      }
    ],
    status: 'active',
    discountType: 'percentage',
    discountValue: 20,
    usageLimit: 100,
    usedCount: 25,
    validFrom: '2024-01-01',
    validTo: '2024-12-31',
    createdAt: '2024-01-15'
  },
  {
    id: '2',
    code: 'NEWUSER50',
    name: 'New User Discount',
    description: 'Special discount for first-time customers',
    rules: [
      {
        id: '3',
        operator: 'equals',
        attribute: 'customer_group',
        value: 'new',
        description: 'Customer group must be "new"'
      }
    ],
    status: 'active',
    discountType: 'fixed_amount',
    discountValue: 50,
    usageLimit: 500,
    usedCount: 120,
    validFrom: '2024-01-01',
    validTo: '2024-12-31',
    createdAt: '2024-01-10'
  }
];

// Operator options
const operatorOptions = [
  { value: 'equals', label: 'Equals' },
  { value: 'not_equals', label: 'Not Equals' },
  { value: 'greater_than', label: 'Greater Than' },
  { value: 'less_than', label: 'Less Than' },
  { value: 'contains', label: 'Contains' },
  { value: 'starts_with', label: 'Starts With' },
  { value: 'ends_with', label: 'Ends With' },
];

// Attribute options
const attributeOptions = [
  { value: 'cart_total', label: 'Cart Total' },
  { value: 'product_category', label: 'Product Category' },
  { value: 'product_tag', label: 'Product Tag' },
  { value: 'customer_email', label: 'Customer Email' },
  { value: 'customer_group', label: 'Customer Group' },
  { value: 'quantity', label: 'Quantity' },
  { value: 'product_sku', label: 'Product SKU' },
];

export const CouponForm: React.FC<CouponFormProps> = ({ isEdit = false, couponId }) => {
  const router = useRouter();
  const params = useParams();
  const storeHandle = params.storeHandle as string;

  const [formData, setFormData] = useState<Partial<Coupon>>({
    code: '',
    name: '',
    description: '',
    rules: [],
    status: 'draft',
    discountType: 'percentage',
    discountValue: 0,
    usageLimit: 0,
    validFrom: '',
    validTo: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Load coupon data for edit mode
  useEffect(() => {
    if (isEdit && couponId) {
      setIsLoading(true);
      // Simulate API call
      setTimeout(() => {
        const coupon = mockCoupons.find(c => c.id === couponId);
        if (coupon) {
          setFormData(coupon);
        }
        setIsLoading(false);
      }, 500);
    }
  }, [isEdit, couponId]);

  // Auto-generate coupon code
  const generateCouponCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setFormData(prev => ({ ...prev, code: result }));
  };

  const handleInputChange = (field: keyof Coupon, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const addRule = () => {
    const newRule: CouponRule = {
      id: Math.random().toString(36).substr(2, 9),
      operator: 'equals',
      attribute: 'cart_total',
      value: '',
      description: '',
    };
    setFormData(prev => ({
      ...prev,
      rules: [...(prev.rules || []), newRule]
    }));
  };

  const updateRule = (index: number, updatedRule: CouponRule) => {
    const newRules = [...(formData.rules || [])];
    newRules[index] = updatedRule;
    setFormData(prev => ({ ...prev, rules: newRules }));
  };

  const removeRule = (index: number) => {
    const newRules = (formData.rules || []).filter((_, i) => i !== index);
    setFormData(prev => ({ ...prev, rules: newRules }));
  };

  const generateRuleDescription = (rule: CouponRule) => {
    const operator = operatorOptions.find(op => op.value === rule.operator)?.label || rule.operator;
    const attribute = attributeOptions.find(attr => attr.value === rule.attribute)?.label || rule.attribute;
    
    let description = `${attribute} ${operator.toLowerCase()} "${rule.value}"`;
    
    // Add context for specific attributes
    if (rule.attribute === 'cart_total' && rule.value) {
      description = `${attribute} ${operator.toLowerCase()} ₹${rule.value}`;
    }
    
    return description;
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.code?.trim()) {
      newErrors.code = 'Coupon code is required';
    } else if (formData.code.length < 3) {
      newErrors.code = 'Coupon code must be at least 3 characters';
    }

    if (!formData.name?.trim()) {
      newErrors.name = 'Coupon name is required';
    }

    if (!formData.description?.trim()) {
      newErrors.description = 'Coupon description is required';
    }

    if (!formData.discountValue || formData.discountValue <= 0) {
      newErrors.discountValue = 'Discount value must be greater than 0';
    }

    if (formData.discountType === 'percentage' && formData.discountValue > 100) {
      newErrors.discountValue = 'Percentage discount cannot exceed 100%';
    }

    if (!formData.validFrom) {
      newErrors.validFrom = 'Valid from date is required';
    }

    if (!formData.validTo) {
      newErrors.validTo = 'Valid to date is required';
    }

    if (formData.validFrom && formData.validTo && formData.validFrom >= formData.validTo) {
      newErrors.validTo = 'Valid to date must be after valid from date';
    }

    // Validate rules
    (formData.rules || []).forEach((rule, index) => {
      if (!rule.value?.trim()) {
        newErrors[`rule_${index}_value`] = 'Rule value is required';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (isEdit) {
        console.log('Updating coupon:', couponId, formData);
      } else {
        console.log('Creating coupon:', formData);
      }
      
      // Redirect to coupons list
      router.push(`/${storeHandle}/admin/coupons`);
    } catch (error) {
      console.error('Error saving coupon:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push(`/${storeHandle}/admin/coupons`);
  };

  if (isEdit && isLoading && !formData.code) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <LoadingSpinner text="Loading coupon data..." size="lg" />
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-6">
        <button
          onClick={handleCancel}
          className="flex items-center text-gray-600 hover:text-gray-800 mb-4"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back to Coupons
        </button>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {isEdit ? 'Edit Coupon' : 'Add New Coupon'}
        </h1>
        <p className="text-gray-600">
          {isEdit ? 'Update coupon information and rules.' : 'Create a new coupon with discount rules and conditions.'}
        </p>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Information */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h2 className="text-xl font-semibold mb-6">Basic Information</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Coupon Code */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Coupon Code *
              </label>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={formData.code || ''}
                  onChange={(e) => handleInputChange('code', e.target.value.toUpperCase())}
                  className={`flex-1 px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.code ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter coupon code"
                />
                <button
                  type="button"
                  onClick={generateCouponCode}
                  className="px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Generate
                </button>
              </div>
              {errors.code && (
                <p className="text-red-500 text-sm mt-1">{errors.code}</p>
              )}
              <p className="text-gray-500 text-sm mt-1">
                Unique code that customers will use to apply the discount
              </p>
            </div>

            {/* Coupon Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Coupon Name *
              </label>
              <input
                type="text"
                value={formData.name || ''}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.name ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter coupon name"
              />
              {errors.name && (
                <p className="text-red-500 text-sm mt-1">{errors.name}</p>
              )}
            </div>

            {/* Description */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                value={formData.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={3}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.description ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Describe the coupon and its benefits..."
              />
              {errors.description && (
                <p className="text-red-500 text-sm mt-1">{errors.description}</p>
              )}
            </div>

            {/* Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status *
              </label>
              <select
                value={formData.status || 'draft'}
                onChange={(e) => handleInputChange('status', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="draft">Draft</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
        </div>

        {/* Discount Settings */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h2 className="text-xl font-semibold mb-6">Discount Settings</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Discount Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Discount Type *
              </label>
              <select
                value={formData.discountType || 'percentage'}
                onChange={(e) => handleInputChange('discountType', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="percentage">Percentage</option>
                <option value="fixed_amount">Fixed Amount</option>
              </select>
            </div>

            {/* Discount Value */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Discount Value *
              </label>
              <div className="relative">
                {formData.discountType === 'fixed_amount' && (
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">₹</span>
                )}
                <input
                  type="number"
                  value={formData.discountValue || ''}
                  onChange={(e) => handleInputChange('discountValue', Number(e.target.value))}
                  className={`w-full ${formData.discountType === 'fixed_amount' ? 'pl-8' : 'pl-4'} pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.discountValue ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="0"
                />
                {formData.discountType === 'percentage' && (
                  <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">%</span>
                )}
              </div>
              {errors.discountValue && (
                <p className="text-red-500 text-sm mt-1">{errors.discountValue}</p>
              )}
            </div>

            {/* Usage Limit */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Usage Limit
              </label>
              <input
                type="number"
                value={formData.usageLimit || ''}
                onChange={(e) => handleInputChange('usageLimit', Number(e.target.value))}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="0 = Unlimited"
              />
              <p className="text-gray-500 text-sm mt-1">
                Maximum number of times this coupon can be used
              </p>
            </div>
          </div>
        </div>

        {/* Validity Period */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h2 className="text-xl font-semibold mb-6">Validity Period</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Valid From */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Valid From *
              </label>
              <input
                type="date"
                value={formData.validFrom || ''}
                onChange={(e) => handleInputChange('validFrom', e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.validFrom ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.validFrom && (
                <p className="text-red-500 text-sm mt-1">{errors.validFrom}</p>
              )}
            </div>

            {/* Valid To */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Valid To *
              </label>
              <input
                type="date"
                value={formData.validTo || ''}
                onChange={(e) => handleInputChange('validTo', e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.validTo ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.validTo && (
                <p className="text-red-500 text-sm mt-1">{errors.validTo}</p>
              )}
            </div>
          </div>
        </div>

        {/* Rules */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Coupon Rules</h2>
            <button
              type="button"
              onClick={addRule}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add Rule
            </button>
          </div>

          <p className="text-gray-600 mb-6">
            Define conditions that must be met for this coupon to be applicable.
          </p>

          {(formData.rules || []).length === 0 ? (
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-gray-500">No rules added yet. Click "Add Rule" to create conditions for this coupon.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {(formData.rules || []).map((rule, index) => (
                <div key={rule.id} className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-sm font-medium text-gray-900">Rule {index + 1}</h3>
                    <button
                      type="button"
                      onClick={() => removeRule(index)}
                      className="text-red-600 hover:text-red-800 p-1"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* Attribute */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Attribute
                      </label>
                      <select
                        value={rule.attribute}
                        onChange={(e) => {
                          const updatedRule = { ...rule, attribute: e.target.value as any };
                          updatedRule.description = generateRuleDescription(updatedRule);
                          updateRule(index, updatedRule);
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        {attributeOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Operator */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Operator
                      </label>
                      <select
                        value={rule.operator}
                        onChange={(e) => {
                          const updatedRule = { ...rule, operator: e.target.value as any };
                          updatedRule.description = generateRuleDescription(updatedRule);
                          updateRule(index, updatedRule);
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        {operatorOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Value */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Value
                      </label>
                      <input
                        type="text"
                        value={rule.value}
                        onChange={(e) => {
                          const updatedRule = { ...rule, value: e.target.value };
                          updatedRule.description = generateRuleDescription(updatedRule);
                          updateRule(index, updatedRule);
                        }}
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                          errors[`rule_${index}_value`] ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="Enter value"
                      />
                      {errors[`rule_${index}_value`] && (
                        <p className="text-red-500 text-sm mt-1">{errors[`rule_${index}_value`]}</p>
                      )}
                    </div>
                  </div>

                  {/* Rule Description */}
                  {rule.value && (
                    <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <p className="text-sm text-blue-800">
                        <strong>Rule:</strong> {generateRuleDescription(rule)}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={handleCancel}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isLoading ? (
              <>
                <svg className="animate-spin w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {isEdit ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              <>
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                {isEdit ? 'Update Coupon' : 'Create Coupon'}
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};