'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import { customersApi, Customer } from '@/lib/api/customers';
import { useToast } from '@/app/providers/toast-provider';

// Material Design 3 Components
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Toolbar,
  Alert,
  AlertTitle,
  Skeleton,
  Stack,
  Divider,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Fab,
  Tooltip,
  InputAdornment,
  Container,
  Grid,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  Breadcrumbs,
  Link as MuiLink,
  Avatar,
  Badge,
} from '@mui/material';

import {
  Search as SearchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Block as BlockIcon,
  MoreVert as MoreVertIcon,
  FilterList as FilterListIcon,
  ViewList as ViewListIcon,
  ViewModule as ViewModuleIcon,
  People as PeopleIcon,
  Refresh as RefreshIcon,
  Download as ExportIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  ShoppingBag as OrdersIcon,
  NavigateNext as NavigateNextIcon,
  Visibility as VisibilityIcon,
  PersonAdd as PersonAddIcon,
} from '@mui/icons-material';

export const AdminCustomersListMD3: React.FC = () => {
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  const { getStoreHandle } = useAuthStore();
  const { showToast } = useToast();
  
  // State management
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');
  const [page, setPage] = useState(1);
  const [rowsPerPage] = useState(10);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedCustomerForMenu, setSelectedCustomerForMenu] = useState<string | null>(null);
  
  // Get dynamic store handle from auth store
  const dynamicStoreHandle = getStoreHandle() || storeHandle;

  // Fetch customers from API
  useEffect(() => {
    const fetchCustomers = async () => {
      if (!dynamicStoreHandle) {
        console.log('No store handle available for customers API call');
        setIsLoading(false);
        return;
      }
      
      setIsLoading(true);
      setError(null);
      
      try {
        console.log('=== FETCHING CUSTOMERS ===');
        console.log('Using store handle as x-tenant-id:', dynamicStoreHandle);
        
        const response = await customersApi.getCustomers(dynamicStoreHandle, {
          limit: rowsPerPage,
          offset: (page - 1) * rowsPerPage,
          search: searchTerm || undefined,
          status: statusFilter !== 'all' ? statusFilter : undefined,
        });
        
        console.log('Customers fetched successfully:', response);
        
        setCustomers(response.customers || []);
        setTotalCount(response.count || 0);
      } catch (error: any) {
        console.error('Failed to fetch customers:', error);
        setError(error.message || 'Failed to fetch customers');
        setCustomers([]);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchCustomers();
  }, [dynamicStoreHandle, searchTerm, statusFilter, page, rowsPerPage]);

  const handleSelectAll = () => {
    if (selectedCustomers.length === customers.length) {
      setSelectedCustomers([]);
    } else {
      setSelectedCustomers(customers.map(c => c.id));
    }
  };

  const handleSelectCustomer = (customerId: string) => {
    setSelectedCustomers(prev =>
      prev.includes(customerId)
        ? prev.filter(id => id !== customerId)
        : [...prev, customerId]
    );
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, customerId: string) => {
    setAnchorEl(event.currentTarget);
    setSelectedCustomerForMenu(customerId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedCustomerForMenu(null);
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  const getStatusColor = (status: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'warning';
      case 'blocked':
        return 'error';
      default:
        return 'default';
    }
  };

  const formatCurrency = (amount: number | undefined): string => {
    if (!amount) return '₹0';
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(amount);
  };

  const getCustomerInitials = (customer: Customer): string => {
    const firstName = customer.first_name || '';
    const lastName = customer.last_name || '';
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase() || customer.email.charAt(0).toUpperCase();
  };

  const renderTableSkeleton = () => (
    <TableContainer component={Paper} elevation={0} sx={{ border: 1, borderColor: 'divider' }}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell padding="checkbox">
              <Skeleton variant="rectangular" width={20} height={20} />
            </TableCell>
            <TableCell>Customer</TableCell>
            <TableCell>Contact</TableCell>
            <TableCell>Orders</TableCell>
            <TableCell>Total Spent</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Last Order</TableCell>
            <TableCell align="right">Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {[...Array(5)].map((_, index) => (
            <TableRow key={index}>
              <TableCell padding="checkbox">
                <Skeleton variant="rectangular" width={20} height={20} />
              </TableCell>
              <TableCell>
                <Stack direction="row" spacing={2} alignItems="center">
                  <Skeleton variant="circular" width={40} height={40} />
                  <Box>
                    <Skeleton variant="text" width={120} />
                    <Skeleton variant="text" width={80} />
                  </Box>
                </Stack>
              </TableCell>
              <TableCell>
                <Skeleton variant="text" width={150} />
              </TableCell>
              <TableCell>
                <Skeleton variant="text" width={60} />
              </TableCell>
              <TableCell>
                <Skeleton variant="text" width={80} />
              </TableCell>
              <TableCell>
                <Skeleton variant="rounded" width={80} height={24} />
              </TableCell>
              <TableCell>
                <Skeleton variant="text" width={100} />
              </TableCell>
              <TableCell align="right">
                <Skeleton variant="text" width={80} />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  const renderGridSkeleton = () => (
    <Grid container spacing={3}>
      {[...Array(8)].map((_, index) => (
        <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
          <Card elevation={1}>
            <CardContent>
              <Stack spacing={2}>
                <Stack direction="row" spacing={2} alignItems="center">
                  <Skeleton variant="circular" width={48} height={48} />
                  <Box sx={{ flexGrow: 1 }}>
                    <Skeleton variant="text" width="80%" />
                    <Skeleton variant="text" width="60%" />
                  </Box>
                </Stack>
                <Skeleton variant="text" width="100%" />
                <Skeleton variant="rounded" width={80} height={24} />
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  const renderCustomerCard = (customer: Customer) => (
    <Grid item xs={12} sm={6} md={4} lg={3} key={customer.id}>
      <Card 
        elevation={1}
        sx={{ 
          height: '100%',
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            elevation: 3,
            transform: 'translateY(-2px)',
          }
        }}
      >
        <CardContent>
          <Stack spacing={2}>
            <Stack direction="row" spacing={2} alignItems="flex-start">
              <Badge
                badgeContent={customer.total_orders || 0}
                color="primary"
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'right',
                }}
              >
                <Avatar
                  sx={{ 
                    width: 48, 
                    height: 48,
                    bgcolor: 'primary.main',
                    fontSize: '1.2rem',
                    fontWeight: 600,
                  }}
                >
                  {getCustomerInitials(customer)}
                </Avatar>
              </Badge>
              
              <Box sx={{ flexGrow: 1, minWidth: 0 }}>
                <Typography variant="subtitle1" fontWeight={600} noWrap>
                  {customer.first_name && customer.last_name 
                    ? `${customer.first_name} ${customer.last_name}`
                    : customer.email
                  }
                </Typography>
                <Typography variant="body2" color="text.secondary" noWrap>
                  {customer.email}
                </Typography>
              </Box>
              
              <IconButton
                size="small"
                onClick={(e) => handleMenuOpen(e, customer.id)}
              >
                <MoreVertIcon />
              </IconButton>
            </Stack>
            
            <Stack direction="row" spacing={1} alignItems="center">
              <EmailIcon fontSize="small" color="action" />
              <Typography variant="body2" color="text.secondary" noWrap>
                {customer.email}
              </Typography>
            </Stack>
            
            {customer.phone && (
              <Stack direction="row" spacing={1} alignItems="center">
                <PhoneIcon fontSize="small" color="action" />
                <Typography variant="body2" color="text.secondary">
                  {customer.phone}
                </Typography>
              </Stack>
            )}
            
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Total Spent
                </Typography>
                <Typography variant="subtitle2" fontWeight={600}>
                  {formatCurrency(customer.total_spent)}
                </Typography>
              </Box>
              
              <Chip
                label={customer.status?.charAt(0).toUpperCase() + customer.status?.slice(1) || 'Active'}
                color={getStatusColor(customer.status)}
                size="small"
                variant="outlined"
              />
            </Stack>
            
            <Stack direction="row" spacing={1} sx={{ mt: 2 }}>
              <Button
                component={Link}
                href={`/${dynamicStoreHandle}/admin/customers/${customer.id}`}
                size="small"
                variant="outlined"
                startIcon={<VisibilityIcon />}
                fullWidth
              >
                View
              </Button>
            </Stack>
          </Stack>
        </CardContent>
      </Card>
    </Grid>
  );

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs 
        separator={<NavigateNextIcon fontSize="small" />}
        sx={{ mb: 3 }}
      >
        <MuiLink 
          component={Link} 
          href={`/${dynamicStoreHandle}/admin`}
          underline="hover"
          color="inherit"
        >
          Admin
        </MuiLink>
        <Typography color="text.primary">Customers</Typography>
      </Breadcrumbs>

      {/* Page Header */}
      <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={4}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight={600}>
            Customers
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage customer accounts and information
          </Typography>
        </Box>
        
        <Stack direction="row" spacing={2}>
          <Tooltip title="Refresh">
            <IconButton onClick={handleRefresh} color="primary">
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          
          <Button
            variant="outlined"
            startIcon={<ExportIcon />}
            size="large"
          >
            Export
          </Button>
          
          <Button
            variant="contained"
            startIcon={<PersonAddIcon />}
            size="large"
          >
            Add Customer
          </Button>
        </Stack>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={1}>
            <CardContent>
              <Stack direction="row" spacing={2} alignItems="center">
                <Box sx={{
                  p: 1.5,
                  bgcolor: 'primary.100',
                  borderRadius: 2,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <PeopleIcon sx={{ fontSize: 28, color: 'primary.main' }} />
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary" fontWeight={600}>
                    Total Customers
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    {totalCount}
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={1}>
            <CardContent>
              <Stack direction="row" spacing={2} alignItems="center">
                <Box sx={{
                  p: 1.5,
                  bgcolor: 'success.100',
                  borderRadius: 2,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <PeopleIcon sx={{ fontSize: 28, color: 'success.main' }} />
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary" fontWeight={600}>
                    Active Customers
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    {customers.filter(c => c.status === 'active').length}
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={1}>
            <CardContent>
              <Stack direction="row" spacing={2} alignItems="center">
                <Box sx={{
                  p: 1.5,
                  bgcolor: 'info.100',
                  borderRadius: 2,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <OrdersIcon sx={{ fontSize: 28, color: 'info.main' }} />
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary" fontWeight={600}>
                    Total Orders
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    {customers.reduce((sum, c) => sum + (c.total_orders || 0), 0)}
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={1}>
            <CardContent>
              <Stack direction="row" spacing={2} alignItems="center">
                <Box sx={{
                  p: 1.5,
                  bgcolor: 'warning.100',
                  borderRadius: 2,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <OrdersIcon sx={{ fontSize: 28, color: 'warning.main' }} />
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary" fontWeight={600}>
                    Total Revenue
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    {formatCurrency(customers.reduce((sum, c) => sum + (c.total_spent || 0), 0))}
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search and Filters */}
      <Card elevation={1} sx={{ mb: 3 }}>
        <CardContent>
          <Stack direction={{ xs: 'column', md: 'row' }} spacing={2} alignItems="center">
            <TextField
              placeholder="Search customers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              variant="outlined"
              size="medium"
              sx={{ flexGrow: 1, minWidth: 300 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="action" />
                  </InputAdornment>
                ),
              }}
            />
            
            <FormControl size="medium" sx={{ minWidth: 150 }}>
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                label="Status"
              >
                <MenuItem value="all">All Status</MenuItem>
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="inactive">Inactive</MenuItem>
                <MenuItem value="blocked">Blocked</MenuItem>
              </Select>
            </FormControl>
            
            <Stack direction="row" spacing={1} alignItems="center">
              <Typography variant="body2" color="text.secondary">
                View:
              </Typography>
              <IconButton
                onClick={() => setViewMode('list')}
                color={viewMode === 'list' ? 'primary' : 'default'}
                size="small"
              >
                <ViewListIcon />
              </IconButton>
              <IconButton
                onClick={() => setViewMode('grid')}
                color={viewMode === 'grid' ? 'primary' : 'default'}
                size="small"
              >
                <ViewModuleIcon />
              </IconButton>
              
              <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />
              
              <Typography variant="body2" color="text.secondary">
                {totalCount} customers
              </Typography>
            </Stack>
          </Stack>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {selectedCustomers.length > 0 && (
        <Card elevation={1} sx={{ mb: 3, bgcolor: 'primary.50', borderColor: 'primary.200' }}>
          <CardContent>
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Typography variant="subtitle1" color="primary.main" fontWeight={500}>
                {selectedCustomers.length} customer(s) selected
              </Typography>
              <Stack direction="row" spacing={1}>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<EmailIcon />}
                >
                  Send Email
                </Button>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<BlockIcon />}
                >
                  Block Selected
                </Button>
                <Button
                  variant="contained"
                  color="error"
                  size="small"
                  startIcon={<ExportIcon />}
                >
                  Export Selected
                </Button>
              </Stack>
            </Stack>
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} action={
          <Button color="inherit" size="small" onClick={handleRefresh}>
            Retry
          </Button>
        }>
          <AlertTitle>Error loading customers</AlertTitle>
          {error}
        </Alert>
      )}

      {/* Content */}
      {isLoading ? (
        viewMode === 'list' ? renderTableSkeleton() : renderGridSkeleton()
      ) : error ? null : (
        <>
          {customers.length > 0 ? (
            <>
              {viewMode === 'list' ? (
                <TableContainer component={Paper} elevation={1}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell padding="checkbox">
                          <input
                            type="checkbox"
                            checked={selectedCustomers.length === customers.length && customers.length > 0}
                            onChange={handleSelectAll}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="subtitle2" fontWeight={600}>
                            Customer
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="subtitle2" fontWeight={600}>
                            Contact
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="subtitle2" fontWeight={600}>
                            Orders
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="subtitle2" fontWeight={600}>
                            Total Spent
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="subtitle2" fontWeight={600}>
                            Status
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="subtitle2" fontWeight={600}>
                            Last Order
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="subtitle2" fontWeight={600}>
                            Actions
                          </Typography>
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {customers.map((customer) => (
                        <TableRow 
                          key={customer.id}
                          hover
                          selected={selectedCustomers.includes(customer.id)}
                        >
                          <TableCell padding="checkbox">
                            <input
                              type="checkbox"
                              checked={selectedCustomers.includes(customer.id)}
                              onChange={() => handleSelectCustomer(customer.id)}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" spacing={2} alignItems="center">
                              <Avatar
                                sx={{ 
                                  width: 40, 
                                  height: 40,
                                  bgcolor: 'primary.main',
                                  fontSize: '0.9rem',
                                  fontWeight: 600,
                                }}
                              >
                                {getCustomerInitials(customer)}
                              </Avatar>
                              <Box>
                                <Typography variant="subtitle2" fontWeight={600}>
                                  {customer.first_name && customer.last_name 
                                    ? `${customer.first_name} ${customer.last_name}`
                                    : customer.email
                                  }
                                </Typography>
                                <Typography variant="body2" color="text.secondary">
                                  ID: {customer.id.slice(0, 8)}...
                                </Typography>
                              </Box>
                            </Stack>
                          </TableCell>
                          <TableCell>
                            <Box>
                              <Typography variant="body2">
                                {customer.email}
                              </Typography>
                              {customer.phone && (
                                <Typography variant="body2" color="text.secondary">
                                  {customer.phone}
                                </Typography>
                              )}
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {customer.total_orders || 0}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" fontWeight={500}>
                              {formatCurrency(customer.total_spent)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={customer.status?.charAt(0).toUpperCase() + customer.status?.slice(1) || 'Active'}
                              color={getStatusColor(customer.status)}
                              size="small"
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {customer.last_order_date 
                                ? new Date(customer.last_order_date).toLocaleDateString('en-IN', {
                                    year: 'numeric',
                                    month: 'short',
                                    day: 'numeric',
                                  })
                                : 'No orders'
                              }
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            <Stack direction="row" spacing={1} justifyContent="flex-end">
                              <Button
                                component={Link}
                                href={`/${dynamicStoreHandle}/admin/customers/${customer.id}`}
                                size="small"
                                variant="outlined"
                                startIcon={<VisibilityIcon />}
                              >
                                View
                              </Button>
                              <IconButton
                                onClick={(e) => handleMenuOpen(e, customer.id)}
                                size="small"
                              >
                                <MoreVertIcon />
                              </IconButton>
                            </Stack>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Grid container spacing={3}>
                  {customers.map(renderCustomerCard)}
                </Grid>
              )}

              {/* Pagination */}
              {totalCount > rowsPerPage && (
                <Box display="flex" justifyContent="center" mt={4}>
                  <Pagination
                    count={Math.ceil(totalCount / rowsPerPage)}
                    page={page}
                    onChange={(_, newPage) => setPage(newPage)}
                    color="primary"
                    size="large"
                  />
                </Box>
              )}
            </>
          ) : (
            /* No Customers Message */
            <Paper elevation={1} sx={{ p: 8, textAlign: 'center' }}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  maxWidth: 400,
                  mx: 'auto',
                }}
              >
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    bgcolor: 'grey.100',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 3,
                  }}
                >
                  <PeopleIcon sx={{ fontSize: 40, color: 'grey.400' }} />
                </Box>
                
                <Typography variant="h5" gutterBottom fontWeight={600}>
                  {searchTerm || statusFilter !== 'all' ? 'No customers found' : 'No customers yet'}
                </Typography>
                
                <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
                  {searchTerm || statusFilter !== 'all'
                    ? 'Try adjusting your search criteria or filters to find customers.'
                    : 'Customers will appear here once they start registering and placing orders.'
                  }
                </Typography>
                
                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                  {(searchTerm || statusFilter !== 'all') && (
                    <Button
                      onClick={() => {
                        setSearchTerm('');
                        setStatusFilter('all');
                      }}
                      variant="outlined"
                      size="large"
                    >
                      Clear Filters
                    </Button>
                  )}
                  <Button
                    variant="contained"
                    size="large"
                    startIcon={<PersonAddIcon />}
                  >
                    {searchTerm || statusFilter !== 'all' ? 'Add New Customer' : 'Add Your First Customer'}
                  </Button>
                </Stack>
              </Box>
            </Paper>
          )}
        </>
      )}

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem
          component={Link}
          href={`/${dynamicStoreHandle}/admin/customers/${selectedCustomerForMenu}`}
          onClick={handleMenuClose}
        >
          <ListItemIcon>
            <VisibilityIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>View Details</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit Customer</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <EmailIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Send Email</ListItemText>
        </MenuItem>
        <MenuItem
          onClick={handleMenuClose}
          sx={{ color: 'error.main' }}
        >
          <ListItemIcon>
            <BlockIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Block Customer</ListItemText>
        </MenuItem>
      </Menu>

      {/* Floating Action Button for Mobile */}
      <Fab
        color="primary"
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          display: { xs: 'flex', md: 'none' },
        }}
      >
        <PersonAddIcon />
      </Fab>
    </Container>
  );
};