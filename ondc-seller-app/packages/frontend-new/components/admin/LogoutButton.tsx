"use client";

import { useRouter } from "next/navigation";
import { performAdminLogout, performLogout } from "@/lib/utils/logout";

interface LogoutButtonProps {
  storeHandle?: string;
  isAdmin?: boolean;
}

export const LogoutButton: React.FC<LogoutButtonProps> = ({ 
  storeHandle, 
  isAdmin = false 
}) => {
  const router = useRouter();

  const logout = () => {
    if (isAdmin && storeHandle) {
      // Use admin-specific logout for admin contexts
      performAdminLogout(router, storeHandle);
    } else {
      // Use regular logout for non-admin contexts
      performLogout(router);
    }
  };

  return (
    <button onClick={logout} className="text-sm text-red-600 hover:underline">
      Logout
    </button>
  );
};

