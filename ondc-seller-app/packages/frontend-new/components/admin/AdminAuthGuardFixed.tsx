'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import { useStoreAuthStore } from '@/stores/storeAuthStore';
import { getAuthToken, isTokenExpired, isValidTokenFormat } from '@/lib/utils/tokenUtils';
import { performAdminLogout } from '@/lib/utils/logout';
import { useAdminTokenCheck } from '@/hooks/useAdminTokenCheck';
import { useAdminNavigationGuard } from '@/hooks/useAdminNavigationGuard';
import { Box, CircularProgress, Typography } from '@mui/material';

interface AdminAuthGuardProps {
  children: React.ReactNode;
}

export const AdminAuthGuardFixed: React.FC<AdminAuthGuardProps> = ({ children }) => {
  const router = useRouter();
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  const [isChecking, setIsChecking] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [hasWaitedForHydration, setHasWaitedForHydration] = useState(false);
  
  const mainAuth = useAuthStore();
  const storeAuth = useStoreAuthStore(storeHandle);
  
  // Set up periodic token checking once authorized
  useAdminTokenCheck({
    storeHandle,
    enabled: isAuthorized,
    checkInterval: 5 * 60 * 1000 // Check every 5 minutes
  });
  
  // Set up navigation guard
  useAdminNavigationGuard({
    storeHandle,
    enabled: isAuthorized
  });

  // First, wait for client-side hydration
  useEffect(() => {
    // Wait a bit for Zustand to hydrate from localStorage
    const timer = setTimeout(() => {
      setHasWaitedForHydration(true);
      console.log('✅ AdminAuthGuard: Hydration wait period complete');
    }, 100); // Small delay to allow hydration

    return () => clearTimeout(timer);
  }, []);

  // Then perform authentication check
  useEffect(() => {
    if (!hasWaitedForHydration) {
      console.log('⏳ AdminAuthGuard: Waiting for hydration...');
      return;
    }

    const checkAuthentication = async () => {
      console.log('=== ADMIN AUTH GUARD CHECK (FIXED) ===');
      console.log('Store handle:', storeHandle);
      console.log('Hydration wait completed:', hasWaitedForHydration);
      
      try {
        // Check if we have any authentication data
        const hasMainAuth = mainAuth.isAuthenticated && mainAuth.token;
        const hasStoreAuth = storeAuth.isAuthenticated && storeAuth.token;
        
        console.log('Main auth status:', {
          isAuthenticated: mainAuth.isAuthenticated,
          hasToken: !!mainAuth.token,
          hasUser: !!mainAuth.user
        });
        
        console.log('Store auth status:', {
          isAuthenticated: storeAuth.isAuthenticated,
          hasToken: !!storeAuth.token,
          hasUser: !!storeAuth.user
        });

        // Also check localStorage directly as a fallback
        const tokenInfo = getAuthToken(storeHandle);
        console.log('Direct token check from localStorage:', {
          hasToken: !!tokenInfo.token,
          source: tokenInfo.source
        });

        // If no authentication at all (check both Zustand and localStorage)
        if (!hasMainAuth && !hasStoreAuth && !tokenInfo.token) {
          console.log('❌ No authentication found anywhere - redirecting to login');
          router.replace('/login?tab=login');
          return;
        }

        // Use the best available token
        const bestToken = tokenInfo.token || mainAuth.token || storeAuth.token;
        
        if (!bestToken) {
          console.log('❌ No valid token found - redirecting to login');
          router.replace('/login?tab=login');
          return;
        }

        // Validate token format
        if (!isValidTokenFormat(bestToken)) {
          console.log('❌ Invalid token format - clearing auth and redirecting');
          await performAdminLogout(router, storeHandle);
          return;
        }

        // Check if token is expired
        if (isTokenExpired(bestToken)) {
          console.log('❌ Token is expired - clearing auth and redirecting');
          await performAdminLogout(router, storeHandle);
          return;
        }

        // Additional check: ensure we have user data (with fallback)
        const hasUserData = mainAuth.user || storeAuth.user;
        if (!hasUserData) {
          // If we have a valid token but no user data, it might be a hydration issue
          // Give it one more chance by checking localStorage directly
          console.log('⚠️ No user data in Zustand stores, checking localStorage...');
          
          // Wait a bit more for potential late hydration
          setTimeout(() => {
            const recheckMainAuth = useAuthStore.getState();
            const recheckStoreAuth = useStoreAuthStore(storeHandle);
            
            if (!recheckMainAuth.user && !recheckStoreAuth.user) {
              console.log('❌ Still no user data found after recheck - redirecting to login');
              router.replace('/login?tab=login');
            } else {
              console.log('✅ User data found after recheck - allowing access');
              setIsAuthorized(true);
            }
          }, 200);
          return;
        }

        // All checks passed
        console.log('✅ Authentication valid - allowing access');
        setIsAuthorized(true);

      } catch (error) {
        console.error('Error during authentication check:', error);
        // On error, redirect to login for safety
        router.replace('/login?tab=login');
      } finally {
        setIsChecking(false);
      }
    };

    checkAuthentication();
  }, [
    hasWaitedForHydration,
    router, 
    storeHandle, 
    mainAuth.isAuthenticated, 
    mainAuth.token, 
    mainAuth.user, 
    storeAuth.isAuthenticated, 
    storeAuth.token, 
    storeAuth.user
  ]);

  // Show loading state while checking authentication or waiting for hydration
  if (isChecking || !hasWaitedForHydration) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          gap: 2,
        }}
      >
        <CircularProgress size={40} />
        <Typography variant="body2" color="text.secondary">
          {!hasWaitedForHydration 
            ? 'Loading authentication...' 
            : 'Verifying authentication...'
          }
        </Typography>
      </Box>
    );
  }

  // Show nothing if not authorized (redirect is in progress)
  if (!isAuthorized) {
    return null;
  }

  // Render children if authenticated and authorized
  return <>{children}</>;
};