'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
// Removed useStoreAuthStore - Admin interfaces should only use main auth store
import { useToast } from '@/app/providers/toast-provider';
import { validateUserProfile, formatValidationErrors, type UserProfileUpdateData } from '@/lib/validations/user';
import { authApi, type UserUpdateRequest } from '@/lib/api/auth';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Avatar,
  Grid,
  Divider,
  InputAdornment,
  Chip,
  Paper,
  Stack,
  Tooltip,
  CircularProgress,
  Zoom,
  Container,
  Breadcrumbs,
  Link as MuiLink,
  IconButton
} from '@mui/material';
import {
  PhotoCamera,
  Person,
  Email,
  Phone,
  Edit,
  Save,
  Cancel,
  AccountCircle,
  CloudUpload,
  Link as LinkIcon,
  NavigateNext as NavigateNextIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

interface ProfileData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  role: string;
  avatar: string;
  avatarUrl: string;
  bio: string;
  timezone: string;
  language: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`profile-tabpanel-${index}`}
      aria-labelledby={`profile-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

// Removed notification and security interfaces - only showing Personal Info for now
// interface NotificationSettings {...}
// interface SecuritySettings {...}

export const AdminProfileSettings: React.FC = () => {
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  
  // Get auth data from main auth store (admin authentication)
  const mainAuth = useAuthStore();
  const { showToast } = useToast();
  
  // Removed tab functionality - only showing Personal Info for now
  // const [tabValue, setTabValue] = useState(0);
  const [isEditing, setIsEditing] = useState(false);
  const [uploadMethod, setUploadMethod] = useState<'file' | 'url'>('file');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  // Initialize profile data with empty values
  const [profileData, setProfileData] = useState<ProfileData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    role: '',
    avatar: '',
    avatarUrl: '',
    bio: '',
    timezone: '',
    language: '',
  });

  // Removed security and notification state - only showing Personal Info for now
  // const [notifications, setNotifications] = useState<NotificationSettings>({...});
  // const [security, setSecurity] = useState<SecuritySettings>({...});
  // const [currentPassword, setCurrentPassword] = useState('');
  // const [newPassword, setNewPassword] = useState('');
  // const [confirmPassword, setConfirmPassword] = useState('');
  // const [showPasswords, setShowPasswords] = useState({...});

  const [isDataLoaded, setIsDataLoaded] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);

  const loadUserData = () => {
    console.log('🔍 Loading admin user data from main auth store');
    console.log('Store handle:', storeHandle);
    console.log('Main auth hydrated:', mainAuth.hasHydrated);
    
    // Wait for hydration to complete
    if (!mainAuth.hasHydrated) {
      console.log('⏳ Waiting for main auth store to hydrate...');
      return;
    }

    // Use only main auth for admin interfaces
    let userData = null;
    let authSource = 'none';
    
    if (mainAuth.user && mainAuth.isAuthenticated) {
      userData = mainAuth.user;
      authSource = 'main-admin';
    }
    

    
    if (userData) {
      // Extract user information from different possible structures
      const user = userData.user || userData; // Handle nested user structure
      
   
      
      // Update profile data with real user data
      setProfileData({
        firstName: user.first_name || '',
        lastName: user.last_name || '',
        email: user.email || '',
        phone: user.metadata?.contact_number || '',
        role: user?.metadata?.user_type || 'Store Administrator',
        avatar: '', // Will be handled separately if available
        avatarUrl: user?.metadata?.profile_image,
        bio: user.metadata?.bio || '',
        timezone: user.metadata?.timezone || 'UTC',
        language: user.metadata?.language || 'English',
      });
      
      console.log('✅ Admin profile data updated from main auth store');
      setIsDataLoaded(true);
    } else {
      console.log('❌ No admin user data available in main auth store');
      setIsDataLoaded(true); // Still mark as loaded even if no data
    }
  };

  // Load user data from main auth store (admin authentication only)
  useEffect(() => {
   

    loadUserData();
  }, [
    mainAuth.hasHydrated,
    mainAuth.user,
    mainAuth.isAuthenticated,
    storeHandle
  ]);

  const handleProfileChange = (field: keyof ProfileData, value: string) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Removed notification and security handlers - only showing Personal Info for now
  // const handleNotificationChange = (field: keyof NotificationSettings) => {...};
  // const handleSecurityChange = (field: keyof SecuritySettings, value: boolean | number) => {...};

  const handleSaveProfile = async () => {
    console.log('📝 Profile save requested');
    console.log('Profile data to save:', profileData);
    
    setIsSaving(true);
    setValidationErrors({});
    
    try {
      // Prepare payload for validation
      const payload: UserProfileUpdateData = {
        first_name: profileData.firstName.trim(),
        last_name: profileData.lastName.trim(),
        metadata: {
          profile_image: profileData.avatar || profileData.avatarUrl || '',
          contact_number: profileData.phone.trim(),
          bio: profileData.bio?.trim() || ''
        }
      };
      
      console.log('Payload to validate:', payload);
      
      // Validate payload using Zod schema
      const validationResult = validateUserProfile(payload);
      
      if (!validationResult.success) {
        console.error('Validation failed:', validationResult.error);
        const errors = formatValidationErrors(validationResult.error);
        setValidationErrors(errors);
        
        // Show first validation error as toast
        const firstError = Object.values(errors)[0];
        showToast(firstError || 'Please fix the validation errors', 'error');
        return;
      }
      
      console.log('✅ Validation passed, making API call');
      
      // Get user ID and auth data from main auth store (admin only)
      const userData = mainAuth.user;
      const user = userData?.user || userData;
      const userId = user?.id;
      const token = mainAuth.token;
      
      if (!userId) {
        showToast('User ID not found. Please log in again.', 'error');
        return;
      }
      
      if (!token) {
        showToast('Authentication token not found. Please log in again.', 'error');
        return;
      }
      
      // Prepare API request payload using validated data
      const apiPayload: UserUpdateRequest = {
        first_name: validationResult.data.first_name,
        last_name: validationResult.data.last_name,
        metadata: {
          // Preserve existing metadata
          ...(user?.metadata || {}),
          // Override with validated values (only include non-empty values)
          ...(validationResult.data.metadata.profile_image && validationResult.data.metadata.profile_image !== '' && {
            profile_image: validationResult.data.metadata.profile_image
          }),
          ...(validationResult.data.metadata.contact_number && validationResult.data.metadata.contact_number !== '' && {
            contact_number: validationResult.data.metadata.contact_number
          }),
          ...(validationResult.data.metadata.bio && validationResult.data.metadata.bio !== '' && {
            bio: validationResult.data.metadata.bio
          })
        }
      };
      
      console.log('API payload:', apiPayload);
      
      // Make API call
      const response = await authApi.updateUserProfile(
        userId,
        apiPayload,
        token,
        storeHandle
      );
      
      console.log('✅ Profile updated successfully:', response);
      
      // Update local profile data with API response
      console.log('🔄 Updating local profile data with API response:', response);
      
      setProfileData(prev => {
        const updatedData = {
          ...prev,
          firstName: response.first_name || prev.firstName,
          lastName: response.last_name || prev.lastName,
          phone: response.metadata?.contact_number || prev.phone,
          bio: response.metadata?.bio || prev.bio,
          avatar: response.metadata?.profile_image || prev.avatar,
          avatarUrl: response.metadata?.profile_image || prev.avatarUrl
        };
        
        console.log('📝 Updated profile data:', updatedData);
        return updatedData;
      });
      
      console.log('mainAuth.setUser<><><><><><>',mainAuth.user)
      // Update main auth store with new data (admin only)
      if (mainAuth.setUser) {
        const currentUser = mainAuth.user?.user || mainAuth.user;
        const updatedUser = {
          ...mainAuth.user.user,
          first_name: response.user.first_name,
          last_name: response.user.last_name,
          metadata: {
            ...(currentUser?.metadata || {}),
            ...(response.user.metadata || {})
          },
          // Handle nested user structure if it exists
          // ...(mainAuth.user?.user && {
          //   user: {
          //     ...currentUser,
          //     first_name: response.first_name,
          //     last_name: response.last_name,
          //     metadata: {
          //       ...(currentUser?.metadata || {}),
          //       ...(response.metadata || {})
          //     }
          //   }
          // })
        };
        // return
        console.log('🔄 Updating main auth store with:', updatedUser);
        mainAuth.setUser(updatedUser);
        
        // Force a re-render by triggering the useEffect
        console.log('🔄 Auth store updated, should trigger re-render');
      }
      
      // Clear validation errors
      setValidationErrors({});
      
      // Show success toast
      loadUserData();
      showToast('Profile updated successfully!', 'success');
      
      // Exit edit mode
      setIsEditing(false);
      
      // Force reload profile data from updated auth store
   
      
      console.log('🎉 Profile update completed successfully');
      
    } catch (error: any) {
      console.error('❌ Profile update failed:', error);
      
      let errorMessage = 'Failed to update profile. Please try again.';
      
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      showToast(errorMessage, 'error');
    } finally {
      setIsSaving(false);
    }
  };

  // Removed password change handler - only showing Personal Info for now
  // const handleChangePassword = async () => {...};

  const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    // Check if editing is enabled
    if (!isEditing) {
      showToast('Please enable edit mode to upload an image', 'warning');
      return;
    }
    
    const file = event.target.files?.[0];
    if (file) {
      // Validate file size (2MB limit)
      if (file.size > 2 * 1024 * 1024) {
        showToast('File size must be less than 2MB', 'error');
        return;
      }
      
      // Validate file type
      if (!file.type.startsWith('image/')) {
        showToast('Please select a valid image file', 'error');
        return;
      }
      
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfileData(prev => ({
          ...prev,
          avatar: e.target?.result as string,
        }));
        showToast('Profile image updated successfully!', 'success');
      };
      reader.onerror = () => {
        showToast('Failed to read the image file', 'error');
      };
      reader.readAsDataURL(file);
    }
  };

  const handleAvatarUrlChange = (url: string) => {
    // Check if editing is enabled
    if (!isEditing) {
      showToast('Please enable edit mode to change image URL', 'warning');
      return;
    }
    
    setProfileData(prev => ({
      ...prev,
      avatarUrl: url,
      avatar: url, // Use URL as avatar source
    }));
    
    // Show success message if URL is valid
    if (url && url.trim()) {
      showToast('Profile image URL updated!', 'success');
    }
  };

  // Removed tab change handler - only showing Personal Info for now
  // const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
  //   setTabValue(newValue);
  // };

  const getDisplayAvatar = () => {
    return profileData.avatar || profileData.avatarUrl;
  };

  const getAvatarFallback = () => {
    return `${profileData.firstName[0] || 'U'}${profileData.lastName[0] || 'S'}`;
  };

  // Show loading state while data is being fetched
  if (!isDataLoaded) {
    return (
      <Box sx={{ 
        display: 'flex', 
        flexDirection: 'column', 
        alignItems: 'center', 
        justifyContent: 'center', 
        minHeight: '60vh',
        gap: 2 
      }}>
        <CircularProgress size={40} />
        <Typography variant="body1" color="text.secondary">
          Loading profile data...
        </Typography>
        {process.env.NODE_ENV === 'development' && (
          <Typography variant="caption" color="text.disabled">
            Store: {storeHandle || 'none'} | Main Auth: {mainAuth.hasHydrated.toString()}
          </Typography>
        )}
      </Box>
    );
  }

  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Breadcrumbs */}
      {/* <Breadcrumbs 
        separator={<NavigateNextIcon fontSize="small" />}
        sx={{ mb: 3 }}
      >
        <MuiLink 
          component={Link} 
          href={`/${storeHandle}/admin`}
          underline="hover"
          color="inherit"
        >
          Admin
        </MuiLink>
        <Typography color="text.primary">Profile Settings</Typography>
      </Breadcrumbs> */}

      {/* Page Header */}
      <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={4}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight={600}>
            Profile Settings
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your personal information and account preferences
          </Typography>
        </Box>
        
        <Stack direction="row" spacing={2}>
          <Tooltip title="Refresh">
            <IconButton onClick={handleRefresh} color="primary">
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Stack>
      </Box>

      {/* Profile Overview Card */}
      <Card elevation={1} sx={{ mb: 4, borderRadius: 2 }}>
        <CardContent sx={{ p: 4 }}>
          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3} alignItems="center">
            <Zoom in={true}>
              <Avatar
                src={getDisplayAvatar()}
                sx={{ 
                  width: 120, 
                  height: 120,
                  fontSize: '2rem',
                  fontWeight: 'bold',
                  bgcolor: 'primary.main'
                }}
              >
                {!getDisplayAvatar() && getAvatarFallback()}
              </Avatar>
            </Zoom>
            <Box sx={{ textAlign: { xs: 'center', sm: 'left' }, flex: 1 }}>
              <Typography variant="h5" fontWeight="bold" gutterBottom>
                {profileData.firstName} {profileData.lastName}
              </Typography>
              <Chip 
                label={profileData.role} 
                color="primary" 
                variant="outlined"
                sx={{ mb: 1 }}
              />
              <Typography variant="body2" color="text.secondary">
                {profileData.email}
                {profileData.phone && ` • ${profileData.phone}`}
              </Typography>
            </Box>
            <Button
              variant={isEditing ? "outlined" : "contained"}
              startIcon={isEditing ? <Cancel /> : <Edit />}
              onClick={() => setIsEditing(!isEditing)}
              sx={{ minWidth: 120 }}
            >
              {isEditing ? 'Cancel' : 'Edit Profile'}
            </Button>
          </Stack>
        </CardContent>
      </Card>

      {/* Personal Information Section */}
        <Grid container spacing={4}>
          <Grid item xs={12} lg={8}>
            <Card elevation={1} sx={{ borderRadius: 2 }}>
              <CardContent sx={{ p: 4 }}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Person color="primary" />
                  Personal Information
                </Typography>
                <Divider sx={{ mb: 3 }} />
                
                {/* Profile Image Upload Section */}
                <Box sx={{ mb: 4 }}>
                  <Typography variant="subtitle1" gutterBottom fontWeight="medium">
                    Profile Image
                  </Typography>
                  <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 2 }}>
                    <Button
                      variant={uploadMethod === 'file' ? 'contained' : 'outlined'}
                      size="small"
                      onClick={() => {
                        if (!isEditing) {
                          showToast('Please enable edit mode to change upload method', 'warning');
                          return;
                        }
                        setUploadMethod('file');
                      }}
                      startIcon={<CloudUpload />}
                      disabled={!isEditing}
                      sx={{ 
                        opacity: !isEditing ? 0.6 : 1
                      }}
                    >
                      Upload File
                    </Button>
                    <Button
                      variant={uploadMethod === 'url' ? 'contained' : 'outlined'}
                      size="small"
                      onClick={() => {
                        if (!isEditing) {
                          showToast('Please enable edit mode to change upload method', 'warning');
                          return;
                        }
                        setUploadMethod('url');
                      }}
                      startIcon={<LinkIcon />}
                      disabled={!isEditing}
                      sx={{ 
                        opacity: !isEditing ? 0.6 : 1
                      }}
                    >
                      Use URL
                    </Button>
                  </Stack>
                  
                  {uploadMethod === 'file' ? (
                    <Stack direction="row" spacing={2} alignItems="center">
                      <input
                        accept="image/*"
                        style={{ display: 'none' }}
                        id="avatar-upload"
                        type="file"
                        ref={fileInputRef}
                        onChange={handleAvatarUpload}
                        disabled={!isEditing}
                      />
                      <Button
                        variant="outlined"
                        component="label"
                        htmlFor="avatar-upload"
                        startIcon={<PhotoCamera />}
                        disabled={!isEditing}
                        sx={{ 
                          opacity: !isEditing ? 0.6 : 1,
                          cursor: !isEditing ? 'not-allowed' : 'pointer'
                        }}
                      >
                        Choose Image
                      </Button>
                      <Typography 
                        variant="caption" 
                        color={!isEditing ? "text.disabled" : "text.secondary"}
                      >
                        JPG, PNG or GIF. Max size 2MB.
                      </Typography>
                    </Stack>
                  ) : (
                    <TextField
                      fullWidth
                      label="Image URL"
                      value={profileData.avatarUrl}
                      onChange={(e) => handleAvatarUrlChange(e.target.value)}
                      disabled={!isEditing}
                      placeholder="https://example.com/image.jpg"
                      helperText={!isEditing ? "Enable edit mode to change image URL" : "Enter a valid image URL"}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <LinkIcon color={!isEditing ? "disabled" : "action"} />
                          </InputAdornment>
                        ),
                      }}
                    />
                  )}
                </Box>

                {/* Form Fields */}
                <Grid container spacing={3}>
                  <Grid item size={{ xs :12, sm :4 }}>
                      <TextField
                        fullWidth
                        label="First Name"
                        value={profileData.firstName}
                        onChange={(e) => {
                          handleProfileChange('firstName', e.target.value);
                          // Clear validation error when user starts typing
                          if (validationErrors['first_name']) {
                            setValidationErrors(prev => {
                              const newErrors = { ...prev };
                              delete newErrors['first_name'];
                              return newErrors;
                            });
                          }
                        }}
                        disabled={!isEditing}
                        error={!!validationErrors['first_name']}
                        helperText={validationErrors['first_name'] || ''}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <Person color="action" />
                            </InputAdornment>
                          ),
                        }}
                      />
                  </Grid>
                  <Grid item size={{ xs :12, sm :4 }}>
                    <TextField
                      fullWidth
                      label="Last Name"
                      value={profileData.lastName}
                      onChange={(e) => {
                        handleProfileChange('lastName', e.target.value);
                        // Clear validation error when user starts typing
                        if (validationErrors['last_name']) {
                          setValidationErrors(prev => {
                            const newErrors = { ...prev };
                            delete newErrors['last_name'];
                            return newErrors;
                          });
                        }
                      }}
                      disabled={!isEditing}
                      error={!!validationErrors['last_name']}
                      helperText={validationErrors['last_name'] || ''}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Person color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>
                  <Grid item size={{ xs :12, sm :4 }}>
                    <TextField
                      fullWidth
                      label="Email Address"
                      type="email"
                      value={profileData.email}
                      onChange={(e) => handleProfileChange('email', e.target.value)}
                      disabled={true}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Email color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>
                  <Grid item size={{ xs :12, sm :4 }}>
                    <TextField
                      fullWidth
                      label="Contact Number"
                      value={profileData.phone}
                      onChange={(e) => {
                        handleProfileChange('phone', e.target.value);
                        // Clear validation error when user starts typing
                        if (validationErrors['metadata.contact_number']) {
                          setValidationErrors(prev => {
                            const newErrors = { ...prev };
                            delete newErrors['metadata.contact_number'];
                            return newErrors;
                          });
                        }
                      }}
                      disabled={!isEditing}
                      type="tel"
                      error={!!validationErrors['metadata.contact_number']}
                      helperText={validationErrors['metadata.contact_number'] || ''}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Phone color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>
                  <Grid item size={{ xs :12, sm :8 }}>
                    <TextField
                      fullWidth
                      label="Bio"
                      multiline
                      rows={3}
                      value={profileData.bio}
                      onChange={(e) => handleProfileChange('bio', e.target.value)}
                      disabled={!isEditing}
                      placeholder="Tell us about yourself..."
                    />
                  </Grid>
                </Grid>

                {isEditing && (
                  <Stack direction="row" spacing={2} sx={{ mt: 4 }}>
                    <Button
                      variant="contained"
                      startIcon={isSaving ? <CircularProgress size={20} color="inherit" /> : <Save />}
                      onClick={handleSaveProfile}
                      disabled={isSaving}
                      size="large"
                    >
                      {isSaving ? 'Saving...' : 'Save Changes'}
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<Cancel />}
                      onClick={() => {
                        setIsEditing(false);
                        setValidationErrors({});
                        
                        // Reload original data from auth store when canceling
                        const userData = mainAuth.user;
                        const user = userData?.user || userData;
                        
                        if (user) {
                          setProfileData({
                            firstName: user.first_name || '',
                            lastName: user.last_name || '',
                            email: user.email || '',
                            phone: user.metadata?.contact_number || '',
                            role: user.role || 'Store Administrator',
                            avatar: user.metadata?.profile_image || '',
                            avatarUrl: user.metadata?.profile_image || '',
                            bio: user.metadata?.bio || '',
                            timezone: user.metadata?.timezone || 'UTC',
                            language: user.metadata?.language || 'English',
                          });
                          console.log('🔄 Profile data reset to original values on cancel');
                        }
                      }}
                      disabled={isSaving}
                      size="large"
                    >
                      Cancel
                    </Button>
                  </Stack>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
    </Container>
  );
};