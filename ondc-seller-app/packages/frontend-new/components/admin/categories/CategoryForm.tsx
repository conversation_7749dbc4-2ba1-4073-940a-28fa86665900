'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  Box,
  Typography,
  TextField,
  Button,
  Card,
  CardContent,
  CardHeader,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack,
  Alert,
  CircularProgress,
  Divider,
  Grid,
} from '@mui/material';
import {
  Save,
  ArrowBack,
  Delete,
  Category,
} from '@mui/icons-material';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useToast } from '@/app/providers/toast-provider';
import { medusaAdminService, type MedusaCategory } from '@/lib/api/medusa-admin';
import { ConfirmationModal } from '@/components/ui/ConfirmationModal';

interface CategoryFormProps {
  isEdit?: boolean;
  categoryId?: string;
}

export const CategoryForm: React.FC<CategoryFormProps> = ({ isEdit = false, categoryId }) => {
  const router = useRouter();
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  const { showToast } = useToast();

  const [formData, setFormData] = useState<Partial<MedusaCategory>>({
    name: '',
    description: '',
    handle: '',
    parent_category_id: null,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [categories, setCategories] = useState<MedusaCategory[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const [createdCategoryId, setCreatedCategoryId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Load categories for parent selection
  useEffect(() => {
    const loadCategories = async () => {
      setIsLoadingCategories(true);
      try {
        const categoriesArray = await medusaAdminService.getCategories(storeHandle);
        setCategories(categoriesArray);
      } catch (error) {
        console.error('Error loading categories:', error);
        showToast('Failed to load categories', 'error');
      } finally {
        setIsLoadingCategories(false);
      }
    };
    
    loadCategories();
  }, [storeHandle, showToast]);

  // Load category data for edit mode
  useEffect(() => {
    if (isEdit && categoryId) {
      const loadCategory = async () => {
        setIsLoading(true);
        try {
          const category = await medusaAdminService.getCategory(storeHandle, categoryId);
          setFormData(category);
        } catch (error) {
          console.error('Error loading category:', error);
          showToast('Failed to load category data', 'error');
          router.push(`/${storeHandle}/admin/categories`);
        } finally {
          setIsLoading(false);
        }
      };
      
      loadCategory();
    }
  }, [isEdit, categoryId, storeHandle, showToast, router]);

  // Auto-generate handle from name
  useEffect(() => {
    if (formData.name && !isEdit) {
      const handle = formData.name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single
        .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
      setFormData(prev => ({ ...prev, handle }));
    }
  }, [formData.name, isEdit]);

  const handleInputChange = (field: keyof MedusaCategory, value: string | null) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name?.trim()) {
      newErrors.name = 'Category name is required';
    }

    if (!formData.handle?.trim()) {
      newErrors.handle = 'Category handle is required';
    } else if (!/^[a-z0-9-]+$/.test(formData.handle)) {
      newErrors.handle = 'Handle can only contain lowercase letters, numbers, and hyphens';
    }

    if (!formData.description?.trim()) {
      newErrors.description = 'Category description is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    if (!formData.name?.trim()) {
      return;
    }

    setIsLoading(true);
    try {
      if (isEdit && categoryId) {
        // Update existing category
        await medusaAdminService.updateCategory(
          storeHandle, 
          categoryId, 
          formData.name.trim(), 
          formData.description?.trim(), 
          formData.parent_category_id || undefined,
          formData.handle?.trim()
        );
        showToast('Category updated successfully', 'success');
        // Don't redirect after update - stay on edit page
      } else {
        // Create new category
        const newCategory = await medusaAdminService.createCategory(
          storeHandle, 
          formData.name.trim(), 
          formData.description?.trim(), 
          formData.parent_category_id || undefined,
          formData.handle?.trim()
        );
        showToast('Category created successfully', 'success');
        
        // Set the created category ID and switch to edit mode
        setCreatedCategoryId(newCategory.id);
        
        // Navigate to edit page with the new category ID
        router.push(`/${storeHandle}/admin/categories/${newCategory.id}`);
      }
    } catch (error: any) {
      console.error('Error saving category:', error);
      const errorMessage = error.response?.data?.message || 'Failed to save category';
      showToast(errorMessage, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push(`/${storeHandle}/admin/categories`);
  };

  const handleDelete = async () => {
    if (!isEdit || !categoryId) return;
    
    setIsDeleting(true);
    try {
      await medusaAdminService.deleteCategory(storeHandle, categoryId);
      showToast('Category deleted successfully', 'success');
      router.push(`/${storeHandle}/admin/categories`);
    } catch (error: any) {
      console.error('Error deleting category:', error);
      const errorMessage = error.response?.data?.message || 'Failed to delete category';
      showToast(errorMessage, 'error');
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  if (isEdit && isLoading && !formData.name) {
    return (
      <Box sx={{ maxWidth: 600, mx: 'auto', p: 3, textAlign: 'center' }}>
        <CircularProgress size={48} />
        <Typography variant="body1" sx={{ mt: 2 }}>
          Loading category data...
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Stack spacing={3} sx={{ mb: 4 }}>
        <Button
          onClick={handleCancel}
          startIcon={<ArrowBack />}
          variant="text"
          sx={{ alignSelf: 'flex-start' }}
        >
          Back to Categories
        </Button>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
            {isEdit ? 'Edit Category' : 'Add New Category'}
          </Typography>
          <Typography variant="body1" color="text.secondary">
            {isEdit ? 'Update category information and settings.' : 'Create a new category for organizing your products.'}
          </Typography>
        </Box>
      </Stack>

      {/* Form */}
      <Card>
        <CardHeader
          title={isEdit ? 'Category Details' : 'New Category'}
          avatar={<Category />}
        />
        <CardContent>
          <Stack component="form" onSubmit={handleSubmit} spacing={3}>
            {/* Category Name */}
            <Grid size={{sm:12, md:6}}>

            <TextField
              label="Category Name"
              value={formData.name || ''}
              onChange={(e) => handleInputChange('name', e.target.value)}
              error={!!errors.name}
              helperText={errors.name}
              placeholder="Enter category name"
              required
              fullWidth
            />
            </Grid>

            {/* Category Handle */}
            <TextField
              label="Category Handle"
              value={formData.handle || ''}
              onChange={(e) => handleInputChange('handle', e.target.value)}
              error={!!errors.handle}
              helperText={errors.handle || 'URL-friendly version of the category name. Only lowercase letters, numbers, and hyphens allowed.'}
              placeholder="category-handle"
              required
              fullWidth
            />

            {/* Category Description */}
            <TextField
              label="Category Description"
              value={formData.description || ''}
              onChange={(e) => handleInputChange('description', e.target.value)}
              error={!!errors.description}
              helperText={errors.description}
              placeholder="Describe this category..."
              multiline
              rows={4}
              required
              fullWidth
            />

            {/* Parent Category */}
            <FormControl fullWidth>
              <InputLabel>Parent Category</InputLabel>
              <Select
                value={formData.parent_category_id || ''}
                onChange={(e) => handleInputChange('parent_category_id', e.target.value || null)}
                disabled={isLoadingCategories}
                displayEmpty
              >
                <MenuItem value="">No Parent (Top Level Category)</MenuItem>
                {categories
                  .filter(cat => cat.id !== categoryId && !cat.parent_category_id) // Exclude self and show only top-level categories
                  .map((category) => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.name}
                    </MenuItem>
                  ))}
              </Select>
              {isLoadingCategories && (
                <Typography variant="caption" color="primary" sx={{ mt: 1, display: 'block' }}>
                  Loading categories...
                </Typography>
              )}
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                Select a parent category to create a subcategory, or leave empty for a top-level category.
              </Typography>
            </FormControl>

            <Divider />

            {/* Form Actions */}
            <Stack direction="row" justifyContent="space-between" alignItems="center" spacing={2}>
              
                <Button
                  type="button"
                  onClick={handleCancel}
                  variant="outlined"
                  size="large"
                >
                  {isEdit ? 'Back to Categories' : 'Cancel'}
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading || isDeleting}
                  variant="contained"
                  size="large"
                  startIcon={isLoading ? <CircularProgress size={16} /> : <Save />}
                >
                  {isLoading
                    ? (isEdit ? 'Updating...' : 'Creating...')
                    : (isEdit ? 'Update Category' : 'Create Category')
                  }
                </Button>
        
            </Stack>
          </Stack>
        </CardContent>
      </Card>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={handleDelete}
        title="Delete Category"
        message={`Are you sure you want to delete the category "${formData.name}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        confirmButtonColor="red"
        icon="danger"
        isLoading={isDeleting}
      />
    </Box>
  );
};