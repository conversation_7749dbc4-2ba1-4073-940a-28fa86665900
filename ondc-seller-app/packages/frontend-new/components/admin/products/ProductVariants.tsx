'use client';

import React from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  IconButton,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormHelperText,
  InputAdornment,
} from '@mui/material';
import {
  Add,
  Delete,
  ExpandMore,
  Inventory,
  AttachMoney,
} from '@mui/icons-material';
import { ProductVariant } from '@/lib/validations/product';

interface ProductVariantsProps {
  variants: ProductVariant[];
  onChange: (variants: ProductVariant[]) => void;
  error?: string;
}

export const ProductVariants: React.FC<ProductVariantsProps> = ({
  variants,
  onChange,
  error,
}) => {
  const addVariant = () => {
    const newVariant: ProductVariant = {
      id: Math.random().toString(36).substr(2, 9),
      title: '',
      sku: '',
      originalPrice: 0,
      salePrice: 0,
      stock: 0,
      stockStatus: 'in_stock',
    };
    onChange([...variants, newVariant]);
  };

  const updateVariant = (index: number, updatedVariant: ProductVariant) => {
    const newVariants = [...variants];
    newVariants[index] = updatedVariant;
    onChange(newVariants);
  };

  const removeVariant = (index: number) => {
    const newVariants = variants.filter((_, i) => i !== index);
    onChange(newVariants);
  };

  const getStockStatusColor = (status: string) => {
    switch (status) {
      case 'in_stock':
        return 'success';
      case 'low_stock':
        return 'warning';
      case 'out_of_stock':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStockStatusLabel = (status: string) => {
    switch (status) {
      case 'in_stock':
        return 'In Stock';
      case 'low_stock':
        return 'Low Stock';
      case 'out_of_stock':
        return 'Out of Stock';
      default:
        return status;
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" component="h3">
          Product Variants
        </Typography>
        <Button
          variant="outlined"
          startIcon={<Add />}
          onClick={addVariant}
          size="small"
        >
          Add Variant
        </Button>
      </Box>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Create different versions of your product with unique pricing, inventory, and specifications.
      </Typography>

      {variants.length === 0 ? (
        <Card variant="outlined" sx={{ p: 4, textAlign: 'center', mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            No variants added yet. Click "Add Variant" to create your first product variant.
          </Typography>
        </Card>
      ) : (
        <Box>
          {variants.map((variant, index) => (
            <Accordion key={variant.id} sx={{ mb: 1 }}>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', mr: 2 }}>
                  <Typography sx={{ flexGrow: 1, fontWeight: 500 }}>
                    {variant.title || `Variant ${index + 1}`}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Chip
                      label={getStockStatusLabel(variant.stockStatus)}
                      color={getStockStatusColor(variant.stockStatus) as any}
                      size="small"
                    />
                    <Typography variant="body2" color="text.secondary">
                      ₹{variant.salePrice}
                    </Typography>
                    <IconButton
                      onClick={(e) => {
                        e.stopPropagation();
                        removeVariant(index);
                      }}
                      size="small"
                      color="error"
                    >
                      <Delete />
                    </IconButton>
                  </Box>
                </Box>
              </AccordionSummary>
              
              <AccordionDetails>
                <Grid container spacing={3}>
                  {/* Basic Information */}
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom>
                      Basic Information
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Variant Title"
                      value={variant.title}
                      onChange={(e) =>
                        updateVariant(index, { ...variant, title: e.target.value })
                      }
                      placeholder="e.g., Red / Large"
                    />
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="SKU"
                      value={variant.sku}
                      onChange={(e) =>
                        updateVariant(index, { ...variant, sku: e.target.value })
                      }
                      placeholder="e.g., PROD-RED-L"
                    />
                  </Grid>

                  {/* Pricing */}
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                      Pricing
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Original Price (MRP)"
                      type="number"
                      value={variant.originalPrice}
                      onChange={(e) =>
                        updateVariant(index, { ...variant, originalPrice: Number(e.target.value) })
                      }
                      InputProps={{
                        startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                      }}
                    />
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Sale Price"
                      type="number"
                      value={variant.salePrice}
                      onChange={(e) =>
                        updateVariant(index, { ...variant, salePrice: Number(e.target.value) })
                      }
                      InputProps={{
                        startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                      }}
                    />
                  </Grid>

                  {/* Inventory */}
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                      Inventory
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Stock Quantity"
                      type="number"
                      value={variant?.metadata?.product_quantity || 0}
                      onChange={(e) =>
                        updateVariant(index, { ...variant, metadata: { ...variant.metadata, product_quantity: Number(e.target.value) } })
                      }
                      InputProps={{
                        startAdornment: <InputAdornment position="start"><Inventory /></InputAdornment>,
                      }}
                    />
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Stock Status</InputLabel>
                      <Select
                        value={variant?.metadata?.product_inventory_status}
                        label="Stock Status"
                        onChange={(e) =>
                          updateVariant(index, { ...variant, metadata: { ...variant.metadata, product_inventory_status: e.target.value as any } })
                        }
                      >
                        <MenuItem value="in_stock">In Stock</MenuItem>
                        <MenuItem value="low_stock">Low Stock</MenuItem>
                        <MenuItem value="out_of_stock">Out of Stock</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  {/* Dimensions */}
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                      Dimensions (Optional)
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6} md={3}>
                    <TextField
                      fullWidth
                      label="Height"
                      type="number"
                      value={variant.height || ''}
                      onChange={(e) =>
                        updateVariant(index, { ...variant, height: e.target.value ? Number(e.target.value) : undefined })
                      }
                      InputProps={{
                        endAdornment: <InputAdornment position="end">cm</InputAdornment>,
                      }}
                    />
                  </Grid>
                  
                  <Grid item xs={6} md={3}>
                    <TextField
                      fullWidth
                      label="Width"
                      type="number"
                      value={variant.width || ''}
                      onChange={(e) =>
                        updateVariant(index, { ...variant, width: e.target.value ? Number(e.target.value) : undefined })
                      }
                      InputProps={{
                        endAdornment: <InputAdornment position="end">cm</InputAdornment>,
                      }}
                    />
                  </Grid>
                  
                  <Grid item xs={6} md={3}>
                    <TextField
                      fullWidth
                      label="Length"
                      type="number"
                      value={variant.length || ''}
                      onChange={(e) =>
                        updateVariant(index, { ...variant, length: e.target.value ? Number(e.target.value) : undefined })
                      }
                      InputProps={{
                        endAdornment: <InputAdornment position="end">cm</InputAdornment>,
                      }}
                    />
                  </Grid>
                  
                  <Grid item xs={6} md={3}>
                    <TextField
                      fullWidth
                      label="Weight"
                      type="number"
                      value={variant.weight || ''}
                      onChange={(e) =>
                        updateVariant(index, { ...variant, weight: e.target.value ? Number(e.target.value) : undefined })
                      }
                      InputProps={{
                        endAdornment: <InputAdornment position="end">kg</InputAdornment>,
                      }}
                    />
                  </Grid>

                  {/* Option Values */}
                  {variant.options && Object.keys(variant.options).length > 0 && (
                    <>
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                          Option Values
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {Object.entries(variant.options).map(([key, value]) => (
                            <Chip
                              key={key}
                              label={`${key}: ${value}`}
                              variant="outlined"
                              size="small"
                            />
                          ))}
                        </Box>
                      </Grid>
                    </>
                  )}
                </Grid>
              </AccordionDetails>
            </Accordion>
          ))}
        </Box>
      )}

      {error && (
        <FormHelperText error sx={{ mt: 1 }}>
          {error}
        </FormHelperText>
      )}
    </Box>
  );
};