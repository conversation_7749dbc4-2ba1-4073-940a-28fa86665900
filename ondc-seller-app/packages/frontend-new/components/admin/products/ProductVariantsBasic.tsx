'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>pography,
  TextField,
  Button,
  IconButton,
  Card,
  CardContent,
  Chip,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack,
  Alert,
  Collapse,
  InputAdornment,
} from '@mui/material';
import {
  Add,
  Delete,
  ExpandMore,
  ExpandLess,
} from '@mui/icons-material';
import { ProductVariant } from '@/lib/validations/product';

interface ProductVariantsProps {
  variants: ProductVariant[];
  onChange: (variants: ProductVariant[]) => void;
  error?: string;
}

export const ProductVariants: React.FC<ProductVariantsProps> = ({
  variants,
  onChange,
  error,
}) => {
  const [expandedVariants, setExpandedVariants] = useState<string[]>([]);

  const addVariant = () => {
    const newVariant: ProductVariant = {
      id: Math.random().toString(36).substr(2, 9),
      title: '',
      sku: '',
      originalPrice: 0,
      salePrice: 0,
      stock: 0,
      stockStatus: 'in_stock',
    };
    onChange([...variants, newVariant]);
    setExpandedVariants([...expandedVariants, newVariant.id]);
  };

  const updateVariant = (index: number, updatedVariant: ProductVariant) => {
    const newVariants = [...variants];
    newVariants[index] = updatedVariant;
    onChange(newVariants);
  };

  const removeVariant = (index: number) => {
    const newVariants = variants.filter((_, i) => i !== index);
    onChange(newVariants);
  };

  const toggleExpanded = (variantId: string) => {
    setExpandedVariants(prev =>
      prev.includes(variantId)
        ? prev.filter(id => id !== variantId)
        : [...prev, variantId]
    );
  };

  const getStockStatusColor = (status: string): 'success' | 'warning' | 'error' | 'default' => {
    switch (status) {
      case 'in_stock':
        return 'success';
      case 'low_stock':
        return 'warning';
      case 'out_of_stock':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStockStatusLabel = (status: string) => {
    switch (status) {
      case 'in_stock':
        return 'In Stock';
      case 'low_stock':
        return 'Low Stock';
      case 'out_of_stock':
        return 'Out of Stock';
      default:
        return status;
    }
  };

  return (
    <Box>
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
        <Typography variant="h5" component="h3" sx={{ fontWeight: 600 }}>
          Product Variants
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={addVariant}
          sx={{ borderRadius: 2 }}
        >
          Add Variant
        </Button>
      </Stack>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 4 }}>
        Create different versions of your product with unique pricing, inventory, and specifications.
      </Typography>

      {variants.length === 0 ? (
        <Card
          variant="outlined"
          sx={{
            p: 6,
            textAlign: 'center',
            border: '2px dashed',
            borderColor: 'divider',
            bgcolor: 'background.paper'
          }}
        >
          <Box sx={{ color: 'text.disabled', mb: 2 }}>
            <Add sx={{ fontSize: 48 }} />
          </Box>
          <Typography variant="body2" color="text.secondary">
            No variants added yet. Click "Add Variant" to create your first product variant.
          </Typography>
        </Card>
      ) : (
        <Stack spacing={2}>
          {variants.map((variant, index) => (
            <Card key={variant.id} variant="outlined">
              {/* Variant Header */}
              <CardContent
                sx={{
                  p: 2,
                  cursor: 'pointer',
                  '&:hover': { bgcolor: 'action.hover' },
                  '&:last-child': { pb: 2 }
                }}
                onClick={() => toggleExpanded(variant.id)}
              >
                <Stack direction="row" justifyContent="space-between" alignItems="center">
                  <Stack direction="row" alignItems="center" spacing={2}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                      {variant.title || `Variant ${index + 1}`}
                    </Typography>
                    <Chip
                      label={getStockStatusLabel(variant.stockStatus)}
                      color={getStockStatusColor(variant.stockStatus)}
                      size="small"
                    />
                    <Typography variant="body2" color="text.secondary">
                      ₹{variant.salePrice}
                    </Typography>
                  </Stack>
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <IconButton
                      onClick={(e) => {
                        e.stopPropagation();
                        removeVariant(index);
                      }}
                      color="error"
                      size="small"
                      aria-label={`Remove ${variant.title || 'variant'}`}
                    >
                      <Delete />
                    </IconButton>
                    {expandedVariants.includes(variant.id) ? (
                      <ExpandLess color="action" />
                    ) : (
                      <ExpandMore color="action" />
                    )}
                  </Stack>
                </Stack>
              </CardContent>

              {/* Variant Details */}
              <Collapse in={expandedVariants.includes(variant.id)}>
                <Box sx={{ px: 3, pb: 3, borderTop: 1, borderColor: 'divider' }}>
                  <Grid container spacing={3} sx={{ pt: 3 }}>
                    {/* Basic Information */}
                    <Grid item xs={12} md={6}>
                      <Stack spacing={3}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          Basic Information
                        </Typography>

                        <TextField
                          label="Variant Title"
                          value={variant.title}
                          onChange={(e) =>
                            updateVariant(index, { ...variant, title: e.target.value })
                          }
                          placeholder="e.g., Red / Large"
                          fullWidth
                          size="small"
                        />

                        <TextField
                          label="SKU"
                          value={variant.sku}
                          onChange={(e) =>
                            updateVariant(index, { ...variant, sku: e.target.value })
                          }
                          placeholder="e.g., PROD-RED-L"
                          fullWidth
                          size="small"
                        />
                      </Stack>
                    </Grid>

                    {/* Pricing */}
                    <Grid item xs={12} md={6}>
                      <Stack spacing={3}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          Pricing
                        </Typography>

                        <TextField
                          label="Original Price (MRP)"
                          type="number"
                          value={variant.originalPrice}
                          onChange={(e) =>
                            updateVariant(index, { ...variant, originalPrice: Number(e.target.value) })
                          }
                          fullWidth
                          size="small"
                          InputProps={{
                            startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                          }}
                        />

                        <TextField
                          label="Sale Price"
                          type="number"
                          value={variant.salePrice}
                          onChange={(e) =>
                            updateVariant(index, { ...variant, salePrice: Number(e.target.value) })
                          }
                          fullWidth
                          size="small"
                          InputProps={{
                            startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                          }}
                        />
                      </Stack>
                    </Grid>

                    {/* Inventory */}
                    <Grid item xs={12} md={6}>
                      <Stack spacing={3}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          Inventory
                        </Typography>

                        <TextField
                          label="Stock Quantity"
                          type="number"
                          value={variant.stock}
                          onChange={(e) =>
                            updateVariant(index, { ...variant, stock: Number(e.target.value) })
                          }
                          fullWidth
                          size="small"
                        />

                        <FormControl fullWidth size="small">
                          <InputLabel>Stock Status</InputLabel>
                          <Select
                            value={variant.stockStatus}
                            label="Stock Status"
                            onChange={(e) =>
                              updateVariant(index, { ...variant, stockStatus: e.target.value as any })
                            }
                          >
                            <MenuItem value="in_stock">In Stock</MenuItem>
                            <MenuItem value="low_stock">Low Stock</MenuItem>
                            <MenuItem value="out_of_stock">Out of Stock</MenuItem>
                          </Select>
                        </FormControl>
                      </Stack>
                    </Grid>

                    {/* Dimensions */}
                    <Grid item xs={12} md={6}>
                      <Stack spacing={3}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          Dimensions (Optional)
                        </Typography>

                        <Grid container spacing={2}>
                          <Grid item xs={6}>
                            <TextField
                              label="Height (cm)"
                              type="number"
                              value={variant.height || ''}
                              onChange={(e) =>
                                updateVariant(index, { ...variant, height: e.target.value ? Number(e.target.value) : undefined })
                              }
                              fullWidth
                              size="small"
                            />
                          </Grid>

                          <Grid item xs={6}>
                            <TextField
                              label="Width (cm)"
                              type="number"
                              value={variant.width || ''}
                              onChange={(e) =>
                                updateVariant(index, { ...variant, width: e.target.value ? Number(e.target.value) : undefined })
                              }
                              fullWidth
                              size="small"
                            />
                          </Grid>

                          <Grid item xs={6}>
                            <TextField
                              label="Length (cm)"
                              type="number"
                              value={variant.length || ''}
                              onChange={(e) =>
                                updateVariant(index, { ...variant, length: e.target.value ? Number(e.target.value) : undefined })
                              }
                              fullWidth
                              size="small"
                            />
                          </Grid>

                          <Grid item xs={6}>
                            <TextField
                              label="Weight (kg)"
                              type="number"
                              value={variant.weight || ''}
                              onChange={(e) =>
                                updateVariant(index, { ...variant, weight: e.target.value ? Number(e.target.value) : undefined })
                              }
                              fullWidth
                              size="small"
                            />
                          </Grid>
                        </Grid>
                      </Stack>
                    </Grid>

                    {/* Option Values */}
                    {variant.options && Object.keys(variant.options).length > 0 && (
                      <Grid item xs={12}>
                        <Stack spacing={2}>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            Option Values
                          </Typography>
                          <Stack direction="row" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>
                            {Object.entries(variant.options).map(([key, value]) => (
                              <Chip
                                key={key}
                                label={`${key}: ${value}`}
                                color="primary"
                                variant="outlined"
                                size="small"
                              />
                            ))}
                          </Stack>
                        </Stack>
                      </Grid>
                    )}
                  </Grid>
                </Box>
              </Collapse>
            </Card>
          ))}
        </Stack>
      )}

      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}
    </Box>
  );
};