'use client';

import React from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  IconButton,
  Card,
  CardContent,
  Chip,
  Grid,
  FormHelperText,
} from '@mui/material';
import {
  Add,
  Delete,
  Close,
} from '@mui/icons-material';
import { ProductOption } from '@/lib/validations/product';

interface ProductOptionsProps {
  options: ProductOption[];
  onChange: (options: ProductOption[]) => void;
  onGenerateVariants?: () => void;
  error?: string;
}

export const ProductOptions: React.FC<ProductOptionsProps> = ({
  options,
  onChange,
  onGenerateVariants,
  error,
}) => {
  const addOption = () => {
    const newOption: ProductOption = {
      id: Math.random().toString(36).substr(2, 9),
      name: '',
      values: [''],
    };
    onChange([...options, newOption]);
  };

  const updateOption = (index: number, updatedOption: ProductOption) => {
    const newOptions = [...options];
    newOptions[index] = updatedOption;
    onChange(newOptions);
  };

  const removeOption = (index: number) => {
    const newOptions = options.filter((_, i) => i !== index);
    onChange(newOptions);
  };

  const addOptionValue = (optionIndex: number) => {
    const newOptions = [...options];
    newOptions[optionIndex].values.push('');
    onChange(newOptions);
  };

  const updateOptionValue = (optionIndex: number, valueIndex: number, value: string) => {
    const newOptions = [...options];
    newOptions[optionIndex].values[valueIndex] = value;
    onChange(newOptions);
  };

  const removeOptionValue = (optionIndex: number, valueIndex: number) => {
    const newOptions = [...options];
    if (newOptions[optionIndex].values.length > 1) {
      newOptions[optionIndex].values.splice(valueIndex, 1);
      onChange(newOptions);
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" component="h3">
          Product Options
        </Typography>
        <Button
          variant="outlined"
          startIcon={<Add />}
          onClick={addOption}
          size="small"
        >
          Add Option
        </Button>
      </Box>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Add options like size, color, material, etc. These will be used to generate product variants.
      </Typography>

      {options.length === 0 ? (
        <Card variant="outlined" sx={{ p: 4, textAlign: 'center', mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            No options added yet. Click "Add Option" to create product variants.
          </Typography>
        </Card>
      ) : (
        <Box sx={{ mb: 3 }}>
          {options.map((option, optionIndex) => (
            <Card key={option.id} sx={{ mb: 2 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <TextField
                    label="Option Name"
                    value={option.name}
                    onChange={(e) =>
                      updateOption(optionIndex, { ...option, name: e.target.value })
                    }
                    placeholder="e.g., Size, Color, Material"
                    size="small"
                    sx={{ flexGrow: 1, mr: 2 }}
                  />
                  <IconButton
                    onClick={() => removeOption(optionIndex)}
                    color="error"
                    size="small"
                  >
                    <Delete />
                  </IconButton>
                </Box>

                <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
                  Option Values:
                </Typography>

                <Grid container spacing={1} sx={{ mb: 2 }}>
                  {option.values.map((value, valueIndex) => (
                    <Grid item xs={12} sm={6} md={4} key={valueIndex}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <TextField
                          value={value}
                          onChange={(e) =>
                            updateOptionValue(optionIndex, valueIndex, e.target.value)
                          }
                          placeholder="Option value"
                          size="small"
                          sx={{ flexGrow: 1 }}
                        />
                        {option.values.length > 1 && (
                          <IconButton
                            onClick={() => removeOptionValue(optionIndex, valueIndex)}
                            size="small"
                            sx={{ ml: 1 }}
                          >
                            <Close />
                          </IconButton>
                        )}
                      </Box>
                    </Grid>
                  ))}
                </Grid>

                <Button
                  variant="text"
                  startIcon={<Add />}
                  onClick={() => addOptionValue(optionIndex)}
                  size="small"
                >
                  Add Value
                </Button>

                {/* Preview of option values */}
                {option.values.some(v => v.trim()) && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                      Preview:
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {option.values
                        .filter(v => v.trim())
                        .map((value, index) => (
                          <Chip
                            key={index}
                            label={value}
                            size="small"
                            variant="outlined"
                          />
                        ))}
                    </Box>
                  </Box>
                )}
              </CardContent>
            </Card>
          ))}
        </Box>
      )}

      {options.length > 0 && onGenerateVariants && (
        <Box sx={{ textAlign: 'center', mb: 2 }}>
          <Button
            variant="contained"
            onClick={onGenerateVariants}
            disabled={!options.every(opt => opt.name && opt.values.some(v => v.trim()))}
          >
            Generate Variants from Options
          </Button>
          <Typography variant="caption" display="block" color="text.secondary" sx={{ mt: 1 }}>
            This will create all possible combinations of your options as variants
          </Typography>
        </Box>
      )}

      {error && (
        <FormHelperText error sx={{ mt: 1 }}>
          {error}
        </FormHelperText>
      )}
    </Box>
  );
};