'use client';

import React from 'react';
import {
  <PERSON>,
  Ty<PERSON>graphy,
  TextField,
  Button,
  IconButton,
  Card,
  CardContent,
  Chip,
  Grid,
  FormHelperText,
  Stack,
  Alert,
} from '@mui/material';
import {
  Add,
  Delete,
  Close,
} from '@mui/icons-material';
import { ProductOption } from '@/lib/validations/product';

interface ProductOptionsProps {
  options: ProductOption[];
  onChange: (options: ProductOption[]) => void;
  onGenerateVariants?: () => void;
  error?: string;
}

export const ProductOptions: React.FC<ProductOptionsProps> = ({
  options,
  onChange,
  onGenerateVariants,
  error,
}) => {
  const addOption = () => {
    const newOption: ProductOption = {
      id: Math.random().toString(36).substr(2, 9),
      name: '',
      values: [''],
    };
    onChange([...options, newOption]);
  };

  const updateOption = (index: number, updatedOption: ProductOption) => {
    const newOptions = [...options];
    newOptions[index] = updatedOption;
    onChange(newOptions);
  };

  const removeOption = (index: number) => {
    const newOptions = options.filter((_, i) => i !== index);
    onChange(newOptions);
  };

  const addOptionValue = (optionIndex: number) => {
    const newOptions = [...options];
    newOptions[optionIndex].values.push('');
    onChange(newOptions);
  };

  const updateOptionValue = (optionIndex: number, valueIndex: number, value: string) => {
    const newOptions = [...options];
    newOptions[optionIndex].values[valueIndex] = value;
    onChange(newOptions);
  };

  const removeOptionValue = (optionIndex: number, valueIndex: number) => {
    const newOptions = [...options];
    if (newOptions[optionIndex].values.length > 1) {
      newOptions[optionIndex].values.splice(valueIndex, 1);
      onChange(newOptions);
    }
  };

  return (
    <Box>
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
        <Typography variant="h5" component="h3" sx={{ fontWeight: 600 }}>
          Product Options
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={addOption}
          sx={{ borderRadius: 2 }}
        >
          Add Option
        </Button>
      </Stack>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 4 }}>
        Add options like size, color, material, etc. These will be used to generate product variants.
      </Typography>

      {options.length === 0 ? (
        <Card
          variant="outlined"
          sx={{
            p: 6,
            textAlign: 'center',
            border: '2px dashed',
            borderColor: 'divider',
            bgcolor: 'background.paper'
          }}
        >
          <Box sx={{ color: 'text.disabled', mb: 2 }}>
            <Add sx={{ fontSize: 48 }} />
          </Box>
          <Typography variant="body2" color="text.secondary">
            No options added yet. Click "Add Option" to create product variants.
          </Typography>
        </Card>
      ) : (
        <Stack spacing={3}>
          {options.map((option, optionIndex) => (
            <Card key={option.id} sx={{ p: 3 }}>
              <CardContent sx={{ p: 0, '&:last-child': { pb: 0 } }}>
                <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
                  <TextField
                    value={option.name}
                    onChange={(e) =>
                      updateOption(optionIndex, { ...option, name: e.target.value })
                    }
                    placeholder="e.g., Size, Color, Material"
                    variant="standard"
                    sx={{
                      flexGrow: 1,
                      '& .MuiInput-input': {
                        fontSize: '1.125rem',
                        fontWeight: 500
                      }
                    }}
                    InputProps={{
                      disableUnderline: true,
                    }}
                  />
                  <IconButton
                    onClick={() => removeOption(optionIndex)}
                    color="error"
                    size="small"
                    aria-label={`Remove ${option.name || 'option'}`}
                  >
                    <Delete />
                  </IconButton>
                </Stack>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 500 }}>
                    Option Values:
                  </Typography>
                  <Grid container spacing={2}>
                    {option.values.map((value, valueIndex) => (
                      <Grid item xs={12} sm={6} lg={4} key={valueIndex}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <TextField
                            value={value}
                            onChange={(e) =>
                              updateOptionValue(optionIndex, valueIndex, e.target.value)
                            }
                            placeholder="Option value"
                            size="small"
                            fullWidth
                            variant="outlined"
                          />
                          {option.values.length > 1 && (
                            <IconButton
                              onClick={() => removeOptionValue(optionIndex, valueIndex)}
                              size="small"
                              color="error"
                              aria-label={`Remove value ${value || 'option value'}`}
                            >
                              <Close />
                            </IconButton>
                          )}
                        </Stack>
                      </Grid>
                    ))}
                  </Grid>
                </Box>

                <Button
                  onClick={() => addOptionValue(optionIndex)}
                  startIcon={<Add />}
                  size="small"
                  variant="text"
                  sx={{ alignSelf: 'flex-start' }}
                >
                  Add Value
                </Button>

                {/* Preview of option values */}
                {option.values.some(v => v.trim()) && (
                  <Box sx={{ mt: 3, pt: 3, borderTop: 1, borderColor: 'divider' }}>
                    <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                      Preview:
                    </Typography>
                    <Stack direction="row" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>
                      {option.values
                        .filter(v => v.trim())
                        .map((value, index) => (
                          <Chip
                            key={index}
                            label={value}
                            size="small"
                            variant="outlined"
                          />
                        ))}
                    </Stack>
                  </Box>
                )}
              </CardContent>
            </Card>
          ))}
        </Stack>
      )}

      {options.length > 0 && onGenerateVariants && (
        <Box sx={{ textAlign: 'center', mt: 4 }}>
          <Button
            onClick={onGenerateVariants}
            disabled={!options.every(opt => opt.name && opt.values.some(v => v.trim()))}
            variant="contained"
            color="success"
            size="large"
            sx={{ px: 4, py: 1.5, borderRadius: 2 }}
          >
            Generate Variants from Options
          </Button>
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            This will create all possible combinations of your options as variants
          </Typography>
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}
    </Box>
  );
};