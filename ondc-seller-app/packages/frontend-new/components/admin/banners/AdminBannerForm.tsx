'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useDropzone } from 'react-dropzone';
import { getBannerByDocumentId, updateBannerByDocumentId, createBanner, type BannerData, type StrapiResponse } from '@/lib/api/strapi/banners';
// Strapi base URL from environment
import { useToast } from '@/app/providers/toast-provider';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  FormControlLabel,
  Switch,
  Stack,
  Avatar,
  IconButton,
  Paper,
  Divider,
  Container,
  Breadcrumbs,
  Link as MuiLink,
  InputAdornment,
  Tooltip,
  CircularProgress,
  Skeleton,
  Tab,
  Tabs,
} from '@mui/material';
import {
  Save as SaveIcon,
  Cancel as CancelIcon,
  CloudUpload as CloudUploadIcon,
  Image as ImageIcon,
  Link as LinkIcon,
  Title as TitleIcon,
  Description as DescriptionIcon,
  NavigateNext as NavigateNextIcon,
  Preview as PreviewIcon,
  Delete as DeleteIcon,
  Close as CloseIcon,
  ArrowBack as ArrowBackIcon
} from '@mui/icons-material';
import Link from 'next/link';

interface BannerFormData {
  title: string;
  description: string;
  imageUrl: string;
  buttonText: string;
  buttonLink: string;
  isActive: boolean;
}

interface UploadedFile {
  file: File;
  preview: string;
}

interface AdminBannerFormProps {
  bannerId?: string; // This will be documentId for edit mode
  mode: 'create' | 'edit';
  storeHandle: string;
}

export const AdminBannerForm: React.FC<AdminBannerFormProps> = ({ 
  bannerId, 
  mode, 
  storeHandle 
}) => {
  const router = useRouter();
  const { showToast } = useToast();
  
  const [formData, setFormData] = useState<BannerFormData>({
    title: '',
    description: '',
    imageUrl: '',
    buttonText: '',
    buttonLink: '',
    isActive: true
  });

  const [errors, setErrors] = useState<Partial<BannerFormData>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingBanner, setIsLoadingBanner] = useState(false);
  const [imagePreview, setImagePreview] = useState<string>('');
  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null);
  const [imageInputMethod, setImageInputMethod] = useState<'url' | 'upload'>('url');

  // Helper function to convert Strapi banner data to form format
  const convertStrapiBannerToForm = (strapiBanner: BannerData): BannerFormData => {
    // Get image URL from either image_url field or nested image object
    let imageUrl = strapiBanner.image_url || strapiBanner.image?.url || '';
    
    // Add Strapi base URL if the image URL is relative
    if (imageUrl && !imageUrl.startsWith('http') && !imageUrl.startsWith('data:')) {
      const strapiBaseUrl = process.env.NEXT_PUBLIC_STRAPI_BASE_URL || 'http://localhost:1337';
      imageUrl = `${strapiBaseUrl}${imageUrl}`;
    }
    
    return {
      title: strapiBanner.title,
      description: strapiBanner.description,
      imageUrl: imageUrl,
      buttonText: strapiBanner.buttonText || '',
      buttonLink: strapiBanner.buttonLink || strapiBanner.link || '',
      isActive: strapiBanner.active
    };
  };

  // Fetch banner data when in edit mode
  useEffect(() => {
    const fetchBannerData = async () => {
      if (mode !== 'edit' || !bannerId) {
        return;
      }

      try {
        setIsLoadingBanner(true);
        
        const response: StrapiResponse<BannerData> = await getBannerByDocumentId(bannerId);
        
        // Convert Strapi data to form format
        const formData = convertStrapiBannerToForm(response.data);
        setFormData(formData);
        
        // Set image preview if available
        if (formData.imageUrl) {
          setImagePreview(formData.imageUrl);
        }
        
      } catch (error: any) {
        console.error('Error fetching banner data:', error);
        showToast(`Failed to load banner: ${error.message || 'Unknown error'}`, 'error');
        
      } finally {
        setIsLoadingBanner(false);
      }
    };

    fetchBannerData();
  }, [mode, bannerId, showToast]);

  // Dropzone configuration
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      const preview = URL.createObjectURL(file);
      setUploadedFile({ file, preview });
      setImagePreview(preview);
      setFormData(prev => ({ ...prev, imageUrl: '' })); // Clear URL when file is uploaded
      showToast('Image uploaded successfully', 'success');
    }
  }, [showToast]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp', '.gif']
    },
    maxFiles: 1,
    maxSize: 5 * 1024 * 1024, // 5MB
  });

  // Remove uploaded file
  const removeUploadedFile = () => {
    if (uploadedFile) {
      URL.revokeObjectURL(uploadedFile.preview);
    }
    setUploadedFile(null);
    setImagePreview('');
    setFormData(prev => ({ ...prev, imageUrl: '' }));
  };

  const handleInputChange = (field: keyof BannerFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  const handleImageUrlChange = (url: string) => {
    setFormData(prev => ({ ...prev, imageUrl: url }));
    setImagePreview(url);
    // Clear uploaded file when URL is entered
    if (uploadedFile) {
      URL.revokeObjectURL(uploadedFile.preview);
      setUploadedFile(null);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<BannerFormData> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Banner title is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Banner description is required';
    }

    if (!formData.imageUrl.trim() && !uploadedFile) {
      newErrors.imageUrl = 'Banner image is required (either upload a file or provide a URL)';
    }

    if (!formData.buttonText.trim()) {
      newErrors.buttonText = 'Button text is required';
    }

    if (!formData.buttonLink.trim()) {
      newErrors.buttonLink = 'Button link is required';
    } else if (!formData.buttonLink.startsWith('/') && !formData.buttonLink.startsWith('http')) {
      newErrors.buttonLink = 'Button link must be a valid URL or path';
    }

    setErrors(newErrors);
    
    // Show validation error toast if there are errors
    if (Object.keys(newErrors).length > 0) {
      const firstError = Object.values(newErrors)[0];
      showToast(firstError || 'Please fix the validation errors', 'error');
    }
    
    return Object.keys(newErrors).length === 0;
  };

  // Helper function to upload file to Strapi
  const uploadFileToStrapi = async (file: File, token: string): Promise<string> => {
    const formData = new FormData();
    formData.append('files', file);
    
    const strapiBaseUrl = process.env.NEXT_PUBLIC_STRAPI_BASE_URL || 'http://localhost:1337';
    const response = await fetch(`${strapiBaseUrl}/api/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: formData
    });
    
    if (!response.ok) {
      throw new Error('Failed to upload image to Strapi');
    }
    
    const uploadResult = await response.json();
    
    // Return the URL of the uploaded file
    return uploadResult[0]?.url || '';
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    
    try {
      let imageUrl = formData.imageUrl;
      
      // Prepare banner data for API
      const bannerData = {
        title: formData.title,
        description: formData.description,
        image_url: imageUrl,
        buttonText: formData.buttonText,
        buttonLink: formData.buttonLink,
        active: formData.isActive,
        tenant_store: storeHandle,
      };
      
      if (mode === 'edit' && bannerId) {
        // Update existing banner using documentId
        await updateBannerByDocumentId(bannerId, bannerData);
        showToast('Banner updated successfully!','success');
      } else {
        // Create new banner
        const response = await createBanner(bannerData);
        if(response?.data?.id) showToast('Banner created successfully!','success');
      }
      
    } catch (error: any) {
      console.error('Error saving banner:', error);
      
      // Show error toast
      showToast(
        `Failed to ${mode === 'create' ? 'create' : 'update'} banner: ${error.message || 'Unknown error'}`,
        'error'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push(`/${storeHandle}/admin/banners`);
  };

  // Show loading state while fetching banner data
  if (isLoadingBanner) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" minHeight="400px">
          <CircularProgress size={48} sx={{ mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            Loading banner data...
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Fetching banner details for editing
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Breadcrumb Navigation */}
      <Box mb={3}>
        <Breadcrumbs 
          separator={<NavigateNextIcon fontSize="small" />}
          sx={{ mb: 2 }}
        >
          <Button
            component={Link}
            href={`/${storeHandle}/admin/banners`}
            startIcon={<ArrowBackIcon />}
            variant="text"
            color="inherit"
            sx={{ 
              textTransform: 'none',
              color: 'text.secondary',
              '&:hover': {
                color: 'primary.main',
                bgcolor: 'transparent'
              }
            }}
          >
            Back to Banners
          </Button>
        </Breadcrumbs>
      </Box>

      {/* Page Header */}
      <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={4}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight={600}>
            {mode === 'create' ? 'Create New Banner' : 'Edit Banner'}
          </Typography>
          <Typography variant="body1" color="text.secondary">
            {mode === 'create' 
              ? 'Create a new promotional banner for your store' 
              : 'Update banner information and settings'
            }
          </Typography>
        </Box>
      </Box>

      {/* Action Buttons - Top Right */}
      <Box display="flex" justifyContent="end" alignItems="center" mb={4}>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            onClick={handleCancel}
            startIcon={<CancelIcon />}
            size="large"
            sx={{
              borderRadius: 2,
              textTransform: 'none',
              px: 3
            }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit}
            startIcon={<SaveIcon />}
            disabled={isLoading}
            size="large"
            sx={{
              borderRadius: 2,
              textTransform: 'none',
              px: 3
            }}
          >
            {isLoading ? (
              <CircularProgress size={20} sx={{ mr: 1 }} />
            ) : null}
            {mode === 'create' ? 'Create Banner' : 'Update Banner'}
          </Button>
        </Box>
      </Box>

      <form onSubmit={handleSubmit}>
        <Grid container spacing={4}>
          {/* Main Form */}
          <Grid item size={12}>
            <Stack spacing={3}>
            {/* Basic Information */}
            <Card elevation={1} sx={{ borderRadius: 2, mb: 3 }}>
              <CardContent sx={{ p: 3 }}>
                <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
                  <Box display="flex" alignItems="center" gap={2}>
                    <TitleIcon color="primary" />
                    <Typography variant="h6" fontWeight={600}>
                      Basic Information
                    </Typography>
                  </Box>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.isActive}
                        onChange={(e) => handleInputChange('isActive', e.target.checked)}
                        color="primary"
                      />
                    }
                    label="Active"
                    labelPlacement="start"
                    sx={{ m: 0 }}
                  />
                </Box>
                  
                  <Grid container spacing={3}>
                    <Grid item size={{xs:12, sm:4}}>
                      <TextField
                        fullWidth
                        label="Banner Title"
                        value={formData.title}
                        onChange={(e) => handleInputChange('title', e.target.value)}
                        error={!!errors.title}
                        helperText={errors.title || 'Enter a compelling title for your banner'}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <TitleIcon color="action" />
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Grid>
                    
                    <Grid item size={{xs:12, sm:8}}>
                      <TextField
                        fullWidth
                        label="Banner Description"
                        multiline
                        rows={3}
                        value={formData.description}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                        error={!!errors.description}
                        helperText={errors.description || 'Describe what this banner promotes'}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start" sx={{ alignSelf: 'flex-start', mt: 1 }}>
                              <DescriptionIcon color="action" />
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Banner Image */}
              <Card elevation={1} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <ImageIcon color="primary" />
                    Banner Image
                  </Typography>
                  <Divider sx={{ mb: 3 }} />
                  
                  {/* Image Input Method Tabs */}
                  <Tabs
                    value={imageInputMethod}
                    onChange={(_, newValue) => setImageInputMethod(newValue)}
                    sx={{ mb: 3 }}
                  >
                    <Tab label="Upload Image" value="upload" />
                    <Tab label="Image URL" value="url" />
                  </Tabs>
                  
                  {imageInputMethod === 'upload' ? (
                    /* Image Upload Section */
                    <Box>
                      <Box
                        {...getRootProps()}
                        sx={{
                          border: '2px dashed',
                          borderColor: isDragActive ? 'primary.main' : 'grey.300',
                          borderRadius: 2,
                          p: 4,
                          textAlign: 'center',
                          cursor: 'pointer',
                          bgcolor: isDragActive ? 'primary.50' : 'grey.50',
                          transition: 'all 0.2s ease',
                          '&:hover': {
                            borderColor: 'primary.main',
                            bgcolor: 'primary.50'
                          }
                        }}
                      >
                        <input {...getInputProps()} />
                        <CloudUploadIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
                        <Typography variant="h6" gutterBottom>
                          {isDragActive ? 'Drop the image here' : 'Drag & drop an image here'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          or click to select a file
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Supports: JPG, PNG, WebP, GIF (Max 5MB)
                        </Typography>
                      </Box>
                      
                      {uploadedFile && (
                        <Box mt={2} p={2} border={1} borderColor="grey.300" borderRadius={2}>
                          <Box display="flex" alignItems="center" justifyContent="space-between">
                            <Typography variant="body2">
                              {uploadedFile.file.name} ({(uploadedFile.file.size / 1024 / 1024).toFixed(2)} MB)
                            </Typography>
                            <IconButton onClick={removeUploadedFile} size="small">
                              <CloseIcon />
                            </IconButton>
                          </Box>
                        </Box>
                      )}
                    </Box>
                  ) : (
                    /* Image URL Section */
                    <TextField
                      fullWidth
                      label="Image URL"
                      value={formData.imageUrl}
                      onChange={(e) => handleImageUrlChange(e.target.value)}
                      error={!!errors.imageUrl}
                      helperText={errors.imageUrl || 'Enter the URL of your banner image (recommended: 1200x400px)'}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <ImageIcon />
                          </InputAdornment>
                        ),
                      }}
                    />
                  )}
                </CardContent>
              </Card>

              {/* Call to Action */}
              <Card elevation={1} sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 4 }}>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <LinkIcon color="primary" />
                    Call to Action
                  </Typography>
                  <Divider sx={{ mb: 3 }} />
                  
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Button Text"
                        value={formData.buttonText}
                        onChange={(e) => handleInputChange('buttonText', e.target.value)}
                        error={!!errors.buttonText}
                        helperText={errors.buttonText || 'Text to display on the button'}
                        placeholder="Shop Now"
                      />
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Button Link"
                        value={formData.buttonLink}
                        onChange={(e) => handleInputChange('buttonLink', e.target.value)}
                        error={!!errors.buttonLink}
                        helperText={errors.buttonLink || 'Where the button should redirect (e.g., /products)'}
                        placeholder="/collections/featured"
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Stack>
          </Grid>
        </Grid>
        
        {/* Full Width Image Preview at Bottom */}
        {(imagePreview || formData.imageUrl || uploadedFile) && (
          <Card elevation={1} sx={{ borderRadius: 2, mt: 4 }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PreviewIcon color="primary" />
                Banner Preview
              </Typography>
              <Divider sx={{ mb: 3 }} />
              
              <Box
                sx={{
                  position: 'relative',
                  width: '100%',
                  borderRadius: 2,
                  overflow: 'hidden',
                  border: '1px solid',
                  borderColor: 'divider',
                  bgcolor: 'grey.50'
                }}
              >
                <Box
                  component="img"
                  src={uploadedFile?.preview || imagePreview || formData.imageUrl}
                  alt="Banner preview"
                  sx={{
                    width: '100%',
                    height: 'auto',
                    maxHeight: 400,
                    objectFit: 'cover',
                    display: 'block'
                  }}
                  onError={() => {
                    setImagePreview('');
                    showToast('Failed to load image preview', 'error');
                  }}
                />
                
                {/* Banner Content Overlay */}
                {(formData.buttonText) && (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'end',
                      alignItems: 'center',
                      color: 'white',
                      textAlign: 'center',
                      p: 4
                    }}
                  >
                    {formData.buttonText && (
                      <Button
                        variant="contained"
                        size="large"
                        sx={{
                          mt: 2,
                          px: 4,
                          py: 1.5,
                          fontSize: '1.1rem',
                          fontWeight: 'bold',
                          borderRadius: 2,
                          textTransform: 'none',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: '0 6px 16px rgba(0,0,0,0.4)'
                          },
                          transition: 'all 0.3s ease'
                        }}
                      >
                        {formData.buttonText}
                      </Button>
                    )}
                  </Box>
                )}
              </Box>
              
              <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block' }}>
                This is how your banner will appear on your store with the call-to-action button
              </Typography>
            </CardContent>
          </Card>
        )}
      </form>
    </Container>
  );
};