'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { useNavigationWithLoading } from '@/hooks/useNavigationWithLoading';
import Link from 'next/link';
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Chip,
  Grid,
  Divider,
  Avatar,
  Stack,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Badge,
  Alert,
  CircularProgress,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Breadcrumbs,
  Link as MuiLink,
} from '@mui/material';
import {
  ArrowBack,
  CheckCircle,
  Schedule,
  Cancel,
  Warning,
  LocalShipping,
  Payment,
  Person,
  LocationOn,
  Receipt,
  MoreVert,
  Edit,
  Print,
  Email,
  Refresh,
  NavigateNext,
} from '@mui/icons-material';
import { useAuthStore } from '@/stores/authStore';
import medusaAdminService from '@/lib/api/medusa-admin';
import { Order } from '@/lib/api/medusa/orders';
import { formatPrice } from '@/lib/cart/mockCartData';
import { format } from 'date-fns';
import { useToast } from '@/app/providers/toast-provider';

interface AdminOrderDetailsProps {
  storeHandle: string;
  orderId: string;
}

export const AdminOrderDetails: React.FC<AdminOrderDetailsProps> = ({ 
  storeHandle, 
  orderId 
}) => {
  const router = useRouter();
  const { getStoreHandle } = useAuthStore();
  const { showToast } = useToast();
  const { navigateWithLoading } = useNavigationWithLoading();
  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [updateStatusDialog, setUpdateStatusDialog] = useState(false);
  const [newStatus, setNewStatus] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);

  // Get dynamic store handle from auth store
  const dynamicStoreHandle = getStoreHandle() || storeHandle;

  // Fetch order details
  useEffect(() => {
    const fetchOrderDetails = async () => {
      if (!dynamicStoreHandle || !orderId) {
        console.log('Missing store handle or order ID');
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        console.log('=== FETCHING ORDER DETAILS ===');
        console.log('Store handle:', dynamicStoreHandle);
        console.log('Order ID:', orderId);

        const orderData = await medusaAdminService.getOrder(dynamicStoreHandle, orderId);
        console.log('Order details fetched successfully:', orderData);
        
        setOrder(orderData);
      } catch (error: any) {
        console.error('Failed to fetch order details:', error);
        setError(error.message || 'Failed to fetch order details');
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrderDetails();
  }, [dynamicStoreHandle, orderId]);

  const getStatusColor = (status?: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    if (!status) return 'default';
    
    switch (status.toLowerCase()) {
      case 'pending':
        return 'warning';
      case 'completed':
        return 'success';
      case 'archived':
        return 'secondary';
      case 'canceled':
      case 'cancelled':
        return 'error';
      case 'requires_action':
        return 'info';
      default:
        return 'default';
    }
  };

  const getStatusText = (status?: string): string => {
    if (!status) return 'Unknown';
    
    switch (status.toLowerCase()) {
      case 'pending':
        return 'Pending';
      case 'completed':
        return 'Completed';
      case 'archived':
        return 'Archived';
      case 'canceled':
      case 'cancelled':
        return 'Cancelled';
      case 'requires_action':
        return 'Requires Action';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  const getFulfillmentStatusColor = (status?: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    if (!status) return 'default';
    
    switch (status.toLowerCase()) {
      case 'not_fulfilled':
        return 'warning';
      case 'partially_fulfilled':
        return 'info';
      case 'fulfilled':
        return 'success';
      case 'partially_shipped':
        return 'primary';
      case 'shipped':
        return 'primary';
      case 'partially_returned':
        return 'warning';
      case 'returned':
        return 'error';
      case 'canceled':
      case 'cancelled':
        return 'secondary';
      case 'requires_action':
        return 'error';
      default:
        return 'default';
    }
  };

  const getFulfillmentStatusText = (status?: string): string => {
    if (!status) return 'Unknown';
    
    switch (status.toLowerCase()) {
      case 'not_fulfilled':
        return 'Not Fulfilled';
      case 'partially_fulfilled':
        return 'Partially Fulfilled';
      case 'fulfilled':
        return 'Fulfilled';
      case 'partially_shipped':
        return 'Partially Shipped';
      case 'shipped':
        return 'Shipped';
      case 'partially_returned':
        return 'Partially Returned';
      case 'returned':
        return 'Returned';
      case 'canceled':
      case 'cancelled':
        return 'Cancelled';
      case 'requires_action':
        return 'Requires Action';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  const getPaymentStatusColor = (status?: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    if (!status) return 'default';
    
    switch (status.toLowerCase()) {
      case 'not_paid':
        return 'warning';
      case 'awaiting':
        return 'info';
      case 'captured':
        return 'success';
      case 'partially_refunded':
        return 'warning';
      case 'refunded':
        return 'error';
      case 'canceled':
      case 'cancelled':
        return 'secondary';
      case 'requires_action':
        return 'error';
      default:
        return 'default';
    }
  };

  const getPaymentStatusText = (status?: string): string => {
    if (!status) return 'Unknown';
    
    switch (status.toLowerCase()) {
      case 'not_paid':
        return 'Not Paid';
      case 'awaiting':
        return 'Awaiting Payment';
      case 'captured':
        return 'Paid';
      case 'partially_refunded':
        return 'Partially Refunded';
      case 'refunded':
        return 'Refunded';
      case 'canceled':
      case 'cancelled':
        return 'Cancelled';
      case 'requires_action':
        return 'Requires Action';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  const getStatusIcon = (status?: string) => {
    if (!status) return <Warning />;
    
    switch (status.toLowerCase()) {
      case 'completed':
      case 'fulfilled':
      case 'captured':
        return <CheckCircle />;
      case 'pending':
      case 'awaiting':
        return <Schedule />;
      case 'canceled':
      case 'cancelled':
        return <Cancel />;
      default:
        return <Warning />;
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleUpdateStatus = () => {
    setNewStatus(order?.status || '');
    setUpdateStatusDialog(true);
    handleMenuClose();
  };

  const handleStatusUpdate = async () => {
    if (!order || !newStatus) return;

    setIsUpdating(true);
    try {
      // TODO: Implement status update API call
      console.log('Updating order status to:', newStatus);
      // await medusaAdminService.updateOrderStatus(dynamicStoreHandle, orderId, newStatus);
      
      // For now, just update local state
      setOrder({ ...order, status: newStatus });
      setUpdateStatusDialog(false);
    } catch (error: any) {
      console.error('Failed to update order status:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <Stack spacing={2} alignItems="center">
          <CircularProgress size={40} />
          <Typography variant="body2" color="text.secondary">
            Loading order details...
          </Typography>
        </Stack>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert 
          severity="error" 
          action={
            <Button color="inherit" size="small" onClick={handleRefresh} startIcon={<Refresh />}>
              Retry
            </Button>
          }
        >
          <strong>Error loading order:</strong> {error}
        </Alert>
      </Box>
    );
  }

  if (!order) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning">
          Order not found. The order may have been deleted or you may not have permission to view it.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Breadcrumbs */}

      {/* Header */}
      <Stack direction="row" justifyContent="space-between" alignItems="flex-start" sx={{ mb: 3 }}>
        <Box>
          <Button
            startIcon={<ArrowBack />}
            onClick={() => navigateWithLoading(`/${dynamicStoreHandle}/admin/orders`)}
            // sx={{ mb: 2 }}
            // color="inherit"
            variant="text"
            sx={{ alignSelf: 'flex-start' }}
          >
            Back to Orders
          </Button>
          
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            Order #{order.display_id}
          </Typography>
          
          <Typography variant="body1" color="text.secondary">
            Placed on {format(new Date(order.created_at), 'EEEE, MMMM d, yyyy \'at\' hh:mm a')}
          </Typography>
        </Box>

        <Stack direction="row" spacing={2}>
          <Button variant="outlined" startIcon={<Print />}>
            Print
          </Button>
          <Button variant="outlined" startIcon={<Email />}>
            Email Customer
          </Button>
          <IconButton onClick={handleMenuOpen}>
            <MoreVert />
          </IconButton>
        </Stack>
      </Stack>

      {/* Status Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item size={{xs:12, md:3}}>
          <Card>
            <CardContent>
              <Stack direction="row" spacing={2} alignItems="center">
                {getStatusIcon(order.status)}
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Order Status
                  </Typography>
                  <Chip
                    label={getStatusText(order.status)}
                    color={getStatusColor(order.status)}
                    size="small"
                  />
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item size={{xs:12, md:3}}>
          <Card>
            <CardContent>
              <Stack direction="row" spacing={2} alignItems="center">
                <LocalShipping />
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Fulfillment
                  </Typography>
                  <Chip
                    label={getFulfillmentStatusText(order.fulfillment_status)}
                    color={getFulfillmentStatusColor(order.fulfillment_status)}
                    size="small"
                  />
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item size={{xs:12, md:3}}>
          <Card>
            <CardContent>
              <Stack direction="row" spacing={2} alignItems="center">
                <Payment />
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Payment
                  </Typography>
                  <Chip
                    label={getPaymentStatusText(order.payment_status)}
                    color={getPaymentStatusColor(order.payment_status)}
                    size="small"
                  />
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Order Items */}
        <Grid item size={{xs:12, md:8}}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Receipt />
                Order Items ({order.items?.length || 0})
              </Typography>
              
              <List>
                {order.items?.map((item, index) => (
                  <React.Fragment key={item.id}>
                    <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                      <ListItemAvatar>
                        <Badge 
                          badgeContent={item.quantity} 
                          color="primary"
                          anchorOrigin={{
                            vertical: 'top',
                            horizontal: 'right',
                          }}
                        >
                          <Avatar
                            variant="rounded"
                            sx={{ width: 60, height: 60, mr: 2 }}
                            src={item.thumbnail}
                          >
                            {!item.thumbnail && (
                              <Typography variant="h6" color="text.secondary">
                                {item.title?.charAt(0) || '?'}
                              </Typography>
                            )}
                          </Avatar>
                        </Badge>
                      </ListItemAvatar>
                      
                      <ListItemText
                        primary={
                          <Typography variant="subtitle1" fontWeight="medium">
                            {item.title}
                          </Typography>
                        }
                        secondary={
                          <Box>
                            {item.description && (
                              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                {item.description}
                              </Typography>
                            )}
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                {formatPrice(item.unit_price)} × {item.quantity}
                              </Typography>
                              <Typography variant="subtitle1" color="primary" fontWeight="bold">
                                {formatPrice(item.unit_price * item.quantity)}
                              </Typography>
                            </Box>
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < (order.items?.length || 0) - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Order Summary and Details */}
        <Grid item size={{xs:12, md:4}}>
          <Stack spacing={3}>
            {/* Order Summary */}
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Order Summary
                </Typography>
                
                <Stack spacing={2}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2" color="text.secondary">
                      Subtotal:
                    </Typography>
                    <Typography variant="body2">
                      {formatPrice(order.subtotal || 0)}
                    </Typography>
                  </Box>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2" color="text.secondary">
                      Shipping:
                    </Typography>
                    <Typography variant="body2">
                      {(order.shipping_total || 0) === 0 ? 'Free' : formatPrice(order.shipping_total || 0)}
                    </Typography>
                  </Box>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2" color="text.secondary">
                      Tax:
                    </Typography>
                    <Typography variant="body2">
                      {formatPrice(order.tax_total || 0)}
                    </Typography>
                  </Box>
                  
                  <Divider />
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="h6" fontWeight="bold">
                      Total:
                    </Typography>
                    <Typography variant="h6" color="primary" fontWeight="bold">
                      {formatPrice(order.total || 0)}
                    </Typography>
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Stack>
        </Grid>
      </Grid>
      <Grid container spacing={3} sx={{ mt: 3 }}>
        <Grid item size={12}>
         {/* Customer Information */}
          <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Person />
                  Customer Information
                </Typography>
                
                <Stack spacing={1}>
                  <Typography variant="body1" fontWeight="medium">
                    {order?.customer 
                      ? `${order.customer.first_name} ${order.customer.last_name}`
                      : order.shipping_address 
                        ? `${order.shipping_address.first_name} ${order.shipping_address.last_name}`
                        : 'Unknown Customer'
                    }
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {order?.customer?.email || 'No email provided'}
                  </Typography>
                  {(order?.customer?.phone || order?.shipping_address?.phone) && (
                    <Typography variant="body2" color="text.secondary">
                      {order?.customer?.phone || order?.shipping_address?.phone}
                    </Typography>
                  )}
                </Stack>
              </CardContent>
          </Card>
        </Grid>
        <Grid item size={12}>
            {/* Shipping Address */}
            {order.shipping_address && (
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <LocalShipping />
                    Shipping Address
                  </Typography>
                  
                  <Stack spacing={0.5}>
                    <Typography variant="body1" fontWeight="medium">
                      {order.shipping_address.first_name} {order.shipping_address.last_name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {order.shipping_address.address_1}
                    </Typography>
                    {order.shipping_address.address_2 && (
                      <Typography variant="body2" color="text.secondary">
                        {order.shipping_address.address_2}
                      </Typography>
                    )}
                    <Typography variant="body2" color="text.secondary">
                      {order.shipping_address.city}, {order.shipping_address.province} {order.shipping_address.postal_code}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {order.shipping_address.country_code}
                    </Typography>
                    {order.shipping_address.phone && (
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                        {order.shipping_address.phone}
                      </Typography>
                    )}
                  </Stack>
                </CardContent>
              </Card>
            )}

        </Grid>
        <Grid item size={12}>
            {/* Billing Address */}
            {order.billing_address && (
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Payment />
                    Billing Address
                  </Typography>
                  
                  <Stack spacing={0.5}>
                    <Typography variant="body1" fontWeight="medium">
                      {order.billing_address.first_name} {order.billing_address.last_name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {order.billing_address.address_1}
                    </Typography>
                    {order.billing_address.address_2 && (
                      <Typography variant="body2" color="text.secondary">
                        {order.billing_address.address_2}
                      </Typography>
                    )}
                    <Typography variant="body2" color="text.secondary">
                      {order.billing_address.city}, {order.billing_address.province} {order.billing_address.postal_code}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {order.billing_address.country_code}
                    </Typography>
                    {order.billing_address.phone && (
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                        {order.billing_address.phone}
                      </Typography>
                    )}
                  </Stack>
                </CardContent>
              </Card>
            )}
        </Grid>
      </Grid>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleUpdateStatus}>
          <Edit sx={{ mr: 1 }} />
          Update Status
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <LocalShipping sx={{ mr: 1 }} />
          Create Fulfillment
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <Payment sx={{ mr: 1 }} />
          Process Refund
        </MenuItem>
      </Menu>

      {/* Update Status Dialog */}
      <Dialog open={updateStatusDialog} onClose={() => setUpdateStatusDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Update Order Status</DialogTitle>
        <DialogContent>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={newStatus}
              onChange={(e) => setNewStatus(e.target.value)}
              label="Status"
            >
              <MenuItem value="pending">Pending</MenuItem>
              <MenuItem value="completed">Completed</MenuItem>
              <MenuItem value="archived">Archived</MenuItem>
              <MenuItem value="canceled">Cancelled</MenuItem>
              <MenuItem value="requires_action">Requires Action</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUpdateStatusDialog(false)}>Cancel</Button>
          <Button 
            onClick={handleStatusUpdate} 
            variant="contained"
            disabled={isUpdating || !newStatus}
          >
            {isUpdating ? 'Updating...' : 'Update Status'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};