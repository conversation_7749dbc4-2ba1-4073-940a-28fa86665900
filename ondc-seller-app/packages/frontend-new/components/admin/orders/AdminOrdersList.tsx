'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Stack,
  Alert,
  CircularProgress,
  InputAdornment,
} from '@mui/material';
import {
  Search,
  Visibility,
  ShoppingCart,
  LocalShipping,
  CheckCircle,
  Refresh,
} from '@mui/icons-material';
import { AdminTableLoading } from '../AdminLoading';
import { useAuthStore } from '@/stores/authStore';
import { ordersAPI, Order } from '@/lib/api/medusa/orders';
import medusaAdminService from '@/lib/api/medusa-admin';
import { format } from 'date-fns';

export const AdminOrdersList: React.FC = () => {
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  const { getStoreHandle } = useAuthStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const [isLoading, setIsLoading] = useState(true);
  const [orders, setOrders] = useState<Order[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  
  // Get dynamic store handle from auth store
  const dynamicStoreHandle = getStoreHandle() || storeHandle;

  // Fetch orders from API
  useEffect(() => {
    const fetchOrders = async () => {
      if (!dynamicStoreHandle) {
        console.log('No store handle available for orders API call');
        setIsLoading(false);
        return;
      }
      
      setIsLoading(true);
      setError(null);
      
      try {
        console.log('=== FETCHING ORDERS ===');
        console.log('Using store handle as x-tenant-id:', dynamicStoreHandle);
        
        const response = await medusaAdminService.getOrders(dynamicStoreHandle);
        
        console.log('Orders fetched successfully:', response);
        
        // Handle different possible response structures
        let ordersArray: Order[] = [];
        let totalCount = 0;

        if (response.orders && Array.isArray(response.orders)) {
          ordersArray = response.orders;
          totalCount = response.orders.length;
        } else if (Array.isArray(response)) {
          ordersArray = response;
          totalCount = response.length;
        } else {
          console.warn('Unexpected response format:', response);
          ordersArray = [];
          totalCount = 0;
        }

        setOrders(ordersArray);
        setTotalCount(totalCount);
      } catch (error: any) {
        console.error('Failed to fetch orders:', error);
        setError(error.message || 'Failed to fetch orders');
        setOrders([]);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchOrders();
  }, [dynamicStoreHandle, searchTerm, statusFilter]);

  // Helper function to safely get order field values
  const getOrderField = (order: any, field: string) => {
    const fieldMappings: { [key: string]: string[] } = {
      orderNumber: ['orderNumber', 'order_number', 'number'],
      customerName: ['customerName', 'customer_name', 'customer'],
      customerEmail: ['customerEmail', 'customer_email', 'email'],
      totalAmount: ['totalAmount', 'total_amount', 'total'],
      status: ['status', 'order_status', 'state'],
      paymentStatus: ['paymentStatus', 'payment_status'],
      itemsCount: ['itemsCount', 'items_count', 'items'],
      createdAt: ['createdAt', 'created_at', 'dateCreated', 'created'],
      currency: ['currency', 'order_currency']
    };
    
    const possibleFields = fieldMappings[field] || [field];
    
    for (const possibleField of possibleFields) {
      if (order[possibleField] !== undefined && order[possibleField] !== null) {
        return order[possibleField];
      }
    }
    
    return null;
  };

  // Helper function to safely extract customer information
  const getCustomerInfo = (order: any) => {
    // Try multiple possible customer object locations
    const customer = order.customer || order.customer_id || order.billing_address || order.shipping_address;
    
    if (customer && typeof customer === 'object') {
      // Extract name from customer object
      const firstName = customer.first_name || customer.firstName || '';
      const lastName = customer.last_name || customer.lastName || '';
      const email = customer.email || customer.customer_email || '';
      
      const name = `${firstName} ${lastName}`.trim() || email || 'Unknown Customer';
      
      return {
        name,
        email: email || customer.email || ''
      };
    }
    
    // Try to get customer email directly from order
    const directEmail = order.email || order.customer_email || order.billing_email;
    
    // Fallback to direct fields
    const customerName = getOrderField(order, 'customerName');
    const customerEmail = getOrderField(order, 'customerEmail') || directEmail;
    
    if (customerName && typeof customerName === 'object') {
      // If customerName is an object, extract the name
      const firstName = customerName.first_name || customerName.firstName || '';
      const lastName = customerName.last_name || customerName.lastName || '';
      const email = customerName.email || '';
      
      return {
        name: `${firstName} ${lastName}`.trim() || email || 'Unknown Customer',
        email: email
      };
    }
    
    // Handle case where customerName might be a string
    const finalName = typeof customerName === 'string' ? customerName : 'Unknown Customer';
    
    return {
      name: finalName,
      email: customerEmail || ''
    };
  };

  const formatPrice = (price: number | string, currency: string = 'INR') => {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    if (isNaN(numPrice)) return 'N/A';
    
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency,
    }).format(numPrice);
  };

  const getStatusChipColor = (status: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    const statusConfig = {
      pending: 'warning' as const,
      confirmed: 'info' as const,
      processing: 'primary' as const,
      shipped: 'secondary' as const,
      // delivered: 'success' as const,
      completed: 'success' as const,
      cancelled: 'error' as const,
      refunded: 'default' as const,
    };
    return statusConfig[status as keyof typeof statusConfig] || 'warning';
  };

  const getPaymentStatusChipColor = (status: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    const statusConfig = {
      pending: 'warning' as const,
      paid: 'success' as const,
      failed: 'error' as const,
      refunded: 'default' as const,
    };
    return statusConfig[status as keyof typeof statusConfig] || 'warning';
  };

  return (
    <Stack spacing={3}>
      {/* Page Header */}
      <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 0.5 }}>
            Orders
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage customer orders and fulfillment
          </Typography>
        </Box>
        <Stack direction="row" spacing={2}>
          <Button variant="outlined">
            Export
          </Button>
          <Button variant="contained">
            Bulk Actions
          </Button>
        </Stack>
      </Stack>

      {/* Stats Cards */}
      <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: 'repeat(4, 1fr)' }, gap: 3 }}>
        <Card>
          <CardContent>
            <Stack direction="row" spacing={2} alignItems="center">
              <Box sx={{
                p: 1,
                bgcolor: 'primary.100',
                borderRadius: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <ShoppingCart sx={{ fontSize: 24, color: 'primary.main' }} />
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600 }}>
                  Total Orders
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                  {totalCount || orders.length}
                </Typography>
              </Box>
            </Stack>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <Stack direction="row" spacing={2} alignItems="center">
              <Box sx={{
                p: 1,
                bgcolor: 'warning.100',
                borderRadius: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <CircularProgress size={24} sx={{ color: 'warning.main' }} />
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600 }}>
                  Pending
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                  {orders.filter(order => getOrderField(order, 'status') === 'pending').length}
                </Typography>
              </Box>
            </Stack>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <Stack direction="row" spacing={2} alignItems="center">
              <Box sx={{
                p: 1,
                bgcolor: 'success.100',
                borderRadius: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <CheckCircle sx={{ fontSize: 24, color: 'success.main' }} />
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600 }}>
                  Completed
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                  {orders.filter(order => getOrderField(order, 'status') === 'delivered').length}
                </Typography>
              </Box>
            </Stack>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <Stack direction="row" spacing={2} alignItems="center">
              <Box sx={{
                p: 1,
                bgcolor: 'secondary.100',
                borderRadius: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <LocalShipping sx={{ fontSize: 24, color: 'secondary.main' }} />
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600 }}>
                  Revenue
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                  {formatPrice(orders.reduce((sum, order) => sum + (getOrderField(order, 'totalAmount') || 0), 0))}
                </Typography>
              </Box>
            </Stack>
          </CardContent>
        </Card>
      </Box>

      {/* Filters and Search */}
      <Card>
        <CardContent>
          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3} alignItems={{ sm: 'center' }} justifyContent="space-between">
            <Stack direction="row" spacing={2} alignItems="center">
              <TextField
                placeholder="Search orders..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                size="small"
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                  }
                }}
                sx={{ minWidth: 250 }}
              />

              <FormControl size="small" sx={{ minWidth: 150 }}>
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  label="Status"
                >
                  <MenuItem value="all">All Status</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="confirmed">Confirmed</MenuItem>
                  <MenuItem value="processing">Processing</MenuItem>
                  <MenuItem value="shipped">Shipped</MenuItem>
                  <MenuItem value="delivered">Delivered</MenuItem>
                  <MenuItem value="cancelled">Cancelled</MenuItem>
                </Select>
              </FormControl>
            </Stack>

            <Typography variant="body2" color="text.secondary">
              {totalCount > 0 ? `${totalCount} total orders` : `${orders.length} orders`}
            </Typography>
          </Stack>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert
          severity="error"
          action={
            <Button
              color="inherit"
              size="small"
              onClick={() => window.location.reload()}
              startIcon={<Refresh />}
            >
              Retry
            </Button>
          }
        >
          <strong>Error loading orders:</strong> {error}
        </Alert>
      )}

      {/* Orders Table */}
      {isLoading ? (
        <AdminTableLoading />
      ) : error ? null : orders.length === 0 ? (
        /* No Orders Message */
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 8 }}>
            <Box sx={{
              mx: 'auto',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 64,
              height: 64,
              borderRadius: '50%',
              bgcolor: 'grey.100',
              mb: 2
            }}>
              <ShoppingCart sx={{ fontSize: 32, color: 'text.secondary' }} />
            </Box>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
              {searchTerm || statusFilter !== 'all' ? 'No orders found' : 'No orders yet'}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              {searchTerm || statusFilter !== 'all'
                ? 'Try adjusting your search criteria or filters to find orders.'
                : 'Orders will appear here once customers start placing them.'
              }
            </Typography>
            {(searchTerm || statusFilter !== 'all') && (
              <Button
                variant="outlined"
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('all');
                }}
              >
                Clear Filters
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                    Order
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                    Customer
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                    Total
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                    Status
                  </Typography>
                </TableCell>
                {/* <TableCell>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                    Payment
                  </Typography>
                </TableCell> */}
                <TableCell>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                    Items
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                    Date
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                    Actions
                  </Typography>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {orders.map((order) => {
                const orderId = getOrderField(order, 'id') || order.id;
                const orderNumber = getOrderField(order, 'orderNumber') || `#order_${order.display_id}`;
                const customerInfo = getCustomerInfo(order);
                const totalAmount = getOrderField(order, 'totalAmount') || 0;
                const orderStatus = getOrderField(order, 'status') || 'pending';
                const paymentStatus = getOrderField(order, 'paymentStatus') || 'pending';
                const itemsCount = getOrderField(order, 'itemsCount') || 0;
                const createdAt = getOrderField(order, 'createdAt');
                const currency = getOrderField(order, 'currency') || 'INR';
                
                return (
                  <TableRow key={orderId} hover>
                    <TableCell>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {String(orderNumber)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          {String(customerInfo.name)}
                        </Typography>
                        {customerInfo.email && (
                          <Typography variant="body2" color="text.secondary">
                            {String(customerInfo.email)}
                          </Typography>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatPrice(totalAmount, currency)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={String(orderStatus).charAt(0).toUpperCase() + String(orderStatus).slice(1)}
                        color={getStatusChipColor(orderStatus)}
                        size="small"
                      />
                    </TableCell>
                    {/* <TableCell>
                      <Chip
                        label={String(paymentStatus).charAt(0).toUpperCase() + String(paymentStatus).slice(1)}
                        color={getPaymentStatusChipColor(paymentStatus)}
                        size="small"
                      />
                    </TableCell> */}
                    <TableCell>
                      <Typography variant="body2">
                        {order.items.length || 0}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {createdAt ? format(new Date(createdAt), 'dd-MMM-yyyy') : 'Date not available'}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Stack direction="row" spacing={1} justifyContent="flex-end">
                        <IconButton
                          component={Link}
                          href={`/${dynamicStoreHandle}/admin/orders/${orderId}`}
                          size="small"
                          color="primary"
                        >
                          <Visibility />
                        </IconButton>
                      </Stack>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Stack>
  );
};