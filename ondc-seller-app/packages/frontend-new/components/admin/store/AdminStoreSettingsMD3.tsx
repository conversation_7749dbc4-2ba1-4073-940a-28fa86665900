'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useDropzone } from 'react-dropzone';
import { useToast } from '@/app/providers/toast-provider';
import strapiStoreService, { NormalizedStore } from '@/lib/api/strapi-store';
import { uploadImageToCloudinary } from '@/lib/utils/colorPalette';
import { useStoreThemeWithAutoLoad } from '@/hooks/useStoreThemeWithAutoLoad';
import { AutoTheme } from '@/lib/utils/simpleTheme';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  TextField,
  Button,
  Grid,
  Avatar,
  Divider,
  Paper,
  InputAdornment,
  Chip,
  Stack,
  IconButton,
  Fab,
  Container,
  FormControl,
  InputLabel,
  OutlinedInput,
  FormHelperText,
  CardActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tooltip,
  LinearProgress,
  FormControlLabel,
  Checkbox,
  FormGroup,
  Tabs,
  Tab,
  Alert,
  Breadcrumbs,
  Link as MuiLink,
} from '@mui/material';
import {
  Store,
  Email,
  Phone,
  Language,
  LocationOn,
  Business,
  Save,
  Cancel,
  PhotoCamera,
  Public,
  Edit,
  Preview,
  ExpandMore,
  Info,
  CheckCircle,
  Warning,
  Error as ErrorIcon,
  Refresh,
  Upload,
  Delete,
  Visibility,
  Settings,
  Security,
  Palette,
  Notifications,
  Payment,
  CreditCard,
  AccountBalance,
  Wallet,
  LocalShipping,
  NavigateNext as NavigateNextIcon,
} from '@mui/icons-material';

interface StoreSettings {
  store_name: string;
  store_logo: string;
  store_description: string;
  store_handle: string;
  store_gst_number: string;
  store_email: string;
  store_address: string;
  store_address_line2: string;
  store_city: string;
  store_state: string;
  store_pincode: string;
  store_phone: string;
  store_website: string;
  // Additional fields that might be missing
  store_country?: string;
  store_currency?: string;
  store_timezone?: string;
  store_language?: string;
  tax_inclusive?: boolean;
  shipping_enabled?: boolean;
  // Payment methods
  payment_upi?: boolean;
  payment_credit_debit_card?: boolean;
  payment_net_banking?: boolean;
  payment_wallet?: boolean;
  payment_cash_on_delivery?: boolean;
  payment_stripe?: boolean;
  payment_paypal?: boolean;
  payment_paytm?: boolean;
  // Auto theme (simplified)
  auto_theme?: AutoTheme | null;
}

const emptySettings: StoreSettings = {
  store_name: '',
  store_logo: '',
  store_description: '',
  store_handle: '',
  store_gst_number: '',
  store_email: '',
  store_address: '',
  store_address_line2: '',
  store_city: '',
  store_state: '',
  store_pincode: '',
  store_phone: '',
  store_website: '',
  store_country: 'India',
  store_currency: 'INR',
  store_timezone: 'Asia/Kolkata',
  store_language: 'en',
  tax_inclusive: true,
  shipping_enabled: true,
  // Payment methods - default to false
  payment_upi: false,
  payment_credit_debit_card: false,
  payment_net_banking: false,
  payment_wallet: false,
  payment_cash_on_delivery: false,
  payment_stripe: false,
  payment_paypal: false,
  payment_paytm: false,
  // Auto theme
  auto_theme: null,
};

export const AdminStoreSettingsMD3: React.FC = () => {
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  const { showToast } = useToast();
  const [settings, setSettings] = useState<StoreSettings>(emptySettings);
  const [originalSettings, setOriginalSettings] = useState<StoreSettings>(emptySettings);
  const [isLoading, setSaving] = useState(false);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [expandedSection, setExpandedSection] = useState<string>('basic');
  const [isLoadingSettings, setIsLoadingSettings] = useState(true);
  const [hasExistingData, setHasExistingData] = useState(false);
  const [storeId, setStoreId] = useState<string | null>(null);
  const [documentId, setDocumentId] = useState<string | null>(null);
  const [hasLoadedOnce, setHasLoadedOnce] = useState(false);
  // Use simplified theme hook with auto-loading
  const { 
    currentTheme, 
    isGenerating: isGeneratingTheme, 
    generateThemeFromLogo, 
    accessibility,
    autoLoader,
    isAnyLoading: isThemeLoading
  } = useStoreThemeWithAutoLoad(storeHandle);
  const [logoUploadMode, setLogoUploadMode] = useState<'file' | 'url'>('file');
  const [logoUrl, setLogoUrl] = useState<string>('');
  const [isUploadingLogo, setIsUploadingLogo] = useState(false);
  // Remove color extraction state - now handled by theme hook
  const [logoUploadError, setLogoUploadError] = useState<string | null>(null);

  // Helper function to convert raw Strapi response to form settings
  const convertStrapiResponseToSettings = useCallback((rawStrapiData: any): StoreSettings => {
    console.log('Converting raw Strapi data to form settings:', rawStrapiData);
    
    return {
      store_name: rawStrapiData.store_name || '',
      store_logo: rawStrapiData.store_logo_url || '',
      store_description: rawStrapiData.store_description || '',
      store_handle: rawStrapiData.store_handle || '',
      store_gst_number: rawStrapiData.gst_number || '',
      store_email: rawStrapiData.email || rawStrapiData.store_email || '',
      store_address: rawStrapiData.address_line_1 || '',
      store_address_line2: rawStrapiData.address_line_2 || '',
      store_city: rawStrapiData.city || '',
      store_state: rawStrapiData.state || '',
      store_pincode: rawStrapiData.pincode || '',
      store_phone: rawStrapiData.phone || '',
      store_website: rawStrapiData.website || '',
      store_country: rawStrapiData.country || 'India',
      store_currency: 'INR', // Static for now
      store_timezone: 'Asia/Kolkata', // Static for now
      store_language: 'en', // Static for now
      tax_inclusive: true, // Static for now
      shipping_enabled: true, // Static for now
      // Payment methods from Strapi
      payment_upi: rawStrapiData.payment_methods?.upi ?? false,
      payment_credit_debit_card: rawStrapiData.payment_methods?.credit_card ?? rawStrapiData.payment_methods?.debit_card ?? false,
      payment_net_banking: rawStrapiData.payment_methods?.net_banking ?? false,
      payment_wallet: rawStrapiData.payment_methods?.wallet ?? false,
      payment_cash_on_delivery: rawStrapiData.payment_methods?.cash_on_delivery ?? false,
      payment_stripe: false, // Not in current payload
      payment_paypal: false, // Not in current payload
      payment_paytm: false, // Not in current payload
      // Auto theme from Strapi
      auto_theme: rawStrapiData.auto_theme || null,
    };
  }, []);

  // Helper function to convert Strapi store data to form settings (for backward compatibility)
  const convertStrapiToSettings = useCallback((strapiStore: NormalizedStore): StoreSettings => {
    return {
      store_name: strapiStore.name || '',
      store_logo: strapiStore.logo || '',
      store_description: strapiStore.description || '',
      store_handle: strapiStore.handle || '',
      store_gst_number: '', // This might need to be added to Strapi schema
      store_email: strapiStore.contact?.email || '',
      store_address: strapiStore.address?.street || '',
      store_address_line2: '', // This might need to be added to Strapi schema
      store_city: strapiStore.address?.city || '',
      store_state: strapiStore.address?.state || '',
      store_pincode: strapiStore.address?.postal_code || '',
      store_phone: strapiStore.contact?.phone || '',
      store_website: strapiStore.contact?.website || '',
      store_country: strapiStore.address?.country || 'India',
      store_currency: strapiStore.settings?.currency || 'INR',
      store_timezone: strapiStore.settings?.timezone || 'Asia/Kolkata',
      store_language: strapiStore.settings?.language || 'en',
      tax_inclusive: strapiStore.settings?.tax_inclusive ?? true,
      shipping_enabled: strapiStore.settings?.shipping_enabled ?? true,
      // Payment methods from Strapi
      payment_upi: strapiStore.settings?.payment_methods?.upi ?? false,
      payment_credit_debit_card: strapiStore.settings?.payment_methods?.credit_debit_card ?? false,
      payment_net_banking: strapiStore.settings?.payment_methods?.net_banking ?? false,
      payment_wallet: strapiStore.settings?.payment_methods?.wallet ?? false,
      payment_cash_on_delivery: strapiStore.settings?.payment_methods?.cash_on_delivery ?? false,
      payment_stripe: strapiStore.settings?.payment_methods?.stripe ?? false,
      payment_paypal: strapiStore.settings?.payment_methods?.paypal ?? false,
      payment_paytm: strapiStore.settings?.payment_methods?.paytm ?? false,
      // Auto theme from Strapi
      auto_theme: strapiStore.theme || null,
    };
  }, []);

  // Load existing store settings on component mount
  useEffect(() => {
    const loadStoreSettings = async () => {
      if (!storeHandle) {
        console.log('No store handle available');
        setIsLoadingSettings(false);
        return;
      }

      // Prevent multiple API calls
      if (hasLoadedOnce) {
        console.log('Store settings already loaded, skipping API call');
        return;
      }

      try {
        console.log('=== LOADING STORE SETTINGS FROM STRAPI ===');
        console.log('Store handle:', storeHandle);
        console.log('API URL:', `{strapi_url}/api/store-configurations?filters[store_handle][$eq]=${storeHandle}&populate=*`);
        
        // Make API call to Strapi to get store details
        const result = await strapiStoreService.getStoreConfigByHandle(storeHandle);
        console.log('Store configuration api triggered:', result);
        
        if (result && result.normalizedData) {
          console.log('Store configuration found:', result.normalizedData);
          console.log('Raw response data:', result.rawResponse.data[0]);
          
          // Convert raw Strapi data to form settings for better field mapping
          const rawData = result.rawResponse.data[0];
          const formSettings = convertStrapiResponseToSettings(rawData);
          
          // Set the form data
          setSettings(formSettings);
          setOriginalSettings(formSettings);
          setHasExistingData(true);
          setStoreId(result.normalizedData.id);
          
          // Store the documentId from the raw response for updates
          if (rawData.documentId) {
            setDocumentId(rawData.documentId);
          } else if (rawData.id) {
            setDocumentId(rawData.id.toString());
          }
          
          // Set logo preview and color palette if available
          const logoUrl = formSettings.store_logo;
          if (logoUrl) {
            setLogoPreview(logoUrl);
            setLogoUrl(logoUrl);
          }
          
          // Auto-generate theme if logo exists (only if not already auto-applied)
          if (formSettings.store_logo && !autoLoader.hasAutoApplied) {
            generateThemeFromLogo(formSettings.store_logo).catch(console.error);
          }
          
          console.log('Store settings loaded and form auto-filled:', formSettings);
          console.log('Document ID set:', rawData.documentId || rawData.id);
          setHasLoadedOnce(true);
          showToast('Store settings loaded successfully!', 'success');
        } else {
          console.log('No store configuration found for handle:', storeHandle);
          console.log('Starting with empty form for new store creation');
          
          // Set store handle in empty form
          const newSettings = { ...emptySettings, store_handle: storeHandle };
          setSettings(newSettings);
          setOriginalSettings(newSettings);
          setHasExistingData(false);
          
          setHasLoadedOnce(true);
          showToast('No existing store configuration found. You can create a new one.', 'info');
        }
      } catch (error: any) {
        console.error('Error loading store settings from Strapi:', error);
        
        // Set store handle in empty form even on error
        const newSettings = { ...emptySettings, store_handle: storeHandle };
        setSettings(newSettings);
        setOriginalSettings(newSettings);
        setHasExistingData(false);
        
        // Show appropriate error message
        setHasLoadedOnce(true);
        if (error.response?.status === 404) {
          showToast('No store configuration found. You can create a new one.', 'info');
        } else {
          showToast('Failed to load store settings. Please try again.', 'error');
        }
      } finally {
        setIsLoadingSettings(false);
      }
    };

    loadStoreSettings();
  }, [storeHandle]); // Removed showToast from dependencies to prevent infinite loop

  const handleInputChange = (field: keyof StoreSettings, value: string | boolean) => {
    setSettings(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handlePaymentMethodChange = (field: keyof StoreSettings, checked: boolean) => {
    setSettings(prev => ({
      ...prev,
      [field]: checked,
    }));
  };

  // React Dropzone for logo upload
  const onLogoDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    setIsUploadingLogo(true);
    setLogoUploadError(null);

    try {
      console.log('=== UPLOADING LOGO ===');
      console.log('File:', file.name, file.size, file.type);
      
      // Upload image
      const uploadedUrl = await uploadImageToCloudinary(file);
      if (!uploadedUrl) {
        throw new Error('Failed to upload image');
      }

      setLogoUrl(uploadedUrl);
      setLogoPreview(uploadedUrl);
      setSettings(prev => ({ ...prev, store_logo: uploadedUrl }));
      // Auto-generate theme from logo
      console.log('=== AUTO-GENERATING THEME FROM LOGO ===');
      const generatedTheme = await generateThemeFromLogo(uploadedUrl);
      
      if (generatedTheme) {
        console.log('Theme auto-generated:', generatedTheme);
        setSettings(prev => ({ ...prev, auto_theme: generatedTheme }));
        showToast('Logo uploaded and theme generated successfully!', 'success');
      } else {
        showToast('Logo uploaded successfully!', 'success');
      }
      
    } catch (error: any) {
      console.error('Error uploading logo:', error);
      setLogoUploadError(error.message || 'Failed to upload logo');
      showToast('Failed to upload logo. Please try again.', 'error');
    } finally {
      setIsUploadingLogo(false);
    }
  }, [showToast]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: onLogoDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']
    },
    multiple: false,
    maxSize: 5 * 1024 * 1024, // 5MB
  });

  const handleLogoUrlSubmit = async () => {
    if (!logoUrl.trim()) return;

    // Auto-generate theme from URL
    setLogoUploadError(null);

    try {
      console.log('=== PROCESSING LOGO URL ===');
      console.log('Logo URL:', logoUrl);
      
      setLogoPreview(logoUrl);
      setSettings(prev => ({ ...prev, store_logo: logoUrl }));
      
      // Auto-generate theme from URL
      console.log('=== AUTO-GENERATING THEME FROM URL ===');
      const generatedTheme = await generateThemeFromLogo(logoUrl);
      
      if (generatedTheme) {
        console.log('Theme auto-generated from URL:', generatedTheme);
        setSettings(prev => ({ ...prev, auto_theme: generatedTheme }));
        showToast('Logo URL processed and theme generated successfully!', 'success');
      } else {
        showToast('Logo URL processed successfully!', 'success');
      }
    } catch (error: any) {
      console.error('Error processing logo URL:', error);
      setLogoUploadError(error.message || 'Failed to process logo URL');
      showToast('Failed to process logo URL. Please try again.', 'error');
    } finally {
      // Theme generation handled by hook
    }
  };

  const handleRemoveLogo = () => {
    setLogoUrl('');
    setLogoPreview(null);
    setSettings(prev => ({ 
      ...prev, 
      store_logo: '',
      auto_theme: null 
    }));
    showToast('Logo removed successfully!', 'info');
  };

  // Helper function to convert form settings to the required Strapi payload format
  const convertSettingsToStrapiPayload = useCallback((formSettings: StoreSettings) => {
    // Get user ID from auth or use a default
    const userId = 'user_01K3GMMQ7MVXJS5C4Q5MSP7HDM'; // This should come from auth context
    const createdByUser = 'Mark Doe'; // This should come from auth context
    
    return {
      store_description: formSettings.store_description || '',
      gst_number: formSettings.store_gst_number || '',
      address_line_1: formSettings.store_address || '',
      address_line_2: formSettings.store_address_line2 || '',
      city: formSettings.store_city || '',
      state: formSettings.store_state || '',
      pincode: formSettings.store_pincode || '',
      country: formSettings.store_country || 'India',
      phone: formSettings.store_phone || '',
      email: formSettings.store_email || '',
      website: formSettings.store_website || null,
      business_type: 'individual', // This could be made dynamic
      business_category: 'fashion', // This could be made dynamic
      onboarding_completed: false,
      onboarding_step: 1,
      user_id: userId,
      medusa_store_id: null,
      store_settings: null,
      social_media: null,
      operating_hours: null,
      shipping_zones: null,
      payment_methods: {
        upi: formSettings.payment_upi ?? false,
        bnpl: false, // Not currently in form
        wallet: formSettings.payment_wallet ?? false,
        debit_card: formSettings.payment_credit_debit_card ?? false,
        credit_card: formSettings.payment_credit_debit_card ?? false,
        net_banking: formSettings.payment_net_banking ?? false,
        cash_on_delivery: formSettings.payment_cash_on_delivery ?? false,
      },
      tax_settings: null,
      notification_preferences: null,
      store_status: 'active',
      created_by_user: createdByUser,
      last_updated_by: null,
      store_logo_url: formSettings.store_logo || '',
      store_email: formSettings.store_email || '',
      auto_theme: formSettings.auto_theme || null,
    };
  }, []);

  const handleSave = async () => {
    setSaving(true);
    try {
      const strapiPayload = convertSettingsToStrapiPayload(settings);
      
      console.log('=== SAVING STORE SETTINGS ===');
      console.log('Payload to send:', strapiPayload);
      console.log('Has existing data:', hasExistingData);
      console.log('Document ID:', documentId);
      console.log('Store ID:', storeId);
      
      if (hasExistingData && documentId) {
        // Store data exists - make UPDATE API call with documentId
        console.log('=== UPDATING EXISTING STORE SETTINGS ===');
        console.log('Using document ID for update:', documentId);
        
        const response = await strapiStoreService.updateStoreConfigWithPayload(documentId, strapiPayload);
        console.log('Store settings updated successfully:', response);
        
        // Update the form with the response data
        if (response.data) {
          const updatedFormSettings = convertStrapiResponseToSettings(response.data);
          setSettings(updatedFormSettings);
          setOriginalSettings(updatedFormSettings);
          
          // Update logo and auto-generate theme
          if (updatedFormSettings.store_logo) {
            setLogoPreview(updatedFormSettings.store_logo);
            setLogoUrl(updatedFormSettings.store_logo);
            // Auto-generate theme from updated logo
            generateThemeFromLogo(updatedFormSettings.store_logo).catch(console.error);
          }
          
          console.log('Form updated with response data:', updatedFormSettings);
        }
        
        showToast('Store settings updated successfully!', 'success');
      } else {
        // No store data exists - make CREATE API call
        console.log('=== CREATING NEW STORE SETTINGS ===');
        
        const response = await strapiStoreService.createStoreConfigWithPayload(strapiPayload);
        console.log('Store settings created successfully:', response);
        
        // After successful creation, mark as having existing data and store the documentId
        setHasExistingData(true);
        if (response.data?.documentId) {
          setDocumentId(response.data.documentId);
        } else if (response.data?.id) {
          setDocumentId(response.data.id.toString());
        }
        
        // Update the form with the response data
        if (response.data) {
          const createdFormSettings = convertStrapiResponseToSettings(response.data);
          setSettings(createdFormSettings);
          setOriginalSettings(createdFormSettings);
          
          // Update logo and auto-generate theme
          if (createdFormSettings.store_logo) {
            setLogoPreview(createdFormSettings.store_logo);
            setLogoUrl(createdFormSettings.store_logo);
            // Auto-generate theme from created logo
            generateThemeFromLogo(createdFormSettings.store_logo).catch(console.error);
          }
          
          console.log('Form updated with created data:', createdFormSettings);
        }
        
        showToast('Store settings created successfully!', 'success');
      }
    } catch (error: any) {
      console.error('Error saving settings to Strapi:', error);
      
      let errorMessage = 'Failed to save settings. Please try again.';
      if (error.response?.data?.error?.message) {
        errorMessage = error.response.data.error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      showToast(errorMessage, 'error');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    if (hasExistingData) {
      // Reset to the originally loaded data
      setSettings(originalSettings);
      setLogoPreview(originalSettings.store_logo || null);
      setLogoUrl(originalSettings.store_logo || '');
      // Auto-apply theme if logo exists
      if (originalSettings.store_logo) {
        generateThemeFromLogo(originalSettings.store_logo).catch(console.error);
      }
    } else {
      // Reset to empty form but keep store handle
      const resetSettings = { ...emptySettings, store_handle: storeHandle };
      setSettings(resetSettings);
      setLogoPreview(null);
      setLogoUrl('');
      // Theme will be cleared by the hook
    }
  };

  const handleAccordionChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedSection(isExpanded ? panel : '');
  };

  // Show loading state while fetching settings
  if (isLoadingSettings) {
    return (
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <Stack alignItems="center" spacing={2}>
            <LinearProgress sx={{ width: 200 }} />
            <Typography variant="h6" color="text.secondary">
              Loading store settings...
            </Typography>
          </Stack>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Breadcrumbs */}
    

      {/* Page Header */}
      <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={4}>
        <Box>
          <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 1 }}>
            <Typography variant="h4" component="h1" gutterBottom fontWeight={600}>
              Store Settings
            </Typography>
            <Chip 
              label={hasExistingData ? 'Edit Mode' : 'Create Mode'} 
              color={hasExistingData ? 'info' : 'success'} 
              size="small"
              variant="outlined"
            />
          </Stack>
          <Typography variant="body1" color="text.secondary">
            {hasExistingData 
              ? 'Update your store information and configuration' 
              : 'Create your store information and configuration'
            }
          </Typography>
        </Box>
        
        <Stack direction="row" spacing={2}>
          <Tooltip title="Refresh">
            <IconButton onClick={handleReset} color="primary">
              <Refresh />
            </IconButton>
          </Tooltip>
          
          <Button
            variant="contained"
            startIcon={isLoading ? <LinearProgress size={20} /> : <Save />}
            onClick={handleSave}
            disabled={isLoading}
            size="large"
            color="primary"
            sx={{ minWidth: 160 }}
          >
            {isLoading 
              ? (hasExistingData ? 'Updating...' : 'Creating...') 
              : (hasExistingData ? 'Update Store' : 'Create Store')
            }
          </Button>
        </Stack>
      </Box>

      <Grid container spacing={4}>
        {/* Main Settings */}
        <Grid item xs={12} lg={8}>
          <Stack spacing={3}>
            {/* Basic Information */}
            <Accordion 
              expanded={expandedSection === 'basic'} 
              onChange={handleAccordionChange('basic')}
              elevation={2}
            >
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Stack direction="row" alignItems="center" spacing={2}>
                  <Avatar sx={{ bgcolor: 'primary.main' }}>
                    <Store />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" fontWeight="bold">
                      Basic Information
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Update your store's basic details
                    </Typography>
                  </Box>
                </Stack>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item size={{ xs: 12, md: 4 }} >
                    <FormControl fullWidth variant="outlined">
                      <InputLabel>Store Name *</InputLabel>
                      <OutlinedInput
                        value={settings.store_name}
                        onChange={(e) => handleInputChange('store_name', e.target.value)}
                        startAdornment={
                          <InputAdornment position="start">
                            <Store color="action" />
                          </InputAdornment>
                        }
                        label="Store Name *"
                        placeholder="Enter store name"
                      />
                      <FormHelperText>This will be displayed as your store title</FormHelperText>
                    </FormControl>
                  </Grid>

                  <Grid item size={{ xs: 12, md: 4 }}>
                    <FormControl fullWidth variant="outlined">
                      <InputLabel>Store Handle *</InputLabel>
                      <OutlinedInput
                        value={settings.store_handle}
                        onChange={(e) => handleInputChange('store_handle', e.target.value)}
                        startAdornment={
                          <InputAdornment position="start">
                            <Typography variant="body2" color="text.secondary">
                              mystore.com/
                            </Typography>
                          </InputAdornment>
                        }
                        label="Store Handle *"
                        placeholder="store-handle"
                      />
                      <FormHelperText>URL-friendly identifier for your store</FormHelperText>
                    </FormControl>
                  </Grid>

                  <Grid item size={{ xs: 12, md: 4 }}>
                    <TextField
                      fullWidth
                      multiline
                      rows={4}
                      label="Store Description"
                      value={settings.store_description}
                      onChange={(e) => handleInputChange('store_description', e.target.value)}
                      placeholder="Describe your store..."
                      helperText="Tell customers what makes your store special"
                      variant="outlined"
                    />
                  </Grid>

                  <Grid item size={{ xs: 12, md: 4 }}>
                    <TextField
                      fullWidth
                      type="email"
                      label="Store Email *"
                      value={settings.store_email}
                      onChange={(e) => handleInputChange('store_email', e.target.value)}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Email color="action" />
                          </InputAdornment>
                        ),
                      }}
                      placeholder="<EMAIL>"
                      helperText="Primary contact email for your store"
                      variant="outlined"
                    />
                  </Grid>

                  <Grid item size={{ xs: 12, md: 4 }}>
                    <TextField
                      fullWidth
                      type="tel"
                      label="Store Phone"
                      value={settings.store_phone}
                      onChange={(e) => handleInputChange('store_phone', e.target.value)}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Phone color="action" />
                          </InputAdornment>
                        ),
                      }}
                      placeholder="+91 98765 43210"
                      helperText="Customer support phone number"
                      variant="outlined"
                    />
                  </Grid>

                  <Grid item size={{ xs: 12, md: 4 }}>
                    <TextField
                      fullWidth
                      type="url"
                      label="Website URL"
                      value={settings.store_website}
                      onChange={(e) => handleInputChange('store_website', e.target.value)}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Public color="action" />
                          </InputAdornment>
                        ),
                      }}
                      placeholder="https://yourstore.com"
                      helperText="Your store's website or social media link"
                      variant="outlined"
                    />
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>

            {/* Address Information */}
            <Accordion 
              expanded={expandedSection === 'address'} 
              onChange={handleAccordionChange('address')}
              elevation={2}
            >
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Stack direction="row" alignItems="center" spacing={2}>
                  <Avatar sx={{ bgcolor: 'secondary.main' }}>
                    <LocationOn />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" fontWeight="bold">
                      Address Information
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Store address and location details
                    </Typography>
                  </Box>
                </Stack>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item size={{ xs: 12, md: 4 }}>
                    <TextField
                      fullWidth
                      label="Address Line 1 *"
                      value={settings.store_address}
                      onChange={(e) => handleInputChange('store_address', e.target.value)}
                      placeholder="Street address"
                      helperText="Primary street address"
                      variant="outlined"
                    />
                  </Grid>

                  <Grid item size={{ xs: 12, md: 4 }}>
                    <TextField
                      fullWidth
                      label="Address Line 2"
                      value={settings.store_address_line2}
                      onChange={(e) => handleInputChange('store_address_line2', e.target.value)}
                      placeholder="Apartment, suite, etc."
                      helperText="Additional address information (optional)"
                      variant="outlined"
                    />
                  </Grid>

                  <Grid item size={{ xs: 12, md: 4 }}>
                    <TextField
                      fullWidth
                      label="City *"
                      value={settings.store_city}
                      onChange={(e) => handleInputChange('store_city', e.target.value)}
                      placeholder="City"
                      variant="outlined"
                    />
                  </Grid>

                  <Grid item size={{ xs: 12, md: 4 }}>
                    <TextField
                      fullWidth
                      label="State *"
                      value={settings.store_state}
                      onChange={(e) => handleInputChange('store_state', e.target.value)}
                      placeholder="State"
                      variant="outlined"
                    />
                  </Grid>

                  <Grid item size={{ xs: 12, md: 4 }}>
                    <TextField
                      fullWidth
                      label="Pincode *"
                      value={settings.store_pincode}
                      onChange={(e) => handleInputChange('store_pincode', e.target.value)}
                      placeholder="400001"
                      helperText="6-digit postal code"
                      variant="outlined"
                    />
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>

            {/* Business Information */}
            <Accordion 
              expanded={expandedSection === 'business'} 
              onChange={handleAccordionChange('business')}
              elevation={2}
            >
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Stack direction="row" alignItems="center" spacing={2}>
                  <Avatar sx={{ bgcolor: 'success.main' }}>
                    <Business />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" fontWeight="bold">
                      Business Information
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Tax and legal information
                    </Typography>
                  </Box>
                </Stack>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item size={{ xs: 12 , md: 4}}>
                    <TextField
                      fullWidth
                      label="GST Number"
                      value={settings.store_gst_number}
                      onChange={(e) => handleInputChange('store_gst_number', e.target.value)}
                      placeholder="27AABCU9603R1ZX"
                      helperText="Enter your 15-digit GST identification number"
                      variant="outlined"
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <Tooltip title="GST number is required for tax compliance">
                              <Info color="action" />
                            </Tooltip>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>

                  <Grid item size={{ xs: 12 , md: 4}}>
                    <TextField
                      fullWidth
                      label="Country"
                      value={settings.store_country || 'India'}
                      onChange={(e) => handleInputChange('store_country', e.target.value)}
                      placeholder="India"
                      helperText="Country where your business is located"
                      variant="outlined"
                    />
                  </Grid>

                  {/* <Grid item size={{ xs: 12 , md: 4}}>
                    <TextField
                      fullWidth
                      label="Currency"
                      value={settings.store_currency || 'INR'}
                      onChange={(e) => handleInputChange('store_currency', e.target.value)}
                      placeholder="INR"
                      helperText="Default currency for your store"
                      variant="outlined"
                    />
                  </Grid> */}

                  {/* <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Timezone"
                      value={settings.store_timezone || 'Asia/Kolkata'}
                      onChange={(e) => handleInputChange('store_timezone', e.target.value)}
                      placeholder="Asia/Kolkata"
                      helperText="Timezone for your store operations"
                      variant="outlined"
                    />
                  </Grid> */}

                  {/* <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Language"
                      value={settings.store_language || 'en'}
                      onChange={(e) => handleInputChange('store_language', e.target.value)}
                      placeholder="en"
                      helperText="Default language for your store"
                      variant="outlined"
                    />
                  </Grid> */}
                </Grid>
              </AccordionDetails>
            </Accordion>

            {/* Payment Methods */}
            <Accordion 
              expanded={expandedSection === 'payment'} 
              onChange={handleAccordionChange('payment')}
              elevation={2}
            >
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Stack direction="row" alignItems="center" spacing={2}>
                  <Avatar sx={{ bgcolor: 'warning.main' }}>
                    <Payment />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" fontWeight="bold">
                      Payment Methods
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Configure accepted payment options for your store
                    </Typography>
                  </Box>
                </Stack>
              </AccordionSummary>
              <AccordionDetails>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Select the payment methods you want to accept in your store. Customers will see these options during checkout.
                </Typography>
                
                <FormGroup>
                  <Grid container spacing={2}>
                    {/* UPI */}
                    <Grid item xs={12} sm={6} md={4}>
                      <Paper 
                        elevation={1} 
                        sx={{ 
                          p: 2, 
                          border: settings.payment_upi ? '2px solid' : '1px solid',
                          borderColor: settings.payment_upi ? 'primary.main' : 'divider',
                          borderRadius: 2,
                          transition: 'all 0.2s ease-in-out',
                          '&:hover': {
                            borderColor: 'primary.main',
                            boxShadow: 2
                          }
                        }}
                      >
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={settings.payment_upi || false}
                              onChange={(e) => handlePaymentMethodChange('payment_upi', e.target.checked)}
                              color="primary"
                            />
                          }
                          label={
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <Payment color="action" />
                              <Box>
                                <Typography variant="subtitle2" fontWeight="bold">
                                  UPI
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  PhonePe, GPay, Paytm UPI
                                </Typography>
                              </Box>
                            </Stack>
                          }
                        />
                      </Paper>
                    </Grid>

                    {/* Credit/Debit Card */}
                    <Grid item xs={12} sm={6} md={4}>
                      <Paper 
                        elevation={1} 
                        sx={{ 
                          p: 2, 
                          border: settings.payment_credit_debit_card ? '2px solid' : '1px solid',
                          borderColor: settings.payment_credit_debit_card ? 'primary.main' : 'divider',
                          borderRadius: 2,
                          transition: 'all 0.2s ease-in-out',
                          '&:hover': {
                            borderColor: 'primary.main',
                            boxShadow: 2
                          }
                        }}
                      >
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={settings.payment_credit_debit_card || false}
                              onChange={(e) => handlePaymentMethodChange('payment_credit_debit_card', e.target.checked)}
                              color="primary"
                            />
                          }
                          label={
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <CreditCard color="action" />
                              <Box>
                                <Typography variant="subtitle2" fontWeight="bold">
                                  Credit/Debit Card
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  Visa, Mastercard, RuPay
                                </Typography>
                              </Box>
                            </Stack>
                          }
                        />
                      </Paper>
                    </Grid>

                    {/* Net Banking */}
                    <Grid item xs={12} sm={6} md={4}>
                      <Paper 
                        elevation={1} 
                        sx={{ 
                          p: 2, 
                          border: settings.payment_net_banking ? '2px solid' : '1px solid',
                          borderColor: settings.payment_net_banking ? 'primary.main' : 'divider',
                          borderRadius: 2,
                          transition: 'all 0.2s ease-in-out',
                          '&:hover': {
                            borderColor: 'primary.main',
                            boxShadow: 2
                          }
                        }}
                      >
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={settings.payment_net_banking || false}
                              onChange={(e) => handlePaymentMethodChange('payment_net_banking', e.target.checked)}
                              color="primary"
                            />
                          }
                          label={
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <AccountBalance color="action" />
                              <Box>
                                <Typography variant="subtitle2" fontWeight="bold">
                                  Net Banking
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  All major banks
                                </Typography>
                              </Box>
                            </Stack>
                          }
                        />
                      </Paper>
                    </Grid>

                    {/* Wallet */}
                    <Grid item xs={12} sm={6} md={4}>
                      <Paper 
                        elevation={1} 
                        sx={{ 
                          p: 2, 
                          border: settings.payment_wallet ? '2px solid' : '1px solid',
                          borderColor: settings.payment_wallet ? 'primary.main' : 'divider',
                          borderRadius: 2,
                          transition: 'all 0.2s ease-in-out',
                          '&:hover': {
                            borderColor: 'primary.main',
                            boxShadow: 2
                          }
                        }}
                      >
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={settings.payment_wallet || false}
                              onChange={(e) => handlePaymentMethodChange('payment_wallet', e.target.checked)}
                              color="primary"
                            />
                          }
                          label={
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <Wallet color="action" />
                              <Box>
                                <Typography variant="subtitle2" fontWeight="bold">
                                  Wallet
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  Digital wallets
                                </Typography>
                              </Box>
                            </Stack>
                          }
                        />
                      </Paper>
                    </Grid>

                    {/* Cash on Delivery */}
                    <Grid item xs={12} sm={6} md={4}>
                      <Paper 
                        elevation={1} 
                        sx={{ 
                          p: 2, 
                          border: settings.payment_cash_on_delivery ? '2px solid' : '1px solid',
                          borderColor: settings.payment_cash_on_delivery ? 'primary.main' : 'divider',
                          borderRadius: 2,
                          transition: 'all 0.2s ease-in-out',
                          '&:hover': {
                            borderColor: 'primary.main',
                            boxShadow: 2
                          }
                        }}
                      >
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={settings.payment_cash_on_delivery || false}
                              onChange={(e) => handlePaymentMethodChange('payment_cash_on_delivery', e.target.checked)}
                              color="primary"
                            />
                          }
                          label={
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <LocalShipping color="action" />
                              <Box>
                                <Typography variant="subtitle2" fontWeight="bold">
                                  Cash on Delivery
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  Pay when you receive
                                </Typography>
                              </Box>
                            </Stack>
                          }
                        />
                      </Paper>
                    </Grid>

                    {/* Stripe */}
                    <Grid item xs={12} sm={6} md={4}>
                      <Paper 
                        elevation={1} 
                        sx={{ 
                          p: 2, 
                          border: settings.payment_stripe ? '2px solid' : '1px solid',
                          borderColor: settings.payment_stripe ? 'primary.main' : 'divider',
                          borderRadius: 2,
                          transition: 'all 0.2s ease-in-out',
                          '&:hover': {
                            borderColor: 'primary.main',
                            boxShadow: 2
                          }
                        }}
                      >
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={settings.payment_stripe || false}
                              onChange={(e) => handlePaymentMethodChange('payment_stripe', e.target.checked)}
                              color="primary"
                            />
                          }
                          label={
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <CreditCard color="action" />
                              <Box>
                                <Typography variant="subtitle2" fontWeight="bold">
                                  Stripe
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  International payments
                                </Typography>
                              </Box>
                            </Stack>
                          }
                        />
                      </Paper>
                    </Grid>

                    {/* PayPal */}
                    <Grid item xs={12} sm={6} md={4}>
                      <Paper 
                        elevation={1} 
                        sx={{ 
                          p: 2, 
                          border: settings.payment_paypal ? '2px solid' : '1px solid',
                          borderColor: settings.payment_paypal ? 'primary.main' : 'divider',
                          borderRadius: 2,
                          transition: 'all 0.2s ease-in-out',
                          '&:hover': {
                            borderColor: 'primary.main',
                            boxShadow: 2
                          }
                        }}
                      >
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={settings.payment_paypal || false}
                              onChange={(e) => handlePaymentMethodChange('payment_paypal', e.target.checked)}
                              color="primary"
                            />
                          }
                          label={
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <Payment color="action" />
                              <Box>
                                <Typography variant="subtitle2" fontWeight="bold">
                                  PayPal
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  Global payment platform
                                </Typography>
                              </Box>
                            </Stack>
                          }
                        />
                      </Paper>
                    </Grid>

                    {/* Paytm */}
                    <Grid item xs={12} sm={6} md={4}>
                      <Paper 
                        elevation={1} 
                        sx={{ 
                          p: 2, 
                          border: settings.payment_paytm ? '2px solid' : '1px solid',
                          borderColor: settings.payment_paytm ? 'primary.main' : 'divider',
                          borderRadius: 2,
                          transition: 'all 0.2s ease-in-out',
                          '&:hover': {
                            borderColor: 'primary.main',
                            boxShadow: 2
                          }
                        }}
                      >
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={settings.payment_paytm || false}
                              onChange={(e) => handlePaymentMethodChange('payment_paytm', e.target.checked)}
                              color="primary"
                            />
                          }
                          label={
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <Wallet color="action" />
                              <Box>
                                <Typography variant="subtitle2" fontWeight="bold">
                                  Paytm
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  Paytm wallet & payments
                                </Typography>
                              </Box>
                            </Stack>
                          }
                        />
                      </Paper>
                    </Grid>
                  </Grid>
                </FormGroup>

                <Box sx={{ mt: 3, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
                  <Typography variant="body2" color="info.dark">
                    <Info sx={{ fontSize: 16, mr: 1, verticalAlign: 'middle' }} />
                    <strong>Note:</strong> You can enable multiple payment methods. Customers will see all selected options during checkout.
                  </Typography>
                </Box>
              </AccordionDetails>
            </Accordion>
          </Stack>

          <Grid item xs={12} lg={4} sx={{mt:3}}>
          <Stack spacing={3}>
            {/* Store Logo */}
            <Card elevation={2}>
              <CardHeader
                avatar={
                  <Avatar sx={{ bgcolor: 'primary.main' }}>
                    <PhotoCamera />
                  </Avatar>
                }
                title="Store Logo"
                subheader="Upload your store logo or provide an image URL"
              />
              <CardContent>
                {/* Upload Mode Tabs */}
                <Tabs 
                  value={logoUploadMode} 
                  onChange={(e, newValue) => setLogoUploadMode(newValue)}
                  sx={{ mb: 3 }}
                  variant="fullWidth"
                >
                  <Tab label="Upload File" value="file" />
                  <Tab label="Image URL" value="url" />
                </Tabs>

                {/* File Upload Mode */}
                {logoUploadMode === 'file' && (
                  <Box
                    {...getRootProps()}
                    sx={{
                      border: '2px dashed',
                      borderColor: isDragActive ? 'primary.main' : 'divider',
                      borderRadius: 2,
                      p: 3,
                      textAlign: 'center',
                      cursor: 'pointer',
                      bgcolor: isDragActive ? 'primary.light' : 'transparent',
                      transition: 'all 0.2s ease-in-out',
                      '&:hover': {
                        borderColor: 'primary.main',
                        bgcolor: 'action.hover'
                      }
                    }}
                  >
                    <input {...getInputProps()} />
                    {isUploadingLogo ? (
                      <Stack alignItems="center" spacing={2}>
                        <LinearProgress sx={{ width: '100%' }} />
                        <Typography variant="body2" color="text.secondary">
                          Uploading logo...
                        </Typography>
                      </Stack>
                    ) : isGeneratingTheme ? (
                      <Stack alignItems="center" spacing={2}>
                        <LinearProgress sx={{ width: '100%' }} color="secondary" />
                        <Typography variant="body2" color="text.secondary">
                          Generating theme from logo...
                        </Typography>
                      </Stack>
                    ) : (
                      <Stack alignItems="center" spacing={2}>
                        <PhotoCamera sx={{ fontSize: 48, color: 'text.secondary' }} />
                        <Typography variant="h6" color="text.secondary">
                          {isDragActive ? 'Drop the image here' : 'Drag & drop an image here, or click to select'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          PNG, JPG, GIF up to 5MB
                        </Typography>
                      </Stack>
                    )}
                  </Box>
                )}

                {/* URL Input Mode */}
                {logoUploadMode === 'url' && (
                  <Stack spacing={2}>
                    <TextField
                      fullWidth
                      label="Image URL"
                      value={logoUrl}
                      onChange={(e) => setLogoUrl(e.target.value)}
                      placeholder="https://example.com/logo.png"
                      variant="outlined"
                      helperText="Enter a direct link to your logo image"
                    />
                    <Stack direction="row" spacing={1}>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => setLogoUrl('https://images.unsplash.com/photo-1603967788945-b2215de1d7b7?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D')}
                      >
                        Test Image 1
                      </Button>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => setLogoUrl('https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400')}
                      >
                        Test Image 2
                      </Button>
                    </Stack>
                    <Button
                      variant="contained"
                      onClick={handleLogoUrlSubmit}
                      disabled={isGeneratingTheme || !logoUrl.trim()}
                      startIcon={isGeneratingTheme ? <LinearProgress size={20} /> : <Upload />}
                      fullWidth
                    >
                      {isGeneratingTheme ? 'Generating Theme...' : 'Process Image URL'}
                    </Button>
                  </Stack>
                )}

                {/* Error Display */}
                {logoUploadError && (
                  <Alert severity="error" sx={{ mt: 2 }}>
                    {logoUploadError}
                  </Alert>
                )}

                {/* Current Logo Preview */}
                {(logoPreview || settings.store_logo) && (
                  <Box sx={{ mt: 3 }}>
                    <Typography variant="subtitle2" sx={{ mb: 2 }}>
                      Current Logo:
                    </Typography>
                    <Stack direction="row" spacing={2} alignItems="flex-start">
                      <Paper
                        elevation={1}
                        sx={{
                          width: 120,
                          height: 120,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          borderRadius: 2,
                          overflow: 'hidden',
                          position: 'relative',
                        }}
                      >
                        <Image
                          src={logoPreview || settings.store_logo}
                          alt="Store Logo"
                          fill
                          style={{ objectFit: 'cover' }}
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                          }}
                        />
                        <IconButton
                          sx={{
                            position: 'absolute',
                            top: 4,
                            right: 4,
                            bgcolor: 'error.main',
                            color: 'white',
                            '&:hover': { bgcolor: 'error.dark' },
                            width: 24,
                            height: 24,
                          }}
                          size="small"
                          onClick={handleRemoveLogo}
                        >
                          <Delete fontSize="small" />
                        </IconButton>
                      </Paper>
                      
                      {/* Auto-Generated Theme Display */}
                      {currentTheme && (
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="subtitle2" sx={{ mb: 1 }}>
                            Auto-Generated Theme:
                          </Typography>
                          <Stack direction="row" spacing={1} sx={{ mb: 1 }}>
                            <Chip 
                              label="Primary" 
                              size="small"
                              sx={{ 
                                bgcolor: currentTheme.primary, 
                                color: 'white',
                                fontWeight: 'bold',
                                fontSize: '0.7rem'
                              }} 
                            />
                            <Chip 
                              label="Secondary" 
                              size="small"
                              sx={{ 
                                bgcolor: currentTheme.secondary, 
                                color: 'white',
                                fontWeight: 'bold',
                                fontSize: '0.7rem'
                              }} 
                            />
                            <Chip 
                              label="Accent" 
                              size="small"
                              sx={{ 
                                bgcolor: currentTheme.accent, 
                                color: 'white',
                                fontWeight: 'bold',
                                fontSize: '0.7rem'
                              }} 
                            />
                          </Stack>
                          {/* {accessibility && (
                            <Typography variant="caption" color={accessibility.isValid ? 'success.main' : 'warning.main'}>
                              {accessibility.isValid ? '✅ Accessible theme' : '⚠️ Accessibility issues detected'}
                            </Typography>
                          )} */}
                        </Box>
                      )}
                    </Stack>
                  </Box>
                )}

                <Typography variant="caption" display="block" sx={{ mt: 2, color: 'text.secondary' }}>
                  Recommended: 512x512px, PNG or JPG format. Theme will be automatically generated from your logo.
                </Typography>
                
                {/* Auto Theme Status */}
                {/* {autoLoader.hasAutoApplied && (
                  <Alert severity="success" sx={{ mt: 2 }}>
                    <Typography variant="body2">
                      ✅ Theme auto-applied from existing logo (source: {autoLoader.logoSource})
                    </Typography>
                  </Alert>
                )} */}
                
                {autoLoader.error && !autoLoader.hasAutoApplied && (
                  <Alert severity="info" sx={{ mt: 2 }}>
                    <Typography variant="body2">
                      ℹ️ No existing logo found for auto-theme generation. Upload a logo to create your theme.
                    </Typography>
                  </Alert>
                )}
              </CardContent>
            </Card>

            {/* Store Preview */}
            <Card elevation={2}>
              <CardHeader
                avatar={
                  <Avatar sx={{ bgcolor: 'secondary.main' }}>
                    <Preview />
                  </Avatar>
                }
                title="Store Preview"
                subheader="How your store appears to customers"
              />
              <CardContent>
                <Paper 
                  elevation={1} 
                  sx={{ 
                    p: 3, 
                    bgcolor: 'grey.50',
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: 2
                  }}
                >
                  <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 2 }}>
                    <Avatar
                      sx={{ width: 56, height: 56 }}
                      src={logoPreview || settings.store_logo}
                    >
                      <Store />
                    </Avatar>
                    <Box>
                      <Typography variant="h6" fontWeight="bold">
                        {settings.store_name}
                      </Typography>
                      <Chip 
                        label={`/${settings.store_handle}`} 
                        size="small" 
                        variant="outlined"
                        sx={{ mt: 0.5 }}
                      />
                    </Box>
                  </Stack>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {settings.store_description}
                  </Typography>

                  <List dense>
                    <ListItem disablePadding>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <Email fontSize="small" color="action" />
                      </ListItemIcon>
                      <ListItemText 
                        primary={settings.store_email || 'No email set'}
                        primaryTypographyProps={{ variant: 'body2' }}
                      />
                    </ListItem>
                    <ListItem disablePadding>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <LocationOn fontSize="small" color="action" />
                      </ListItemIcon>
                      <ListItemText 
                        primary={settings.store_city && settings.store_state 
                          ? `${settings.store_city}, ${settings.store_state}` 
                          : 'No address set'
                        }
                        primaryTypographyProps={{ variant: 'body2' }}
                      />
                    </ListItem>
                    {settings.store_phone && (
                      <ListItem disablePadding>
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          <Phone fontSize="small" color="action" />
                        </ListItemIcon>
                        <ListItemText 
                          primary={settings.store_phone}
                          primaryTypographyProps={{ variant: 'body2' }}
                        />
                      </ListItem>
                    )}
                    {settings.store_website && (
                      <ListItem disablePadding>
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          <Public fontSize="small" color="action" />
                        </ListItemIcon>
                        <ListItemText 
                          primary={settings.store_website}
                          primaryTypographyProps={{ variant: 'body2' }}
                        />
                      </ListItem>
                    )}
                  </List>
                </Paper>
              </CardContent>
            </Card>


          </Stack>
        </Grid>


        </Grid>

        {/* Sidebar */}
     
      </Grid>
    </Container>
  );
};