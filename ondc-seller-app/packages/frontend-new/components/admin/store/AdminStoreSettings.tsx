'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { useToast } from '@/app/providers/toast-provider';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Avatar,
  Divider,
  Paper,
  InputAdornment,
  Chip,
} from '@mui/material';
import {
  Store,
  Email,
  Phone,
  Language,
  LocationOn,
  Business,
  Save,
  Cancel,
  PhotoCamera,
  Public,
} from '@mui/icons-material';

interface StoreSettings {
  store_name: string;
  store_logo: string;
  store_description: string;
  store_handle: string;
  store_gst_number: string;
  store_email: string;
  store_address: string;
  store_address_line2: string;
  store_city: string;
  store_state: string;
  store_pincode: string;
  store_phone: string;
  store_website: string;
}

const initialSettings: StoreSettings = {
  store_name: 'My Fashion Store',
  store_logo: '/images/store-logo.png',
  store_description: 'Your one-stop destination for trendy fashion and accessories.',
  store_handle: 'my-fashion-store',
  store_gst_number: '27AABCU9603R1ZX',
  store_email: '<EMAIL>',
  store_address: '123 Fashion Street',
  store_address_line2: 'Near City Mall',
  store_city: 'Mumbai',
  store_state: 'Maharashtra',
  store_pincode: '400001',
  store_phone: '+91 98765 43210',
  store_website: 'https://myfashionstore.com',
};

export const AdminStoreSettings: React.FC = () => {
  const { showToast } = useToast();
  const [settings, setSettings] = useState<StoreSettings>(initialSettings);
  const [isLoading, setSaving] = useState(false);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);

  const handleInputChange = (field: keyof StoreSettings, value: string) => {
    setSettings(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setLogoPreview(result);
        setSettings(prev => ({
          ...prev,
          store_logo: result,
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Saving store settings:', settings);
      showToast('Store settings saved successfully!', 'success');
    } catch (error) {
      console.error('Error saving settings:', error);
      showToast('Failed to save settings. Please try again.', 'error');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    setSettings(initialSettings);
    setLogoPreview(null);
  };

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Page Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Store Settings
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage your store information and configuration
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Main Settings */}
        <Grid item xs={12} lg={8}>
          {/* Basic Information */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <Store sx={{ mr: 1, verticalAlign: 'middle' }} />
                Basic Information
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Update your store's basic details
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Store Name"
                    required
                    value={settings.store_name}
                    onChange={(e) => handleInputChange('store_name', e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Store />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Store Handle"
                    required
                    value={settings.store_handle}
                    onChange={(e) => handleInputChange('store_handle', e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          mystore.com/
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
              </Grid>

              <TextField
                fullWidth
                label="Store Description"
                multiline
                rows={4}
                value={settings.store_description}
                onChange={(e) => handleInputChange('store_description', e.target.value)}
                placeholder="Describe your store..."
                sx={{ mt: 2 }}
              />

              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Store Email"
                    type="email"
                    required
                    value={settings.store_email}
                    onChange={(e) => handleInputChange('store_email', e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Email />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Store Phone"
                    type="tel"
                    value={settings.store_phone}
                    onChange={(e) => handleInputChange('store_phone', e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Phone />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
              </Grid>

              <TextField
                fullWidth
                label="Website URL"
                type="url"
                value={settings.store_website}
                onChange={(e) => handleInputChange('store_website', e.target.value)}
                placeholder="https://yourstore.com"
                sx={{ mt: 2 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Public />
                    </InputAdornment>
                  ),
                }}
              />
            </CardContent>
          </Card>

          {/* Address Information */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <LocationOn sx={{ mr: 1, verticalAlign: 'middle' }} />
                Address Information
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Store address and location details
              </Typography>
              <TextField
                fullWidth
                label="Address Line 1"
                required
                value={settings.store_address}
                onChange={(e) => handleInputChange('store_address', e.target.value)}
                placeholder="Street address"
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="Address Line 2"
                value={settings.store_address_line2}
                onChange={(e) => handleInputChange('store_address_line2', e.target.value)}
                placeholder="Apartment, suite, etc."
                sx={{ mb: 2 }}
              />

              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    label="City"
                    required
                    value={settings.store_city}
                    onChange={(e) => handleInputChange('store_city', e.target.value)}
                    placeholder="City"
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    label="State"
                    required
                    value={settings.store_state}
                    onChange={(e) => handleInputChange('store_state', e.target.value)}
                    placeholder="State"
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    label="Pincode"
                    required
                    value={settings.store_pincode}
                    onChange={(e) => handleInputChange('store_pincode', e.target.value)}
                    placeholder="400001"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Business Information */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <Business sx={{ mr: 1, verticalAlign: 'middle' }} />
                Business Information
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Tax and legal information
              </Typography>
              <TextField
                fullWidth
                label="GST Number"
                value={settings.store_gst_number}
                onChange={(e) => handleInputChange('store_gst_number', e.target.value)}
                placeholder="27AABCU9603R1ZX"
                helperText="Enter your 15-digit GST identification number"
              />
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              startIcon={<Save />}
              onClick={handleSave}
              disabled={isLoading}
              size="large"
            >
              {isLoading ? 'Saving...' : 'Save Changes'}
            </Button>
            <Button
              variant="outlined"
              startIcon={<Cancel />}
              onClick={handleReset}
              size="large"
            >
              Reset
            </Button>
          </Box>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} lg={4}>
          {/* Store Logo */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Store Logo
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Upload your store logo
              </Typography>
              <Box sx={{ textAlign: 'center' }}>
                <Avatar
                  src={logoPreview || settings.store_logo}
                  sx={{ width: 120, height: 120, mx: 'auto', mb: 2 }}
                >
                  <PhotoCamera sx={{ fontSize: 40 }} />
                </Avatar>
                
                <input
                  accept="image/*"
                  style={{ display: 'none' }}
                  id="logo-upload"
                  type="file"
                  onChange={handleLogoUpload}
                />
                <label htmlFor="logo-upload">
                  <Button
                    variant="contained"
                    component="span"
                    startIcon={<PhotoCamera />}
                    sx={{ mb: 1 }}
                  >
                    Upload Logo
                  </Button>
                </label>
                
                <Typography variant="caption" display="block" color="text.secondary">
                  Recommended: 512x512px, PNG or JPG
                </Typography>
              </Box>
            </CardContent>
          </Card>

          {/* Store Preview */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Store Preview
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                How your store appears to customers
              </Typography>
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar
                    src={logoPreview || settings.store_logo}
                    sx={{ width: 48, height: 48, mr: 2 }}
                  >
                    <Store />
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle1" fontWeight="bold">
                      {settings.store_name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      /{settings.store_handle}
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  {settings.store_description}
                </Typography>
                <Box>
                  <Chip
                    icon={<Email />}
                    label={settings.store_email}
                    size="small"
                    variant="outlined"
                    sx={{ mr: 1, mb: 1 }}
                  />
                  <Chip
                    icon={<LocationOn />}
                    label={`${settings.store_city}, ${settings.store_state}`}
                    size="small"
                    variant="outlined"
                  />
                </Box>
              </Paper>
            </CardContent>
          </Card>
        </Grid>
      </Grid>


    </Box>
  );
};