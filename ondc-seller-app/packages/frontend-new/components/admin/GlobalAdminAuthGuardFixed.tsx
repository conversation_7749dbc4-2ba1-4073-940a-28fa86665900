'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import { getAuthToken, isTokenExpired, isValidTokenFormat } from '@/lib/utils/tokenUtils';
import { performLogout } from '@/lib/utils/logout';
import { Box, CircularProgress, Typography } from '@mui/material';

interface GlobalAdminAuthGuardProps {
  children: React.ReactNode;
}

export const GlobalAdminAuthGuardFixed: React.FC<GlobalAdminAuthGuardProps> = ({ children }) => {
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [hasWaitedForHydration, setHasWaitedForHydration] = useState(false);
  
  const mainAuth = useAuthStore();

  // First, wait for client-side hydration
  useEffect(() => {
    // Wait a bit for <PERSON><PERSON><PERSON> to hydrate from localStorage
    const timer = setTimeout(() => {
      setHasWaitedForHydration(true);
      console.log('✅ GlobalAdminAuthGuard: Hydration wait period complete');
    }, 100); // Small delay to allow hydration

    return () => clearTimeout(timer);
  }, []);

  // Then perform authentication check
  useEffect(() => {
    if (!hasWaitedForHydration) {
      console.log('⏳ GlobalAdminAuthGuard: Waiting for hydration...');
      return;
    }

    const checkAuthentication = async () => {
      console.log('=== GLOBAL ADMIN AUTH GUARD CHECK (FIXED) ===');
      console.log('Hydration wait completed:', hasWaitedForHydration);
      
      try {
        // Check if we have any authentication data
        const hasMainAuth = mainAuth.isAuthenticated && mainAuth.token;
        
        console.log('Main auth status:', {
          isAuthenticated: mainAuth.isAuthenticated,
          hasToken: !!mainAuth.token,
          hasUser: !!mainAuth.user
        });

        // Also check localStorage directly as a fallback
        const tokenInfo = getAuthToken();
        console.log('Direct token check from localStorage:', {
          hasToken: !!tokenInfo.token,
          source: tokenInfo.source
        });

        // If no authentication at all (check both Zustand and localStorage)
        if (!hasMainAuth && !tokenInfo.token) {
          console.log('❌ No authentication found anywhere - redirecting to login');
          router.replace('/login?tab=login');
          return;
        }

        // Use the best available token
        const bestToken = tokenInfo.token || mainAuth.token;

        if (!bestToken) {
          console.log('❌ No valid token found - redirecting to login');
          router.replace('/login?tab=login');
          return;
        }

        // Validate token format
        if (!isValidTokenFormat(bestToken)) {
          console.log('❌ Invalid token format - clearing auth and redirecting');
          await performLogout(router);
          return;
        }

        // Check if token is expired
        if (isTokenExpired(bestToken)) {
          console.log('❌ Token is expired - clearing auth and redirecting');
          await performLogout(router);
          return;
        }

        // Additional check: ensure we have user data (with fallback)
        if (!mainAuth.user) {
          // If we have a valid token but no user data, it might be a hydration issue
          // Give it one more chance by checking localStorage directly
          console.log('⚠️ No user data in Zustand store, checking for late hydration...');
          
          // Wait a bit more for potential late hydration
          setTimeout(() => {
            const recheckMainAuth = useAuthStore.getState();
            
            if (!recheckMainAuth.user) {
              console.log('❌ Still no user data found after recheck - redirecting to login');
              router.replace('/login?tab=login');
            } else {
              console.log('✅ User data found after recheck - allowing access');
              setIsAuthorized(true);
            }
          }, 200);
          return;
        }

        // All checks passed
        console.log('✅ Authentication valid - allowing access');
        setIsAuthorized(true);

      } catch (error) {
        console.error('Error during authentication check:', error);
        // On error, redirect to login for safety
        router.replace('/login?tab=login');
      } finally {
        setIsChecking(false);
      }
    };

    checkAuthentication();
  }, [hasWaitedForHydration, router, mainAuth.isAuthenticated, mainAuth.token, mainAuth.user]);

  // Show loading state while checking authentication or waiting for hydration
  if (isChecking || !hasWaitedForHydration) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          gap: 2,
        }}
      >
        <CircularProgress size={40} />
        <Typography variant="body2" color="text.secondary">
          {!hasWaitedForHydration 
            ? 'Loading authentication...' 
            : 'Verifying authentication...'
          }
        </Typography>
      </Box>
    );
  }

  // Show nothing if not authorized (redirect is in progress)
  if (!isAuthorized) {
    return null;
  }

  // Render children if authenticated and authorized
  return <>{children}</>;
};