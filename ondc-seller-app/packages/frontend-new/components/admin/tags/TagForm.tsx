'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useToast } from '@/app/providers/toast-provider';
import { medusaAdminService, type MedusaTag } from '@/lib/api/medusa-admin';
import { ConfirmationModal } from '@/components/ui/ConfirmationModal';

interface TagFormProps {
  isEdit?: boolean;
  tagId?: string;
}

export const TagForm: React.FC<TagFormProps> = ({ isEdit = false, tagId }) => {
  const router = useRouter();
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  const { showToast } = useToast();

  const [formData, setFormData] = useState<Partial<MedusaTag>>({
    value: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [createdTagId, setCreatedTagId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Load tag data for edit mode
  useEffect(() => {
    if (isEdit && tagId) {
      const loadTag = async () => {
        setIsLoading(true);
        try {
          const tag = await medusaAdminService.getTag(storeHandle, tagId);
          setFormData(tag);
        } catch (error) {
          console.error('Error loading tag:', error);
          showToast('Failed to load tag data', 'error');
          router.push(`/${storeHandle}/admin/tags`);
        } finally {
          setIsLoading(false);
        }
      };
      
      loadTag();
    }
  }, [isEdit, tagId, storeHandle, showToast, router]);

  const handleInputChange = (field: keyof MedusaTag, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.value?.trim()) {
      newErrors.value = 'Tag value is required';
    } else if (formData.value.trim().length < 2) {
      newErrors.value = 'Tag value must be at least 2 characters long';
    } else if (formData.value.trim().length > 50) {
      newErrors.value = 'Tag value must be less than 50 characters';
    } else if (!/^[a-zA-Z0-9\s-]+$/.test(formData.value.trim())) {
      newErrors.value = 'Tag value can only contain letters, numbers, spaces, and hyphens';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    if (!formData.value?.trim()) {
      return;
    }

    setIsLoading(true);
    try {
      if (isEdit && tagId) {
        // Update existing tag
        await medusaAdminService.updateTag(storeHandle, tagId, formData.value.trim());
        showToast('Tag updated successfully', 'success');
        // Don't redirect after update - stay on edit page
      } else {
        // Create new tag
        const newTag = await medusaAdminService.createTag(storeHandle, formData.value.trim());
        showToast('Tag created successfully', 'success');
        
        // Set the created tag ID and switch to edit mode
        setCreatedTagId(newTag.id);
        
        // Navigate to edit page with the new tag ID
        router.push(`/${storeHandle}/admin/tags/${newTag.id}`);
      }
    } catch (error: any) {
      console.error('Error saving tag:', error);
      const errorMessage = error.response?.data?.message || 'Failed to save tag';
      showToast(errorMessage, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push(`/${storeHandle}/admin/tags`);
  };

  const handleDelete = async () => {
    if (!isEdit || !tagId) return;
    
    setIsDeleting(true);
    try {
      await medusaAdminService.deleteTag(storeHandle, tagId);
      showToast('Tag deleted successfully', 'success');
      router.push(`/${storeHandle}/admin/tags`);
    } catch (error: any) {
      console.error('Error deleting tag:', error);
      const errorMessage = error.response?.data?.message || 'Failed to delete tag';
      showToast(errorMessage, 'error');
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  if (isEdit && isLoading && !formData.value) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <LoadingSpinner text="Loading tag data..." size="lg" />
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6">
      {/* Header */}
      <div className="mb-6">
        <button
          onClick={handleCancel}
          className="flex items-center text-gray-600 hover:text-gray-800 mb-4"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back to Tags
        </button>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {isEdit ? 'Edit Tag' : 'Add New Tag'}
        </h1>
        <p className="text-gray-600">
          {isEdit ? 'Update tag information.' : 'Create a new tag to help categorize and filter products.'}
        </p>
      </div>

      {/* Form */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Tag Value */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tag Value *
            </label>
            <input
              type="text"
              value={formData.value || ''}
              onChange={(e) => handleInputChange('value', e.target.value)}
              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                errors.value ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Enter tag value"
              maxLength={50}
            />
            {errors.value && (
              <p className="text-red-500 text-sm mt-1">{errors.value}</p>
            )}
            <p className="text-gray-500 text-sm mt-1">
              A short, descriptive label for products (e.g., "trending", "eco-friendly", "premium")
            </p>
          </div>

          {/* Tag Preview */}
          {formData.value && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Preview
              </label>
              <div className="flex items-center space-x-2">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 border">
                  {formData.value}
                </span>
                <span className="text-gray-500 text-sm">This is how the tag will appear on products</span>
              </div>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex items-center justify-between pt-6 border-t border-gray-200">
            <div>
              {isEdit && (
                <button
                  type="button"
                  onClick={() => setShowDeleteConfirm(true)}
                  disabled={isDeleting || isLoading}
                  className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isDeleting ? (
                    <>
                      <svg className="animate-spin w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Deleting...
                    </>
                  ) : (
                    <>
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                      Delete Tag
                    </>
                  )}
                </button>
              )}
            </div>
            <div className="flex items-center space-x-4">
              <button
                type="button"
                onClick={handleCancel}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                {isEdit ? 'Back to Tags' : 'Cancel'}
              </button>
              <button
                type="submit"
                disabled={isLoading || isDeleting}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {isEdit ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  <>
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    {isEdit ? 'Update Tag' : 'Create Tag'}
                  </>
                )}
              </button>
            </div>
          </div>
        </form>
      </div>

      {/* Info Box */}
      <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <svg className="w-5 h-5 text-blue-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <h3 className="text-sm font-medium text-blue-900 mb-1">About Tags</h3>
            <p className="text-sm text-blue-800">
              Tags are simple labels that help customers find products through filtering and search. 
              Use descriptive, single-word tags like "trending", "eco-friendly", or "premium" for best results.
            </p>
          </div>
        </div>
      </div>

      {/* Popular Tags Examples */}
      <div className="mt-4 bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-gray-900 mb-2">Popular Tag Examples</h3>
        <div className="flex flex-wrap gap-2">
          {['trending', 'eco-friendly', 'premium', 'limited-edition', 'bestseller', 'new', 'sale', 'featured'].map((tag) => (
            <span
              key={tag}
              className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-200 text-gray-700"
            >
              {tag}
            </span>
          ))}
          </div>
        ))}
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={handleDelete}
        title="Delete Tag"
        message={`Are you sure you want to delete the tag "${formData.value}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        confirmButtonColor="red"
        icon="danger"
        isLoading={isDeleting}
      />
    </div>
  );
};