'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { useToast } from '@/app/providers/toast-provider';
import { medusaAdminService, type MedusaTag } from '@/lib/api/medusa-admin';
import { ConfirmationModal } from '@/components/ui/ConfirmationModal';

// Material Design 3 Components
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Chip,
  IconButton,
  Alert,
  AlertTitle,
  Skeleton,
  Stack,
  Divider,
  Container,
  Breadcrumbs,
  Link as MuiLink,
  Paper,
  CircularProgress,
  Tooltip,
  Fade,
  Zoom,
  Collapse,
} from '@mui/material';

import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  Delete as DeleteIcon,
  Info as InfoIcon,
  LocalOffer as TagIcon,
  NavigateNext as NavigateNextIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Lightbulb as LightbulbIcon,
} from '@mui/icons-material';

interface TagFormMD3Props {
  isEdit?: boolean;
  tagId?: string;
}

export const TagFormMD3: React.FC<TagFormMD3Props> = ({ isEdit = false, tagId }) => {
  const router = useRouter();
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  const { showToast } = useToast();

  const [formData, setFormData] = useState<Partial<MedusaTag>>({
    value: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(isEdit);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  // Load tag data for edit mode
  useEffect(() => {
    if (isEdit && tagId) {
      const loadTag = async () => {
        setIsInitialLoading(true);
        try {
          const tag = await medusaAdminService.getTag(storeHandle, tagId);
          setFormData(tag);
          setShowPreview(true);
        } catch (error) {
          console.error('Error loading tag:', error);
          showToast('Failed to load tag data', 'error');
          router.push(`/${storeHandle}/admin/tags`);
        } finally {
          setIsInitialLoading(false);
        }
      };
      
      loadTag();
    }
  }, [isEdit, tagId, storeHandle, showToast, router]);

  // Show preview when user starts typing
  useEffect(() => {
    if (formData.value && formData.value.trim().length > 0) {
      setShowPreview(true);
    } else {
      setShowPreview(false);
    }
  }, [formData.value]);

  const handleInputChange = (field: keyof MedusaTag, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.value?.trim()) {
      newErrors.value = 'Tag value is required';
    } else if (formData.value.trim().length < 2) {
      newErrors.value = 'Tag value must be at least 2 characters long';
    } else if (formData.value.trim().length > 50) {
      newErrors.value = 'Tag value must be less than 50 characters';
    } else if (!/^[a-zA-Z0-9\s-]+$/.test(formData.value.trim())) {
      newErrors.value = 'Tag value can only contain letters, numbers, spaces, and hyphens';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    if (!formData.value?.trim()) {
      return;
    }

    setIsLoading(true);
    try {
      if (isEdit && tagId) {
        // Update existing tag
        await medusaAdminService.updateTag(storeHandle, tagId, formData.value.trim());
        showToast('Tag updated successfully', 'success');
        // Don't redirect after update - stay on edit page
      } else {
        // Create new tag
        const newTag = await medusaAdminService.createTag(storeHandle, formData.value.trim());
        showToast('Tag created successfully', 'success');
        
        // Navigate to edit page with the new tag ID
        router.push(`/${storeHandle}/admin/tags/${newTag.id}`);
      }
    } catch (error: any) {
      console.error('Error saving tag:', error);
      const errorMessage = error.response?.data?.message || 'Failed to save tag';
      showToast(errorMessage, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push(`/${storeHandle}/admin/tags`);
  };

  const handleDelete = async () => {
    if (!isEdit || !tagId) return;
    
    setIsDeleting(true);
    try {
      await medusaAdminService.deleteTag(storeHandle, tagId);
      showToast('Tag deleted successfully', 'success');
      router.push(`/${storeHandle}/admin/tags`);
    } catch (error: any) {
      console.error('Error deleting tag:', error);
      const errorMessage = error.response?.data?.message || 'Failed to delete tag';
      showToast(errorMessage, 'error');
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  const popularTags = [
    'trending', 'eco-friendly', 'premium', 'limited-edition', 
    'bestseller', 'new', 'sale', 'featured', 'organic', 'handmade'
  ];

  if (isInitialLoading) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Stack spacing={3}>
          <Skeleton variant="text" width={200} height={40} />
          <Skeleton variant="text" width={300} height={24} />
          <Card elevation={1}>
            <CardContent>
              <Stack spacing={3}>
                <Skeleton variant="text" width={150} height={24} />
                <Skeleton variant="rectangular" width="100%" height={56} />
                <Skeleton variant="rectangular" width={200} height={40} />
              </Stack>
            </CardContent>
          </Card>
        </Stack>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs 
        separator={<NavigateNextIcon fontSize="small" />}
        sx={{ mb: 3 }}
      >
        <MuiLink 
          component={Link} 
          href={`/${storeHandle}/admin`}
          underline="hover"
          color="inherit"
        >
          Admin
        </MuiLink>
        <MuiLink 
          component={Link} 
          href={`/${storeHandle}/admin/tags`}
          underline="hover"
          color="inherit"
        >
          Tags
        </MuiLink>
        <Typography color="text.primary">
          {isEdit ? 'Edit Tag' : 'Add New Tag'}
        </Typography>
      </Breadcrumbs>

      {/* Header */}
      <Box mb={4}>
        <Button
          onClick={handleCancel}
          startIcon={<ArrowBackIcon />}
          sx={{ mb: 2 }}
          color="inherit"
        >
          Back to Tags
        </Button>
        
        <Typography variant="h4" component="h1" gutterBottom fontWeight={600}>
          {isEdit ? 'Edit Tag' : 'Add New Tag'}
        </Typography>
        
        <Typography variant="body1" color="text.secondary">
          {isEdit 
            ? 'Update tag information to better organize your products.' 
            : 'Create a new tag to help categorize and filter products for your customers.'
          }
        </Typography>
      </Box>

      {/* Main Form */}
      <Card elevation={1} sx={{ mb: 3 }}>
        <CardContent sx={{ p: 4 }}>
          <form onSubmit={handleSubmit}>
            <Stack spacing={4}>
              {/* Tag Value Input */}
              <Box>
                <Typography variant="h6" gutterBottom fontWeight={600}>
                  Tag Information
                </Typography>
                
                <TextField
                  label="Tag Value"
                  value={formData.value || ''}
                  onChange={(e) => handleInputChange('value', e.target.value)}
                  error={!!errors.value}
                  helperText={errors.value || 'A short, descriptive label for products (e.g., "trending", "eco-friendly", "premium")'}
                  fullWidth
                  required
                  variant="outlined"
                  placeholder="Enter tag value"
                  inputProps={{ maxLength: 50 }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                    },
                  }}
                />
                
                <Box display="flex" justifyContent="space-between" mt={1}>
                  <Typography variant="caption" color="text.secondary">
                    Use letters, numbers, spaces, and hyphens only
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {formData.value?.length || 0}/50
                  </Typography>
                </Box>
              </Box>

              {/* Tag Preview */}
              <Collapse in={showPreview}>
                <Box>
                  <Typography variant="subtitle1" gutterBottom fontWeight={600}>
                    Preview
                  </Typography>
                  
                  <Paper 
                    elevation={0} 
                    sx={{ 
                      p: 3, 
                      bgcolor: 'grey.50', 
                      border: 1, 
                      borderColor: 'divider',
                      borderRadius: 2,
                    }}
                  >
                    <Stack direction="row" spacing={2} alignItems="center">
                      <Zoom in={showPreview}>
                        <Chip
                          label={formData.value || 'Tag Preview'}
                          color="primary"
                          variant="outlined"
                          size="medium"
                          sx={{ fontWeight: 500 }}
                        />
                      </Zoom>
                      <Typography variant="body2" color="text.secondary">
                        This is how the tag will appear on products
                      </Typography>
                    </Stack>
                  </Paper>
                </Box>
              </Collapse>

              {/* Form Actions */}
              <Divider />
              
              <Stack 
                direction={{ xs: 'column', sm: 'row' }} 
                justifyContent="space-between" 
                alignItems={{ xs: 'stretch', sm: 'center' }}
                spacing={2}
              >
                {/* Delete Button (Edit Mode Only) */}
                <Box>
                  {isEdit && (
                    <Button
                      onClick={() => setShowDeleteConfirm(true)}
                      disabled={isDeleting || isLoading}
                      color="error"
                      variant="outlined"
                      startIcon={isDeleting ? <CircularProgress size={16} /> : <DeleteIcon />}
                    >
                      {isDeleting ? 'Deleting...' : 'Delete Tag'}
                    </Button>
                  )}
                </Box>

                {/* Save/Cancel Buttons */}
                <Stack direction="row" spacing={2}>
                  <Button
                    onClick={handleCancel}
                    variant="outlined"
                    size="large"
                    disabled={isLoading || isDeleting}
                  >
                    Cancel
                  </Button>
                  
                  <Button
                    type="submit"
                    variant="contained"
                    size="large"
                    disabled={isLoading || isDeleting || !formData.value?.trim()}
                    startIcon={isLoading ? <CircularProgress size={16} /> : <SaveIcon />}
                    sx={{ minWidth: 140 }}
                  >
                    {isLoading 
                      ? (isEdit ? 'Updating...' : 'Creating...') 
                      : (isEdit ? 'Update Tag' : 'Create Tag')
                    }
                  </Button>
                </Stack>
              </Stack>
            </Stack>
          </form>
        </CardContent>
      </Card>

      {/* Info Cards */}
      <Stack spacing={3}>
        {/* About Tags Info */}
        <Card elevation={1} sx={{ bgcolor: 'info.50', borderColor: 'info.200' }}>
          <CardContent>
            <Stack direction="row" spacing={2}>
              <InfoIcon color="info" />
              <Box>
                <Typography variant="subtitle1" fontWeight={600} color="info.main" gutterBottom>
                  About Tags
                </Typography>
                <Typography variant="body2" color="info.dark">
                  Tags are simple labels that help customers find products through filtering and search. 
                  Use descriptive, single-word tags like "trending", "eco-friendly", or "premium" for best results.
                  Tags make it easier for customers to discover products and can improve your store's organization.
                </Typography>
              </Box>
            </Stack>
          </CardContent>
        </Card>

        {/* Popular Tags Examples */}
        <Card elevation={1}>
          <CardContent>
            <Stack direction="row" spacing={2} alignItems="flex-start">
              <LightbulbIcon color="warning" />
              <Box sx={{ flexGrow: 1 }}>
                <Typography variant="subtitle1" fontWeight={600} gutterBottom>
                  Popular Tag Examples
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Get inspired by these commonly used tags:
                </Typography>
                <Stack direction="row" flexWrap="wrap" gap={1}>
                  {popularTags.map((tag) => (
                    <Chip
                      key={tag}
                      label={tag}
                      variant="outlined"
                      size="small"
                      onClick={() => handleInputChange('value', tag)}
                      sx={{ 
                        cursor: 'pointer',
                        '&:hover': {
                          bgcolor: 'primary.50',
                          borderColor: 'primary.main',
                        }
                      }}
                    />
                  ))}
                </Stack>
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  Click on any tag to use it as your tag value
                </Typography>
              </Box>
            </Stack>
          </CardContent>
        </Card>

        {/* Best Practices */}
        <Card elevation={1}>
          <CardContent>
            <Typography variant="subtitle1" fontWeight={600} gutterBottom>
              Best Practices
            </Typography>
            <Stack spacing={1}>
              <Stack direction="row" spacing={1} alignItems="center">
                <CheckIcon color="success" fontSize="small" />
                <Typography variant="body2">
                  Keep tags short and descriptive (1-2 words)
                </Typography>
              </Stack>
              <Stack direction="row" spacing={1} alignItems="center">
                <CheckIcon color="success" fontSize="small" />
                <Typography variant="body2">
                  Use consistent naming conventions
                </Typography>
              </Stack>
              <Stack direction="row" spacing={1} alignItems="center">
                <CheckIcon color="success" fontSize="small" />
                <Typography variant="body2">
                  Focus on customer-facing benefits and features
                </Typography>
              </Stack>
              <Stack direction="row" spacing={1} alignItems="center">
                <CloseIcon color="error" fontSize="small" />
                <Typography variant="body2">
                  Avoid overly technical or internal terminology
                </Typography>
              </Stack>
            </Stack>
          </CardContent>
        </Card>
      </Stack>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={handleDelete}
        title="Delete Tag"
        message={`Are you sure you want to delete the tag "${formData.value}"? This action cannot be undone and will remove the tag from all associated products.`}
        confirmText="Delete"
        cancelText="Cancel"
        confirmButtonColor="red"
        icon="danger"
        isLoading={isDeleting}
      />
    </Container>
  );
};