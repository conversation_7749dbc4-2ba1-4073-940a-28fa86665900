'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { AdminTableLoading } from '../AdminLoading';
import { useAuthStore } from '@/stores/authStore';
import { medusaAdminService, type MedusaTag } from '@/lib/api/medusa-admin';
import { useToast } from '@/app/providers/toast-provider';
import { ConfirmationModal } from '@/components/ui/ConfirmationModal';

// Removed mock data - now using API

export const AdminTagsList: React.FC = () => {
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  const { getStoreHandle } = useAuthStore();
  const { showToast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [tags, setTags] = useState<MedusaTag[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [deletingTagId, setDeletingTagId] = useState<string | null>(null);
  const [deleteConfirmModal, setDeleteConfirmModal] = useState<{
    isOpen: boolean;
    tagId: string;
    tagName: string;
  }>({ isOpen: false, tagId: '', tagName: '' });
  
  // Get dynamic store handle from auth store
  const dynamicStoreHandle = getStoreHandle() || storeHandle;

  // Fetch tags from API
  useEffect(() => {
    const fetchTags = async () => {
      if (!dynamicStoreHandle) {
        console.log('No store handle available for tags API call');
        setIsLoading(false);
        return;
      }
      
      setIsLoading(true);
      setError(null);
      
      try {
        console.log('=== FETCHING TAGS ===');
        console.log('Using store handle as x-tenant-id:', dynamicStoreHandle);
        
        const tagsArray = await medusaAdminService.getTags(dynamicStoreHandle);
        
        console.log('Tags fetched successfully:', tagsArray);
        
        // Filter tags by search term if provided
        let filteredTags = tagsArray;
        if (searchTerm) {
          filteredTags = tagsArray.filter(tag => 
            tag.value.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }
        
        setTags(filteredTags);
        setTotalCount(tagsArray.length);
      } catch (error: any) {
        console.error('Failed to fetch tags:', error);
        setError(error.message || 'Failed to fetch tags');
        setTags([]);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchTags();
  }, [dynamicStoreHandle, searchTerm]);

  // Tags are already filtered by the API based on search
  const filteredTags = tags;

  // Helper function to safely get tag field values for MedusaTag
  const getTagField = (tag: MedusaTag, field: string) => {
    switch (field) {
      case 'id':
        return tag.id;
      case 'name':
        return tag.value; // Medusa uses 'value' instead of 'name'
      case 'productCount':
        return 0; // Medusa doesn't return product count in basic tag response
      case 'createdAt':
        return tag.created_at;
      case 'status':
        return 'active'; // Default status
      case 'color':
        return null; // Medusa doesn't have color field
      default:
        return (tag as any)[field] || null;
    }
  };

  const handleSelectAll = () => {
    if (selectedTags.length === filteredTags.length) {
      setSelectedTags([]);
    } else {
      setSelectedTags(filteredTags.map(t => t.id));
    }
  };

  const handleSelectTag = (tagId: string) => {
    setSelectedTags(prev =>
      prev.includes(tagId)
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    );
  };

  const handleDeleteClick = (tagId: string, tagValue: string) => {
    setDeleteConfirmModal({
      isOpen: true,
      tagId,
      tagName: tagValue
    });
  };

  const handleDeleteConfirm = async () => {
    const { tagId } = deleteConfirmModal;
    
    setDeletingTagId(tagId);
    try {
      await medusaAdminService.deleteTag(dynamicStoreHandle, tagId);
      showToast('Tag deleted successfully', 'success');
      
      // Remove the deleted tag from the local state
      setTags(prev => prev.filter(tag => tag.id !== tagId));
      setSelectedTags(prev => prev.filter(id => id !== tagId));
      setTotalCount(prev => prev - 1);
      
      // Close the modal
      setDeleteConfirmModal({ isOpen: false, tagId: '', tagName: '' });
    } catch (error: any) {
      console.error('Error deleting tag:', error);
      const errorMessage = error.response?.data?.message || 'Failed to delete tag';
      showToast(errorMessage, 'error');
    } finally {
      setDeletingTagId(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteConfirmModal({ isOpen: false, tagId: '', tagName: '' });
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Tags</h1>
          <p className="text-gray-600">Manage product tags and labels</p>
        </div>
        <div className="flex items-center space-x-3">
      
          <Link
            href={`/${dynamicStoreHandle}/admin/tags/new`}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Add Tag
          </Link>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Search tags..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">
              {totalCount > 0 ? `${totalCount} total tags` : `${filteredTags.length} tags`}
            </span>
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedTags.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-blue-900">
              {selectedTags.length} tag(s) selected
            </span>
            <div className="flex items-center space-x-2">
              <button className="px-3 py-1 text-sm bg-white border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors">
                Bulk Edit
              </button>
              <button className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">
                <span className="font-medium">Error loading tags:</span> {error}
              </p>
            </div>
            <div className="ml-auto">
              <button
                onClick={() => window.location.reload()}
                className="text-sm text-red-600 hover:text-red-700 font-medium"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Tags Table */}
      {isLoading ? (
        <AdminTableLoading />
      ) : error ? null : (
      <div className="table-container">
        {filteredTags.length > 0 ? (
          <>
            <table className="table-admin">
              <thead>
                <tr>
                  <th className="w-12">
                    <input
                      type="checkbox"
                      checked={selectedTags.length === filteredTags.length && filteredTags.length > 0}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </th>
                  <th>Tag Name</th>
                  {/* <th>Products</th> */}
                  <th>Created</th>
                  <th className="text-right">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredTags.map((tag) => {
                  const tagId = tag.id;
                  const tagName = tag.value || 'Unnamed Tag';
                  const tagProductCount = 0; // Medusa doesn't return product count in basic response
                  const tagCreatedAt = tag.created_at;
                  const tagColor = null; // Medusa doesn't have color field
                  
                  return (
                    <tr key={tagId}>
                      <td>
                        <input
                          type="checkbox"
                          checked={selectedTags.includes(tagId)}
                          onChange={() => handleSelectTag(tagId)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                      <td>
                        <span 
                          className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 border"
                          style={tagColor ? { backgroundColor: tagColor + '20', color: tagColor, borderColor: tagColor + '40' } : {}}
                        >
                          {tagName}
                        </span>
                      </td>
                      {/* <td>
                        <span className="text-sm text-gray-900">
                          {tagProductCount} products
                        </span>
                      </td> */}
                      <td>
                        <span className="text-sm text-gray-900">
                          {tagCreatedAt ? new Date(tagCreatedAt).toLocaleDateString('en-IN', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric',
                        }) : 'Date not available'}
                        </span>
                      </td>
                      <td>
                        <div className="flex items-center justify-end space-x-2">
                          <Link
                            href={`/${dynamicStoreHandle}/admin/tags/${tagId}`}
                            className="text-blue-600 hover:text-blue-900 text-sm"
                          >
                            Edit
                          </Link>
                          <button 
                            onClick={() => handleDeleteClick(tagId, tagName)}
                            disabled={deletingTagId === tagId}
                            className="text-red-600 hover:text-red-900 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {deletingTagId === tagId ? 'Deleting...' : 'Delete'}
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>

            {/* Pagination */}
            <div className="bg-white px-6 py-3 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-500">
                  Showing 1 to {filteredTags.length} of {totalCount || filteredTags.length} results
                </div>
                <div className="flex items-center space-x-2">
                  <button className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors">
                    Previous
                  </button>
                  <button className="px-3 py-1 text-sm bg-blue-600 text-white rounded">
                    1
                  </button>
                  <button className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors">
                    Next
                  </button>
                </div>
              </div>
            </div>
          </>
        ) : (
          /* No Tags Message */
          <div className="p-12 text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gray-100 mb-4">
              <svg className="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm ? 'No tags found' : 'No tags available'}
            </h3>
            <p className="text-gray-500 mb-6">
              {searchTerm 
                ? 'Try adjusting your search criteria to find tags.'
                : 'Get started by creating your first product tag.'
              }
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Clear Search
                </button>
              )}
              <Link
                href={`/${dynamicStoreHandle}/admin/tags/new`}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Add Your First Tag
              </Link>
            </div>
          </div>
        )}
      </div>
      )}

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteConfirmModal.isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Tag"
        message={`Are you sure you want to delete the tag "${deleteConfirmModal.tagName}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        confirmButtonColor="red"
        icon="danger"
        isLoading={deletingTagId === deleteConfirmModal.tagId}
      />
    </div>
  );
};