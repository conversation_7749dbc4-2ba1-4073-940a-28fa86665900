'use client';

import React from 'react';
import { useNavigation } from './NavigationProvider';
import { LoadingSpinner } from '../ui/LoadingSpinner';

export const NavigationLoader: React.FC = () => {
  const { isNavigating } = useNavigation();

  if (!isNavigating) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-opacity-75 backdrop-blur-sm transition-all duration-300"
        style={{ backgroundColor: 'var(--theme-background, #ffffff)' }}
      />
      
      {/* Loading Content */}
      <div className="relative z-10 animate-fade-in">
        <div 
          className="p-8 rounded-xl shadow-2xl border-2 max-w-sm mx-4"
          style={{ 
            backgroundColor: 'var(--theme-surface, #ffffff)',
            borderColor: 'var(--theme-primary, #3b82f6)',
          }}
        >
          <div className="flex flex-col items-center space-y-6">
            {/* Animated Logo/Icon */}
            <div 
              className="w-16 h-16 rounded-full flex items-center justify-center animate-pulse"
              style={{ backgroundColor: 'var(--theme-primary, #3b82f6)' }}
            >
              <svg 
                className="w-8 h-8 text-white animate-spin" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </div>

            {/* Loading Spinner */}
            <LoadingSpinner size="md" />
            
            {/* Loading Text */}
            <div className="text-center">
              <h3 
                className="text-lg font-semibold mb-2"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                Loading Page
              </h3>
              <p 
                className="text-sm animate-pulse"
                style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
              >
                Please wait while we prepare your content...
              </p>
            </div>

            {/* Progress Bar */}
            <div 
              className="w-full h-1 rounded-full overflow-hidden"
              style={{ backgroundColor: 'var(--theme-border, #e5e7eb)' }}
            >
              <div 
                className="h-full rounded-full animate-progress"
                style={{ backgroundColor: 'var(--theme-primary, #3b82f6)' }}
              />
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes fade-in {
          from {
            opacity: 0;
            transform: scale(0.95);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }

        @keyframes progress {
          0% {
            width: 0%;
            transform: translateX(-100%);
          }
          50% {
            width: 100%;
            transform: translateX(0%);
          }
          100% {
            width: 100%;
            transform: translateX(100%);
          }
        }

        .animate-fade-in {
          animation: fade-in 0.3s ease-out;
        }

        .animate-progress {
          animation: progress 2s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
};