'use client';

import React from 'react';
import { ActionButton } from '../ui/ActionButton';

export interface BreadcrumbItem {
  label: string;
  href: string;
  isActive?: boolean;
}

interface BreadcrumbNavigationProps {
  items: BreadcrumbItem[];
  className?: string;
  style?: React.CSSProperties;
}

export const BreadcrumbNavigation: React.FC<BreadcrumbNavigationProps> = ({
  items,
  className = '',
  style = {},
}) => {
  return (
    <nav className={`mb-8 ${className}`} style={style} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2 text-sm">
        {items.map((item, index) => (
          <React.Fragment key={item.href}>
            {index > 0 && (
              <svg 
                className="w-4 h-4 mx-2" 
                fill="none" 
                stroke="var(--breadcrumb-separator, var(--theme-text-secondary, #6b7280))" 
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            )}
            <li>
              {index === items.length - 1 || item.isActive ? (
                <span 
                  style={{ color: 'var(--breadcrumb-active, var(--theme-text, #111827))' }} 
                  className="font-medium"
                  aria-current="page"
                >
                  {item.label}
                </span>
              ) : (
                <ActionButton
                  href={item.href}
                  variant="ghost"
                  className="p-0 h-auto font-normal hover:bg-transparent hover:opacity-80 transition-opacity"
                  style={{ color: 'var(--breadcrumb-text, var(--theme-text-secondary, #6b7280))' }}
                  loadingType="navigation"
                  actionId={`breadcrumb-${index}`}
                >
                  {item.label}
                </ActionButton>
              )}
            </li>
          </React.Fragment>
        ))}
      </ol>
    </nav>
  );
};