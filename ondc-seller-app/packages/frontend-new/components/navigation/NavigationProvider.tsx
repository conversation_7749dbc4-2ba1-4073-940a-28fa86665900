'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';

interface NavigationContextType {
  isNavigating: boolean;
  setNavigating: (loading: boolean) => void;
  startNavigation: () => void;
  completeNavigation: () => void;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

interface NavigationProviderProps {
  children: ReactNode;
}

export const NavigationProvider: React.FC<NavigationProviderProps> = ({ children }) => {
  const [isNavigating, setIsNavigating] = useState(false);
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Auto-complete navigation when route changes
  useEffect(() => {
    setIsNavigating(false);
  }, [pathname, searchParams]);

  // Listen for navigation events
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleLinkClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const link = target.closest('a');
      
      // Check if the click is inside a modal or dialog
      const isInModal = target.closest('[role="dialog"], .modal, [data-modal], .fixed.inset-0');
      if (isInModal) {
        return; // Don't trigger navigation loading for modal interactions
      }
      
      // Check if it's a button that's not a link
      const button = target.closest('button');
      if (button && !link) {
        return; // Don't trigger navigation loading for button clicks that aren't links
      }
      
      if (link && link.href && !link.target && !link.download) {
        try {
          const url = new URL(link.href);
          const currentUrl = new URL(window.location.href);
          
          // Check if it's a different page
          if (url.origin === currentUrl.origin && 
              (url.pathname !== currentUrl.pathname || url.search !== currentUrl.search)) {
            
            // Don't show loader for hash links or external links
            if (!link.href.includes('#') && !link.href.startsWith('mailto:') && !link.href.startsWith('tel:')) {
              timeoutId = setTimeout(() => {
                setIsNavigating(true);
              }, 100);
            }
          }
        } catch (error) {
          // Invalid URL, ignore
        }
      }
    };

    const handleFormSubmit = (e: SubmitEvent) => {
      const form = e.target as HTMLFormElement;
      
      // Check if the form is inside a modal or dialog
      const isInModal = form.closest('[role="dialog"], .modal, [data-modal], .fixed.inset-0');
      if (isInModal) {
        return; // Don't trigger navigation loading for modal form submissions
      }
      
      // Only show loading for GET forms that actually navigate
      if (form.method === 'get' && form.action) {
        timeoutId = setTimeout(() => {
          setIsNavigating(true);
        }, 100);
      }
    };

    const handlePopState = () => {
      setIsNavigating(true);
      // Browser navigation completes quickly, so shorter timeout
      setTimeout(() => setIsNavigating(false), 500);
    };

    document.addEventListener('click', handleLinkClick, true);
    document.addEventListener('submit', handleFormSubmit, true);
    window.addEventListener('popstate', handlePopState);

    return () => {
      clearTimeout(timeoutId);
      document.removeEventListener('click', handleLinkClick, true);
      document.removeEventListener('submit', handleFormSubmit, true);
      window.removeEventListener('popstate', handlePopState);
    };
  }, []);

  const setNavigating = (loading: boolean) => {
    setIsNavigating(loading);
  };

  const startNavigation = () => {
    setIsNavigating(true);
  };

  const completeNavigation = () => {
    setIsNavigating(false);
  };

  const value = {
    isNavigating,
    setNavigating,
    startNavigation,
    completeNavigation,
  };

  return (
    <NavigationContext.Provider value={value}>
      {children}
    </NavigationContext.Provider>
  );
};

export const useNavigation = (): NavigationContextType => {
  const context = useContext(NavigationContext);
  if (context === undefined) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
};