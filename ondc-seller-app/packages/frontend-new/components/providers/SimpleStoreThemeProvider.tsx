'use client';

import React, { ReactNode } from 'react';
import { ThemeProvider, createTheme, Theme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';

interface SimpleStoreThemeProviderProps {
  children: ReactNode;
  storeHandle: string;
}

/**
 * Simple theme provider without auto-loading
 * Uses a basic theme to avoid loading issues
 */
export const SimpleStoreThemeProvider: React.FC<SimpleStoreThemeProviderProps> = ({
  children,
  storeHandle,
}) => {
  const theme = createTheme({
    palette: {
      mode: 'light',
      primary: {
        main: '#1976d2',
      },
      secondary: {
        main: '#dc004e',
      },
    },
    typography: {
      fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    },
    shape: {
      borderRadius: 12,
    },
  });

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  );
};