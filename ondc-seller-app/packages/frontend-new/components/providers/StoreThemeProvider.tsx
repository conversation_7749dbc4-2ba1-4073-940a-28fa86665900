/**
 * Store Theme Provider
 * Ensures all store screens use the same auto-generated theme
 */

'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { ThemeProvider, createTheme, Theme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { AutoTheme } from '@/lib/utils/simpleTheme';
import { useStoreThemeWithAutoLoad } from '@/hooks/useStoreThemeWithAutoLoad';

interface StoreThemeContextType {
  currentTheme: AutoTheme | null;
  muiTheme: Theme;
  isLoading: boolean;
}

const StoreThemeContext = createContext<StoreThemeContextType | undefined>(undefined);

export const useStoreThemeContext = () => {
  const context = useContext(StoreThemeContext);
  if (context === undefined) {
    throw new Error('useStoreThemeContext must be used within a StoreThemeProvider');
  }
  return context;
};

interface StoreThemeProviderProps {
  children: ReactNode;
  storeHandle: string;
}

/**
 * Create MUI theme from AutoTheme
 */
const createMuiThemeFromAutoTheme = (autoTheme: AutoTheme): Theme => {
  return createTheme({
    palette: {
      mode: 'light',
      primary: {
        main: autoTheme.primary,
        light: autoTheme.primaryLight,
        dark: autoTheme.primaryDark,
        contrastText: '#ffffff',
      },
      secondary: {
        main: autoTheme.secondary,
        light: autoTheme.secondaryLight,
        dark: autoTheme.secondaryDark,
        contrastText: '#ffffff',
      },
      background: {
        default: autoTheme.background,
        paper: autoTheme.surface,
      },
      text: {
        primary: autoTheme.text,
        secondary: autoTheme.onSurface,
      },
      divider: autoTheme.onSurface + '20', // 20% opacity
    },
    typography: {
      fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
      h1: {
        fontWeight: 700,
        fontSize: '2.5rem',
        lineHeight: 1.2,
      },
      h2: {
        fontWeight: 600,
        fontSize: '2rem',
        lineHeight: 1.3,
      },
      h3: {
        fontWeight: 600,
        fontSize: '1.75rem',
        lineHeight: 1.3,
      },
      h4: {
        fontWeight: 600,
        fontSize: '1.5rem',
        lineHeight: 1.4,
      },
      h5: {
        fontWeight: 600,
        fontSize: '1.25rem',
        lineHeight: 1.4,
      },
      h6: {
        fontWeight: 600,
        fontSize: '1.125rem',
        lineHeight: 1.4,
      },
      button: {
        fontWeight: 600,
        textTransform: 'none',
      },
    },
    shape: {
      borderRadius: 12,
    },
    components: {
      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            padding: '8px 16px',
            fontWeight: 600,
            boxShadow: 'none',
            '&:hover': {
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            },
          },
          contained: {
            background: `linear-gradient(135deg, ${autoTheme.primary} 0%, ${autoTheme.primaryDark} 100%)`,
            '&:hover': {
              background: `linear-gradient(135deg, ${autoTheme.primaryDark} 0%, ${autoTheme.primary} 100%)`,
            },
          },
        },
      },
      MuiCard: {
        styleOverrides: {
          root: {
            borderRadius: 12,
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
            border: `1px solid ${autoTheme.surface}`,
            '&:hover': {
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
            },
          },
        },
      },
      MuiChip: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            fontWeight: 500,
          },
          colorPrimary: {
            backgroundColor: autoTheme.primary,
            color: '#ffffff',
          },
          colorSecondary: {
            backgroundColor: autoTheme.secondary,
            color: '#ffffff',
          },
        },
      },
      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 8,
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: autoTheme.primary,
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: autoTheme.primary,
              },
            },
          },
        },
      },
      MuiAppBar: {
        styleOverrides: {
          root: {
            backgroundColor: autoTheme.background,
            color: autoTheme.text,
            boxShadow: `0 1px 3px rgba(0,0,0,0.1)`,
            borderBottom: `1px solid ${autoTheme.surface}`,
          },
        },
      },
      MuiDrawer: {
        styleOverrides: {
          paper: {
            backgroundColor: autoTheme.surface,
            borderRight: `1px solid ${autoTheme.onSurface}20`,
          },
        },
      },
      MuiListItemButton: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            margin: '2px 8px',
            '&:hover': {
              backgroundColor: autoTheme.primary + '10',
            },
            '&.Mui-selected': {
              backgroundColor: autoTheme.primary + '20',
              color: autoTheme.primary,
              '&:hover': {
                backgroundColor: autoTheme.primary + '30',
              },
            },
          },
        },
      },
      MuiBreadcrumbs: {
        styleOverrides: {
          root: {
            color: autoTheme.text,
            '& .MuiBreadcrumbs-separator': {
              color: autoTheme.onSurface,
            },
          },
          li: {
            color: autoTheme.onSurface,
            '&:last-child': {
              color: autoTheme.text,
              fontWeight: 500,
            },
          },
        },
      },
      MuiTypography: {
        styleOverrides: {
          root: {
            '&.MuiTypography-colorTextPrimary': {
              color: autoTheme.text,
            },
            '&.MuiTypography-colorTextSecondary': {
              color: autoTheme.onSurface,
            },
          },
          h1: {
            color: autoTheme.text,
          },
          h2: {
            color: autoTheme.text,
          },
          h3: {
            color: autoTheme.text,
          },
          h4: {
            color: autoTheme.text,
          },
          h5: {
            color: autoTheme.text,
          },
          h6: {
            color: autoTheme.text,
          },
          subtitle1: {
            color: autoTheme.onSurface,
          },
          subtitle2: {
            color: autoTheme.onSurface,
          },
          body1: {
            color: autoTheme.text,
          },
          body2: {
            color: autoTheme.onSurface,
          },
          caption: {
            color: autoTheme.onSurface,
          },
        },
      },
      MuiLink: {
        styleOverrides: {
          root: {
            color: autoTheme.primary,
            textDecoration: 'none',
            '&:hover': {
              color: autoTheme.primaryDark,
              textDecoration: 'underline',
            },
          },
        },
      },
    },
  });
};

/**
 * Default MUI theme for fallback
 */
const createDefaultMuiTheme = (): Theme => {
  return createTheme({
    palette: {
      mode: 'light',
      primary: {
        main: '#1976d2',
      },
      secondary: {
        main: '#dc004e',
      },
    },
    typography: {
      fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    },
    shape: {
      borderRadius: 12,
    },
  });
};

export const StoreThemeProvider: React.FC<StoreThemeProviderProps> = ({
  children,
  storeHandle,
}) => {
  const [muiTheme, setMuiTheme] = useState<Theme>(createDefaultMuiTheme());
  
  // Use the new auto-loading theme system
  const {
    currentTheme,
    isAnyLoading,
    autoLoader
  } = useStoreThemeWithAutoLoad(storeHandle);

  // Update MUI theme when auto theme changes
  useEffect(() => {
    if (currentTheme) {
      console.log('🎨 Updating MUI theme from auto theme:', currentTheme);
      setMuiTheme(createMuiThemeFromAutoTheme(currentTheme));
    } else {
      console.log('⚠️ No auto theme available, using default MUI theme');
      setMuiTheme(createDefaultMuiTheme());
    }
  }, [currentTheme]);

  // Log auto-loader status
  useEffect(() => {
    console.log('🔍 StoreThemeProvider - Auto loader status:', {
      hasAutoApplied: autoLoader.hasAutoApplied,
      logoSource: autoLoader.logoSource,
      isLoading: autoLoader.isLoading,
      error: autoLoader.error
    });
  }, [autoLoader]);

  const contextValue: StoreThemeContextType = {
    currentTheme,
    muiTheme,
    isLoading: isAnyLoading,
  };

  // Show loading only if theme is actively being generated (with timeout)
  const [showLoading, setShowLoading] = useState(false);
  const [loadingTimeout, setLoadingTimeout] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (isAnyLoading && !currentTheme) {
      setShowLoading(true);
      
      // Set a timeout to stop showing loading after 10 seconds
      const timeout = setTimeout(() => {
        console.warn('Theme loading timeout reached, proceeding with default theme');
        setShowLoading(false);
      }, 10000);
      
      setLoadingTimeout(timeout);
    } else {
      setShowLoading(false);
      if (loadingTimeout) {
        clearTimeout(loadingTimeout);
        setLoadingTimeout(null);
      }
    }

    return () => {
      if (loadingTimeout) {
        clearTimeout(loadingTimeout);
      }
    };
  }, [isAnyLoading, currentTheme]);

  // Only show loading for a limited time
  if (showLoading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        fontFamily: 'Inter, sans-serif'
      }}>
        <div>Loading store theme...</div>
      </div>
    );
  }

  return (
    <StoreThemeContext.Provider value={contextValue}>
      <ThemeProvider theme={muiTheme}>
        <CssBaseline />
        {children}
      </ThemeProvider>
    </StoreThemeContext.Provider>
  );
};

/**
 * HOC to wrap components with store theme
 */
export const withStoreTheme = <P extends object>(
  Component: React.ComponentType<P>,
  storeHandle: string
) => {
  const WrappedComponent = (props: P) => (
    <StoreThemeProvider storeHandle={storeHandle}>
      <Component {...props} />
    </StoreThemeProvider>
  );

  WrappedComponent.displayName = `withStoreTheme(${Component.displayName || Component.name})`;
  return WrappedComponent;
};