'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { useStoreAuthContext } from '../auth/StoreAuthProvider';
import { useStoreAuthStore } from '@/stores/storeAuthStore';
import { useToast } from '@/app/providers/toast-provider';
import { profileApi, type StoreUser, type Address, type ProfileUpdatePayload, type AddressPayload } from '@/lib/api/profile';
import {
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Avatar,
  Box,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Stack,
  Paper,
  CircularProgress,
  Fab,
  Tooltip
} from '@mui/material';
import {
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Lock as LockIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Cake as CakeIcon,
  Wc as GenderIcon,
  LocationOn as LocationIcon,
  Home as HomeIcon,
  Business as BusinessIcon
} from '@mui/icons-material';

// Types are now imported from the API layer

interface ProfileDetailsProps {
  user: StoreUser;
  storeHandle: string;
}

export const ProfileDetails: React.FC<ProfileDetailsProps> = ({ user, storeHandle }) => {
  const { showToast } = useToast();
  const { refreshUserData } = useStoreAuthContext();
  const {  setAddresses, addAddress, updateAddress, removeAddress } = useStoreAuthStore(storeHandle);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showAddressForm, setShowAddressForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState<Address | null>(null);
  const [formData, setFormData] = useState({
    firstName: user.first_name || '',
    lastName: user.last_name || '',
    email: user.email,
    phone: user.phone || '',
    dateOfBirth: user.metadata?.date_of_birth || '',
    gender: user.metadata?.gender || '',
  });
  const addresses=user?.addresses ||[];
  const [addressFormData, setAddressFormData] = useState({
    first_name: '',
    last_name: '',
    phone: '',
    company: '',
    address_1: '',
    address_2: '',
    city: '',
    country_code: 'IN',
    province: 'in',
    postal_code: '',
    address_name: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  const handleAddressInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setAddressFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    
    try {
      // Only send updatable fields (exclude email, first_name, last_name)
      const payload: ProfileUpdatePayload = {
        phone: formData.phone,
        metadata: {
          date_of_birth: formData.dateOfBirth,
          gender: formData.gender
        }
      };
      
      const result = await profileApi.updateProfile(payload, storeHandle);
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to update profile');
      }
      
      showToast(result.message || 'Profile updated successfully!', 'success');
      setIsEditing(false);
      
      // Fetch updated user details and update store
      await refreshUserData(storeHandle);
    } catch (error: any) {
      console.error('Error updating profile:', error);
      showToast(error.message || 'Failed to update profile. Please try again.', 'error');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      firstName: user.first_name || '',
      lastName: user.last_name || '',
      email: user.email,
      phone: user.phone || '',
      dateOfBirth: user.metadata?.date_of_birth || '',
      gender: user.metadata?.gender || '',
    });
    setIsEditing(false);
  };
  
  // Addresses are now loaded from Zustand store
  // No need for separate loadAddresses function
  
  // Add or update address
  const handleSaveAddress = async () => {
    try {
      const addressPayload: AddressPayload = {
        ...addressFormData,
        metadata: {}
      };
      
      let result;
      if (editingAddress) {
        result = await profileApi.updateAddress(editingAddress.id, addressPayload, storeHandle);
      } else {
        result = await profileApi.addAddress(addressPayload, storeHandle);
      }
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to save address');
      }
      
      if (editingAddress && result.data) {
        // Update address in store
        updateAddress(editingAddress.id, result.data);
      } else if (result.data) {
        // Add new address to store
        addAddress(result.data);
      }
      
      // Refresh user data to get updated addresses from server
      await refreshUserData(storeHandle);
      
      showToast(result.message || (editingAddress ? 'Address updated successfully!' : 'Address added successfully!'), 'success');
      setShowAddressForm(false);
      setEditingAddress(null);
      resetAddressForm();
    } catch (error: any) {
      console.error('Error saving address:', error);
      showToast(error.message || 'Failed to save address. Please try again.', 'error');
    }
  };
  
  // Delete address
  const handleDeleteAddress = async (addressId: string) => {
    if (!confirm('Are you sure you want to delete this address?')) {
      return;
    }
    
    try {
      const result = await profileApi.deleteAddress(addressId, storeHandle);
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete address');
      }
      
      // Remove address from store
      removeAddress(addressId);
      
      // Refresh user data to sync with server
      await refreshUserData(storeHandle);
      
      showToast(result.message || 'Address deleted successfully!', 'success');
    } catch (error: any) {
      console.error('Error deleting address:', error);
      showToast(error.message || 'Failed to delete address. Please try again.', 'error');
    }
  };
  
  const resetAddressForm = () => {
    setAddressFormData({
      first_name: '',
      last_name: '',
      phone: '',
      company: '',
      address_1: '',
      address_2: '',
      city: '',
      country_code: 'IN',
      province: 'in',
      postal_code: '',
      address_name: '',
    });
  };
  
  const handleEditAddress = (address: Address) => {
    setEditingAddress(address);
    setAddressFormData({
      first_name: address.first_name,
      last_name: address.last_name,
      phone: address.phone || '',
      company: address.company || '',
      address_1: address.address_1,
      address_2: address.address_2 || '',
      city: address.city,
      country_code: 'IN', // Always set to India
      province: 'in', // Always set to 'in'
      postal_code: address.postal_code,
      address_name: address.address_name || '',
    });
    setShowAddressForm(true);
  };
  
  const handleCancelAddressForm = () => {
    setShowAddressForm(false);
    setEditingAddress(null);
    resetAddressForm();
  };
  

  
  // Update form data when user prop changes (after refresh)
  useEffect(() => {
    setFormData({
      firstName: user.first_name || '',
      lastName: user.last_name || '',
      email: user.email,
      phone: user.phone || '',
      dateOfBirth: user.metadata?.date_of_birth || '',
      gender: user.metadata?.gender || '',
    });
  }, [user]);
  
  // Addresses are loaded from Zustand store automatically
  // No need to load them separately

  return (
    <Box sx={{ p: 3 }}>
      <Grid container spacing={3}>
        {/* Profile Overview */}
        {/* <Grid item xs={12} lg={4}>
          <Card elevation={2} sx={{ mb: 3 }}>
            <CardContent sx={{ textAlign: 'center', p: 3 }}>
              <Avatar
                sx={{ 
                  width: 96, 
                  height: 96, 
                  mx: 'auto', 
                  mb: 2,
                  bgcolor: 'primary.main',
                  fontSize: '2rem',
                  fontWeight: 'bold'
                }}
              >
                {(user.first_name?.charAt(0) || user.email.charAt(0)).toUpperCase()}
                {(user.last_name?.charAt(0) || '').toUpperCase()}
              </Avatar>

              <Typography variant="h5" component="h2" fontWeight="bold" gutterBottom>
                {user.first_name && user.last_name 
                  ? `${user.first_name} ${user.last_name}`
                  : user.email.split('@')[0]
                }
              </Typography>
              
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {user.email}
              </Typography>

              <Paper 
                elevation={0} 
                sx={{ 
                  p: 2, 
                  mt: 2, 
                  bgcolor: 'grey.50',
                  borderRadius: 2
                }}
              >
                <Typography variant="caption" fontWeight="medium" display="block">
                  Member since
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {user.created_at 
                    ? new Date(user.created_at).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })
                    : 'Recently'
                  }
                </Typography>
              </Paper>
            </CardContent>
          </Card>

          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" component="h3" gutterBottom fontWeight="bold">
                Quick Stats
              </Typography>
              
              <Stack spacing={2}>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="body2" color="text.secondary">
                    Total Orders:
                  </Typography>
                  <Chip label="12" size="small" color="primary" variant="outlined" />
                </Box>
                
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="body2" color="text.secondary">
                    Wishlist Items:
                  </Typography>
                  <Chip label="4" size="small" color="secondary" variant="outlined" />
                </Box>
                
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="body2" color="text.secondary">
                    Saved Addresses:
                  </Typography>
                  <Chip 
                    label={addresses.length} 
                    size="small" 
                    color="info" 
                    variant="outlined" 
                  />
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid> */}

        {/* Profile Details */}
        <Grid item xs={12} lg={8}>
          <Card elevation={2}>
            <CardContent>
              {/* Header */}
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h6" component="h3" fontWeight="bold">
                  <PersonIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Personal Information
                </Typography>
                
                {!isEditing ? (
                  <Button
                    variant="outlined"
                    startIcon={<EditIcon />}
                    onClick={() => setIsEditing(true)}
                    sx={{ borderRadius: 2 }}
                  >
                    Edit Profile
                  </Button>
                ) : (
                  <Stack direction="row" spacing={1}>
                    <Button
                      variant="outlined"
                      startIcon={<CancelIcon />}
                      onClick={handleCancel}
                      disabled={isSaving}
                      sx={{ borderRadius: 2 }}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="contained"
                      startIcon={isSaving ? <CircularProgress size={16} color="inherit" /> : <SaveIcon />}
                      onClick={handleSave}
                      disabled={isSaving}
                      sx={{ borderRadius: 2 }}
                    >
                      {isSaving ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </Stack>
                )}
              </Box>

              {/* Form */}
              <Grid container spacing={3}>
                {/* First Name - Disabled */}
                <Grid item size={{xs: 12, sm: 4}}>
                  <TextField
                    fullWidth
                    label="First Name"
                    value={formData.firstName}
                    disabled
                    // InputProps={{
                    //   endAdornment: <LockIcon color="disabled" />,
                    //   readOnly: true,
                    // }}
                    // helperText={isEditing ? "Cannot be changed" : ""}
                    variant="outlined"
                    sx={{
                      '& .MuiInputBase-input.Mui-disabled': {
                        WebkitTextFillColor: 'rgba(0, 0, 0, 0.6)',
                      },
                    }}
                  />
                  {/* {isEditing && (
                    <Chip 
                      label="Cannot be changed" 
                      size="small" 
                      color="default" 
                      variant="outlined"
                      icon={<LockIcon />}
                      sx={{ mt: 1 }}
                    />
                  )} */}
                </Grid>

                {/* Last Name - Disabled */}
                <Grid item size={{xs: 12, sm: 4}}>
                  <TextField
                    fullWidth
                    label="Last Name"
                    value={formData.lastName}
                    disabled
                  
                    variant="outlined"
                    sx={{
                      '& .MuiInputBase-input.Mui-disabled': {
                        WebkitTextFillColor: 'rgba(0, 0, 0, 0.6)',
                      },
                    }}
                  />
                 
                </Grid>

                {/* Email - Disabled */}
                <Grid item size={{xs: 12, sm: 4}}>
                  <TextField
                    fullWidth
                    label="Email Address"
                    value={formData.email}
                    disabled
             
                    variant="outlined"
                    sx={{
                      '& .MuiInputBase-input.Mui-disabled': {
                        WebkitTextFillColor: 'rgba(0, 0, 0, 0.6)',
                      },
                    }}
                  />
                 
                </Grid>

                {/* Phone - Editable */}
                <Grid item size={{xs: 12, sm: 4}}>
                  <TextField
                    fullWidth
                    label="Phone Number"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                  
                    placeholder="+****************"
                    variant="outlined"
                  />
               
                </Grid>

                {/* Date of Birth - Editable */}
                <Grid item size={{xs: 12, sm: 4}}>
                  <TextField
                    fullWidth
                    label="Date of Birth"
                    name="dateOfBirth"
                    type="date"
                    value={formData.dateOfBirth}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    format="DD/MM/YYYY"
                    variant="outlined"
                  />
               
                </Grid>

                {/* Gender - Editable */}
                <Grid item size={{xs: 12, sm: 4}}>
                  <FormControl fullWidth disabled={!isEditing}>
                    <InputLabel>Gender</InputLabel>
                    <Select
                      name="gender"
                      value={formData.gender}
                      onChange={handleInputChange}
                      label="Gender"
                      startAdornment={<GenderIcon color={isEditing ? "primary" : "disabled"} sx={{ mr: 1 }} />}
                      variant="outlined"
                    >
                      <MenuItem value="">Select gender</MenuItem>
                      <MenuItem value="male">Male</MenuItem>
                      <MenuItem value="female">Female</MenuItem>
                      <MenuItem value="other">Other</MenuItem>
                    </Select>
                  </FormControl>
                  
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Preferences Section - Commented out for now */}
          {/* 
          <Card elevation={2} sx={{ mt: 3 }}>
            <CardContent>
              <Typography variant="h6" component="h3" gutterBottom fontWeight="bold">
                Preferences
              </Typography>
              
              <Stack spacing={2}>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box>
                    <Typography variant="body1" fontWeight="medium">
                      Email Notifications
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Receive order updates and promotional emails
                    </Typography>
                  </Box>
                  <Switch checked={true} disabled />
                </Box>
                
                <Divider />
                
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box>
                    <Typography variant="body1" fontWeight="medium">
                      SMS Notifications
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Receive order updates via SMS
                    </Typography>
                  </Box>
                  <Switch checked={false} disabled />
                </Box>
                
                <Divider />
                
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box>
                    <Typography variant="body1" fontWeight="medium">
                      Newsletter
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Subscribe to our newsletter for updates and offers
                    </Typography>
                  </Box>
                  <Switch checked={true} disabled />
                </Box>
              </Stack>
            </CardContent>
          </Card>
          */}

          {/* Addresses Section */}
          <Card elevation={2} sx={{ mt: 3 }}>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h6" component="h3" fontWeight="bold">
                  <LocationIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Saved Addresses
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => {
                    resetAddressForm();
                    setShowAddressForm(true);
                  }}
                  sx={{ borderRadius: 2 }}
                >
                  Add Address
                </Button>
              </Box>

              {/* Address List */}
              {addresses.length === 0 ? (
                <Box display="flex" flexDirection="column" alignItems="center" py={6}>
                  <LocationIcon sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
                  <Typography variant="body2" color="text.secondary" textAlign="center">
                    No addresses saved yet. Add your first address to get started.
                  </Typography>
                </Box>
              ) : (
                <Grid container spacing={2}>
                  {addresses.map((address) => (
                    <Grid item size={{xs:12, sm:4}}  key={address.id}>
                      <Paper 
                        elevation={1} 
                        sx={{ 
                          p: 2, 
                          borderRadius: 2,
                          transition: 'all 0.2s',
                          '&:hover': {
                            elevation: 3,
                            transform: 'translateY(-2px)'
                          }
                        }}
                      >
                        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                          <Box>
                            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                              {address.address_name ? (
                                <>
                                  <HomeIcon sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
                                  {address.address_name}
                                </>
                              ) : (
                                `${address.first_name} ${address.last_name}`
                              )}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {address.first_name} {address.last_name}
                            </Typography>
                          </Box>
                          <Stack direction="row" spacing={1}>
                            <Tooltip title="Edit Address">
                              <IconButton 
                                size="small" 
                                onClick={() => handleEditAddress(address)}
                                sx={{ color: 'primary.main' }}
                              >
                                <EditIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Delete Address">
                              <IconButton 
                                size="small" 
                                onClick={() => handleDeleteAddress(address.id)}
                                sx={{ color: 'error.main' }}
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Stack>
                        </Box>
                        
                        <Stack spacing={0.5}>
                          <Typography variant="body2" color="text.secondary">
                            {address.address_1}
                          </Typography>
                          {address.address_2 && (
                            <Typography variant="body2" color="text.secondary">
                              {address.address_2}
                            </Typography>
                          )}
                          <Typography variant="body2" color="text.secondary">
                            {address.city}, {address.postal_code}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {address.address_name}
                          </Typography>
                          {address.phone && (
                            <Typography variant="body2" color="text.secondary">
                              <PhoneIcon sx={{ fontSize: 14, mr: 0.5, verticalAlign: 'middle' }} />
                              {address.phone}
                            </Typography>
                          )}
                        </Stack>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              )}

          {/* Address Form Modal */}
          {showAddressForm && (
            <Dialog 
              open={showAddressForm} 
              onClose={handleCancelAddressForm}
              maxWidth="md"
              fullWidth
              scroll="paper"
              sx={{
                '& .MuiDialog-container': {
                  alignItems: 'center',
                  justifyContent: 'center'
                },
                '& .MuiDialog-paper': {
                  margin: 'auto',
                  position: 'relative'
                }
              }}
              PaperProps={{
                sx: {
                  borderRadius: 3,
                  maxHeight: '90vh',
                  minHeight: '60vh',
                  display: 'flex',
                  flexDirection: 'column',
                  margin: 2,
                  boxShadow: 24
                }
              }}
            >
              {/* Fixed Header */}
              <DialogTitle 
                sx={{ 
                  pb: 2,
                  borderBottom: '1px solid',
                  borderBottomColor: 'divider',
                  flexShrink: 0
                }}
              >
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box display="flex" alignItems="center">
                    <Avatar 
                      sx={{ 
                        bgcolor: 'primary.main', 
                        mr: 2,
                        width: 40,
                        height: 40
                      }}
                    >
                      <LocationIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="h5" component="h2" fontWeight="bold">
                        {editingAddress ? 'Edit Address' : 'Add New Address'}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {editingAddress ? 'Update your address details' : 'Add a new delivery address'}
                      </Typography>
                    </Box>
                  </Box>
                  <Tooltip title="Close">
                    <IconButton 
                      onClick={handleCancelAddressForm}
                      size="large"
                      sx={{ 
                        color: 'text.secondary',
                        '&:hover': {
                          bgcolor: 'action.hover'
                        }
                      }}
                    >
                      <CancelIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </DialogTitle>

              {/* Scrollable Content */}
              <DialogContent 
                sx={{ 
                  flex: 1,
                  overflow: 'auto',
                  // pt: 3,
                  pb: 2,
                  
                }}
              >
                <Grid container spacing={3} mt={3}>
                  {/* Address Name */}
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Address Name (Optional)"
                      name="address_name"
                      value={addressFormData.address_name}
                      onChange={handleAddressInputChange}
                      placeholder="Home, Office, etc."
                      InputProps={{
                        startAdornment: <HomeIcon color="action" sx={{ mr: 1 }} />,
                      }}
                      variant="outlined"
                      helperText="Give your address a friendly name"
                      sx={{ mb: 1 }}
                    />
                  </Grid>

                  {/* First Name */}
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="First Name"
                      name="first_name"
                      value={addressFormData.first_name}
                      onChange={handleAddressInputChange}
                      required
                      InputProps={{
                        startAdornment: <PersonIcon color="action" sx={{ mr: 1 }} />,
                      }}
                      placeholder="John"
                      variant="outlined"
                      sx={{ mb: 1 }}
                    />
                  </Grid>

                  {/* Last Name */}
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Last Name"
                      name="last_name"
                      value={addressFormData.last_name}
                      onChange={handleAddressInputChange}
                      required
                      InputProps={{
                        startAdornment: <PersonIcon color="action" sx={{ mr: 1 }} />,
                      }}
                      placeholder="Doe"
                      variant="outlined"
                      sx={{ mb: 1 }}
                    />
                  </Grid>

                  {/* Phone */}
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Phone Number"
                      name="phone"
                      value={addressFormData.phone}
                      onChange={handleAddressInputChange}
                      InputProps={{
                        startAdornment: <PhoneIcon color="action" sx={{ mr: 1 }} />,
                      }}
                      placeholder="+91 98765 43210"
                      variant="outlined"
                      helperText="Optional"
                      sx={{ mb: 1 }}
                    />
                  </Grid>

                  {/* Company */}
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Company"
                      name="company"
                      value={addressFormData.company}
                      onChange={handleAddressInputChange}
                      InputProps={{
                        startAdornment: <BusinessIcon color="action" sx={{ mr: 1 }} />,
                      }}
                      placeholder="Company Name"
                      variant="outlined"
                      helperText="Optional"
                      sx={{ mb: 1 }}
                    />
                  </Grid>

                  {/* Address Line 1 */}
                  <Grid item size={{xs:12, sm:4}}>
                    <TextField
                      fullWidth
                      label="Address Line 1"
                      name="address_1"
                      value={addressFormData.address_1}
                      onChange={handleAddressInputChange}
                      required
                      InputProps={{
                        startAdornment: <LocationIcon color="action" sx={{ mr: 1 }} />,
                      }}
                      placeholder="House/Flat No., Building Name, Street"
                      variant="outlined"
                      // multiline
                      // rows={2}
                      sx={{ mb: 1 }}
                    />
                  </Grid>

                  {/* Address Line 2 */}
                  <Grid item size={{xs:12, sm:4}}>
                    <TextField
                      fullWidth
                      label="Address Line 2 (Optional)"
                      name="address_2"
                      value={addressFormData.address_2}
                      onChange={handleAddressInputChange}
                      placeholder="Landmark, Area, Locality"
                      variant="outlined"
                      helperText="Optional - Add landmark or area details"
                      sx={{ mb: 1 }}
                    />
                  </Grid>

                  {/* City */}
                  <Grid item size={{xs:12, sm:4}} >
                    <TextField
                      fullWidth
                      label="City"
                      name="city"
                      value={addressFormData.city}
                      onChange={handleAddressInputChange}
                      required
                      placeholder="Mumbai, Delhi, Bangalore"
                      variant="outlined"
                      sx={{ mb: 1 }}
                    />
                  </Grid>



                  {/* Postal Code */}
                  <Grid item size={{xs:12, sm:4}} >
                    <TextField
                      fullWidth
                      label="PIN Code"
                      name="postal_code"
                      value={addressFormData.postal_code}
                      onChange={handleAddressInputChange}
                      required
                      placeholder="400001"
                      variant="outlined"
                      inputProps={{
                        maxLength: 6,
                        pattern: '[0-9]{6}'
                      }}
                      helperText="6-digit PIN code"
                      sx={{ mb: 1 }}
                    />
                  </Grid>

                  
                  {/* Country Info Display */}
                  {/* <Grid item xs={12}>
                    <Paper 
                      elevation={0} 
                      sx={{ 
                        p: 2, 
                        bgcolor: 'primary.50',
                        border: '1px solid',
                        borderColor: 'primary.200',
                        borderRadius: 2
                      }}
                    >
                      <Box display="flex" alignItems="center">
                        <LocationIcon color="primary" sx={{ mr: 1 }} />
                        <Typography variant="body2" color="primary.main" fontWeight="medium">
                          Country: India | Province: IN
                        </Typography>
                      </Box>
                      <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                        All addresses are automatically set to India
                      </Typography>
                    </Paper>
                  </Grid> */}
                </Grid>

              </DialogContent>
              
              {/* Fixed Footer */}
              <DialogActions 
                sx={{ 
                  p: 3,
                  borderTop: '1px solid',
                  borderTopColor: 'divider',
                  flexShrink: 0,
                  gap: 2
                }}
              >
                <Button
                  onClick={handleCancelAddressForm}
                  variant="outlined"
                  startIcon={<CancelIcon />}
                  size="large"
                  sx={{ 
                    borderRadius: 3,
                    px: 4,
                    py: 1.5,
                    textTransform: 'none',
                    fontWeight: 600
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSaveAddress}
                  variant="contained"
                  startIcon={<SaveIcon />}
                  size="large"
                  sx={{ 
                    borderRadius: 3,
                    px: 4,
                    py: 1.5,
                    textTransform: 'none',
                    fontWeight: 600,
                    boxShadow: 2,
                    '&:hover': {
                      boxShadow: 4
                    }
                  }}
                >
                  {editingAddress ? 'Update Address' : 'Save Address'}
                </Button>
              </DialogActions>
            </Dialog>
          )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};