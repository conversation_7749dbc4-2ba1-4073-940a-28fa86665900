'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Store } from '@/types';
import { Product } from '@/types/product';
import { ProductCard } from '../store/ProductCard';
import { ProductFilters } from './ProductFilters';
import { ProductSort } from './ProductSort';
import { ProductPagination } from './ProductPagination';
import { CategoryBadges } from './CategoryBadges';
import { ProductSortOptions } from './ProductSortOptions';
import { ProductGrid } from './ProductGrid';
import { BreadcrumbNavigation, BreadcrumbItem } from '../navigation/BreadcrumbNavigation';
import { demoCategories } from '@/lib/demo-data';
import { getProductsByCategory, getAllProducts } from '@/lib/product/mockProductData';
import { useStoreCategories, useProductsByCategory, useStoreProducts } from '@/hooks/useStore';

interface ProductListingContentProps {
  store: Store;
  storeHandle: string;
  categorySlug?: string;
  subcategorySlug?: string;
  isSubcategoryPage?: boolean;
}

// Helper interface for category structure
interface Category {
  id: string;
  name: string;
  slug: string;
  subcategories: Subcategory[];
}

interface Subcategory {
  id: string;
  name: string;
  slug: string;
}

// Product filters type
interface ProductFiltersType {
  priceRange: [number, number];
  brands: string[];
  colors: string[];
  sizes: string[];
  ratings: number;
  inStock: boolean;
}

export const ProductListingContent: React.FC<ProductListingContentProps> = ({
  store,
  storeHandle,
  categorySlug,
  subcategorySlug,
  isSubcategoryPage = false,
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [selectedSubcategory, setSelectedSubcategory] = useState<string | null>(
    isSubcategoryPage ? subcategorySlug || null : searchParams.get('subcategory')
  );
  const [sortBy, setSortBy] = useState<string>('featured');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filters, setFilters] = useState<ProductFiltersType>({
    priceRange: [0, 50000],
    brands: [],
    colors: [],
    sizes: [],
    ratings: 0,
    inStock: false,
  });

  // Fetch real categories from API
  const { 
    data: apiCategories, 
    isLoading: categoriesLoading,
    error: categoriesError 
  } = useStoreCategories(storeHandle);
  
  // Use API categories if available, fallback to demo categories
  const categories: Category[] = (apiCategories && apiCategories.length > 0 ? apiCategories : demoCategories).map(cat => ({
    id: cat.id,
    name: cat.name,
    slug: cat.handle,
    subcategories: cat.category_children?.map(sub => ({
      id: sub.id,
      name: sub.name,
      slug: sub.handle,
    })) || []
  }));

  // Find current category and subcategory with better logic
  let currentCategory: Category | undefined;
  let currentSubcategory: Subcategory | undefined;

  if (isSubcategoryPage && subcategorySlug) {
    // For subcategory pages, find the category that contains this subcategory
    currentCategory = categories.find(cat => 
      cat.subcategories.some(sub => sub.slug === subcategorySlug)
    );
    currentSubcategory = currentCategory?.subcategories.find(sub => 
      sub.slug === subcategorySlug
    );
  } else if (categorySlug) {
    // For category pages
    currentCategory = categories.find(cat => cat.slug === categorySlug);
    if (selectedSubcategory) {
      currentSubcategory = currentCategory?.subcategories.find(sub => 
        sub.slug === selectedSubcategory
      );
    }
  }
  
  // Get category/subcategory IDs for API calls
  const categoryId = currentCategory?.id;
  const subcategoryId = currentSubcategory?.id;
  
  console.log('🔍 Category/Subcategory Debug:', {
    categorySlug,
    subcategorySlug,
    selectedSubcategory,
    currentCategory: currentCategory ? { id: currentCategory.id, name: currentCategory.name, slug: currentCategory.slug } : null,
    currentSubcategory: currentSubcategory ? { id: currentSubcategory.id, name: currentSubcategory.name, slug: currentSubcategory.slug } : null,
    categoryId,
    subcategoryId
  });

  // Fetch products based on current selection using real API calls
  const {
    data: categoryProducts,
    isLoading: categoryProductsLoading,
    error: categoryProductsError
  } = useProductsByCategory(
    storeHandle,
    (isSubcategoryPage || selectedSubcategory) ? subcategoryId || '' : categoryId || '',
    50 // Increased limit for better filtering
  );
  // Fallback to all store products if no category is selected
  const {
    data: allStoreProducts,
    isLoading: allProductsLoading,
    error: allProductsError
  } = useStoreProducts(storeHandle, 50);
  
  // Determine which products to use
  const products = (categoryId || subcategoryId) ? (categoryProducts || []) : (allStoreProducts || []);
  const isLoading = categoriesLoading || categoryProductsLoading || allProductsLoading;


  

  // Apply filters and sorting
  useEffect(() => {
    if (!products || products.length === 0) {
      setFilteredProducts([]);
      return;
    }
    
    // let filtered = [...products];
    let filtered = products.map((data)=>({
      id: data?.id,
      title: data?.title,
      description: data?.description,
      price: data?.variants[0]?.metadata?.sale_price || 0,
      originalPrice: data?.variants[0]?.metadata?.original_price || 0,
      images: data?.thumbnail ? [data.thumbnail, ...data.images] : [...data.images],
      category: data?.categories,
      subcategory: data?.categories,
      brand: 0,
      rating: 0,
      reviewCount: 0,
      variants: data?.variants,
      tags: data?.tags,
      features: data?.features||[],
      specifications: data?.specifications||[],
      createdAt: data?.created_at,
      updatedAt: data?.updated_at,
      
    }));

    // Apply price filter
    filtered = filtered.filter(product => 
      product?.variants[0]?.metadata?.sale_price >= filters.priceRange[0] && product?.variants[0]?.metadata?.sale_price <= filters.priceRange[1]
    );

    // Apply brand filter
    if (filters.brands.length > 0) {
      filtered = filtered.filter(product => 
        filters.brands.includes(product.brand)
      );
    }

    // Apply color filter
    if (filters.colors.length > 0) {
      filtered = filtered.filter(product => 
        (product.variants || []).some(variant => 
          variant?.options?.color && filters.colors.includes(variant.options.color)
        )
      );
    }

    // Apply size filter
    if (filters.sizes.length > 0) {
      filtered = filtered.filter(product => 
        (product.variants || []).some(variant => 
          variant?.options?.size && filters.sizes.includes(variant.options.size)
        )
      );
    }

    // Apply rating filter
    if (filters.ratings > 0) {
      filtered = filtered.filter(product => 
        product.rating >= filters.ratings
      );
    }

    // Apply stock filter
    if (filters.inStock) {
      filtered = filtered.filter(product => 
        (product.variants || []).some(variant => (variant?.stock || 0) > 0)
      );
    }

    // Apply sorting
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case 'newest':
        filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
      case 'name':
        filtered.sort((a, b) => a.title.localeCompare(b.title));
        break;
      default:
        // Featured - keep original order
        break;
    }

    setFilteredProducts(filtered);
  }, [products, filters, sortBy]);

  const handleSubcategoryChange = (subcategorySlug: string | null) => {
    if (isSubcategoryPage) return; // Don't allow changes on subcategory page
    
    setSelectedSubcategory(subcategorySlug);
    
    // Update URL without navigation
    const newSearchParams = new URLSearchParams(searchParams.toString());
    if (subcategorySlug) {
      newSearchParams.set('subcategory', subcategorySlug);
    } else {
      newSearchParams.delete('subcategory');
    }
    
    const newUrl = `/${storeHandle}/categories/${categorySlug}${
      newSearchParams.toString() ? `?${newSearchParams.toString()}` : ''
    }`;
    
    window.history.replaceState({}, '', newUrl);
  };

  const getPageTitle = () => {
    if (isSubcategoryPage && currentSubcategory) {
      return currentSubcategory.name;
    }
    if (selectedSubcategory && currentSubcategory) {
      return currentSubcategory.name;
    }
    if (currentCategory) {
      return currentCategory.name;
    }
    return 'Products';
  };

  const getBreadcrumbs = (): BreadcrumbItem[] => {
    const breadcrumbs: BreadcrumbItem[] = [
      { label: 'Home', href: `/${storeHandle}` },
    ];

    if (currentCategory) {
      breadcrumbs.push({
        label: currentCategory.name,
        href: `/${storeHandle}/categories/${currentCategory.slug}`,
        isActive: !currentSubcategory,
      });
    }
    
    if (currentSubcategory) {
      breadcrumbs.push({
        label: currentSubcategory.name,
        href: `/${storeHandle}/subcategories/${currentSubcategory.slug}`,
        isActive: true,
      });
    }

    return breadcrumbs;
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Breadcrumbs */}
      <BreadcrumbNavigation items={getBreadcrumbs()} />

      {/* Page Header */}
      <div className="mb-8">
        <h1 
          className="text-3xl font-bold mb-2"
          style={{ color: 'var(--theme-text, #111827)' }}
        >
          {getPageTitle()}
        </h1>
        <p 
          className="text-lg"
          style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
        >
          {filteredProducts.length} {filteredProducts.length === 1 ? 'product' : 'products'} found
        </p>
      </div>

      {/* Category Badges (only on category page, not subcategory page) */}
      {!isSubcategoryPage && currentCategory && (
        <CategoryBadges
          category={currentCategory}
          selectedSubcategory={selectedSubcategory}
          onSubcategoryChange={handleSubcategoryChange}
        />
      )}

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Filters Sidebar */}
        <div className="lg:w-64 flex-shrink-0">
          <ProductFilters
            filters={filters}
            onFiltersChange={setFilters}
            products={products}
          />
        </div>

        {/* Main Content */}
        <div className="flex-1">
          {/* Sort Options */}
          <div className="mb-6">
            <ProductSortOptions
              sortBy={sortBy}
              onSortChange={setSortBy}
              productCount={filteredProducts.length}
              viewMode={viewMode}
              onViewModeChange={setViewMode}
            />
          </div>

          {/* Product Grid */}
          <ProductGrid
            products={filteredProducts}
            isLoading={isLoading}
            storeHandle={storeHandle}
            viewMode={viewMode}
          />
        </div>
      </div>
    </div>
  );
};