'use client';

import React from 'react';

interface ProductSortOptionsProps {
  sortBy: string;
  onSortChange: (sortBy: string) => void;
  productCount: number;
  viewMode?: 'grid' | 'list';
  onViewModeChange?: (viewMode: 'grid' | 'list') => void;
}

const sortOptions = [
  { value: 'featured', label: 'Featured' },
  { value: 'newest', label: 'Newest' },
  { value: 'price-low', label: 'Price: Low to High' },
  { value: 'price-high', label: 'Price: High to Low' },
  { value: 'rating', label: 'Highest Rated' },
  { value: 'name', label: 'Name: A to Z' },
];

export const ProductSortOptions: React.FC<ProductSortOptionsProps> = ({
  sortBy,
  onSortChange,
  productCount,
  viewMode = 'grid',
  onViewModeChange,
}) => {
  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-4 bg-white rounded-lg shadow-sm border"
      style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
    >
      {/* Product Count */}
      <div className="flex items-center space-x-2">
        <svg 
          className="w-5 h-5" 
          fill="none" 
          stroke="var(--theme-text-secondary, #6b7280)" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14-7H3m16 14H5" />
        </svg>
        <span 
          className="text-sm font-medium"
          style={{ color: 'var(--theme-text, #111827)' }}
        >
          {productCount} {productCount === 1 ? 'Product' : 'Products'}
        </span>
      </div>

      {/* Sort Options */}
      <div className="flex items-center space-x-3">
        <span 
          className="text-sm font-medium"
          style={{ color: 'var(--theme-text, #111827)' }}
        >
          Sort by:
        </span>
        <select
          value={sortBy}
          onChange={(e) => onSortChange(e.target.value)}
          className="px-3 py-2 text-sm border rounded-lg focus:ring-2 focus:border-transparent transition-colors"
          style={{ 
            borderColor: 'var(--theme-border, #d1d5db)',
            '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
            color: 'var(--theme-text, #111827)',
          }}
        >
          {sortOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* View Options */}
      {onViewModeChange && (
        <div className="flex items-center space-x-2">
          <span 
            className="text-sm font-medium"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            View:
          </span>
          <div className="flex items-center border rounded-lg" style={{ borderColor: 'var(--theme-border, #d1d5db)' }}>
            <button
              onClick={() => onViewModeChange('grid')}
              className={`p-2 transition-colors hover:opacity-80 ${
                viewMode === 'grid' ? 'rounded-l-lg' : ''
              }`}
              style={{ 
                backgroundColor: viewMode === 'grid' 
                  ? 'var(--theme-primary, #3b82f6)' 
                  : 'transparent',
                color: viewMode === 'grid' 
                  ? 'var(--btn-text, #ffffff)' 
                  : 'var(--theme-text-secondary, #6b7280)',
              }}
              title="Grid View"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
            </button>
            <button
              onClick={() => onViewModeChange('list')}
              className={`p-2 transition-colors hover:opacity-80 ${
                viewMode === 'list' ? 'rounded-r-lg' : ''
              }`}
              style={{ 
                backgroundColor: viewMode === 'list' 
                  ? 'var(--theme-primary, #3b82f6)' 
                  : 'transparent',
                color: viewMode === 'list' 
                  ? 'var(--btn-text, #ffffff)' 
                  : 'var(--theme-text-secondary, #6b7280)',
              }}
              title="List View"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};