'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Store } from '@/types';
import { Product } from '@/types/product';
import { ProductImageGallery } from './ProductImageGallery';
import { ProductInfo } from './ProductInfo';
import { ProductTabs } from './ProductTabs';
import { RelatedProducts } from './RelatedProducts';
import { BreadcrumbNavigation, BreadcrumbItem } from '../navigation/BreadcrumbNavigation';
import { demoCategories } from '@/lib/demo-data';
import { getProductsByCategory } from '@/lib/product/mockProductData';

interface ProductDetailContentProps {
  product: Product;
  store: Store;
  storeHandle: string;
}

export const ProductDetailContent: React.FC<ProductDetailContentProps> = ({
  product,
  store,
  storeHandle,
}) => {
  const router = useRouter();
  const [relatedProducts, setRelatedProducts] = useState<Product[]>([]);

  // Load related products
  useEffect(() => {
    const loadRelatedProducts = () => {
      try {
        // Get products from the same category, excluding current product
        const categoryProducts = getProductsByCategory(product.category);
        const filtered = categoryProducts
          .filter(p => p.id !== product.id)
          .slice(0, 4); // Limit to 4 related products
        
        setRelatedProducts(filtered);
      } catch (error) {
        console.error('Error loading related products:', error);
      }
    };

    loadRelatedProducts();
  }, [product.category, product.id]);

  // Find category and subcategory info for breadcrumbs
  const categories = demoCategories.map(cat => ({
    id: cat.id,
    name: cat.name,
    slug: cat.handle,
    subcategories: cat.category_children?.map(sub => ({
      id: sub.id,
      name: sub.name,
      slug: sub.handle,
    })) || []
  }));

  console.log("product:::::::::<<>>>",product,categories)
  
  // Safely handle categories array
  const productCategories = Array.isArray(product?.categories) ? product.categories : [];
  const currentCategory = productCategories.find(cat => !cat.parent_category_id);
  const currentSubcategory = productCategories.find(sub => sub.parent_category_id);

  const getBreadcrumbs = (): BreadcrumbItem[] => {
    const breadcrumbs: BreadcrumbItem[] = [
      { label: 'Home', href: `/${storeHandle}` },
    ];

    if (currentCategory) {
      breadcrumbs.push({
        label: currentCategory.name,
        href: `/${storeHandle}/categories/${currentCategory.handle}`,
      });
    }

    if (currentSubcategory) {
      breadcrumbs.push({
        label: currentSubcategory.name,
        href: `/${storeHandle}/subcategories/${currentSubcategory.handle}`,
      });
    }

    breadcrumbs.push({
      label: product.title,
      href: `/${storeHandle}/products/${product.id}`,
      isActive: true,
    });

    return breadcrumbs;
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Breadcrumbs */}
      <BreadcrumbNavigation items={getBreadcrumbs()} />

      {/* Main Product Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 mb-12">
        {/* Product Images */}
        <div>
          <ProductImageGallery product={product} />
        </div>

        {/* Product Information */}
        <div>
          <ProductInfo 
            product={product} 
            storeHandle={storeHandle}
            category={currentCategory}
            subcategory={currentSubcategory}
          />
        </div>
      </div>

      {/* Product Details Tabs */}
      <div className="mb-12">
        <ProductTabs product={product} />
      </div>

      {/* Related Products */}
      {relatedProducts.length > 0 && (
        <div>
          <RelatedProducts 
            products={relatedProducts} 
            storeHandle={storeHandle}
            currentCategory={currentCategory}
          />
        </div>
      )}
    </div>
  );
};