'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { Product } from '@/types/product';
import { formatPrice } from '@/lib/cart/mockCartData';
import { getStoreCartAPI, calculateCartMetadata } from '@/lib/api/cart';
import { useToast } from '@/app/providers/toast-provider';
import { FormControl, InputLabel, Select, MenuItem, SelectChangeEvent } from '@mui/material';

interface Category {
  id: string;
  name: string;
  slug: string;
}

interface Subcategory {
  id: string;
  name: string;
  slug: string;
}

interface ProductInfoProps {
  product: Product;
  storeHandle: string;
  category?: Category;
  subcategory?: Subcategory;
}

export const ProductInfo: React.FC<ProductInfoProps> = ({ product, storeHandle }) => {
  const [selectedImage, setSelectedImage] = useState(0);
  const [selectedVariant, setSelectedVariant] = useState(product.variants?.[0] || null);
  const [quantity, setQuantity] = useState(1);
  const [selectedSize, setSelectedSize] = useState('');
  const [selectedColor, setSelectedColor] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const { showToast } = useToast();
  
  // Get store-specific cart API
  const cartAPI = getStoreCartAPI(storeHandle);
  const hasDiscount = product.originalPrice && product.originalPrice > product.price;
  const discountPercentage = hasDiscount 
    ? Math.round(((product.originalPrice! - product.price) / product.originalPrice!) * 100)
    : 0;

  const isInStock = selectedVariant && (selectedVariant.metadata?.product_quantity || 0) > 0;
  const maxQuantity = selectedVariant ? (selectedVariant.metadata?.product_quantity || 0) : 0;
  const currentVariant = selectedVariant || product.variants?.[0];

  const handleVariantChange = (event: SelectChangeEvent<string>) => {
    const variantId = event.target.value;
    const variant = product.variants?.find(v => v.id === variantId);
    if (variant) {
      setSelectedVariant(variant);
      // Reset quantity if it exceeds new variant's stock
      const variantStock = variant.metadata?.product_quantity || 0;
      if (quantity > variantStock) {
        setQuantity(Math.min(1, variantStock));
      }
    }
  };

  const handleAddToCart = async () => {
    if (!isInStock || isLoading) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Get or create cart
      let cartId = cartAPI.getStoredCartId();
      
      if (!cartId) {
        // Create new cart if none exists
        const regionsResponse = await cartAPI.getRegions('Asia');
        const region = regionsResponse.regions.find(r => 
          r.name.toLowerCase().includes('asia')
        ) || regionsResponse.regions[0];
        
        if (!region) {
          throw new Error('No region available');
        }
        
        const cartResponse = await cartAPI.createCart({ region_id: region.id });
        cartId = cartResponse.cart.id;
        // Cart ID is automatically stored by IsolatedCartAPI
      }
      
      // Calculate metadata
      const metadata = calculateCartMetadata(
        currentVariant?.metadata?.sale_price || product.price,
        quantity,
        0.08 // 8% tax
      );
      
      // Add item to cart
      await cartAPI.addLineItem(cartId, {
        variant_id: currentVariant?.id || product.variants?.[0]?.id,
        quantity,
        metadata,
      });
      
      showToast('Product added to cart successfully!', 'success');
      
      // Trigger cart update event
      window.dispatchEvent(new CustomEvent('cartUpdated'));
      
      // Reset quantity after adding
      setQuantity(1);
    } catch (error) {
      console.error('Error adding to cart:', error);
      showToast('Failed to add product to cart', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddToWishlist = () => {
    // Future: Implement wishlist functionality
    showToast('Added to wishlist!', 'success');
  };

  return (
    <div className="space-y-6">
      {/* Product Title and Brand */}
      <div>
        {/* <div className="flex items-center space-x-2 mb-2">
        
          {category && (
            <span 
              className="text-xs px-2 py-1 rounded-full border"
              style={{ 
                borderColor: 'var(--theme-border, #d1d5db)',
                color: 'var(--theme-text-secondary, #6b7280)',
              }}
            >
              {category.name}
            </span>
          )}
        </div> */}
        
        <h1 
          className="text-3xl font-bold mb-2"
          style={{ color: 'var(--theme-text, #111827)' }}
        >
          {product.title}
        </h1>
        
        <p 
          className="text-lg"
          style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
        >
          {product.description}
        </p>
      </div>

      {/* Rating and Reviews */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-1">
          {[...Array(5)].map((_, i) => (
            <svg
              key={i}
              className={`w-5 h-5 ${i < Math.floor(product.rating) ? 'text-yellow-400' : 'text-gray-300'}`}
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
          ))}
          <span 
            className="text-sm font-medium ml-2"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            {product.rating}
          </span>
        </div>
        
        <span 
          className="text-sm"
          style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
        >
          ({product.reviewCount || 0} reviews)
        </span>
      </div>

      {/* Price */}
      <div className="flex items-center space-x-3">
        <span 
          className="text-3xl font-bold"
          style={{ color: 'var(--theme-primary, #3b82f6)' }}
        >
          {formatPrice(selectedVariant?.metadata?.sale_price || product.price)}
        </span>
        
        {hasDiscount && (
          <>
            <span 
              className="text-xl line-through"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
            >
              {formatPrice(product.originalPrice!)}
            </span>
            <span 
              className="text-sm font-bold px-2 py-1 rounded-full"
              style={{ 
                backgroundColor: 'var(--theme-accent, #ef4444)',
                color: 'white',
              }}
            >
              -{discountPercentage}% OFF
            </span>
          </>
        )}
      </div>

      {/* Stock Status */}
      <div className="flex items-center space-x-2">
        <div 
          className={`w-3 h-3 rounded-full ${isInStock ? 'bg-green-500' : 'bg-red-500'}`}
        />
        <span 
          className={`text-sm font-medium ${isInStock ? 'text-green-600' : 'text-red-600'}`}
        >
          {isInStock ? `In Stock (${selectedVariant?.metadata?.product_quantity || 0} available)` : 'Out of Stock'}
        </span>
      </div>

      {/* Variant Selection */}
      {product.variants && product.variants.length > 1 && (
        <div>
          <FormControl fullWidth variant="outlined">
            <InputLabel id="variant-select-label">Select Variant</InputLabel>
            <Select
              labelId="variant-select-label"
              id="variant-select"
              value={selectedVariant?.id || ''}
              label="Select Variant"
              onChange={handleVariantChange}
              sx={{
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: 'var(--theme-border, #d1d5db)',
                  },
                  '&:hover fieldset': {
                    borderColor: 'var(--theme-primary, #3b82f6)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: 'var(--theme-primary, #3b82f6)',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: 'var(--theme-text-secondary, #6b7280)',
                  '&.Mui-focused': {
                    color: 'var(--theme-primary, #3b82f6)',
                  },
                },
                '& .MuiSelect-select': {
                  color: 'var(--theme-text, #111827)',
                },
              }}
            >
              {product.variants?.map((variant) => {
                const isAvailable = (variant.metadata?.product_quantity || 0) > 0;
                const variantLabel = variant.title || 'Default Variant';
                return (
                  <MenuItem 
                    key={variant.id} 
                    value={variant.id}
                    disabled={!isAvailable}
                    sx={{
                      color: isAvailable ? 'var(--theme-text, #111827)' : 'var(--theme-text-secondary, #6b7280)',
                      '&:hover': {
                        backgroundColor: 'var(--theme-hover, #f3f4f6)',
                      },
                      '&.Mui-selected': {
                        backgroundColor: 'var(--theme-primary, #3b82f6)',
                        color: 'var(--btn-text, #ffffff)',
                        '&:hover': {
                          backgroundColor: 'var(--theme-primary, #3b82f6)',
                        },
                      },
                    }}
                  >
                    <div className="flex justify-between items-center w-full">
                      <span>{variantLabel}</span>
                      <div className="flex items-center space-x-2 ml-4">
                        {variant.metadata?.sale_price && (
                          <span className="text-sm font-medium">
                            {formatPrice(variant.metadata.sale_price)}
                          </span>
                        )}
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          isAvailable ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {isAvailable ? `${variant.metadata?.product_quantity} in stock` : 'Out of stock'}
                        </span>
                      </div>
                    </div>
                  </MenuItem>
                );
              }) || []}
            </Select>
          </FormControl>
          
     
        </div>
      )}

      {/* Quantity Selector */}
      {isInStock && (
        <div>
          <h3 
            className="text-sm font-medium mb-3"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            Quantity:
          </h3>
          
          <div className="flex items-center space-x-3">
            <div className="flex items-center border rounded-lg" style={{ borderColor: 'var(--theme-border, #d1d5db)' }}>
              <button
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                disabled={quantity <= 1}
                className="p-2 transition-colors hover:opacity-80 disabled:opacity-50"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                </svg>
              </button>
              
              <span 
                className="px-4 py-2 text-center min-w-[3rem]"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                {quantity}
              </span>
              
              <button
                onClick={() => setQuantity(Math.min(maxQuantity, quantity + 1))}
                disabled={quantity >= maxQuantity}
                className="p-2 transition-colors hover:opacity-80 disabled:opacity-50"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </button>
            </div>
            
            <span 
              className="text-sm"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
            >
              {maxQuantity} available
            </span>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="space-y-4">
            <button
              onClick={handleAddToCart}
              disabled={!isInStock || isLoading}
              className={`w-full py-4 px-8 rounded-lg font-semibold text-lg transition-all duration-200 ${
                (isInStock && !isLoading) 
                  ? 'hover:shadow-lg hover:scale-105' 
                  : 'opacity-50 cursor-not-allowed'
              }`}
              style={{ 
                backgroundColor: (isInStock && !isLoading) 
                  ? 'var(--btn-primary, #3b82f6)' 
                  : 'var(--theme-text-secondary, #6b7280)',
                color: 'var(--btn-text, #ffffff)',
              }}
            >
              {isLoading ? 'Adding to Cart...' : isInStock ? 'Add to Cart' : 'Out of Stock'}
            </button>

        <div className="flex space-x-3">
          <button
            onClick={handleAddToWishlist}
            className="flex-1 py-3 px-4 text-sm font-medium rounded-lg border transition-all duration-200 hover:shadow-sm"
            style={{ 
              borderColor: 'var(--theme-border, #d1d5db)',
              color: 'var(--theme-text, #111827)',
            }}
          >
            <div className="flex items-center justify-center space-x-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
              <span>Add to Wishlist</span>
            </div>
          </button>

          <button
            onClick={() => {
              if (navigator.share) {
                navigator.share({
                  title: product.title,
                  text: product.description,
                  url: window.location.href,
                }).then(() => {
                  showToast('Product link shared!', 'success');
                }).catch(() => {
                  // Fallback to clipboard
                  navigator.clipboard.writeText(window.location.href);
                  showToast('Product link copied!', 'success');
                });
              } else {
                navigator.clipboard.writeText(window.location.href);
                showToast('Product link copied!', 'success');
              }
            }}
            className="flex-1 py-3 px-4 text-sm font-medium rounded-lg border transition-all duration-200 hover:shadow-sm"
            style={{ 
              borderColor: 'var(--theme-border, #d1d5db)',
              color: 'var(--theme-text, #111827)',
            }}
          >
            <div className="flex items-center justify-center space-x-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
              </svg>
              <span>Share</span>
            </div>
          </button>
        </div>
      </div>

      {/* Product Tags */}
      {product.tags && product.tags.length > 0 && (
        <div>
          <h3 
            className="text-sm font-medium mb-3"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            Tags:
          </h3>
          <div className="flex flex-wrap gap-2">
            {product.tags.map((tag, index) => (
              <span
                key={index}
                className="px-3 py-1 text-xs rounded-full"
                style={{ 
                  backgroundColor: 'var(--theme-background, #f9fafb)',
                  color: 'var(--theme-text-secondary, #6b7280)',
                }}
              >
                #{tag}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};