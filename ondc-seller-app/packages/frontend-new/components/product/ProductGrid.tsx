'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  CircularProgress,
  Stack,
  Chip,
  IconButton,
} from '@mui/material';
import {
  Inventory,
  ShoppingCart,
  Add,
} from '@mui/icons-material';
import { Product } from '@/types/product';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { formatPrice } from '@/lib/cart/mockCartData';
import { getStoreCartAPI, calculateCartMetadata } from '@/lib/api/cart';
import { useToast } from '@/app/providers/toast-provider';

interface ProductGridProps {
  products: Product[];
  isLoading: boolean;
  storeHandle: string;
  viewMode?: 'grid' | 'list';
}

export const ProductGrid: React.FC<ProductGridProps> = ({
  products,
  isLoading,
  storeHandle,
  viewMode = 'grid',
}) => {
  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 6 }}>
        <CircularProgress size={48} />
      </Box>
    );
  }

  if (!products || products.length === 0) {
    return (
      <Card sx={{ p: 6, textAlign: 'center' }}>
        <CardContent>
          <Box sx={{
            width: 64,
            height: 64,
            mx: 'auto',
            borderRadius: '50%',
            bgcolor: 'grey.100',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mb: 3
          }}>
            <Inventory sx={{ fontSize: 32, color: 'text.secondary' }} />
          </Box>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
            No products found
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Try adjusting your filters or search criteria
          </Typography>
          <Button
            onClick={() => window.location.reload()}
            variant="contained"
            size="large"
          >
            Reset Filters
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Grid container spacing={3}>
      {products.map((product) => (
        <Grid
          item
          key={product.id}
          xs={12}
          sm={viewMode === 'grid' ? 6 : 12}
          md={viewMode === 'grid' ? 4 : 12}
          lg={viewMode === 'grid' ? 3 : 12}
        >
          <ProductCard
            product={product}
            storeHandle={storeHandle}
            viewMode={viewMode}
          />
        </Grid>
      ))}
    </Grid>
  );
};

interface ProductCardProps {
  product: Product;
  storeHandle: string;
  viewMode?: 'grid' | 'list';
}

const ProductCard: React.FC<ProductCardProps> = ({ product, storeHandle, viewMode = 'grid' }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { showToast } = useToast();
  
  // Get store-specific cart API
  const cartAPI = getStoreCartAPI(storeHandle);
  // Safe access to product properties with fallbacks
  const productImages = product?.images || [];
  const productTitle = product?.title || 'Untitled Product';
  const productDescription = product?.description || '';
  const productPrice = product?.price || 0;
  const productOriginalPrice = product?.originalPrice;
  const productRating = product?.rating || 0;
  const productReviewCount = product?.reviewCount || 0;
  const productVariants = product?.variants || [];
  
  const hasDiscount = productOriginalPrice && productOriginalPrice > productPrice;
  const discountPercentage = hasDiscount 
    ? Math.round(((productOriginalPrice - productPrice) / productOriginalPrice) * 100)
    : 0;

  const inStockVariants = productVariants.filter(variant => (variant?.metadata?.product_quantity || 0) > 0);
  const isInStock = inStockVariants.length > 0;
  
  // Get the first available image or use placeholder
  const primaryImage = productImages.length > 0 ? productImages[0] : '/images/default-product.png';
  
  // Handle add to cart - DIRECT API CALL
  const handleAddToCart = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!isInStock || isLoading) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Get the first available variant
      const selectedVariant = inStockVariants[0];
      
      if (!selectedVariant) {
        showToast('No available variant found', 'error');
        return;
      }
      
      // Get or create cart
      let cartId = cartAPI.getStoredCartId();
      
      if (!cartId) {
        // Create new cart if none exists
        const regionsResponse = await cartAPI.getRegions('Asia');
        const region = regionsResponse.regions.find(r => 
          r.name.toLowerCase().includes('asia')
        ) || regionsResponse.regions[0];
        
        if (!region) {
          throw new Error('No region available');
        }
        
        const cartResponse = await cartAPI.createCart({ region_id: region.id });
        cartId = cartResponse.cart.id;
        // Cart ID is automatically stored by IsolatedCartAPI
      }
      
      // Calculate metadata
      const metadata = calculateCartMetadata(
        productPrice,
        1,
        0.08 // 8% tax
      );
      
      // Add item to cart
      await cartAPI.addLineItem(cartId, {
        variant_id: selectedVariant.id,
        quantity: 1,
        metadata,
      });
      
      showToast('Product added to cart successfully!', 'success');
      
      // Trigger cart update event
      window.dispatchEvent(new CustomEvent('cartUpdated'));
      
    } catch (error) {
      console.error('Error adding to cart:', error);
      showToast('Failed to add product to cart', 'error');
    } finally {
      setIsLoading(false);
    }
  };
  


  if (viewMode === 'list') {
    return (
      
        <div 
          className="bg-white rounded-lg w-full shadow-sm border transition-all duration-200 hover:shadow-lg group cursor-pointer p-4"
          style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
        >
          <div className="flex gap-4">
            {/* Product Image */}
            <div className="relative w-32 h-32 flex-shrink-0 overflow-hidden rounded-lg">
              {productImages.length > 0 ? (
                <Link href={`/${storeHandle}/products/${product.id}`}>
                  <Image
                    src={primaryImage}
                    alt={productTitle}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-300"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </Link>
              ) : (
                <Link href={`/${storeHandle}/products/${product.id}`}>
                  <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                    <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                </Link>
              )}

              {/* Badges */}
              <div className="absolute top-2 left-2 flex flex-col space-y-1">
                {hasDiscount && (
                  <span 
                    className="text-white text-xs font-bold px-2 py-1 rounded-full"
                    style={{ backgroundColor: 'var(--theme-accent, #ef4444)' }}
                  >
                    -{discountPercentage}%
                  </span>
                )}
                {!isInStock && (
                  <span className="bg-gray-600 text-white text-xs font-bold px-2 py-1 rounded-full">
                    Out of Stock
                  </span>
                )}
              </div>
            </div>

            {/* Product Details */}
            <div className="flex-1 min-w-0">
              {/* Title */}
              <Link href={`/${storeHandle}/products/${product.id}`}>
                <h3 
                  className="font-medium text-lg mb-2 line-clamp-2 group-hover:text-opacity-80"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  {productTitle}
                </h3>
              </Link>
              {/* Description */}
              {productDescription && (
                <p 
                  className="text-sm mb-3 line-clamp-2"
                  style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                >
                  {productDescription}
                </p>
              )}

              {/* Rating */}
              <div className="flex items-center space-x-1 mb-3">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <svg
                      key={i}
                      className={`w-4 h-4 ${i < Math.floor(productRating) ? 'text-yellow-400' : 'text-gray-300'}`}
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
                <span 
                  className="text-sm"
                  style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                >
                  ({productReviewCount})
                </span>
              </div>

              {/* Price and Actions */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span 
                    className="font-bold text-xl"
                    style={{ color: 'var(--theme-primary, #3b82f6)' }}
                  >
                    {formatPrice(productPrice)}
                  </span>
                  {hasDiscount && (
                    <span 
                      className="text-sm line-through"
                      style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                    >
                      {formatPrice(productOriginalPrice!)}
                    </span>
                  )}
                </div>

                {/* Add to Cart Button */}
                <button
                  onClick={handleAddToCart}
                  disabled={!isInStock || isLoading}
                  className={`py-2 px-6 text-sm font-medium rounded-lg transition-all duration-200 ${
                    (isInStock && !isLoading) 
                      ? 'hover:shadow-lg hover:scale-105' 
                      : 'opacity-50 cursor-not-allowed'
                  }`}
                  style={{ 
                    backgroundColor: (isInStock && !isLoading) 
                      ? 'var(--btn-primary, #3b82f6)' 
                      : 'var(--theme-text-secondary, #6b7280)',
                    color: 'var(--btn-text, #ffffff)',
                  }}
                >
                  {isLoading ? 'Adding...' : isInStock ? 'Add to Cart' : 'Out of Stock'}
                </button>
              </div>
            </div>
          </div>
        </div>
     
    );
  }

  // Grid view (default)
  return (
    
      <div 
        className="bg-white min-w-[240px]  rounded-lg shadow-sm border transition-all duration-200 hover:shadow-lg hover:scale-105 group cursor-pointer"
        style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
      >
        {/* Product Image */}
        <div className="relative aspect-square overflow-hidden rounded-t-lg">
          {productImages.length > 0 ? (
            <Link href={`/${storeHandle}/products/${product.id}`}>
              <Image
                src={primaryImage}
                alt={productTitle}
                fill
                className="object-cover group-hover:scale-110 transition-transform duration-300"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            </Link>
          ) : (
            <Link href={`/${storeHandle}/products/${product.id}`}>
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
            </Link>
          )}

          {/* Badges */}
          <div className="absolute top-2 left-2 flex flex-col space-y-1">
            {hasDiscount && (
              <span 
                className="text-white text-xs font-bold px-2 py-1 rounded-full"
                style={{ backgroundColor: 'var(--theme-accent, #ef4444)' }}
              >
                -{discountPercentage}%
              </span>
            )}
            {!isInStock && (
              <span className="bg-gray-600 text-white text-xs font-bold px-2 py-1 rounded-full">
                Out of Stock
              </span>
            )}
          </div>

          {/* Quick Actions */}
          <div className="absolute top-2 right-2 flex flex-col space-y-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              className="p-2 bg-white rounded-full shadow-md transition-colors hover:opacity-80"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
              onClick={(e) => {
                e.preventDefault();
                // Add to wishlist functionality
                alert('Added to wishlist!');
              }}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </button>
            <Link href={`/${storeHandle}/products/${product.id}`}>
              <button
                className="p-2 bg-white rounded-full shadow-md transition-colors hover:opacity-80"
                style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </button>
            </Link>
          </div>
        </div>

        {/* Product Details */}
        <div className="p-4">
          {/* Title */}
          <Link href={`/${storeHandle}/products/${product.id}`}>
            <h3 
              className="font-medium text-sm mb-2 line-clamp-2 group-hover:text-opacity-80"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              {productTitle}
            </h3>
          </Link>

          {/* Rating */}
          <div className="flex items-center space-x-1 mb-2">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <svg
                  key={i}
                  className={`w-3 h-3 ${i < Math.floor(productRating||0) ? 'text-yellow-400' : 'text-gray-300'}`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
            </div>
            <span 
              className="text-xs"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
            >
              ({productReviewCount})
            </span>
          </div>

          {/* Price */}
          <div className="flex items-center space-x-2 mb-3">
            <span 
              className="font-bold text-lg"
              style={{ color: 'var(--theme-primary, #3b82f6)' }}
            >
              {formatPrice(productPrice)}
            </span>
            {hasDiscount && (
              <span 
                className="text-sm line-through"
                style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
              >
                {formatPrice(productOriginalPrice!)}
              </span>
            )}
          </div>

          {/* Add to Cart Button */}
          <button
            onClick={handleAddToCart}
            disabled={!isInStock || isLoading}
            className={`w-full py-2 px-4 text-sm font-medium rounded-lg transition-all duration-200 ${
              (isInStock && !isLoading) 
                ? 'hover:shadow-lg hover:scale-105' 
                : 'opacity-50 cursor-not-allowed'
            }`}
            style={{ 
              backgroundColor: (isInStock && !isLoading) 
                ? 'var(--btn-primary, #3b82f6)' 
                : 'var(--theme-text-secondary, #6b7280)',
              color: 'var(--btn-text, #ffffff)',
            }}
          >
            {isLoading ? 'Adding...' : isInStock ? 'Add to Cart' : 'Out of Stock'}
          </button>
        </div>
      </div>
  );
};