'use client';

import React from 'react';

interface Category {
  id: string;
  name: string;
  slug: string;
  subcategories: Subcategory[];
}

interface Subcategory {
  id: string;
  name: string;
  slug: string;
}

interface CategoryBadgesProps {
  category: Category;
  selectedSubcategory: string | null;
  onSubcategoryChange: (subcategorySlug: string | null) => void;
}

export const CategoryBadges: React.FC<CategoryBadgesProps> = ({
  category,
  selectedSubcategory,
  onSubcategoryChange,
}) => {
  return (
    <div className="mb-8">
      <div className="flex flex-wrap gap-3">
        {/* All Products Badge */}
        <button
          onClick={() => onSubcategoryChange(null)}
          className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
            selectedSubcategory === null
              ? 'text-white shadow-lg transform scale-105'
              : 'border-2 hover:shadow-md hover:scale-105'
          }`}
          style={{
            backgroundColor: selectedSubcategory === null 
              ? 'var(--theme-primary, #3b82f6)' 
              : 'transparent',
            borderColor: 'var(--theme-primary, #3b82f6)',
            color: selectedSubcategory === null 
              ? 'var(--btn-text, #ffffff)' 
              : 'var(--theme-primary, #3b82f6)',
          }}
        >
          All {category.name}
        </button>

        {/* Subcategory Badges */}
        {category.subcategories.map((subcategory) => (
          <button
            key={subcategory.slug}
            onClick={() => onSubcategoryChange(subcategory.slug)}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
              selectedSubcategory === subcategory.slug
                ? 'text-white shadow-lg transform scale-105'
                : 'border-2 hover:shadow-md hover:scale-105'
            }`}
            style={{
              backgroundColor: selectedSubcategory === subcategory.slug 
                ? 'var(--theme-primary, #3b82f6)' 
                : 'transparent',
              borderColor: 'var(--theme-primary, #3b82f6)',
              color: selectedSubcategory === subcategory.slug 
                ? 'var(--btn-text, #ffffff)' 
                : 'var(--theme-primary, #3b82f6)',
            }}
          >
            {subcategory.name}
          </button>
        ))}
      </div>

      {/* Selected Category Info */}
      <div className="mt-4 p-4 rounded-lg" style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}>
        <div className="flex items-center space-x-2">
          <svg 
            className="w-5 h-5" 
            fill="none" 
            stroke="var(--theme-primary, #3b82f6)" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
          </svg>
          <span 
            className="text-sm font-medium"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            Showing: {selectedSubcategory 
              ? category.subcategories.find(sub => sub.slug === selectedSubcategory)?.name 
              : `All ${category.name}`
            }
          </span>
        </div>
      </div>
    </div>
  );
};