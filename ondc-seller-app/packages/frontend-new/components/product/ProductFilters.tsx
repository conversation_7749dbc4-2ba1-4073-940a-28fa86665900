'use client';

import React, { useState } from 'react';
import { Product, ProductFilters as ProductFiltersType } from '@/types/product';
import { getFilterOptions } from '@/lib/product/mockProductData';

interface ProductFiltersProps {
  filters: ProductFiltersType;
  onFiltersChange: (filters: ProductFiltersType) => void;
  products: Product[];
}

export const ProductFilters: React.FC<ProductFiltersProps> = ({
  filters,
  onFiltersChange,
  products,
}) => {

  console.log("products::::::::::>>>>>>>===",products);
  
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { brands, colors, sizes } = getFilterOptions(products);

  const handlePriceRangeChange = (range: [number, number]) => {
    onFiltersChange({ ...filters, priceRange: range });
  };

  const handleBrandChange = (brand: string, checked: boolean) => {
    const newBrands = checked
      ? [...filters.brands, brand]
      : filters.brands.filter(b => b !== brand);
    onFiltersChange({ ...filters, brands: newBrands });
  };

  const handleColorChange = (color: string, checked: boolean) => {
    const newColors = checked
      ? [...filters.colors, color]
      : filters.colors.filter(c => c !== color);
    onFiltersChange({ ...filters, colors: newColors });
  };

  const handleSizeChange = (size: string, checked: boolean) => {
    const newSizes = checked
      ? [...filters.sizes, size]
      : filters.sizes.filter(s => s !== size);
    onFiltersChange({ ...filters, sizes: newSizes });
  };

  const handleRatingChange = (rating: number) => {
    onFiltersChange({ ...filters, ratings: rating });
  };

  const handleStockChange = (inStock: boolean) => {
    onFiltersChange({ ...filters, inStock });
  };

  const clearAllFilters = () => {
    onFiltersChange({
      priceRange: [0, 50000],
      brands: [],
      colors: [],
      sizes: [],
      ratings: 0,
      inStock: false,
    });
  };

  const hasActiveFilters = 
    filters.brands.length > 0 ||
    filters.colors.length > 0 ||
    filters.sizes.length > 0 ||
    filters.ratings > 0 ||
    filters.inStock ||
    filters.priceRange[0] > 0 ||
    filters.priceRange[1] < 50000;

  return (
    <div 
      className="bg-white rounded-lg shadow-sm border p-6 sticky top-4"
      style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 
          className="text-lg font-semibold"
          style={{ color: 'var(--theme-text, #111827)' }}
        >
          Filters
        </h3>
        <div className="flex items-center space-x-2">
          {hasActiveFilters && (
            <button
              onClick={clearAllFilters}
              className="text-xs px-2 py-1 rounded transition-colors hover:opacity-80"
              style={{ 
                color: 'var(--theme-primary, #3b82f6)',
                backgroundColor: 'var(--theme-background, #f9fafb)',
              }}
            >
              Clear All
            </button>
          )}
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="lg:hidden p-1 rounded transition-colors hover:opacity-80"
            style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
          >
            <svg 
              className={`w-4 h-4 transition-transform ${isCollapsed ? 'rotate-180' : ''}`}
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
      </div>

      <div className={`space-y-6 ${isCollapsed ? 'hidden lg:block' : ''}`}>
        {/* Price Range */}
        <div>
          <h4 
            className="font-medium mb-3"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            Price Range
          </h4>
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <input
                type="number"
                placeholder="Min"
                value={filters.priceRange[0]}
                onChange={(e) => handlePriceRangeChange([parseInt(e.target.value) || 0, filters.priceRange[1]])}
                className="w-full px-3 py-2 text-sm border rounded focus:ring-2 focus:border-transparent"
                style={{ 
                  borderColor: 'var(--theme-border, #d1d5db)',
                  '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                }}
              />
              <span style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>-</span>
              <input
                type="number"
                placeholder="Max"
                value={filters.priceRange[1]}
                onChange={(e) => handlePriceRangeChange([filters.priceRange[0], parseInt(e.target.value) || 50000])}
                className="w-full px-3 py-2 text-sm border rounded focus:ring-2 focus:border-transparent"
                style={{ 
                  borderColor: 'var(--theme-border, #d1d5db)',
                  '--tw-ring-color': 'var(--theme-primary, #3b82f6)',
                }}
              />
            </div>
            <div className="text-xs" style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>
              ₹{filters.priceRange[0].toLocaleString()} - ₹{filters.priceRange[1].toLocaleString()}
            </div>
          </div>
        </div>

        {/* Brands */}
        {brands.length > 0 && (
          <div>
            <h4 
              className="font-medium mb-3"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              Brands
            </h4>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {brands.map((brand) => (
                <label key={brand} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.brands.includes(brand)}
                    onChange={(e) => handleBrandChange(brand, e.target.checked)}
                    className="rounded"
                    style={{ accentColor: 'var(--theme-primary, #3b82f6)' }}
                  />
                  <span 
                    className="text-sm"
                    style={{ color: 'var(--theme-text, #111827)' }}
                  >
                    {brand}
                  </span>
                </label>
              ))}
            </div>
          </div>
        )}

        {/* Colors */}
        {colors.length > 0 && (
          <div>
            <h4 
              className="font-medium mb-3"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              Colors
            </h4>
            <div className="flex flex-wrap gap-2">
              {colors.map((color) => (
                <button
                  key={color}
                  onClick={() => handleColorChange(color, !filters.colors.includes(color))}
                  className={`px-3 py-1 text-xs rounded-full border transition-all ${
                    filters.colors.includes(color)
                      ? 'border-2 shadow-md'
                      : 'hover:shadow-sm'
                  }`}
                  style={{
                    borderColor: filters.colors.includes(color) 
                      ? 'var(--theme-primary, #3b82f6)' 
                      : 'var(--theme-border, #d1d5db)',
                    backgroundColor: filters.colors.includes(color)
                      ? 'var(--theme-primary, #3b82f6)'
                      : 'transparent',
                    color: filters.colors.includes(color)
                      ? 'var(--btn-text, #ffffff)'
                      : 'var(--theme-text, #111827)',
                  }}
                >
                  {color}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Sizes */}
        {sizes.length > 0 && (
          <div>
            <h4 
              className="font-medium mb-3"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              Sizes
            </h4>
            <div className="flex flex-wrap gap-2">
              {sizes.map((size) => (
                <button
                  key={size}
                  onClick={() => handleSizeChange(size, !filters.sizes.includes(size))}
                  className={`px-3 py-1 text-xs rounded border transition-all ${
                    filters.sizes.includes(size)
                      ? 'border-2 shadow-md'
                      : 'hover:shadow-sm'
                  }`}
                  style={{
                    borderColor: filters.sizes.includes(size) 
                      ? 'var(--theme-primary, #3b82f6)' 
                      : 'var(--theme-border, #d1d5db)',
                    backgroundColor: filters.sizes.includes(size)
                      ? 'var(--theme-primary, #3b82f6)'
                      : 'transparent',
                    color: filters.sizes.includes(size)
                      ? 'var(--btn-text, #ffffff)'
                      : 'var(--theme-text, #111827)',
                  }}
                >
                  {size}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Rating */}
        <div>
          <h4 
            className="font-medium mb-3"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            Minimum Rating
          </h4>
          <div className="space-y-2">
            {[4, 3, 2, 1].map((rating) => (
              <button
                key={rating}
                onClick={() => handleRatingChange(filters.ratings === rating ? 0 : rating)}
                className={`flex items-center space-x-2 w-full text-left p-2 rounded transition-colors ${
                  filters.ratings === rating ? 'shadow-sm' : 'hover:opacity-80'
                }`}
                style={{
                  backgroundColor: filters.ratings === rating 
                    ? 'var(--theme-background, #f9fafb)' 
                    : 'transparent',
                }}
              >
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <svg
                      key={i}
                      className={`w-4 h-4 ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`}
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
                <span 
                  className="text-sm"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  {rating}+ Stars
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* In Stock */}
        <div>
          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              checked={filters.inStock}
              onChange={(e) => handleStockChange(e.target.checked)}
              className="rounded"
              style={{ accentColor: 'var(--theme-primary, #3b82f6)' }}
            />
            <span 
              className="text-sm font-medium"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              In Stock Only
            </span>
          </label>
        </div>
      </div>
    </div>
  );
};