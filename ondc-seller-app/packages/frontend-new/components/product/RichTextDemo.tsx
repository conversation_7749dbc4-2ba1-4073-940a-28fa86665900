'use client';

import React from 'react';
import { RichTextRenderer } from '@/components/ui/RichTextRenderer';

export const RichTextDemo: React.FC = () => {
  // Example rich text content (HTML)
  const richTextContent = `
    <h2>Product Overview</h2>
    <p>This is a <strong>premium quality</strong> product designed for <em>modern consumers</em>.</p>
    
    <h3>Key Features</h3>
    <ul>
      <li>High-quality materials</li>
      <li>Durable construction</li>
      <li>Modern design</li>
      <li>Easy to use</li>
    </ul>
    
    <h3>Specifications</h3>
    <table>
      <tr>
        <th>Property</th>
        <th>Value</th>
      </tr>
      <tr>
        <td>Weight</td>
        <td>2.5 kg</td>
      </tr>
      <tr>
        <td>Dimensions</td>
        <td>30 x 20 x 15 cm</td>
      </tr>
    </table>
    
    <blockquote>
      "This product has exceeded my expectations in every way." - Customer Review
    </blockquote>
    
    <p>For more information, visit our <a href="/support">support page</a>.</p>
  `;

  // Example plain text content
  const plainTextContent = `Product Overview

This is a premium quality product designed for modern consumers.

Key Features:
- High-quality materials
- Durable construction  
- Modern design
- Easy to use

Specifications:
Weight: 2.5 kg
Dimensions: 30 x 20 x 15 cm

"This product has exceeded my expectations in every way." - Customer Review

For more information, visit our support page.`;

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <h1 className="text-3xl font-bold text-center mb-8">Rich Text Renderer Demo</h1>
      
      {/* Rich Text Example */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-800">Rich Text Content (HTML)</h2>
        <div className="border rounded-lg p-4 bg-gray-50">
          <RichTextRenderer content={richTextContent} />
        </div>
      </div>

      {/* Plain Text Example */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-800">Plain Text Content</h2>
        <div className="border rounded-lg p-4 bg-gray-50">
          <RichTextRenderer content={plainTextContent} />
        </div>
      </div>

      {/* Empty Content Example */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-800">Empty Content with Fallback</h2>
        <div className="border rounded-lg p-4 bg-gray-50">
          <RichTextRenderer 
            content="" 
            fallback={
              <div className="text-center py-8 text-gray-500">
                <div className="text-4xl mb-4">📝</div>
                <p>No content available</p>
              </div>
            }
          />
        </div>
      </div>

      {/* Code Example */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-800">Usage Example</h2>
        <pre className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto">
{`import { RichTextRenderer } from '@/components/ui/RichTextRenderer';

// In your component
<RichTextRenderer
  content={product.productOverview}
  fallback={
    <div className="text-center py-8">
      <p>No overview available</p>
    </div>
  }
/>`}
        </pre>
      </div>
    </div>
  );
};