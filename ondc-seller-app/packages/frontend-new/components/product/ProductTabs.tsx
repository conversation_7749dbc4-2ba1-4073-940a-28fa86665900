'use client';

import React, { useState } from 'react';
import { Product } from '@/types/product';
import { RichTextRenderer } from '@/components/ui/RichTextRenderer';

interface ProductTabsProps {
  product: Product;
}

export const ProductTabs: React.FC<ProductTabsProps> = ({ product }) => {
  const [activeTab, setActiveTab] = useState('overview'); // Auto-select first tab

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📝' },
    { id: 'specifications', label: 'Specifications', icon: '📋' },
    { id: 'features', label: 'Features', icon: '⭐' },
    { id: 'reviews', label: 'Reviews', icon: '💬' },
  ];

  return (
    <div 
      className="bg-white rounded-lg shadow-sm border"
      style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
    >
      {/* Tab Navigation */}
      <div className="border-b" style={{ borderBottomColor: 'var(--theme-border, #e5e7eb)' }}>
        <nav className="flex space-x-8 px-6">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500'
                  : 'border-transparent hover:border-gray-300'
              }`}
              style={{
                color: activeTab === tab.id 
                  ? 'var(--theme-primary, #3b82f6)' 
                  : 'var(--theme-text-secondary, #6b7280)',
                borderBottomColor: activeTab === tab.id 
                  ? 'var(--theme-primary, #3b82f6)' 
                  : 'transparent',
              }}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {activeTab === 'overview' && (
          <div className="space-y-4">
            <RichTextRenderer
              content={product?.productOverview}
              fallback={
                <div 
                  className="text-center py-8"
                  style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                >
                  <div className="text-4xl mb-4">📝</div>
                  <h3 className="text-lg font-medium mb-2">No Overview Available</h3>
                  <p className="text-sm">
                    Product overview information will be displayed here when available.
                  </p>
                </div>
              }
            />
          </div>
        )}

        {activeTab === 'specifications' && (
          <div className="space-y-4">
            <RichTextRenderer
              content={product?.productSpecifications}
              fallback={
                <div 
                  className="text-center py-8"
                  style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                >
                  <div className="text-4xl mb-4">📋</div>
                  <h3 className="text-lg font-medium mb-2">No Specifications Available</h3>
                  <p className="text-sm">
                    Product specifications will be displayed here when available.
                  </p>
                </div>
              }
            />
          </div>
        )}

        {activeTab === 'features' && (
          <div className="space-y-4">
            <RichTextRenderer
              content={product?.productFeatures}
              fallback={
                <div 
                  className="text-center py-8"
                  style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                >
                  <div className="text-4xl mb-4">⭐</div>
                  <h3 className="text-lg font-medium mb-2">No Features Available</h3>
                  <p className="text-sm">
                    Product features will be displayed here when available.
                  </p>
                </div>
              }
            />
          </div>
        )}

        {activeTab === 'reviews' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 
                className="text-lg font-semibold"
                style={{ color: 'var(--theme-text, #111827)' }}
              >
                Customer Reviews
              </h3>
              <button
                className="px-4 py-2 text-sm font-medium rounded-lg transition-colors hover:opacity-80"
                style={{ 
                  backgroundColor: 'var(--btn-primary, #3b82f6)',
                  color: 'var(--btn-text, #ffffff)',
                }}
              >
                Write a Review
              </button>
            </div>

            {/* Rating Summary */}
            <div 
              className="p-4 rounded-lg"
              style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}
            >
              <div className="flex items-center space-x-4">
                <div className="text-center">
                  <div 
                    className="text-3xl font-bold"
                    style={{ color: 'var(--theme-primary, #3b82f6)' }}
                  >
                    {product.rating || '4.5'}
                  </div>
                  <div className="flex items-center justify-center space-x-1 mt-1">
                    {[...Array(5)].map((_, i) => (
                      <svg
                        key={i}
                        className={`w-4 h-4 ${i < Math.floor(product.rating || 4.5) ? 'text-yellow-400' : 'text-gray-300'}`}
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                  <div 
                    className="text-sm mt-1"
                    style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                  >
                    {product.reviewCount || '0'} reviews
                  </div>
                </div>
                
                <div className="flex-1">
                  <div 
                    className="text-sm"
                    style={{ color: 'var(--theme-text, #111827)' }}
                  >
                    Based on {product.reviewCount || '0'} customer reviews
                  </div>
                  <div 
                    className="text-sm mt-1"
                    style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                  >
                    {Math.round(((product.rating || 4.5) / 5) * 100)}% of customers recommend this product
                  </div>
                </div>
              </div>
            </div>

            {/* Sample Reviews */}
            <div className="space-y-4">
              {[
                {
                  name: 'Sarah Johnson',
                  rating: 5,
                  date: '2 weeks ago',
                  review: 'Excellent quality and fast shipping! Exactly what I was looking for.',
                },
                {
                  name: 'Mike Chen',
                  rating: 4,
                  date: '1 month ago',
                  review: 'Great product overall. Good value for the price. Would recommend.',
                },
                {
                  name: 'Emily Davis',
                  rating: 5,
                  date: '1 month ago',
                  review: 'Love this! Perfect fit and great quality. Will definitely buy again.',
                },
              ].map((review, index) => (
                <div 
                  key={index}
                  className="p-4 border rounded-lg"
                  style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <div 
                        className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
                        style={{ backgroundColor: 'var(--theme-primary, #3b82f6)' }}
                      >
                        {review.name.charAt(0)}
                      </div>
                      <div>
                        <div 
                          className="font-medium text-sm"
                          style={{ color: 'var(--theme-text, #111827)' }}
                        >
                          {review.name}
                        </div>
                        <div className="flex items-center space-x-1">
                          {[...Array(5)].map((_, i) => (
                            <svg
                              key={i}
                              className={`w-3 h-3 ${i < review.rating ? 'text-yellow-400' : 'text-gray-300'}`}
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                          ))}
                        </div>
                      </div>
                    </div>
                    <span 
                      className="text-xs"
                      style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                    >
                      {review.date}
                    </span>
                  </div>
                  <p 
                    className="text-sm"
                    style={{ color: 'var(--theme-text, #111827)' }}
                  >
                    {review.review}
                  </p>
                </div>
              ))}
            </div>

            {/* No Reviews State */}
            {(!product.reviewCount || product.reviewCount === 0) && (
              <div 
                className="text-center py-8"
                style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
              >
                <div className="text-4xl mb-4">💬</div>
                <h3 className="text-lg font-medium mb-2">No Reviews Yet</h3>
                <p className="text-sm mb-4">
                  Be the first to review this product and help other customers make informed decisions.
                </p>
                <button
                  className="px-6 py-2 text-sm font-medium rounded-lg transition-colors hover:opacity-80"
                  style={{ 
                    backgroundColor: 'var(--btn-primary, #3b82f6)',
                    color: 'var(--btn-text, #ffffff)',
                  }}
                >
                  Write the First Review
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};