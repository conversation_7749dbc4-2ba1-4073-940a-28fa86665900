'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Product } from '@/types/product';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { formatPrice } from '@/lib/cart/mockCartData';
import { getStoreCartAPI, calculateCartMetadata } from '@/lib/api/cart';
import { useToast } from '@/app/providers/toast-provider';

interface Category {
  id: string;
  name: string;
  slug: string;
}

interface RelatedProductsProps {
  products: Product[];
  storeHandle: string;
  currentCategory?: Category;
}

export const RelatedProducts: React.FC<RelatedProductsProps> = ({ products, storeHandle }) => {
  const [isLoading, setIsLoading] = useState<string | null>(null);
  const { showToast } = useToast();
  
  // Get store-specific cart API
  const cartAPI = getStoreCartAPI(storeHandle);
  
  if (products.length === 0) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 
            className="text-2xl font-bold"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            Related Products
          </h2>
          <p 
            className="text-sm mt-1"
            style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
          >
            {currentCategory ? `More from ${currentCategory.name}` : 'You might also like'}
          </p>
        </div>
        
        {currentCategory && (
          <Link
            href={`/${storeHandle}/categories/${currentCategory.slug}`}
            className="text-sm font-medium transition-colors hover:opacity-80"
            style={{ color: 'var(--theme-primary, #3b82f6)' }}
          >
            View All →
          </Link>
        )}
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {products.map((product) => (
          <RelatedProductCard 
            key={product.id} 
            product={product} 
            storeHandle={storeHandle} 
          />
        ))}
      </div>
    </div>
  );
};

interface RelatedProductCardProps {
  product: Product;
  storeHandle: string;
}

const RelatedProductCard: React.FC<RelatedProductCardProps> = ({ product, storeHandle }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { showToast } = useToast();
  
  const hasDiscount = product.originalPrice && product.originalPrice > product.price;
  const discountPercentage = hasDiscount 
    ? Math.round(((product.originalPrice! - product.price) / product.originalPrice!) * 100)
    : 0;

  const inStockVariants = product.variants.filter(variant => variant.stock > 0);
  const isInStock = inStockVariants.length > 0;
  
  // Handle add to cart
  const handleAddToCart = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!isInStock || isLoading) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Get the first available variant
      const selectedVariant = inStockVariants[0];
      
      if (!selectedVariant) {
        showToast('No available variant found', 'error');
        return;
      }
      
      // Get or create cart
      let cartId = cartAPI.getStoredCartId();
      
      if (!cartId) {
        // Create new cart if none exists
        const regionsResponse = await cartAPI.getRegions('Asia');
        const region = regionsResponse.regions.find(r => 
          r.name.toLowerCase().includes('asia')
        ) || regionsResponse.regions[0];
        
        if (!region) {
          throw new Error('No region available');
        }
        
        const cartResponse = await cartAPI.createCart({ region_id: region.id });
        cartId = cartResponse.cart.id;
        // Cart ID is automatically stored by IsolatedCartAPI
      }
      
      // Calculate metadata
      const metadata = calculateCartMetadata(
        product.price,
        1,
        0.08 // 8% tax
      );
      
      // Add item to cart
      await cartAPI.addLineItem(cartId, {
        variant_id: availableVariant.id,
        quantity: 1,
        metadata,
      });
      
      showToast('Product added to cart successfully!', 'success');
      
      // Trigger cart update event
      window.dispatchEvent(new CustomEvent('cartUpdated'));
      
    } catch (error) {
      console.error('Error adding to cart:', error);
      showToast('Failed to add product to cart', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Link href={`/${storeHandle}/products/${product.id}`}>
      <div 
        className="bg-white rounded-lg shadow-sm border transition-all duration-200 hover:shadow-lg hover:scale-105 group cursor-pointer"
        style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
      >
        {/* Product Image */}
        <div className="relative aspect-square overflow-hidden rounded-t-lg">
          <Image
            src={product.images[0]}
            alt={product.title}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-300"
            onError={(e) => {
              e.currentTarget.style.display = 'none';
            }}
          />
          
          {/* Fallback */}
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>

          {/* Badges */}
          <div className="absolute top-2 left-2 flex flex-col space-y-1">
            {hasDiscount && (
              <span 
                className="text-white text-xs font-bold px-2 py-1 rounded-full"
                style={{ backgroundColor: 'var(--theme-accent, #ef4444)' }}
              >
                -{discountPercentage}%
              </span>
            )}
            {!isInStock && (
              <span className="bg-gray-600 text-white text-xs font-bold px-2 py-1 rounded-full">
                Out of Stock
              </span>
            )}
          </div>

          {/* Quick Actions */}
          <div className="absolute top-2 right-2 flex flex-col space-y-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              className="p-2 bg-white rounded-full shadow-md transition-colors hover:opacity-80"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
              onClick={(e) => {
                e.preventDefault();
                alert('Added to wishlist!');
              }}
              title="Add to Wishlist"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </button>
            
            <button
              className="p-2 bg-white rounded-full shadow-md transition-colors hover:opacity-80"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
              onClick={(e) => {
                e.preventDefault();
                alert('Quick view coming soon!');
              }}
              title="Quick View"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </button>
          </div>
        </div>

        {/* Product Details */}
        <div className="p-4">
          {/* Brand */}
          <p 
            className="text-xs font-medium mb-1"
            style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
          >
            {product.brand}
          </p>

          {/* Title */}
          <h3 
            className="font-medium text-sm mb-2 line-clamp-2 group-hover:text-opacity-80"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            {product.title}
          </h3>

          {/* Rating */}
          <div className="flex items-center space-x-1 mb-2">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <svg
                  key={i}
                  className={`w-3 h-3 ${i < Math.floor(product.rating) ? 'text-yellow-400' : 'text-gray-300'}`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
            </div>
            <span 
              className="text-xs"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
            >
              ({product.reviewCount})
            </span>
          </div>

          {/* Price */}
          <div className="flex items-center space-x-2 mb-3">
            <span 
              className="font-bold text-lg"
              style={{ color: 'var(--theme-primary, #3b82f6)' }}
            >
              {formatPrice(product.price)}
            </span>
            {hasDiscount && (
              <span 
                className="text-sm line-through"
                style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
              >
                {formatPrice(product.originalPrice!)}
              </span>
            )}
          </div>

          {/* Quick Add to Cart */}
          <button
            onClick={handleAddToCart}
            disabled={!isInStock || isLoading}
            className={`w-full py-2 px-4 text-sm font-medium rounded-lg transition-all duration-200 ${
              (isInStock && !isLoading) 
                ? 'hover:shadow-lg hover:scale-105' 
                : 'opacity-50 cursor-not-allowed'
            }`}
            style={{ 
              backgroundColor: (isInStock && !isLoading) 
                ? 'var(--btn-primary, #3b82f6)' 
                : 'var(--theme-text-secondary, #6b7280)',
              color: 'var(--btn-text, #ffffff)',
            }}
          >
            {isLoading ? 'Adding...' : isInStock ? 'Add to Cart' : 'Out of Stock'}
          </button>
        </div>
      </div>
    </Link>
  );
};