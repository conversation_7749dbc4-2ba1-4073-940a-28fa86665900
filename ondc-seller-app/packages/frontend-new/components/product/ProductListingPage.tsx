'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Button,
  Chip,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Skeleton,
  Alert,
  Divider,
  Container,
  Stack,
  Breadcrumbs,
  Link,
} from '@mui/material';
import {
  Search as SearchIcon,
  ShoppingCart as CartIcon,
  Visibility as ViewIcon,
  Home as HomeIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { useSearchStore } from '@/stores/searchStore';
import { createSearchAPI } from '@/lib/api/search';
import { ProductFilters } from './ProductFilters';
import { ProductSortOptions } from './ProductSortOptions';
import { ProductGrid } from './ProductGrid';
import { BreadcrumbNavigation, BreadcrumbItem } from '../navigation/BreadcrumbNavigation';
import { useStoreProducts } from '@/hooks/useStore';
import { Product } from '@/types/product';
import { Store } from '@/types';
import { ErrorBoundary } from '../ui/ErrorBoundary';

interface ProductListingPageProps {
  store: Store | null;
  storeHandle: string;
  searchParams: {
    q?: string;
    page?: string;
    sort?: string;
    category?: string;
    collection?: string;
    price_min?: string;
    price_max?: string;
    brand?: string;
    color?: string;
    size?: string;
    rating?: string;
    in_stock?: string;
  };
}

// Product filters type
interface ProductFiltersType {
  priceRange: [number, number];
  brands: string[];
  colors: string[];
  sizes: string[];
  ratings: number;
  inStock: boolean;
}

export const ProductListingPage: React.FC<ProductListingPageProps> = ({ 
  store,
  storeHandle, 
  searchParams 
}) => {
  const router = useRouter();
  const urlSearchParams = useSearchParams();
  
  // Memoize searchAPI to prevent recreation on every render
  const searchAPI = useMemo(() => createSearchAPI(storeHandle), [storeHandle]);

  // Extract search parameters
  const searchQuery = searchParams.q || '';
  const currentPage = parseInt(searchParams.page || '1');
  const sortBy = searchParams.sort || 'created_at:desc';

  // State management
  const [products, setProducts] = useState<any[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalHits, setTotalHits] = useState(0);
  const [processingTimeMs, setProcessingTimeMs] = useState(0);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [initialized, setInitialized] = useState(false);

  // Filters state
  const [filters, setFilters] = useState<ProductFiltersType>({
    priceRange: [
      parseInt(searchParams.price_min || '0'),
      parseInt(searchParams.price_max || '50000')
    ],
    brands: searchParams.brand ? [searchParams.brand] : [],
    colors: searchParams.color ? [searchParams.color] : [],
    sizes: searchParams.size ? [searchParams.size] : [],
    ratings: parseInt(searchParams.rating || '0'),
    inStock: searchParams.in_stock === 'true',
  });

  // Search store for managing search state - use callback to prevent recreation
  const addToHistory = useSearchStore(state => state.addToHistory);

  // Fetch all store products as fallback
  const {
    data: allStoreProducts,
    isLoading: allProductsLoading,
    error: allProductsError
  } = useStoreProducts(storeHandle, 100);

  // Perform search or load all products
  useEffect(() => {
    const loadProducts = async () => {
      console.log('🔄 ProductListingPage: Starting loadProducts', {
        searchQuery,
        currentPage,
        sortBy,
        storeHandle,
        allProductsLoading
      });
      
      setIsLoading(true);
      setError(null);

      try {
        if (searchQuery.trim()) {
          // Perform search
          console.log('🔍 Performing search for:', searchQuery);
          try {
            const response = await searchAPI.searchProducts(
              storeHandle,
              searchQuery,
              {}, // filters will be applied client-side for now
              {
                limit: 50,
                offset: (currentPage - 1) * 20,
                sort: sortBy,
              }
            );

            console.log('✅ Search response:', response);
            setProducts(response.hits || []);
            setTotalHits(response.estimatedTotalHits || 0);
            setProcessingTimeMs(response.processingTimeMs || 0);
            addToHistory(searchQuery);
          } catch (searchError) {
            console.error('❌ Search API failed, falling back to all products:', searchError);
            // Fallback to showing all products if search fails
            const allProducts = allStoreProducts || [];
            setProducts(allProducts);
            setTotalHits(allProducts.length);
            setProcessingTimeMs(0);
            setError('Search is temporarily unavailable. Showing all products instead.');
          }
        } else {
          // Load all products
          console.log('📦 Loading all products');
          const allProducts = allStoreProducts || [];
          setProducts(allProducts);
          setTotalHits(allProducts.length);
          setProcessingTimeMs(0);
        }
      } catch (err) {
        console.error('❌ Failed to load products:', err);
        setError(err instanceof Error ? err.message : 'Failed to load products');
        setProducts([]);
        setTotalHits(0);
      } finally {
        setIsLoading(false);
        setInitialized(true);
        console.log('✅ ProductListingPage: loadProducts completed');
      }
    };

    if (!allProductsLoading) {
      loadProducts();
    }
  }, [
    searchQuery,
    currentPage,
    sortBy,
    storeHandle,
    searchAPI,
    allStoreProducts,
    allProductsLoading,
    addToHistory
  ]);

  // Transform and filter products
  useEffect(() => {
    if (!products || products.length === 0) {
      setFilteredProducts([]);
      return;
    }

    // Transform products to match expected format
    let transformed = products.map((data) => ({
      id: data?.id,
      title: data?.title,
      description: data?.description,
      price: data?.variants?.[0]?.metadata?.sale_price || data?.price || 0,
      originalPrice: data?.variants?.[0]?.metadata?.original_price || data?.originalPrice || 0,
      images: data?.thumbnail ? [data.thumbnail, ...(data.images || [])] : (data.images || []),
      category: data?.categories,
      subcategory: data?.categories,
      brand: data?.brand || '',
      rating: data?.rating || 0,
      reviewCount: data?.reviewCount || 0,
      variants: data?.variants || [],
      tags: data?.tags || [],
      features: data?.features || [],
      specifications: data?.specifications || [],
      createdAt: data?.created_at,
      updatedAt: data?.updated_at,
      handle: data?.handle,
      thumbnail: data?.thumbnail,
    }));

    // Apply filters
    let filtered = [...transformed];

    // Apply price filter
    filtered = filtered.filter(product => 
      product.price >= filters.priceRange[0] && product.price <= filters.priceRange[1]
    );

    // Apply brand filter
    if (filters.brands.length > 0) {
      filtered = filtered.filter(product => 
        filters.brands.includes(product.brand)
      );
    }

    // Apply color filter
    if (filters.colors.length > 0) {
      filtered = filtered.filter(product => 
        (product.variants || []).some(variant => 
          variant?.options?.color && filters.colors.includes(variant.options.color)
        )
      );
    }

    // Apply size filter
    if (filters.sizes.length > 0) {
      filtered = filtered.filter(product => 
        (product.variants || []).some(variant => 
          variant?.options?.size && filters.sizes.includes(variant.options.size)
        )
      );
    }

    // Apply rating filter
    if (filters.ratings > 0) {
      filtered = filtered.filter(product => 
        product.rating >= filters.ratings
      );
    }

    // Apply stock filter
    if (filters.inStock) {
      filtered = filtered.filter(product => 
        (product.variants || []).some(variant => (variant?.stock || 0) > 0)
      );
    }

    // Apply sorting
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case 'created_at:asc':
        filtered.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
        break;
      case 'created_at:desc':
        filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
      case 'title:asc':
        filtered.sort((a, b) => a.title.localeCompare(b.title));
        break;
      case 'title:desc':
        filtered.sort((a, b) => b.title.localeCompare(a.title));
        break;
      default:
        // Keep original order
        break;
    }

    setFilteredProducts(filtered);
  }, [products, filters, sortBy]);

  // Update URL when parameters change - memoized to prevent recreation
  const updateURL = useCallback((newParams: Partial<typeof searchParams>) => {
    const params = new URLSearchParams();
    
    // Preserve existing params and add new ones
    Object.entries({ ...searchParams, ...newParams }).forEach(([key, value]) => {
      if (value && value !== '' && value !== '1' && key !== 'page') {
        params.set(key, value);
      }
    });

    // Handle page separately
    if (newParams.page && parseInt(newParams.page) > 1) {
      params.set('page', newParams.page);
    }

    const newUrl = `/${storeHandle}/products${params.toString() ? `?${params.toString()}` : ''}`;
    router.push(newUrl, { scroll: false });
  }, [searchParams, storeHandle, router]);

  // Handle sort change - memoized
  const handleSortChange = useCallback((newSort: string) => {
    updateURL({ sort: newSort, page: '1' });
  }, [updateURL]);

  // Handle page change - memoized
  const handlePageChange = useCallback((event: React.ChangeEvent<unknown>, page: number) => {
    updateURL({ page: page.toString() });
  }, [updateURL]);

  // Handle product view - memoized
  const handleViewProduct = useCallback((product: any) => {
    router.push(`/${storeHandle}/products/${product.handle || product.id}`);
  }, [router, storeHandle]);

  // Calculate pagination
  const itemsPerPage = 20;
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentProducts = filteredProducts.slice(startIndex, endIndex);

  // Breadcrumbs - memoized
  const breadcrumbs = useMemo(() => {
    const items: BreadcrumbItem[] = [
      { label: 'Home', href: `/${storeHandle}` },
      { label: 'Products', href: `/${storeHandle}/products`, isActive: !searchQuery },
    ];
    
    if (searchQuery) {
      items.push({ label: 'Search Results', href: '', isActive: true });
    }
    
    return items;
  }, [storeHandle, searchQuery]);

  if (!initialized || isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-8"></div>
          <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-8"></div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {[...Array(8)].map((_, index) => (
              <div key={index} className="bg-gray-200 rounded-lg h-64"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Breadcrumbs */}
      <BreadcrumbNavigation items={breadcrumbs} />

      {/* Page Header */}
      <div className="mb-8">
        {searchQuery ? (
          <>
            <h1 
              className="text-3xl font-bold mb-2"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              Search result for "{searchQuery}"
            </h1>
            <p 
              className="text-lg"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
            >
              {filteredProducts.length} {filteredProducts.length === 1 ? 'product' : 'products'} found
              {processingTimeMs > 0 && ` in ${processingTimeMs}ms`}
            </p>
          </>
        ) : (
          <>
            <h1 
              className="text-3xl font-bold mb-2"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              All Products
            </h1>
            <p 
              className="text-lg"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
            >
              Browse our complete collection of {filteredProducts.length} {filteredProducts.length === 1 ? 'product' : 'products'}
            </p>
          </>
        )}
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Filters Sidebar */}
        <div className="lg:w-64 flex-shrink-0">
          <ProductFilters
            filters={filters}
            onFiltersChange={setFilters}
            products={products}
          />
        </div>

        {/* Main Content */}
        <div className="flex-1">
          {/* Sort Options */}
          <div className="mb-6">
            <ProductSortOptions
              sortBy={sortBy}
              onSortChange={handleSortChange}
              productCount={filteredProducts.length}
              viewMode={viewMode}
              onViewModeChange={setViewMode}
            />
          </div>

          {/* Product Grid */}
          {filteredProducts.length > 0 ? (
            <>
              <ProductGrid
                products={currentProducts}
                isLoading={false}
                storeHandle={storeHandle}
                viewMode={viewMode}
              />

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center mt-8">
                  <Pagination
                    count={totalPages}
                    page={currentPage}
                    onChange={handlePageChange}
                    color="primary"
                    size="large"
                    showFirstButton
                    showLastButton
                  />
                </div>
              )}
            </>
          ) : (
            /* No Results */
            <div className="text-center py-12">
              <SearchIcon className="mx-auto h-16 w-16 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchQuery ? `No results found for "${searchQuery}"` : 'No products available'}
              </h3>
              <p className="text-gray-500 mb-6">
                {searchQuery 
                  ? 'Try adjusting your search terms or filters'
                  : 'Check back later for new products'
                }
              </p>
              {searchQuery && (
                <button
                  onClick={() => updateURL({ q: '' })}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Browse All Products
                </button>
              )}
            </div>
          )}
        </div>
      </div>
      </div>
    </ErrorBoundary>
  );
};