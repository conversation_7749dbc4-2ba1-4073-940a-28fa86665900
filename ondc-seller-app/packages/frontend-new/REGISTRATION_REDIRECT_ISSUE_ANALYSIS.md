# Registration API Redirect Issue Analysis

## Problem
When the registration API fails, the application is incorrectly redirecting to `/[storeHandle]/login` instead of staying on the registration form and showing the error message.

## Root Cause
The issue is in the **Medusa Store Client** (`lib/api/clients/medusa-store-client.ts`). The client has a global 401 error handler that automatically redirects to login for ANY 401 response:

```typescript
// In medusa-store-client.ts
if (response.status === 401) {
  console.log('🚨 Medusa Store API returned 401 Unauthorized:', endpoint);
  handleUnauthorizedWithSmartRedirect(storeHandle);  // ← This causes the redirect
  
  const errorMessage = 'Authentication failed - redirecting to login';
  throw new Error(errorMessage);
}
```

## Why This Happens During Registration

1. **Registration API Call**: `/auth/customer/emailpass/register`
2. **Server Returns 401**: For validation errors, duplicate email, etc.
3. **Client Intercepts 401**: Treats it as authentication failure
4. **Auto-Redirect Triggered**: `handleUnauthorizedWithSmartRedirect()` is called
5. **User Redirected**: To `/[storeHandle]/login` instead of seeing the error

## The Problem with Current Logic

The current logic assumes that **ALL 401 responses** mean "user needs to authenticate", but during registration:
- 401 might mean "email already exists"
- 401 might mean "invalid registration data"
- 401 might mean "registration validation failed"

These are **registration errors**, not **authentication failures**.

## Solution Options

### Option 1: Endpoint-Specific 401 Handling (Recommended)
Don't auto-redirect on 401 for registration endpoints:

```typescript
// In medusa-store-client.ts
if (response.status === 401) {
  console.log('🚨 Medusa Store API returned 401 Unauthorized:', endpoint);
  
  // Don't auto-redirect for registration endpoints
  if (!endpoint.includes('/register') && !endpoint.includes('/signup')) {
    handleUnauthorizedWithSmartRedirect(storeHandle);
  }
  
  // Let the calling code handle the error
  let errorMessage = 'Authentication failed';
  try {
    const error = await response.json();
    errorMessage = error.message || errorMessage;
  } catch {
    errorMessage = `HTTP ${response.status}: ${response.statusText}`;
  }
  throw new Error(errorMessage);
}
```

### Option 2: Context-Aware Error Handling
Pass a context flag to disable auto-redirect:

```typescript
const request = async <T>(
  endpoint: string,
  options: RequestInit = {},
  storeHandle?: string,
  skipAutoRedirect?: boolean  // ← New parameter
): Promise<T> => {
  // ... existing code ...
  
  if (response.status === 401) {
    console.log('🚨 Medusa Store API returned 401 Unauthorized:', endpoint);
    
    if (!skipAutoRedirect) {
      handleUnauthorizedWithSmartRedirect(storeHandle);
    }
    
    // ... rest of error handling
  }
}
```

### Option 3: Registration-Specific Client
Create a separate client for registration that doesn't auto-redirect.

## Recommended Fix

**Option 1** is the best approach because:
1. **Minimal Code Changes**: Only affects the error handling logic
2. **Maintains Existing Behavior**: For all other endpoints
3. **Registration-Friendly**: Allows proper error handling for registration
4. **Future-Proof**: Works for any future registration-related endpoints

## Implementation

Update the `medusa-store-client.ts` to check the endpoint before auto-redirecting:

```typescript
// Handle 401 Unauthorized responses
if (response.status === 401) {
  console.log('🚨 Medusa Store API returned 401 Unauthorized:', endpoint);
  
  // Don't auto-redirect for registration/signup endpoints
  const isRegistrationEndpoint = endpoint.includes('/register') || 
                                 endpoint.includes('/signup') ||
                                 endpoint.includes('/auth/customer/emailpass/register');
  
  if (!isRegistrationEndpoint) {
    handleUnauthorizedWithSmartRedirect(storeHandle);
    const errorMessage = 'Authentication failed - redirecting to login';
    throw new Error(errorMessage);
  }
  
  // For registration endpoints, return the actual error message
  let errorMessage = 'Registration failed';
  try {
    const error = await response.json();
    errorMessage = error.message || error.error || errorMessage;
  } catch {
    errorMessage = `HTTP ${response.status}: ${response.statusText}`;
  }
  throw new Error(errorMessage);
}
```

## Expected Behavior After Fix

1. **Registration Fails**: Server returns 401 with error message
2. **Client Handles Error**: Extracts actual error message from response
3. **No Auto-Redirect**: User stays on registration form
4. **Error Displayed**: Toast/error message shows the actual registration error
5. **User Can Retry**: User can fix the issue and try again

## Testing Scenarios

1. **Valid Registration**: Should work normally
2. **Duplicate Email**: Should show "Email already exists" error, no redirect
3. **Invalid Data**: Should show validation error, no redirect
4. **Network Error**: Should show network error, no redirect
5. **Other 401 Endpoints**: Should still auto-redirect (login, profile, etc.)

This fix will resolve the registration redirect issue while maintaining security for other authenticated endpoints.