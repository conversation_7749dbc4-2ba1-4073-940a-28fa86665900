# Naming Conflict Fix Summary

## Issue
The application was throwing a compilation error: `the name 'medusaClient' is defined multiple times` in `./lib/api/client.ts`.

## Root Cause
The `lib/api/client.ts` file had a naming conflict:
1. **Import**: `import { medusaClient } from './medusa-client'` - importing the MedusaClient class instance
2. **Export**: `export const medusaClient = async <T>(...)` - exporting a function with the same name

This created a duplicate identifier error because both the imported class instance and the exported function had the same name `medusaClient`.

## Solution Applied

### 1. **Fixed lib/api/client.ts**
- ✅ **Changed import**: `import { medusaClient as medusaApiClient } from './medusa-client'`
- ✅ **Updated usage**: `medusaApiClient.getBaseUrl()` instead of `medusaClient.getBaseUrl()`
- ✅ **Kept export**: `export const medusaClient = async <T>(...)` - the function export remains unchanged
- ✅ **Fixed Strapi auth**: Commented out undefined `config.strapiAccessToken` reference

### 2. **Fixed lib/api/store.ts**
The `store.ts` file was importing both the function and the class with the same name:
- ✅ **Changed import**: `import { medusaClient as medusaStoreClient, strapiClient } from "./client"`
- ✅ **Added import**: `import { medusaClient } from "./medusa-client"` - for the class instance
- ✅ **Updated function calls**: All `medusaClient<T>(...)` calls now use `medusaStoreClient<T>(...)`
- ✅ **Kept class calls**: All `medusaClient.getBaseUrl()` calls remain unchanged

## File Changes Summary

### lib/api/client.ts
```typescript
// BEFORE (causing conflict)
import { medusaClient } from './medusa-client';
export const medusaClient = async <T>(...) => { ... }

// AFTER (conflict resolved)
import { medusaClient as medusaApiClient } from './medusa-client';
export const medusaClient = async <T>(...) => { ... }
```

### lib/api/store.ts
```typescript
// BEFORE (mixed usage)
import { medusaClient, strapiClient } from "./client";
// Used both: medusaClient<T>(...) and medusaClient.getBaseUrl()

// AFTER (clear separation)
import { medusaClient as medusaStoreClient, strapiClient } from "./client";
import { medusaClient } from "./medusa-client";
// Now uses: medusaStoreClient<T>(...) and medusaClient.getBaseUrl()
```

## Key Distinctions

### 1. **MedusaClient Class Instance** (`./medusa-client`)
- **Purpose**: Centralized API client with axios instances
- **Usage**: `medusaClient.getBaseUrl()`, `medusaClient.admin.get()`, `medusaClient.store.post()`
- **Type**: Class instance with methods

### 2. **medusaClient Function** (`./client`)
- **Purpose**: Legacy fetch-based API function for store endpoints
- **Usage**: `medusaClient<T>('/products', options)`
- **Type**: Async function that returns Promise<T>

### 3. **strapiClient Function** (`./client`)
- **Purpose**: Fetch-based API function for Strapi endpoints
- **Usage**: `strapiClient<T>('/endpoint', options)`
- **Type**: Async function that returns Promise<T>

## Verification

After these changes:
- ✅ **No naming conflicts**: Each identifier has a unique name in its scope
- ✅ **Preserved functionality**: All existing API calls continue to work
- ✅ **Clear separation**: Class methods vs. function calls are distinct
- ✅ **Type safety**: TypeScript compilation should succeed

## Usage Examples

```typescript
// Class instance usage (for base URL and axios clients)
import { medusaClient } from './medusa-client';
const baseUrl = medusaClient.getBaseUrl();
const response = await medusaClient.admin.get('/products');

// Function usage (for legacy store API calls)
import { medusaClient as medusaStoreClient } from './client';
const products = await medusaStoreClient<ProductsResponse>('/products');

// Strapi function usage
import { strapiClient } from './client';
const data = await strapiClient<StrapiResponse>('/store-configurations');
```

The naming conflict error should now be completely resolved!