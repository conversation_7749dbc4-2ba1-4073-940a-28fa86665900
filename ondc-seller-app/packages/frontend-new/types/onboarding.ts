export interface OnboardingStore {
  name: string;
  description: string;
  handle: string;
  gstNumber: string;
  // Contact Information
  ownerEmail: string; // User's email address
  storeEmail: string; // Store's email address
  phone: string;
  website: string;
  // Address Information
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  pincode: string;
  country: string;
  logo?: string;
  paymentMethods: string[];
  businessType: string;
  businessCategory: string;
}

export interface OnboardingCategory {
  id: string;
  name: string;
  handle: string;
  description: string;
  parentId?: string;
  isSubcategory?: boolean;
}

export interface OnboardingProduct {
  id: string;
  name: string;
  handle: string;
  description: string;
  originalPrice: number;
  salePrice?: number;
  stock: number;
  status: 'draft' | 'published';
  thumbnail?: string;
  images: string[];
  variants: ProductVariant[];
  categoryId: string;
  subcategoryId?: string;
}

export interface ProductVariant {
  id: string;
  name: string;
  sku: string;
  price: number;
  stock: number;
  attributes: { [key: string]: string };
}

export interface OnboardingData {
  store: OnboardingStore | null;
  categories: OnboardingCategory[];
  subcategories: OnboardingCategory[];
  products: OnboardingProduct[];
  bulkImportCompleted: boolean;
}

export interface BulkImportTemplate {
  productName: string;
  handle: string;
  description: string;
  originalPrice: number;
  salePrice?: number;
  stock: number;
  status: 'draft' | 'published';
  category: string;
  subcategory?: string;
  thumbnail?: string;
  images?: string;
  variants?: string; // JSON string of variants
}

// Business types with value/label structure
export const BUSINESS_TYPES = [
  { value: 'individual', label: 'Individual' },
  { value: 'partnership', label: 'Partnership' },
  { value: 'private_limited', label: 'Private Limited' },
  { value: 'public_limited', label: 'Public Limited' },
  { value: 'llp', label: 'Limited Liability Partnership (LLP)' },
  { value: 'proprietorship', label: 'Proprietorship' },
];

// Business categories with value/label structure
export const BUSINESS_CATEGORIES = [
  { value: 'fashion', label: 'Fashion & Apparel' },
  { value: 'electronics', label: 'Electronics' },
  { value: 'home_garden', label: 'Home & Garden' },
  { value: 'health_beauty', label: 'Health & Beauty' },
  { value: 'sports_outdoors', label: 'Sports & Outdoors' },
  { value: 'books_media', label: 'Books & Media' },
  { value: 'food_beverages', label: 'Food & Beverages' },
  { value: 'automotive', label: 'Automotive' },
  { value: 'toys_games', label: 'Toys & Games' },
  { value: 'jewelry_accessories', label: 'Jewelry & Accessories' },
  { value: 'other', label: 'Other' },
];

// Payment methods
export const PAYMENT_METHODS = [
  'Credit/Debit Cards',
  'UPI',
  'Net Banking',
  'Wallets',
  'Cash on Delivery',
  'Bank Transfer',
  'PayPal',
  'Razorpay',
  'Stripe',
  'Paytm'
];

// Indian states
export const INDIAN_STATES = [
  'Andhra Pradesh',
  'Arunachal Pradesh',
  'Assam',
  'Bihar',
  'Chhattisgarh',
  'Goa',
  'Gujarat',
  'Haryana',
  'Himachal Pradesh',
  'Jharkhand',
  'Karnataka',
  'Kerala',
  'Madhya Pradesh',
  'Maharashtra',
  'Manipur',
  'Meghalaya',
  'Mizoram',
  'Nagaland',
  'Odisha',
  'Punjab',
  'Rajasthan',
  'Sikkim',
  'Tamil Nadu',
  'Telangana',
  'Tripura',
  'Uttar Pradesh',
  'Uttarakhand',
  'West Bengal',
  'Andaman and Nicobar Islands',
  'Chandigarh',
  'Dadra and Nagar Haveli and Daman and Diu',
  'Delhi',
  'Jammu and Kashmir',
  'Ladakh',
  'Lakshadweep',
  'Puducherry'
];

// Countries
export const COUNTRIES = [
  'India',
  'United States',
  'United Kingdom',
  'Canada',
  'Australia',
  'Germany',
  'France',
  'Japan',
  'Singapore',
  'UAE',
  'Other'
];