// Product types
export interface Product {
  id: string;
  title: string;
  description: string;
  price: number;
  originalPrice?: number;
  images: string[];
  category: string;
  subcategory: string;
  brand: string;
  rating: number;
  reviewCount: number;
  variants: ProductVariant[];
  tags: string[];
  features: string[];
  specifications: { [key: string]: string };
  createdAt: string;
  updatedAt: string;
}

export interface ProductVariant {
  id: string;
  title: string;
  price?: number;
  originalPrice?: number;
  stock: number;
  sku: string;
  options: { [key: string]: string };
  images?: string[];
}

export interface ProductFilters {
  priceRange: [number, number];
  brands: string[];
  colors: string[];
  sizes: string[];
  ratings: number;
  inStock: boolean;
}

export interface FilterOption {
  value: string;
  label: string;
  count: number;
}

export interface SortOption {
  value: string;
  label: string;
}