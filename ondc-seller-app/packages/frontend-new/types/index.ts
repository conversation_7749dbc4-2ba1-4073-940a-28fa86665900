// Store related types
export interface Store {
  id: string;
  handle: string;
  name: string;
  description?: string;
  logo?: string;
  banner?: string;
  category: StoreCategory;
  subcategory?: StoreSubcategory;
  contact_info?: ContactInfo;
  social_links?: SocialLinks;
  settings?: StoreSettings;
  created_at: string;
  updated_at: string;
}

export interface StoreCategory {
  id: string;
  name: string;
  description?: string;
  icon?: string;
}

export interface StoreSubcategory {
  id: string;
  name: string;
  description?: string;
  category_id: string;
}

export interface ContactInfo {
  email?: string;
  phone?: string;
  address?: string;
  website?: string;
}

export interface SocialLinks {
  facebook?: string;
  instagram?: string;
  twitter?: string;
  linkedin?: string;
}

export interface StoreSettings {
  theme_color?: string;
  currency?: string;
  timezone?: string;
  language?: string;
}

// Product related types
export interface Product {
  id: string;
  title: string;
  description?: string;
  handle: string;
  status: 'draft' | 'published' | 'proposed' | 'rejected';
  thumbnail?: string;
  images?: ProductImage[];
  variants?: ProductVariant[];
  options?: ProductOption[];
  tags?: ProductTag[];
  type?: ProductType;
  collection?: ProductCollection;
  categories?: ProductCategory[];
  weight?: number;
  length?: number;
  height?: number;
  width?: number;
  hs_code?: string;
  origin_country?: string;
  mid_code?: string;
  material?: string;
  created_at: string;
  updated_at: string;
}

export interface ProductImage {
  id: string;
  url: string;
  metadata?: Record<string, any>;
}

export interface ProductVariant {
  id: string;
  title: string;
  sku?: string;
  barcode?: string;
  ean?: string;
  upc?: string;
  inventory_quantity: number;
  allow_backorder: boolean;
  manage_inventory: boolean;
  weight?: number;
  length?: number;
  height?: number;
  width?: number;
  origin_country?: string;
  mid_code?: string;
  material?: string;
  metadata?: Record<string, any>;
  prices: ProductPrice[];
  options: ProductOptionValue[];
}

export interface ProductPrice {
  id: string;
  currency_code: string;
  amount: number;
  min_quantity?: number;
  max_quantity?: number;
}

export interface ProductOption {
  id: string;
  title: string;
  values: ProductOptionValue[];
}

export interface ProductOptionValue {
  id: string;
  value: string;
  option_id: string;
}

export interface ProductTag {
  id: string;
  value: string;
}

export interface ProductType {
  id: string;
  value: string;
}

export interface ProductCollection {
  id: string;
  title: string;
  handle: string;
  metadata?: Record<string, any>;
}

export interface ProductCategory {
  id: string;
  name: string;
  description?: string;
  handle: string;
  is_active: boolean;
  is_internal: boolean;
  parent_category_id?: string;
  parent_category?: ProductCategory;
  category_children?: ProductCategory[];
  metadata?: Record<string, any>;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  count?: number;
  offset?: number;
  limit?: number;
}

export interface StoreResponse extends ApiResponse<Store> {}
export interface ProductsResponse extends ApiResponse<Product[]> {}
export interface CategoriesResponse extends ApiResponse<ProductCategory[]> {}

// Component Props types
export interface StoreHomeProps {
  storeHandle: string;
}

// Error types
export interface ApiError {
  message: string;
  code?: string;
  details?: Record<string, any>;
}

// Export cart types
export * from './cart';

// Export checkout types
export * from './checkout';

// Export order types
export * from './order';

// Export auth types
export * from './auth';

// Export product types
export * from './product';

// Export onboarding types
export * from './onboarding';