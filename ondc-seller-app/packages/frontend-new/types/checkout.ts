// Checkout flow types
export interface ContactInformation {
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
}

export interface Address {
  address: string;
  address2?: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface ShippingAddress extends Address {}

export interface BillingAddress extends Address {}

export interface SavedAddress {
  id: string;
  type: 'home' | 'work' | 'other';
  firstName: string;
  lastName: string;
  company?: string;
  address: string;
  address2?: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phone?: string;
  email?: string;
  isDefault: boolean;
}

export interface PaymentMethod {
  id: string;
  type: 'credit_card' | 'paypal' | 'apple_pay' | 'google_pay' | 'bank_transfer';
  name: string;
  description: string;
  icon: string;
  isAvailable: boolean;
}

export interface CreditCardDetails {
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  cardholderName: string;
}

export interface CheckoutStep {
  id: number;
  name: string;
  title: string;
  description: string;
  isCompleted: boolean;
  isActive: boolean;
}

export interface CheckoutState {
  currentStep: number;
  steps: CheckoutStep[];
  contactInfo: ContactInformation;
  shippingAddress: ShippingAddress;
  billingAddress: BillingAddress;
  useSameAddressForBilling: boolean;
  selectedPaymentMethod: PaymentMethod | null;
  creditCardDetails: CreditCardDetails;
  orderNotes: string;
  isProcessing: boolean;
}

export interface OrderSummaryData {
  items: any[];
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  currency: string;
}