// <PERSON>t types
export interface CartItem {
  id: string;
  productId: string;
  title: string;
  image: string;
  price: number;
  quantity: number;
  variant?: {
    id: string;
    title: string;
    options: { [key: string]: string };
  };
  maxQuantity?: number;
}

export interface Cart {
  id: string;
  items: CartItem[];
  totalItems: number;
  totalAmount: number;
  currency: string;
  updatedAt: string;
}

// Add to cart item interface
export interface AddToCartItem {
  productId: string;
  variantId: string;
  quantity: number;
  product: {
    id: string;
    title: string;
    price: number;
    image: string;
    variant: string;
  };
}

export interface CartContextType {
  cart: Cart;
  isOpen: boolean;
  isLoading: boolean;
  addItem: (item: Omit<CartItem, 'id'>) => void;
  addToCart: (item: AddToCartItem) => Promise<void>;
  removeItem: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  openCart: () => void;
  closeCart: () => void;
  toggleCart: () => void;
}