# API Reorganization Summary

## Overview
I have successfully reorganized the API structure to ensure proper separation between Strapi, Medusa Store, and Medusa Admin API calls with distinct function names and no overlaps.

## New API Structure

### 1. API Clients (`lib/api/clients/`)
- **`medusa-admin-client.ts`** - Dedicated Medusa Admin API client with axios
- **`medusa-store-client.ts`** - Dedicated Medusa Store API client with fetch
- **`strapi-client.ts`** - Dedicated Strapi API client with fetch and file upload support

### 2. API Services (`lib/api/services/`)
- **`medusa-admin-auth.ts`** - All Medusa Admin authentication functions
- **`medusa-store-auth.ts`** - All Medusa Store authentication and customer functions
- **`strapi-store-config.ts`** - All Strapi store configuration functions
- **`strapi-banners.ts`** - All Strapi banner management functions

### 3. Main Export (`lib/api/index.ts`)
- Centralized exports with clear naming conventions
- Backward compatibility exports
- Comprehensive naming guide

## Function Naming Convention

### Medusa Admin API Functions (Prefix: `admin`)
```typescript
// Authentication
adminLogin(credentials)
adminSignup(userData)
adminGetUserDetails(token, storeHandle)
adminUpdateOnboardingStatus(status, token, storeHandle)
adminCompleteOnboarding(userId, token, storeHandle)
adminUpdateUserProfile(userId, userData, token, storeHandle)
```

### Medusa Store API Functions (Prefix: `store`)
```typescript
// Customer Authentication
storeCustomerLogin(credentials, storeHandle)
storeCustomerRegister(registrationData, storeHandle)
storeGetCustomerDetails(token, storeHandle)
storeUpdateCustomerProfile(token, profileData, storeHandle)

// Customer Addresses
storeGetCustomerAddresses(token, storeHandle)
storeCreateCustomerAddress(token, addressData, storeHandle)
storeUpdateCustomerAddress(token, addressId, addressData, storeHandle)
storeDeleteCustomerAddress(token, addressId, storeHandle)
```

### Strapi API Functions (Prefix: `strapi`)
```typescript
// Store Configuration
strapiSaveStoreConfiguration(payload)
strapiUpdateStoreConfiguration(documentId, payload)
strapiGetStoreConfigurationByHandle(storeHandle)
strapiGetStoreConfigurationByUserId(userId)
strapiGetStoreConfigurationByDocumentId(documentId)
strapiDeleteStoreConfiguration(documentId)

// Banners
strapiGetBanners(storeHandle?)
strapiGetBannerByDocumentId(documentId)
strapiCreateBanner(payload)
strapiUpdateBannerByDocumentId(documentId, payload)
strapiDeleteBannerByDocumentId(documentId)
strapiUploadFile(file)
strapiGetActiveBannersForStore(storeHandle)
```

## Key Improvements

### 1. Environment Variable Usage
- ✅ All hardcoded URLs removed
- ✅ Proper fallback handling with error messages
- ✅ Environment variable validation

### 2. Error Handling
- ✅ Consistent error handling across all services
- ✅ 401 unauthorized handling with smart redirects
- ✅ Timeout handling for all requests

### 3. Type Safety
- ✅ Comprehensive TypeScript interfaces for all API functions
- ✅ Proper request/response typing
- ✅ Clear parameter documentation

### 4. Separation of Concerns
- ✅ No function name overlaps between services
- ✅ Clear API responsibility boundaries
- ✅ Modular and maintainable structure

## Environment Variables Required

```env
# Medusa Configuration
NEXT_PUBLIC_MEDUSA_BASE_URL=http://localhost:9000
NEXT_PUBLIC_MEDUSA_API_URL=http://localhost:9000/admin
NEXT_PUBLIC_MEDUSA_STORE_API_URL=http://localhost:9000/store
NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=your_medusa_publishable_key

# Strapi Configuration
NEXT_PUBLIC_STRAPI_BASE_URL=http://localhost:1337
NEXT_PUBLIC_STRAPI_API_URL=http://localhost:1337
NEXT_PUBLIC_STRAPI_API_TOKEN=your_strapi_api_token
NEXT_PUBLIC_STRAPI_ACCESS_TOKEN=your_strapi_access_token
```

## Updated Files

### New Files Created:
1. `lib/api/clients/medusa-admin-client.ts`
2. `lib/api/clients/medusa-store-client.ts`
3. `lib/api/clients/strapi-client.ts`
4. `lib/api/services/medusa-admin-auth.ts`
5. `lib/api/services/medusa-store-auth.ts`
6. `lib/api/services/strapi-store-config.ts`
7. `lib/api/services/strapi-banners.ts`
8. `lib/api/index.ts` (updated)

### Existing Files Updated:
1. `lib/api/auth.ts` - Removed hardcoded Strapi URL
2. `lib/api/strapi.ts` - Removed all hardcoded URLs
3. `stores/storeConfigStore.ts` - Fixed hardcoded URL in console.log
4. `components/auth/StoreAuthModal.tsx` - Updated to use new API services

## Usage Examples

### Using Medusa Admin API:
```typescript
import { medusaAdminAuthService } from '@/lib/api';

// Admin login
const response = await medusaAdminAuthService.adminLogin({
  email: '<EMAIL>',
  password: 'password'
});

// Get admin user details
const user = await medusaAdminAuthService.adminGetUserDetails(token, storeHandle);
```

### Using Medusa Store API:
```typescript
import { medusaStoreAuthService } from '@/lib/api';

// Customer login
const response = await medusaStoreAuthService.storeCustomerLogin({
  email: '<EMAIL>',
  password: 'password'
}, storeHandle);

// Get customer details
const customer = await medusaStoreAuthService.storeGetCustomerDetails(token, storeHandle);
```

### Using Strapi API:
```typescript
import { strapiStoreConfigService, strapiBannersService } from '@/lib/api';

// Get store configuration
const config = await strapiStoreConfigService.strapiGetStoreConfigurationByHandle(storeHandle);

// Get banners
const banners = await strapiBannersService.strapiGetActiveBannersForStore(storeHandle);
```

## Benefits

1. **Clear Separation**: No confusion about which API is being used
2. **No Name Conflicts**: Each service has unique function names
3. **Type Safety**: Full TypeScript support with proper interfaces
4. **Environment Variable Compliance**: No hardcoded URLs anywhere
5. **Maintainable**: Easy to add new functions or modify existing ones
6. **Consistent**: Same patterns across all services
7. **Error Handling**: Robust error handling and timeout management
8. **Backward Compatibility**: Existing code continues to work during migration

## Next Steps

1. **Component Migration**: Update remaining components to use new API services
2. **Testing**: Test all API functions with proper environment variables
3. **Documentation**: Update component documentation to reference new API functions
4. **Legacy Cleanup**: Remove old API files once migration is complete

This reorganization ensures that your API structure is clean, maintainable, and follows best practices for separation of concerns.