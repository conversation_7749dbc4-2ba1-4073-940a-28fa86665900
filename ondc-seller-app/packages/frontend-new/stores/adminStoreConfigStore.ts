import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

type StoreDetails = {
    id?: number;
    documentId?: string;
    storeName: string;
    storeHandle?: string;
    storeDescription?: string;
    gstNumber?: string;
    addressLine1: string;
    addressLine2: string;
    city?: string;
    state?: string;
    pincode?: string;
    country?: string;
    phone?: string;
    email?: string;
    website?: string | null;
    businessType: string;
    businessCategory: string;
    onboardingCompleted?: boolean;
    onboardingStep?: number;
    userId?: string;
    medusaStoreId?: string | null;
    storeSettings?: any;
    socialMedia?: any;
    operatingHours?: any;
    shippingZones?: any;
    paymentMethods?: {
      upi?: boolean;
      bnpl?: boolean;
      wallet?: boolean;
      debitCard?: boolean;
      creditCard?: boolean;
      netBanking?: boolean;
      cashOnDelivery?: boolean;
    };
    taxSettings?: any;
    notificationPreferences?: any;
    storeStatus?: string;
    createdByUser?: string;
    lastUpdatedBy?: string | null;
    createdAt?: string;
    updatedAt?: string;
    publishedAt?: string;
    storeLogoUrl?: string;
    storeEmail?: string | null;
    storeColorPalette?: {
      muted?: string;
      vibrant?: string;
      dominant?: string;
      mutedDark?: string;
      mutedLight?: string;
      population?: number;
      extractedAt?: string;
      vibrantDark?: string;
      vibrantLight?: string;
    };
    storeLogo?: any;
  };
  
  type StoreConfigType = {
    storeDetails: StoreDetails;
    setStoreDetails: (storeDetails: StoreDetails) => void;
    resetStoreDetails: () => void;
  };
  
  const defaultStoreDetails: StoreDetails = {
    addressLine1: "",
    addressLine2: "",
    businessCategory: "",
    businessType: "",
    storeName: "",
  };
  
  const createStoreConfigStore = (storeHandle: string) =>
    create<StoreConfigType>()(
      persist(
        (set, get) => ({
          storeDetails: defaultStoreDetails,
          setStoreDetails: (storeDetails: StoreDetails) => set({ storeDetails }),
          resetStoreDetails: () => set({ storeDetails: defaultStoreDetails }),
        }),
        {
          name: `admin-store-details-${storeHandle}`,
          storage: createJSONStorage(() => sessionStorage),
        },
      ),
    );
  
  // Export the factory function
  export { createStoreConfigStore };
