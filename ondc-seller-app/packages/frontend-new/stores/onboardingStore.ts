import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { useAuthStore } from './authStore';

interface StoreConfigurationData {
  // Basic Store Info
  store_name: string;
  store_description: string;
  gst_number: string;
  business_type: string;
  business_category: string;

  // Contact Info
  owner_email: string; // User's email address
  store_email: string; // Store's email address
  phone: string;
  website?: string;

  // Address
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  pincode: string;
  country: string;

  // Branding
  store_theme: string;
  store_logo?: File;

  // System fields
  user_id: string;
  onboarding_step: number;
  onboarding_completed: boolean;
}

interface ProductData {
  name: string;
  price: string;
  description: string;
  category: string;
  sku?: string;
  inventory_quantity: string;
}

interface OnboardingState {
  currentStep: number;
  storeData: StoreConfigurationData;
  productData: ProductData;
  bulkUploadFile: File | null;
  isLoading: boolean;
  error: string | null;
  completedSteps: boolean[];
  hasUnsavedChanges: boolean;
  stepDataCache: Record<number, any>;

  // Actions
  setCurrentStep: (step: number) => void;
  updateStoreData: (data: Partial<StoreConfigurationData>) => void;
  updateProductData: (data: Partial<ProductData>) => void;
  setBulkUploadFile: (file: File | null) => void;
  setError: (error: string | null) => void;
  setHasUnsavedChanges: (hasChanges: boolean) => void;
  
  // Cache management
  cacheStepData: (step: number, data: any) => void;
  hasStepDataChanged: (step: number, currentData: any) => boolean;
  clearStepCache: () => void;
  
  // Backend integration
  initializeFromBackend: (user: any) => void;
  initializeFromAuthStore: () => void;
  saveStoreConfiguration: () => Promise<void>;
  saveProduct: () => Promise<void>;
  handleBulkUpload: () => Promise<void>;
  completeOnboarding: () => Promise<void>;
  updateBackendOnboardingStatus: (status: any) => Promise<void>;
}

const defaultStoreData: StoreConfigurationData = {
  store_name: '',
  store_description: '',
  gst_number: '',
  business_type: '',
  business_category: '',
  owner_email: '',
  store_email: '',
  phone: '',
  website: '',
  address_line_1: '',
  address_line_2: '',
  city: '',
  state: '',
  pincode: '',
  country: 'India',
  store_theme: 'modern',
  store_logo: undefined,
  user_id: '',
  onboarding_step: 0,
  onboarding_completed: false,
};

const defaultProductData: ProductData = {
  name: '',
  price: '',
  description: '',
  category: '',
  sku: '',
  inventory_quantity: '',
};

export const useOnboardingStore = create<OnboardingState>()(persist(
  (set, get) => ({
    currentStep: 0,
    storeData: defaultStoreData,
    productData: defaultProductData,
    bulkUploadFile: null,
    isLoading: false,
    error: null,
    completedSteps: [false, false, false],
    hasUnsavedChanges: false,
    stepDataCache: {},

    setCurrentStep: (step) => {
      set({ currentStep: step });
    },

    updateStoreData: (data) => {
      set((state) => ({
        storeData: { ...state.storeData, ...data },
        hasUnsavedChanges: true,
      }));
    },

    updateProductData: (data) => {
      set((state) => ({
        productData: { ...state.productData, ...data },
        hasUnsavedChanges: true,
      }));
    },

    setBulkUploadFile: (file) => {
      set({ bulkUploadFile: file });
    },

    setError: (error) => {
      set({ error });
    },

    setHasUnsavedChanges: (hasChanges) => {
      set({ hasUnsavedChanges: hasChanges });
    },

    cacheStepData: (step, data) => {
      set((state) => ({
        stepDataCache: {
          ...state.stepDataCache,
          [step]: JSON.parse(JSON.stringify(data)),
        },
      }));
    },

    hasStepDataChanged: (step, currentData) => {
      const state = get();
      const cachedData = state.stepDataCache[step];
      
      if (!cachedData) {
        return true; // No cache means it's new data
      }
      
      return JSON.stringify(cachedData) !== JSON.stringify(currentData);
    },

    clearStepCache: () => {
      set({ stepDataCache: {} });
    },

    initializeFromBackend: (user) => {
      console.log('=== INITIALIZING ONBOARDING FROM BACKEND ===');
      console.log('User data:', user);
      
      // Initialize with user data if available
      if (user) {
        const updatedStoreData = {
          ...get().storeData,
          user_id: user.id || '',
          owner_email: user.email || '',
          store_email: user.email || '',
          // Add any other user fields that should pre-fill the form
        };
        
        set({ storeData: updatedStoreData });
      }
    },

    initializeFromAuthStore: () => {
      console.log('=== INITIALIZING ONBOARDING FROM AUTH STORE ===');
      
      const authState = useAuthStore.getState();
      const user = authState.user;
      
      console.log('Auth store user:', user);
      
      if (user) {
        // Handle nested user structure from API response
        const userData = user.user || user;
        const userMetadata = userData?.metadata || user?.metadata || {};
        
        const updatedStoreData = {
          ...get().storeData,
          user_id: userData?.id || user?.id || '',
          owner_email: userData?.email || user?.email || '',
          store_email: userMetadata.store_email || userData?.email || user?.email || '',
          store_name: userMetadata.store_name || '',
          phone: userMetadata.phone || userMetadata.contact_number || '',
          website: userMetadata.website || '',
          // Pre-fill address data
          address_line_1: userMetadata.address_line_1 || userMetadata.address || '',
          address_line_2: userMetadata.address_line_2 || '',
          city: userMetadata.city || '',
          state: userMetadata.state || '',
          pincode: userMetadata.pincode || '',
          country: userMetadata.country || 'India',
          // Pre-fill business data
          business_type: userMetadata.business_type || '',
          business_category: userMetadata.business_category || '',
          gst_number: userMetadata.gst_number || '',
        };
        
        console.log('Pre-filling store data:', updatedStoreData);
        set({ storeData: updatedStoreData });
      }
    },

    saveStoreConfiguration: async () => {
      set({ isLoading: true, error: null });
      try {
        // TODO: Implement actual API call to save store configuration
        console.log('Saving store configuration:', get().storeData);
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        set({ hasUnsavedChanges: false });
      } catch (error: any) {
        set({ error: error.message || 'Failed to save store configuration' });
        throw error;
      } finally {
        set({ isLoading: false });
      }
    },

    saveProduct: async () => {
      set({ isLoading: true, error: null });
      try {
        // TODO: Implement actual API call to save product
        console.log('Saving product:', get().productData);
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        set({ hasUnsavedChanges: false });
      } catch (error: any) {
        set({ error: error.message || 'Failed to save product' });
        throw error;
      } finally {
        set({ isLoading: false });
      }
    },

    handleBulkUpload: async () => {
      const { bulkUploadFile } = get();
      if (!bulkUploadFile) return;
      
      set({ isLoading: true, error: null });
      try {
        // TODO: Implement actual bulk upload logic
        console.log('Processing bulk upload:', bulkUploadFile.name);
        
        // Simulate processing
        await new Promise(resolve => setTimeout(resolve, 2000));
        
      } catch (error: any) {
        set({ error: error.message || 'Failed to process bulk upload' });
        throw error;
      } finally {
        set({ isLoading: false });
      }
    },

    completeOnboarding: async () => {
      set({ isLoading: true, error: null });
      try {
        // TODO: Implement API call to mark onboarding as completed
        console.log('Completing onboarding...');
        
        // Update store data
        set((state) => ({
          storeData: {
            ...state.storeData,
            onboarding_completed: true,
          },
          completedSteps: [true, true, true],
        }));
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error: any) {
        set({ error: error.message || 'Failed to complete onboarding' });
        throw error;
      } finally {
        set({ isLoading: false });
      }
    },

    updateBackendOnboardingStatus: async (status) => {
      try {
        // TODO: Implement API call to update onboarding status
        console.log('Updating backend onboarding status:', status);
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        
      } catch (error: any) {
        console.error('Failed to update backend onboarding status:', error);
        throw error;
      }
    },
  }),
  {
    name: 'onboarding-storage',
    partialize: (state) => ({
      currentStep: state.currentStep,
      storeData: state.storeData,
      productData: state.productData,
      completedSteps: state.completedSteps,
      stepDataCache: state.stepDataCache,
    }),
  }
));