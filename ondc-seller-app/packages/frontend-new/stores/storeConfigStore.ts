import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { OnboardingStore } from '@/types/onboarding';
import { ColorPalette } from '@/lib/utils/colorPalette';
import { strapiStoreService, type NormalizedStore } from '@/lib/api/strapi-store';
// Removed complex storage wrapper

export interface StoreConfiguration {
  // Basic Information
  store_name: string;
  store_description: string;
  store_handle: string;
  gst_number: string;
  
  // Contact Information
  owner_email: string;
  store_email: string;
  phone: string;
  website: string;
  
  // Address Information
  address_line_1: string;
  address_line_2: string;
  city: string;
  state: string;
  pincode: string;
  country: string;
  
  // Business Information
  business_type: string;
  business_category: string;
  
  // Store Branding
  store_logo_url: string;
  store_color_palette: ColorPalette | null;
  
  // Payment Methods
  payment_methods: {
    upi: boolean;
    bnpl: boolean;
    wallet: boolean;
    debit_card: boolean;
    credit_card: boolean;
    net_banking: boolean;
    cash_on_delivery: boolean;
  };
  
  // Metadata
  user_id: string;
  created_by_user: string;
  store_status: string;
  onboarding_completed: boolean;
  onboarding_step: number;
  
  // Tracking
  strapi_document_id?: string; // To track if this is an update
  last_updated: string;
}

interface StoreConfigState {
  storeConfig: StoreConfiguration | null;
  hasUnsavedChanges: boolean;
  
  // Store data fetching state
  storeData: NormalizedStore | null;
  isLoading: boolean;
  error: string | null;
  currentStoreHandle: string | null; // Track current store handle
  
  // Actions
  setStoreConfig: (config: StoreConfiguration) => void;
  updateStoreConfig: (updates: Partial<StoreConfiguration>) => void;
  clearStoreConfig: () => void;
  markAsSaved: (documentId?: string) => void;
  markAsChanged: () => void;
  hasChanges: () => boolean;
  isFieldModified: (fieldName: string, newValue: any) => boolean;
  
  // Store data fetching with auto-clear functionality
  fetchStoreConfig: (storeHandle: string, forceRefresh?: boolean) => Promise<void>;
  setStoreData: (data: NormalizedStore | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearStoreData: () => void;
  
  // Store handle management
  setCurrentStoreHandle: (storeHandle: string) => void;
  isStoreCacheValid: (storeHandle: string) => boolean;
  
  // Conversion helpers
  fromOnboardingStore: (onboardingStore: OnboardingStore, userId: string, createdBy: string) => StoreConfiguration;
  toStrapiPayload: (excludeFields?: string[]) => any;
}

// Store instances cache for isolated stores
const storeConfigInstances = new Map<string, ReturnType<typeof createStoreConfigStore>>();

/**
 * Create store-specific config store with isolated persistence
 */
export const createStoreConfigStore = (storeHandle: string) => {
  console.log(`🔒 Creating isolated config store for: ${storeHandle}`);
  
  return create<StoreConfigState>()(persist(
    (set, get) => ({
      storeConfig: null,
      hasUnsavedChanges: false,
      
      // Store data fetching state
      storeData: null,
      isLoading: false,
      error: null,
      currentStoreHandle: storeHandle,
    
      setStoreConfig: (config) => {
        console.log(`📝 Setting store config for ${storeHandle}:`, config);
        set({ 
          storeConfig: { ...config, last_updated: new Date().toISOString() },
          hasUnsavedChanges: false,
          currentStoreHandle: storeHandle,
        });
      },
    
      updateStoreConfig: (updates) => {
        const currentConfig = get().storeConfig;
        if (!currentConfig) return;
        
        console.log(`🔄 Updating store config for ${storeHandle}:`, updates);
        
        const updatedConfig = {
          ...currentConfig,
          ...updates,
          last_updated: new Date().toISOString()
        };
        
        set({ 
          storeConfig: updatedConfig,
          hasUnsavedChanges: true 
        });
      },
    
      clearStoreConfig: () => {
        console.log(`🧹 Clearing store config for ${storeHandle}`);
        set({ 
          storeConfig: null,
          hasUnsavedChanges: false,
          storeData: null,
          error: null,
        });
      },
    
      markAsSaved: (documentId) => {
        const currentConfig = get().storeConfig;
        if (!currentConfig) return;
        
        console.log(`✅ Marking store config as saved for ${storeHandle}:`, documentId);
        
        set({ 
          storeConfig: {
            ...currentConfig,
            strapi_document_id: documentId || currentConfig.strapi_document_id,
            last_updated: new Date().toISOString()
          },
          hasUnsavedChanges: false 
        });
      },
    
      markAsChanged: () => {
        set({ hasUnsavedChanges: true });
      },
      
      hasChanges: () => {
        return get().hasUnsavedChanges;
      },
    
    isFieldModified: (fieldName, newValue) => {
      const currentConfig = get().storeConfig;
      if (!currentConfig) return true; // If no existing config, consider it modified
      
      // Map form field names to store config field names
      const fieldMapping: { [key: string]: string } = {
        handle: 'store_handle',
        name: 'store_name',
        description: 'store_description',
        gstNumber: 'gst_number',
        ownerEmail: 'owner_email',
        storeEmail: 'store_email',
        phone: 'phone',
        website: 'website',
        addressLine1: 'address_line_1',
        addressLine2: 'address_line_2',
        city: 'city',
        state: 'state',
        pincode: 'pincode',
        country: 'country',
        businessType: 'business_type',
        businessCategory: 'business_category',
      };
      
      const configFieldName = fieldMapping[fieldName] || fieldName;
      const currentValue = (currentConfig as any)[configFieldName];
      
      console.log(`Checking if ${fieldName} (${configFieldName}) is modified:`);
      console.log('Current value:', currentValue);
      console.log('New value:', newValue);
      console.log('Is modified:', currentValue !== newValue);
      
      return currentValue !== newValue;
    },
    
    fromOnboardingStore: (onboardingStore, userId, createdBy) => {
      console.log('=== CONVERTING FROM ONBOARDING STORE ===');
      console.log('Onboarding store:', onboardingStore);
      
      // Convert payment methods array to object format
      const paymentMethods = onboardingStore.paymentMethods || [];
      const paymentMethodsObj = {
        upi: paymentMethods.includes('UPI'),
        bnpl: paymentMethods.includes('BNPL'),
        wallet: paymentMethods.includes('Wallets'),
        debit_card: paymentMethods.includes('Credit/Debit Cards'),
        credit_card: paymentMethods.includes('Credit/Debit Cards'),
        net_banking: paymentMethods.includes('Net Banking'),
        cash_on_delivery: paymentMethods.includes('Cash on Delivery'),
      };
      
      const config: StoreConfiguration = {
        store_name: onboardingStore.name || '',
        store_description: onboardingStore.description || '',
        store_handle: onboardingStore.handle || '',
        gst_number: onboardingStore.gstNumber || '',
        owner_email: onboardingStore.ownerEmail || '',
        store_email: onboardingStore.storeEmail || '',
        phone: onboardingStore.phone || '',
        website: onboardingStore.website || '',
        address_line_1: onboardingStore.addressLine1 || '',
        address_line_2: onboardingStore.addressLine2 || '',
        city: onboardingStore.city || '',
        state: onboardingStore.state || '',
        pincode: onboardingStore.pincode || '',
        country: onboardingStore.country || 'India',
        business_type: onboardingStore.businessType || '',
        business_category: onboardingStore.businessCategory || '',
        store_logo_url: onboardingStore.logo || '',
        store_color_palette: null, // Will be set separately
        payment_methods: paymentMethodsObj,
        user_id: userId,
        created_by_user: createdBy,
        store_status: 'active',
        onboarding_completed: false,
        onboarding_step: 1,
        last_updated: new Date().toISOString(),
      };
      
      console.log('Converted config:', config);
      return config;
    },
    
    // Store data fetching with auto-clear functionality
    fetchStoreConfig: async (storeHandle: string, forceRefresh = false) => {
      const currentState = get();
      
      // Check if store handle has changed - if so, clear all data first
      if (currentState.currentStoreHandle && currentState.currentStoreHandle !== storeHandle) {
        console.log('=== STORE HANDLE CHANGED - CLEARING DATA ===');
        console.log('Previous handle:', currentState.currentStoreHandle);
        console.log('New handle:', storeHandle);
        
        // Clear all store data from Zustand
        set({ 
          storeData: null,
          storeConfig: null,
          hasUnsavedChanges: false,
          error: null,
          currentStoreHandle: storeHandle
        });
        
        // Clear localStorage data for the previous store
        if (typeof window !== 'undefined') {
          try {
            // Clear theme-related localStorage
            localStorage.removeItem('store-theme');
            localStorage.removeItem(`store-config-${currentState.currentStoreHandle}`);
            
            console.log('Cleared localStorage for previous store:', currentState.currentStoreHandle);
          } catch (error) {
            console.warn('Error clearing localStorage:', error);
          }
        }
      }
      
      // Check if we need to fetch (new store handle, force refresh, or no existing data)
      const shouldFetch = forceRefresh || 
                         !currentState.storeData || 
                         currentState.storeData.handle !== storeHandle ||
                         currentState.currentStoreHandle !== storeHandle;
      
      if (!shouldFetch) {
        console.log('Store config already loaded for handle:', storeHandle);
        return;
      }
      
      set({ isLoading: true, error: null, currentStoreHandle: storeHandle });
      
      try {
        console.log('=== FETCHING STORE CONFIG FROM STRAPI ===');
        console.log('Store handle:', storeHandle);
        const strapiApiUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || process.env.NEXT_PUBLIC_STRAPI_BASE_URL + '/api';
        const baseUrl = strapiApiUrl;
        console.log('API URL:', `${baseUrl}/api/store-configurations?filters[store_handle][$eq]=${storeHandle}&populate=*`);
        
        const result = await strapiStoreService.getStoreConfigByHandle(storeHandle);
        
        if (result) {
          console.log('Store config fetched successfully:', result.normalizedData);
          console.log('Raw API response:', result.rawResponse);
          
          // Extract store_color_palette from raw response for easy access
          const colorPalette = result.rawResponse.data?.[0]?.store_color_palette || null;
          console.log('Extracted store_color_palette:', colorPalette);
          
          // Store both normalized data and raw response
          const enhancedStoreData = {
            ...result.normalizedData,
            // Ensure store_color_palette is easily accessible
            store_color_palette: colorPalette,
            // Store raw response for complete access
            _rawResponse: result.rawResponse
          };
          
          set({ 
            storeData: enhancedStoreData,
            isLoading: false,
            error: null,
            currentStoreHandle: storeHandle
          });
          
          // Persist store config to localStorage for this specific store
          if (typeof window !== 'undefined') {
            try {
              localStorage.setItem(`store-config-${storeHandle}`, JSON.stringify({
                storeData: enhancedStoreData,
                timestamp: Date.now(),
                storeHandle
              }));
              console.log('Persisted store config to localStorage for:', storeHandle);
            } catch (error) {
              console.warn('Error persisting to localStorage:', error);
            }
          }
        } else {
          console.log('No store config found for handle:', storeHandle);
          set({ 
            storeData: null,
            isLoading: false,
            error: `Store not found: ${storeHandle}`,
            currentStoreHandle: storeHandle
          });
        }
      } catch (error) {
        console.error('Error fetching store config:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch store configuration';
        set({ 
          storeData: null,
          isLoading: false,
          error: errorMessage,
          currentStoreHandle: storeHandle
        });
        throw error;
      }
    },
    
    setStoreData: (data) => set({ storeData: data }),
    setLoading: (loading) => set({ isLoading: loading }),
    setError: (error) => set({ error }),
    
    clearStoreData: () => {
      console.log('=== CLEARING STORE DATA ===');
      set({ 
        storeData: null, 
        storeConfig: null, 
        hasUnsavedChanges: false, 
        error: null,
        currentStoreHandle: null
      });
      
      // Clear localStorage
      if (typeof window !== 'undefined') {
        try {
          const currentHandle = get().currentStoreHandle;
          if (currentHandle) {
            localStorage.removeItem(`store-config-${currentHandle}`);
          }
          localStorage.removeItem('store-theme');
          console.log('Cleared localStorage data');
        } catch (error) {
          console.warn('Error clearing localStorage:', error);
        }
      }
    },
    
    setCurrentStoreHandle: (storeHandle) => {
      set({ currentStoreHandle: storeHandle });
    },
    
    isStoreCacheValid: (storeHandle: string) => {
      const currentState = get();
      return currentState.storeData !== null && 
             currentState.storeData.handle === storeHandle &&
             currentState.currentStoreHandle === storeHandle;
    },
    
    toStrapiPayload: (excludeFields = []) => {
      const config = get().storeConfig;
      if (!config) return null;
      
      console.log('=== CONVERTING TO STRAPI PAYLOAD ===');
      console.log('Excluding fields:', excludeFields);
      
      const payload: any = {
        country: config.country,
        onboarding_completed: config.onboarding_completed,
        onboarding_step: config.onboarding_step,
        store_status: config.store_status,
        store_name: config.store_name,
        store_description: config.store_description,
        gst_number: config.gst_number,
        address_line_1: config.address_line_1,
        address_line_2: config.address_line_2,
        city: config.city,
        state: config.state,
        pincode: config.pincode,
        phone: config.phone,
        email: config.owner_email,
        store_email: config.store_email,
        business_type: config.business_type,
        business_category: config.business_category,
        user_id: config.user_id,
        created_by_user: config.created_by_user,
        payment_methods: config.payment_methods,
        store_color_palette: config.store_color_palette || undefined,
        store_logo_url: config.store_logo_url || undefined,
      };
      console.log("excludeFields===========<><><><>",{excludeFields, delete:!excludeFields.includes('store_handle')})
      // Only include store_handle if not excluded
      if (!excludeFields.includes('store_handle')) {
        payload.store_handle = config.store_handle;
      }
      
      // Remove any excluded fields
      excludeFields.forEach(field => {
        delete payload[field];
      });
      
      console.log('Strapi payload:', payload);
      return payload;
    },
    }),
    {
      name: `${storeHandle}-config`, // Simple naming: {storeHandle}-config
      partialize: (state) => ({
        storeConfig: state.storeConfig,
        hasUnsavedChanges: state.hasUnsavedChanges,
        storeData: state.storeData,
        currentStoreHandle: state.currentStoreHandle,
      }),
    }
  ));
};

/**
 * Get or create store-specific config store
 */
export const getStoreConfigStore = (storeHandle: string) => {
  if (!storeHandle) {
    console.warn('No store handle provided for config store');
    return null;
  }
  
  if (!storeConfigInstances.has(storeHandle)) {
    const store = createStoreConfigStore(storeHandle);
    storeConfigInstances.set(storeHandle, store);
    console.log(`✨ Created new config store instance for: ${storeHandle}`);
  }
  
  return storeConfigInstances.get(storeHandle)!;
};

/**
 * Clear config store for specific store
 */
export const clearStoreConfigStore = (storeHandle: string) => {
  const store = storeConfigInstances.get(storeHandle);
  if (store) {
    store.getState().clearStoreData();
    storeConfigInstances.delete(storeHandle);
    console.log(`🗑️ Cleared config store for: ${storeHandle}`);
  }
};

/**
 * Hook to use store-specific config - maintains backward compatibility
 */
export const useStoreConfigStore = (storeHandle?: string) => {
  // If no store handle provided, use a default store for backward compatibility
  const handle = storeHandle || 'default';
  const store = getStoreConfigStore(handle);
  
  if (!store) {
    console.error(`No config store available for store handle: ${handle}`);
    // Return a fallback store to prevent crashes
    return createStoreConfigStore('fallback')();
  }
  
  return store();
};