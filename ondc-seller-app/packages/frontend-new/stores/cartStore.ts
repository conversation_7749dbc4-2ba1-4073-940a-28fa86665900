'use client';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { createStoreSpecificStorage } from '@/lib/utils/storeIsolation/zustandMiddleware';
import { Cart, CartItem, AddToCartItem } from '@/types/cart';
import { calculateCartTotals } from '@/lib/cart/mockCartData';

interface CartState {
  items: CartItem[];
  isOpen: boolean;
  isLoading: boolean;
  
  // Actions
  addItem: (item: Omit<CartItem, 'id'>) => void;
  addToCart: (item: AddToCartItem) => Promise<void>;
  removeItem: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  openCart: () => void;
  closeCart: () => void;
  toggleCart: () => void;
  
  // Computed properties
  getCart: () => Cart;
}

// Store instances cache for isolated stores
const cartStoreInstances = new Map<string, ReturnType<typeof createCartStore>>();

/**
 * Create store-specific cart store with isolated persistence
 */
export const createCartStore = (storeHandle: string) => {
  console.log(`🔒 Creating isolated cart store for: ${storeHandle}`);
  
  return create<CartState>()(persist(
    (set, get) => ({
      items: [],
      isOpen: false,
      isLoading: false,
      
      // Add item to cart (legacy function)
      addItem: (newItem: Omit<CartItem, 'id'>) => {
        console.log(`➕ Adding item to cart for ${storeHandle}:`, newItem.title);
        set({ isLoading: true });
        
        setTimeout(() => {
          set((state) => {
            const existingItemIndex = state.items.findIndex(
              item => item.productId === newItem.productId && 
                      item.variant?.id === newItem.variant?.id
            );

            let updatedItems;
            if (existingItemIndex >= 0) {
              // Update existing item quantity
              updatedItems = [...state.items];
              const existingItem = updatedItems[existingItemIndex];
              const newQuantity = existingItem.quantity + newItem.quantity;
              const maxQuantity = existingItem.maxQuantity || 99;
              
              updatedItems[existingItemIndex] = {
                ...existingItem,
                quantity: Math.min(newQuantity, maxQuantity)
              };
            } else {
              // Add new item
              const cartItem: CartItem = {
                ...newItem,
                id: `cart-item-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              };
              updatedItems = [...state.items, cartItem];
            }
            
            return {
              items: updatedItems,
              isLoading: false,
              isOpen: true // Open cart when item is added
            };
          });
        }, 300); // Simulate API delay
      },

      // Add to cart function (new interface)
      addToCart: async (item: AddToCartItem) => {
        console.log(`🛒 Adding to cart for ${storeHandle}:`, item.product.title);
        set({ isLoading: true });
        
        return new Promise<void>((resolve) => {
          setTimeout(() => {
            const cartItem: Omit<CartItem, 'id'> = {
              productId: item.productId,
              title: item.product.title,
              image: item.product.image,
              price: item.product.price,
              quantity: item.quantity,
              variant: {
                id: item.variantId,
                title: item.product.variant,
                options: {}, // This would be populated from the actual variant data
              },
              maxQuantity: 99,
            };
            
            set((state) => {
              const existingItemIndex = state.items.findIndex(
                cartItem => cartItem.productId === item.productId && 
                           cartItem.variant?.id === item.variantId
              );

              let updatedItems;
              if (existingItemIndex >= 0) {
                // Update existing item quantity
                updatedItems = [...state.items];
                const existingItem = updatedItems[existingItemIndex];
                const newQuantity = existingItem.quantity + item.quantity;
                const maxQuantity = existingItem.maxQuantity || 99;
                
                updatedItems[existingItemIndex] = {
                  ...existingItem,
                  quantity: Math.min(newQuantity, maxQuantity)
                };
              } else {
                // Add new item
                const newCartItem: CartItem = {
                  ...cartItem,
                  id: `cart-item-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                };
                updatedItems = [...state.items, newCartItem];
              }
              
              return {
                items: updatedItems,
                isLoading: false,
                isOpen: true // Open cart when item is added
              };
            });
            
            resolve();
          }, 300); // Simulate API delay
        });
      },

      // Remove item from cart
      removeItem: (itemId: string) => {
        console.log(`🗑️ Removing item from cart for ${storeHandle}:`, itemId);
        set({ isLoading: true });
        
        setTimeout(() => {
          set((state) => ({
            items: state.items.filter(item => item.id !== itemId),
            isLoading: false
          }));
        }, 200);
      },

      // Update item quantity
      updateQuantity: (itemId: string, quantity: number) => {
        console.log(`📝 Updating quantity for ${storeHandle}:`, itemId, 'to', quantity);
        
        if (quantity <= 0) {
          get().removeItem(itemId);
          return;
        }

        set((state) => ({
          items: state.items.map(item => {
            if (item.id === itemId) {
              const maxQuantity = item.maxQuantity || 99;
              return {
                ...item,
                quantity: Math.min(quantity, maxQuantity)
              };
            }
            return item;
          })
        }));
      },

      // Clear cart
      clearCart: () => {
        console.log(`🧹 Clearing cart for ${storeHandle}`);
        set({ isLoading: true });
        
        setTimeout(() => {
          set({
            items: [],
            isLoading: false
          });
        }, 300);
      },

      // Cart visibility controls
      openCart: () => set({ isOpen: true }),
      closeCart: () => set({ isOpen: false }),
      toggleCart: () => set((state) => ({ isOpen: !state.isOpen })),

      // Get computed cart object
      getCart: (): Cart => {
        const state = get();
        const totals = calculateCartTotals(state.items);
        
        return {
          id: `${storeHandle}-cart`,
          items: state.items,
          totalItems: totals.totalItems,
          totalAmount: totals.totalAmount,
          currency: 'USD',
          updatedAt: new Date().toISOString(),
        };
      },
    }),
    {
      name: `cart-${storeHandle}`,
      storage: createStoreSpecificStorage(storeHandle),
      partialize: (state) => ({
        items: state.items,
        // Don't persist UI state like isOpen, isLoading
      }),
    }
  ));
};

/**
 * Get or create store-specific cart store
 */
export const getCartStore = (storeHandle: string) => {
  if (!storeHandle) {
    console.warn('No store handle provided for cart store');
    return null;
  }
  
  if (!cartStoreInstances.has(storeHandle)) {
    const store = createCartStore(storeHandle);
    cartStoreInstances.set(storeHandle, store);
    console.log(`✨ Created new cart store instance for: ${storeHandle}`);
  }
  
  return cartStoreInstances.get(storeHandle)!;
};

/**
 * Clear cart store for specific store
 */
export const clearCartStore = (storeHandle: string) => {
  const store = cartStoreInstances.get(storeHandle);
  if (store) {
    store.getState().clearCart();
    cartStoreInstances.delete(storeHandle);
    console.log(`🗑️ Cleared cart store for: ${storeHandle}`);
  }
};

/**
 * Hook to use store-specific cart - maintains backward compatibility
 */
export const useCartStore = (storeHandle?: string) => {
  // If no store handle provided, use a default store for backward compatibility
  const handle = storeHandle || 'default';
  const store = getCartStore(handle);
  
  if (!store) {
    console.error(`No cart store available for store handle: ${handle}`);
    // Return a fallback store to prevent crashes
    return createCartStore('fallback')();
  }
  
  return store();
};