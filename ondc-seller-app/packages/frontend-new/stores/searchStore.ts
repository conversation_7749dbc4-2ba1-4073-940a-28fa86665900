import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface SearchResult {
  id: string;
  title: string;
  description?: string;
  handle: string;
  status: string;
  thumbnail?: string;
  tags?: string[];
  collection_id?: string;
  category_id?: string;
  created_at: string;
  updated_at: string;
  _formatted?: {
    title?: string;
    description?: string;
  };
}

export interface SearchSuggestion {
  id: string;
  title: string;
  handle: string;
  thumbnail?: string;
  highlighted: string;
}

export interface SearchFilters {
  category_id?: string;
  collection_id?: string;
  status?: string;
  tags?: string[];
  price_range?: {
    min?: number;
    max?: number;
  };
}

export interface SearchState {
  // Search query and results
  query: string;
  results: SearchResult[];
  suggestions: SearchSuggestion[];
  
  // Search metadata
  isLoading: boolean;
  isLoadingSuggestions: boolean;
  error: string | null;
  totalHits: number;
  processingTimeMs: number;
  
  // Pagination
  currentPage: number;
  limit: number;
  offset: number;
  
  // Filters and sorting
  filters: SearchFilters;
  sortBy: string;
  
  // Search history
  searchHistory: string[];
  
  // UI state
  isSearchOpen: boolean;
  showSuggestions: boolean;
  
  // Actions
  setQuery: (query: string) => void;
  setResults: (results: SearchResult[], totalHits: number, processingTimeMs: number) => void;
  setSuggestions: (suggestions: SearchSuggestion[]) => void;
  setLoading: (loading: boolean) => void;
  setLoadingSuggestions: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setFilters: (filters: SearchFilters) => void;
  setSortBy: (sortBy: string) => void;
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
  addToHistory: (query: string) => void;
  clearHistory: () => void;
  setSearchOpen: (open: boolean) => void;
  setShowSuggestions: (show: boolean) => void;
  clearResults: () => void;
  reset: () => void;
}

const initialState = {
  query: '',
  results: [],
  suggestions: [],
  isLoading: false,
  isLoadingSuggestions: false,
  error: null,
  totalHits: 0,
  processingTimeMs: 0,
  currentPage: 1,
  limit: 20,
  offset: 0,
  filters: {},
  sortBy: 'created_at:desc',
  searchHistory: [],
  isSearchOpen: false,
  showSuggestions: false,
};

export const useSearchStore = create<SearchState>()(
  persist(
    (set, get) => ({
      ...initialState,

      setQuery: (query: string) => {
        set({ query });
        
        // Clear suggestions when query changes
        if (!query.trim()) {
          set({ suggestions: [], showSuggestions: false });
        }
      },

      setResults: (results: SearchResult[], totalHits: number, processingTimeMs: number) => {
        set({ 
          results, 
          totalHits, 
          processingTimeMs,
          isLoading: false,
          error: null 
        });
      },

      setSuggestions: (suggestions: SearchSuggestion[]) => {
        set({ 
          suggestions, 
          isLoadingSuggestions: false,
          showSuggestions: suggestions.length > 0 
        });
      },

      setLoading: (isLoading: boolean) => {
        set({ isLoading });
        if (isLoading) {
          set({ error: null });
        }
      },

      setLoadingSuggestions: (isLoadingSuggestions: boolean) => {
        set({ isLoadingSuggestions });
      },

      setError: (error: string | null) => {
        set({ error, isLoading: false, isLoadingSuggestions: false });
      },

      setFilters: (filters: SearchFilters) => {
        set({ filters, currentPage: 1, offset: 0 });
      },

      setSortBy: (sortBy: string) => {
        set({ sortBy, currentPage: 1, offset: 0 });
      },

      setPage: (currentPage: number) => {
        const { limit } = get();
        set({ 
          currentPage, 
          offset: (currentPage - 1) * limit 
        });
      },

      setLimit: (limit: number) => {
        const { currentPage } = get();
        set({ 
          limit, 
          offset: (currentPage - 1) * limit 
        });
      },

      addToHistory: (query: string) => {
        if (!query.trim()) return;
        
        const { searchHistory } = get();
        const newHistory = [
          query,
          ...searchHistory.filter(item => item !== query)
        ].slice(0, 10); // Keep only last 10 searches
        
        set({ searchHistory: newHistory });
      },

      clearHistory: () => {
        set({ searchHistory: [] });
      },

      setSearchOpen: (isSearchOpen: boolean) => {
        set({ isSearchOpen });
        if (!isSearchOpen) {
          set({ showSuggestions: false });
        }
      },

      setShowSuggestions: (showSuggestions: boolean) => {
        set({ showSuggestions });
      },

      clearResults: () => {
        set({ 
          results: [], 
          suggestions: [],
          totalHits: 0, 
          processingTimeMs: 0,
          error: null,
          showSuggestions: false 
        });
      },

      reset: () => {
        set({ ...initialState, searchHistory: get().searchHistory });
      },
    }),
    {
      name: 'search-store',
      partialize: (state) => ({
        searchHistory: state.searchHistory,
        filters: state.filters,
        sortBy: state.sortBy,
        limit: state.limit,
      }),
    }
  )
);

// Selectors for better performance
export const useSearchQuery = () => useSearchStore(state => state.query);
export const useSearchResults = () => useSearchStore(state => state.results);
export const useSearchSuggestions = () => useSearchStore(state => state.suggestions);
export const useSearchLoading = () => useSearchStore(state => state.isLoading);
export const useSearchError = () => useSearchStore(state => state.error);
export const useSearchFilters = () => useSearchStore(state => state.filters);
export const useSearchHistory = () => useSearchStore(state => state.searchHistory);
