import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { categoriesApi } from '@/lib/api/categories';

export interface Category {
  id: string;
  name: string;
  handle: string;
  description: string;
  parent_category_id?: string;
  created_at?: string;
  updated_at?: string;
}

export interface OnboardingCategory {
  id: string;
  name: string;
  handle: string;
  description: string;
  parentId?: string;
  isSubcategory: boolean;
}

interface CategoriesState {
  // State
  categories: OnboardingCategory[];
  subcategories: OnboardingCategory[];
  apiCategories: Category[];
  currentStep: 'category' | 'subcategory' | 'complete';
  isSubmitting: boolean;
  isFetchingCategories: boolean;
  error: string | null;

  // Actions
  setCurrentStep: (step: 'category' | 'subcategory' | 'complete') => void;
  setIsSubmitting: (isSubmitting: boolean) => void;
  setIsFetchingCategories: (isFetching: boolean) => void;
  setError: (error: string | null) => void;
  
  // Category management
  addCategory: (category: OnboardingCategory) => void;
  addSubcategory: (subcategory: OnboardingCategory) => void;
  setApiCategories: (categories: Category[]) => void;
  
  // API actions
  fetchCategories: () => Promise<Category[]>;
  createCategory: (payload: {
    name: string;
    handle: string;
    description: string;
    parent_category_id?: string;
  }) => Promise<{ success: boolean; data?: any; error?: string; message?: string }>;
  
  // Reset
  reset: () => void;
  resetToCategory: () => void;
}

const initialState = {
  categories: [],
  subcategories: [],
  apiCategories: [],
  currentStep: 'category' as const,
  isSubmitting: false,
  isFetchingCategories: false,
  error: null,
};

export const useCategoriesStore = create<CategoriesState>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Basic setters
      setCurrentStep: (step) => set({ currentStep: step }),
      setIsSubmitting: (isSubmitting) => set({ isSubmitting }),
      setIsFetchingCategories: (isFetching) => set({ isFetchingCategories: isFetching }),
      setError: (error) => set({ error }),

      // Category management
      addCategory: (category) => {
        set((state) => ({
          categories: [...state.categories, category],
        }));
      },

      addSubcategory: (subcategory) => {
        set((state) => ({
          subcategories: [...state.subcategories, subcategory],
        }));
      },

      setApiCategories: (categories) => {
        // Filter only main categories (no parent_category_id)
        const mainCategories = categories.filter(cat => !cat.parent_category_id);
        console.log('Setting API categories:', mainCategories);
        set({ apiCategories: mainCategories });
      },

      // API actions
      fetchCategories: async () => {
        const { setIsFetchingCategories, setApiCategories, setError } = get();
        
        setIsFetchingCategories(true);
        setError(null);
        
        try {
          console.log('=== FETCHING CATEGORIES FOR SUBCATEGORY DROPDOWN ===');
          const result = await categoriesApi.getCategories();
          console.log('Full API result:', result);
          
          if (result.success && result.data) {
            console.log('Raw fetched categories:', result.data);
            console.log('Categories array length:', result.data.length);
            
            setApiCategories(result.data);
            return result.data;
          } else {
            console.log('API call failed or no data:', result);
            setError('Failed to fetch categories');
            return [];
          }
        } catch (error) {
          console.error('Error fetching categories:', error);
          setError('Error fetching categories');
          return [];
        } finally {
          setIsFetchingCategories(false);
        }
      },

      createCategory: async (payload) => {
        const { setIsSubmitting, setError, addCategory, addSubcategory, fetchCategories, setCurrentStep } = get();
        const { currentStep } = get();
        
        setIsSubmitting(true);
        setError(null);

        try {
          console.log('Creating category/subcategory:', payload);

          // Make API call
          const result = await categoriesApi.createCategory(payload);

          if (result.success && result.data) {
            console.log('Category creation successful:', result.data);
            
            // Create local item for UI
            const newItem: OnboardingCategory = {
              id: result.data?.product_category?.id || result.data._id || result.data.category_id || `temp-${Date.now()}`,
              name: result.data?.product_category?.name,
              handle: result.data?.product_category?.handle,
              description: result.data?.product_category?.description,
              parentId: result.data?.product_category?.parent_category_id || undefined,
              isSubcategory: currentStep === 'subcategory'
            };

            if (currentStep === 'category') {
              // After category creation, fetch categories and show subcategory section
              addCategory(newItem);
              // await fetchCategories();
              setCurrentStep('subcategory');
            } else {
              // After subcategory creation, move to complete
              addSubcategory(newItem);
              setCurrentStep('complete');
            }

            return result;
          } else {
            setError(result.error || 'Failed to create category');
            return result;
          }
        } catch (error) {
          console.error('Error in createCategory:', error);
          const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
          setError(errorMessage);
          return {
            success: false,
            error: errorMessage,
          };
        } finally {
          setIsSubmitting(false);
        }
      },

      // Reset functions
      reset: () => set(initialState),
      
      resetToCategory: () => {
        set({
          currentStep: 'category',
          isSubmitting: false,
          isFetchingCategories: false,
          error: null,
        });
      },
    }),
    {
      name: 'categories-storage',
      partialize: (state) => ({
        categories: state.categories,
        subcategories: state.subcategories,
        currentStep: state.currentStep,
      }),
    }
  )
);