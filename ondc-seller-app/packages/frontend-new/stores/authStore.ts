// stores/auth-store.ts
import { create } from "zustand";
import { persist } from "zustand/middleware";

interface User {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  role?: string;
  created_at?: string;
  updated_at?: string;
  metadata?: {
    onboarding_status?: 'completed' | 'pending';
    store_handle?: string;
    [key: string]: any;
  };
  store_handle?: string;
  onboarding_status?: 'completed' | 'pending';
  // Handle nested user structure from API response
  user?: {
    id: string;
    email: string;
    first_name?: string;
    last_name?: string;
    metadata?: {
      onboarding_status?: 'completed' | 'pending';
      store_handle?: string;
      [key: string]: any;
    };
    [key: string]: any;
  };
}

interface AuthState {
  token: string | null;
  user: User | null;
  isAuthenticated: boolean;
  hasHydrated: boolean;
  setToken: (token: string) => void;
  setUser: (user: User) => void;
  setAuth: (token: string, user: User) => void;
  clearAuth: () => void;
  loadFromStorage: () => void;
  getStoreHandle: () => string | null;
  getOnboardingStatus: () => 'completed' | 'pending' | null;
  setHasHydrated: (hasHydrated: boolean) => void;
}

export const useAuthStore = create<AuthState>()(persist(
  (set, get) => ({
  token: null,
  user: null,
  isAuthenticated: false,
  hasHydrated: false,
  
  setToken: (token) => {
    set({ token, isAuthenticated: !!token });
  },
  
  setUser: (user) => {
    console.log("user<><><><><><><>",user)
    set({ user });
  },
  
  setAuth: (token, user) => {
    console.log('=== SETTING AUTH ===');
    console.log('Token:', token ? 'exists' : 'null');
    console.log('User:', user);
    set({ token, user, isAuthenticated: true });
  },
  
  clearAuth: () => {
    console.log('=== CLEARING AUTH STORE ===');
    const currentState = get();
    console.log('Current auth state before clearing:', {
      token: currentState.token ? 'exists' : 'null',
      user: currentState.user ? 'exists' : 'null',
      isAuthenticated: currentState.isAuthenticated,
      hasHydrated: currentState.hasHydrated
    });
    
    // Reset state but keep hydration status
    set({ token: null, user: null, isAuthenticated: false });
    
    // Clear additional localStorage items
    if (typeof window !== 'undefined') {
      try {
        localStorage.removeItem('ondc_auth_token');
        console.log('Additional auth tokens cleared');
      } catch (error) {
        console.error('Error clearing additional localStorage items:', error);
      }
    }
    
    console.log('=== AUTH STORE CLEARED ===');
  },
  
  loadFromStorage: () => {
    // This is now handled by the persist middleware automatically
    // Just ensure hydration is marked as complete
    const currentState = get();
    if (!currentState.hasHydrated) {
      set({ hasHydrated: true });
    }
    console.log('=== LOAD FROM STORAGE CALLED ===');
    console.log('Current auth state after loading:', {
      token: currentState.token ? 'exists' : 'null',
      user: currentState.user ? 'exists' : 'null',
      isAuthenticated: currentState.isAuthenticated,
      hasHydrated: true
    });
  },
  
  setHasHydrated: (hasHydrated) => {
    set({ hasHydrated });
  },
  
  getStoreHandle: () => {
    const state = get();
    // Check multiple possible locations for store handle (prioritize nested user data)
    const storeHandle = 
      state.user?.user?.metadata?.store_handle || // From nested API response structure
      state.user?.user?.store_handle || // From nested API response structure
      state.user?.store_handle || 
      state.user?.metadata?.store_handle ||
      null;
    
    console.log('=== getStoreHandle called ===');
    console.log('Current user state:', state.user);
    console.log('Checking paths:');
    console.log('  state.user?.user?.metadata?.store_handle:', state.user?.user?.metadata?.store_handle);
    console.log('  state.user?.user?.store_handle:', state.user?.user?.store_handle);
    console.log('  state.user?.store_handle:', state.user?.store_handle);
    console.log('  state.user?.metadata?.store_handle:', state.user?.metadata?.store_handle);
    console.log('Extracted store handle:', storeHandle);
    return storeHandle;
  },
  
  getOnboardingStatus: () => {
    const state = get();
    // Check multiple possible locations for onboarding status (prioritize nested user data)
    const status = 
      state.user?.user?.metadata?.onboarding_status || // From nested API response structure
      state.user?.onboarding_status || 
      state.user?.metadata?.onboarding_status || 
      null;
    
    console.log('=== getOnboardingStatus called ===');
    console.log('Current user state:', state.user);
    console.log('Checking paths:');
    console.log('  state.user?.user?.metadata?.onboarding_status:', state.user?.user?.metadata?.onboarding_status);
    console.log('  state.user?.onboarding_status:', state.user?.onboarding_status);
    console.log('  state.user?.metadata?.onboarding_status:', state.user?.metadata?.onboarding_status);
    console.log('Extracted onboarding status:', status);
    return status;
  },
  }),
  {
    name: 'auth-storage',
    partialize: (state) => ({
      token: state.token,
      user: state.user,
      isAuthenticated: state.isAuthenticated,
    }),
    onRehydrateStorage: () => {
      console.log('🔄 Starting hydration for main auth store');
      
      // Safety timeout to ensure hydration completes
      setTimeout(() => {
        const currentState = useAuthStore.getState();
        if (!currentState.hasHydrated) {
          console.warn('⚠️ Hydration timeout - forcing completion');
          currentState.setHasHydrated(true);
        }
      }, 1000); // 1 second timeout
      
      return (state, error) => {
        if (error) {
          console.error('❌ Main auth hydration failed:', error);
          if (state) {
            state.setHasHydrated(true);
          }
        } else {
          console.log('✅ Main auth hydration complete:', {
            token: state?.token ? 'exists' : 'null',
            user: state?.user ? 'exists' : 'null',
            isAuthenticated: state?.isAuthenticated
          });
          if (state) {
            state.setHasHydrated(true);
            // Fix isAuthenticated flag if needed
            if (state.token && state.user && !state.isAuthenticated) {
              console.log('🔧 Fixing isAuthenticated flag for main auth');
              state.setToken(state.token);
            }
          }
        }
      };
    },
  }
));
