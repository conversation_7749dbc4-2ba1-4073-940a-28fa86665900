import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { categoriesApi } from '@/lib/api/categories';
import { OnboardingProduct, ProductVariant } from '@/types/onboarding';

export interface Category {
  id: string;
  name: string;
  handle: string;
  description: string;
  parent_category_id?: string;
  created_at?: string;
  updated_at?: string;
}

interface ProductsState {
  // State
  products: OnboardingProduct[];
  editingProduct: OnboardingProduct | null;
  thumbnailFile: File | null;
  thumbnailPreview: string;
  imageFiles: File[];
  imagePreviews: string[];
  variants: ProductVariant[];
  showVariants: boolean;
  categories: Category[];
  subcategories: Category[];
  isFetchingCategories: boolean;
  error: string | null;

  // Actions
  setEditingProduct: (product: OnboardingProduct | null) => void;
  setThumbnailFile: (file: File | null) => void;
  setThumbnailPreview: (preview: string) => void;
  setImageFiles: (files: File[]) => void;
  setImagePreviews: (previews: string[]) => void;
  setVariants: (variants: ProductVariant[]) => void;
  setShowVariants: (show: boolean) => void;
  setCategories: (categories: Category[]) => void;
  setSubcategories: (subcategories: Category[]) => void;
  setIsFetchingCategories: (isFetching: boolean) => void;
  setError: (error: string | null) => void;

  // Product management
  addProduct: (product: OnboardingProduct) => void;
  updateProduct: (product: OnboardingProduct) => void;
  deleteProduct: (id: string) => void;

  // Variant management
  addVariant: () => void;
  updateVariant: (index: number, field: keyof ProductVariant, value: any) => void;
  removeVariant: (index: number) => void;

  // API actions
  fetchCategories: () => Promise<void>;

  // Reset
  reset: () => void;
  resetForm: () => void;
}

const initialState = {
  products: [],
  editingProduct: null,
  thumbnailFile: null,
  thumbnailPreview: '',
  imageFiles: [],
  imagePreviews: [],
  variants: [],
  showVariants: false,
  categories: [],
  subcategories: [],
  isFetchingCategories: true,
  error: null,
};

export const useProductsStore = create<ProductsState>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Basic setters
      setEditingProduct: (product) => set({ editingProduct: product }),
      setThumbnailFile: (file) => set({ thumbnailFile: file }),
      setThumbnailPreview: (preview) => set({ thumbnailPreview: preview }),
      setImageFiles: (files) => set({ imageFiles: files }),
      setImagePreviews: (previews) => set({ imagePreviews: previews }),
      setVariants: (variants) => set({ variants }),
      setShowVariants: (show) => set({ showVariants: show }),
      setCategories: (categories) => set({ categories }),
      setSubcategories: (subcategories) => set({ subcategories }),
      setIsFetchingCategories: (isFetching) => set({ isFetchingCategories: isFetching }),
      setError: (error) => set({ error }),

      // Product management
      addProduct: (product) => {
        set((state) => ({
          products: [...state.products, product],
        }));
      },

      updateProduct: (updatedProduct) => {
        set((state) => ({
          products: state.products.map(product => 
            product.id === updatedProduct.id ? updatedProduct : product
          ),
        }));
      },

      deleteProduct: (id) => {
        set((state) => ({
          products: state.products.filter(product => product.id !== id),
        }));
      },

      // Variant management
      addVariant: () => {
        const newVariant: ProductVariant = {
          id: `variant-${Date.now()}`,
          name: '',
          sku: '',
          price: 0,
          stock: 0,
          attributes: {}
        };
        set((state) => ({
          variants: [...state.variants, newVariant],
        }));
      },

      updateVariant: (index, field, value) => {
        set((state) => {
          const updatedVariants = [...state.variants];
          updatedVariants[index] = { ...updatedVariants[index], [field]: value };
          return { variants: updatedVariants };
        });
      },

      removeVariant: (index) => {
        set((state) => ({
          variants: state.variants.filter((_, i) => i !== index),
        }));
      },

      // API actions
      fetchCategories: async () => {
        const { setIsFetchingCategories, setCategories, setSubcategories, setError } = get();
        
        setIsFetchingCategories(true);
        setError(null);
        
        try {
          console.log('=== FETCHING CATEGORIES FOR PRODUCT MANAGEMENT ===');
          const result = await categoriesApi.getCategories();
          
          if (result.success && result.data) {
            console.log('Fetched categories for products:', result.data);
            
            // Separate categories and subcategories
            const mainCategories = result.data.filter((cat: Category) => !cat.parent_category_id);
            const subCategories = result.data.filter((cat: Category) => cat.parent_category_id);
            
            setCategories(mainCategories);
            setSubcategories(subCategories);
            
            console.log('Main categories:', mainCategories);
            console.log('Subcategories:', subCategories);
          } else {
            console.log('No categories found or API error');
            setError('Failed to load categories');
          }
        } catch (error) {
          console.error('Error fetching categories:', error);
          setError('Failed to load categories');
        } finally {
          setIsFetchingCategories(false);
        }
      },

      // Reset functions
      reset: () => set(initialState),
      
      resetForm: () => {
        set({
          editingProduct: null,
          thumbnailFile: null,
          thumbnailPreview: '',
          imageFiles: [],
          imagePreviews: [],
          variants: [],
          showVariants: false,
          error: null,
        });
      },
    }),
    {
      name: 'products-storage',
      partialize: (state) => ({
        products: state.products,
        categories: state.categories,
        subcategories: state.subcategories,
      }),
    }
  )
);