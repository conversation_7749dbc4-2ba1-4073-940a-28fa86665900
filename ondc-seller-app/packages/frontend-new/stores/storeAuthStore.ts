'use client';

import { create } from "zustand";
import { persist } from "zustand/middleware";
// Removed complex storage wrapper

interface StoreUser {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  has_account?: boolean;
  created_at?: string;
  updated_at?: string;
  addresses?: Address[];
  metadata?: {
    [key: string]: any;
  };
}

interface Address {
  id: string;
  first_name: string;
  last_name: string;
  phone?: string;
  company?: string;
  address_1: string;
  address_2?: string;
  city: string;
  country_code: string;
  province: string;
  postal_code: string;
  address_name?: string;
  metadata?: {
    [key: string]: any;
  };
}

interface StoreAuthState {
  token: string | null;
  user: StoreUser | null;
  addresses: Address[];
  isAuthenticated: boolean;
  isLoading: boolean;
  loadingMessage: string | null;
  error: string | null;
  hasHydrated: boolean;
  setToken: (token: string) => void;
  setUser: (user: StoreUser) => void;
  setAddresses: (addresses: Address[]) => void;
  addAddress: (address: Address) => void;
  updateAddress: (addressId: string, address: Address) => void;
  removeAddress: (addressId: string) => void;
  setAuth: (token: string, user: StoreUser, addresses?: Address[]) => void;
  setLoading: (loading: boolean, message?: string) => void;
  setError: (error: string | null) => void;
  clearAuth: () => void;
  loadFromStorage: () => void;
  setHasHydrated: (hasHydrated: boolean) => void;
}

// Store instances cache for isolated stores
const storeAuthInstances = new Map<string, ReturnType<typeof createStoreAuthStore>>();

// Expose store instances for debugging
if (typeof globalThis !== 'undefined') {
  (globalThis as any).__storeAuthInstances = storeAuthInstances;
}

/**
 * Create store-specific auth store with isolated persistence
 */
export const createStoreAuthStore = (storeHandle: string) => {
  console.log(`🔒 Creating isolated auth store for: ${storeHandle}`);
  
  return create<StoreAuthState>()(persist(
    (set, get) => ({
      token: null,
      user: null,
      addresses: [],
      isAuthenticated: false,
      isLoading: false,
      loadingMessage: null,
      error: null,
      hasHydrated: false,
    
      setToken: (token) => {
        set({ token, isAuthenticated: !!token });
      },
      
      setUser: (user) => {
        set({ user });
      },
      
      setAddresses: (addresses) => {
        set({ addresses });
      },
      
      addAddress: (address) => {
        const currentAddresses = get().addresses;
        set({ addresses: [...currentAddresses, address] });
      },
      
      updateAddress: (addressId, updatedAddress) => {
        const currentAddresses = get().addresses;
        const updatedAddresses = currentAddresses.map(addr => 
          addr.id === addressId ? updatedAddress : addr
        );
        set({ addresses: updatedAddresses });
      },
      
      removeAddress: (addressId) => {
        const currentAddresses = get().addresses;
        const filteredAddresses = currentAddresses.filter(addr => addr.id !== addressId);
        set({ addresses: filteredAddresses });
      },
    
      setAuth: (token, user, addresses) => {
        console.log(`🔐 Setting store auth for ${storeHandle}:`);
        console.log('Token:', token ? 'exists' : 'null');
        console.log('User:', user);
        
        // Extract addresses from user data if not provided separately
        const userAddresses = addresses || user.addresses || [];
        console.log('Addresses:', userAddresses.length);
        
        set({ token, user, addresses: userAddresses, isAuthenticated: true, error: null, isLoading: false, loadingMessage: null });
      },
    
      setLoading: (loading, message = null) => {
        set({ isLoading: loading, loadingMessage: message });
      },
      
      setError: (error) => {
        set({ error, isLoading: false, loadingMessage: null });
      },
    
      clearAuth: () => {
        console.log(`🧹 Clearing store auth for ${storeHandle}`);
        const currentState = get();
        console.log('Current store auth state before clearing:', {
          token: currentState.token ? 'exists' : 'null',
          user: currentState.user ? 'exists' : 'null',
          addresses: currentState.addresses.length,
          isAuthenticated: currentState.isAuthenticated
        });
        
        // Reset state
        set({ 
          token: null, 
          user: null, 
          addresses: [],
          isAuthenticated: false, 
          isLoading: false, 
          loadingMessage: null,
          error: null 
        });
        
        console.log(`✅ Store auth cleared for ${storeHandle}`);
      },
    
      loadFromStorage: () => {
        // This is handled by the persist middleware automatically
        console.log(`💾 Loading store auth from storage for ${storeHandle}`);
        const currentState = get();
        console.log('Current store auth state after loading:', {
          token: currentState.token ? 'exists' : 'null',
          user: currentState.user ? 'exists' : 'null',
          isAuthenticated: currentState.isAuthenticated
        });
      },
      
      setHasHydrated: (hasHydrated) => {
        set({ hasHydrated });
      },
    }),
    {
      name: `${storeHandle}-auth`, // Simple naming: {storeHandle}-auth
      partialize: (state) => ({
        token: state.token,
        user: state.user,
        addresses: state.addresses,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => {
        console.log(`🔄 Starting hydration for store auth: ${storeHandle}`);
        return (state, error) => {
          if (error) {
            console.error(`❌ Hydration failed for store auth ${storeHandle}:`, error);
            if (state) {
              state.setHasHydrated(true);
            }
          } else {
            console.log(`✅ Hydration complete for store auth ${storeHandle}:`, {
              token: state?.token ? 'exists' : 'null',
              user: state?.user ? 'exists' : 'null',
              isAuthenticated: state?.isAuthenticated
            });
            if (state) {
              state.setHasHydrated(true);
              // Fix isAuthenticated flag if needed
              if (state.token && state.user && !state.isAuthenticated) {
                console.log(`🔧 Fixing isAuthenticated flag for store ${storeHandle}`);
                state.setToken(state.token);
              }
            }
          }
        };
      },
    }
  ));
};

/**
 * Get or create store-specific auth store
 */
export const getStoreAuthStore = (storeHandle: string) => {
  if (!storeHandle) {
    console.warn('No store handle provided for auth store');
    return null;
  }
  
  if (!storeAuthInstances.has(storeHandle)) {
    const store = createStoreAuthStore(storeHandle);
    storeAuthInstances.set(storeHandle, store);
    console.log(`✨ Created new auth store instance for: ${storeHandle}`);
  }
  
  return storeAuthInstances.get(storeHandle)!;
};

/**
 * Clear auth store for specific store
 */
export const clearStoreAuthStore = (storeHandle: string) => {
  const store = storeAuthInstances.get(storeHandle);
  if (store) {
    store.getState().clearAuth();
    storeAuthInstances.delete(storeHandle);
    console.log(`🗑️ Cleared auth store for: ${storeHandle}`);
  }
};

/**
 * Hook to use store-specific auth - maintains backward compatibility
 */
export const useStoreAuthStore = (storeHandle?: string) => {
  // If no store handle provided, use a default store for backward compatibility
  const handle = storeHandle || 'default';
  const store = getStoreAuthStore(handle);
  
  if (!store) {
    console.error(`No auth store available for store handle: ${handle}`);
    // Return a fallback store to prevent crashes
    return createStoreAuthStore('fallback')();
  }
  
  return store();
};