# Medusa API Migration Summary

## Overview
This document summarizes the changes made to standardize all Medusa API calls to use the base URL from environment variables (`NEXT_PUBLIC_MEDUSA_BASE_URL=http://localhost:9000`) with appropriate prefixes.

## Key Changes

### 1. Created Centralized Medusa Client
**File:** `lib/api/medusa-client.ts`
- Created a centralized `MedusaClient` class that manages all Medusa API calls
- Provides separate clients for:
  - **Admin API**: `medusaClient.admin` (uses base URL + `/admin`)
  - **Store API**: `medusaClient.store` (uses base URL + `/store`)
  - **Auth API**: `medusaClient.auth` (uses base URL only, no prefix)
- Includes automatic authentication token handling
- Provides store-specific client instances with caching
- Handles 401 unauthorized responses with smart redirects

### 2. Updated API Files

#### Admin API Files (using `/admin` prefix):
- ✅ `lib/api/auth.ts` - Updated to use `medusaClient.admin` for admin endpoints
- ✅ `lib/api/products.ts` - Updated to use `medusaClient.admin` and fixed response handling
- ✅ `lib/api/categories.ts` - Updated to use `medusaClient.admin` and fixed response handling
- ✅ `lib/api/collections.ts` - Updated to use `medusaClient.admin`
- ✅ `lib/api/tags.ts` - Updated to use `medusaClient.admin`
- ✅ `lib/api/customers.ts` - Updated to use `medusaClient.admin`
- ✅ `lib/api/coupons.ts` - Updated to use `medusaClient.admin`
- ✅ `lib/api/bulk-import.ts` - Updated to use `medusaClient.getBaseUrl()`

#### Store API Files (using `/store` prefix):
- ✅ `lib/api/cart.ts` - Updated to use `medusaClient.getBaseUrl()` for store endpoints
- ✅ `lib/api/orders.ts` - Updated to use `medusaClient.getBaseUrl()` for store endpoints
- ✅ `lib/api/search.ts` - Updated to use `medusaClient.getBaseUrl()` for store endpoints
- ✅ `lib/api/store.ts` - Updated to use `medusaClient.getBaseUrl()` for store endpoints
- ✅ `lib/api/profile.ts` - Updated to use `medusaClient.getBaseUrl()` for store endpoints
- ✅ `lib/api/storeAuth.ts` - Updated to use `medusaClient.getBaseUrl()` for store endpoints

#### Auth API Files (no prefix):
- ✅ `lib/api/auth.ts` - Updated to use `medusaClient.auth` for auth endpoints

### 3. Environment Variables
The following environment variables are properly configured and being used:

```env
# Medusa Configuration
NEXT_PUBLIC_MEDUSA_BASE_URL=http://localhost:9000          # Medusa base URL
NEXT_PUBLIC_MEDUSA_API_URL=http://localhost:9000/admin     # Medusa admin API
NEXT_PUBLIC_MEDUSA_STORE_API_URL=http://localhost:9000/store  # Medusa store API
```

### 4. URL Structure
All API calls now follow this pattern:

#### Admin API Calls:
- **Base URL**: `http://localhost:9000`
- **Admin Prefix**: `/admin`
- **Example**: `http://localhost:9000/admin/products`

#### Store API Calls:
- **Base URL**: `http://localhost:9000`
- **Store Prefix**: `/store`
- **Example**: `http://localhost:9000/store/products`

#### Auth API Calls:
- **Base URL**: `http://localhost:9000`
- **No Prefix**: Direct to base URL
- **Example**: `http://localhost:9000/auth/user/emailpass`

### 5. Benefits of This Approach

1. **Centralized Configuration**: All API URLs are managed from environment variables
2. **Consistent URL Construction**: No more hardcoded URLs scattered throughout the codebase
3. **Easy Environment Switching**: Change base URL in one place to switch environments
4. **Type Safety**: Centralized client provides better TypeScript support
5. **Authentication Handling**: Automatic token management and 401 error handling
6. **Store Isolation**: Proper tenant ID handling for multi-store support

### 6. Usage Examples

#### Admin API Usage:
```typescript
import { medusaClient } from './medusa-client';

// GET /admin/products
const response = await medusaClient.admin.get('/products', {
  headers: { 'x-tenant-id': storeHandle }
});

// POST /admin/products
const response = await medusaClient.admin.post('/products', productData, {
  headers: { 'x-tenant-id': storeHandle }
});
```

#### Store API Usage:
```typescript
import { medusaClient } from './medusa-client';

// GET /store/products
const url = `${medusaClient.getBaseUrl()}/store/products`;
const response = await fetch(url, {
  headers: { 'x-tenant-id': storeHandle }
});
```

#### Auth API Usage:
```typescript
import { medusaClient } from './medusa-client';

// POST /auth/user/emailpass
const response = await medusaClient.auth.post('/auth/user/emailpass', credentials);
```

### 7. Migration Checklist

- ✅ Created centralized `MedusaClient` class
- ✅ Updated all admin API files to use `medusaClient.admin`
- ✅ Updated all store API files to use `medusaClient.getBaseUrl()`
- ✅ Updated auth API files to use `medusaClient.auth`
- ✅ Fixed response handling for axios-based calls
- ✅ Maintained backward compatibility with existing interfaces
- ✅ Preserved authentication and tenant ID handling
- ✅ Updated environment variable usage

### 8. Testing Recommendations

1. **Environment Variables**: Verify that `NEXT_PUBLIC_MEDUSA_BASE_URL` is properly set
2. **Admin API**: Test admin operations like product creation, category management
3. **Store API**: Test store operations like product browsing, cart management
4. **Auth API**: Test login/logout functionality
5. **Multi-tenant**: Test with different store handles to ensure proper tenant isolation
6. **Error Handling**: Test 401 responses to ensure proper redirect behavior

### 9. Future Considerations

1. **API Versioning**: The centralized client can easily support API versioning
2. **Rate Limiting**: Can add rate limiting logic to the centralized client
3. **Caching**: Can implement response caching at the client level
4. **Monitoring**: Can add API call monitoring and analytics
5. **Mock Support**: Can easily switch to mock APIs for testing

## Conclusion

All Medusa API calls have been successfully migrated to use the centralized configuration with proper base URL handling. The system now uses `http://localhost:9000` from environment variables and constructs URLs with appropriate prefixes (`/admin`, `/store`, or no prefix for auth) as required.