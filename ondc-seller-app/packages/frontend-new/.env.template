# ========== FRONTEND ==========
# Strapi Configuration
NEXT_PUBLIC_STRAPI_BASE_URL=http://localhost:1337
NEXT_PUBLIC_STRAPI_API_URL=http://localhost:1337
NEXT_PUBLIC_API_URL=http://localhost:1337/api              # Strapi CMS base URL

# Medusa Configuration
NEXT_PUBLIC_MEDUSA_BASE_URL=http://localhost:9000          # Medusa base URL
NEXT_PUBLIC_MEDUSA_API_URL=http://localhost:9000/admin     # Medusa admin API
NEXT_PUBLIC_MEDUSA_STORE_API_URL=http://localhost:9000/store  # Medusa store API

# Authentication Token (Optional fallback)
NEXT_PUBLIC_ADMIN_TOKEN=your_static_strapi_admin_token
NEXT_PUBLIC_STRAPI_API_TOKEN=your_static_strapi_admin_token

# MeiliSearch Configuration (Frontend)
NEXT_PUBLIC_MEILISEARCH_HOST="your_hosted_meilisearch_url"
NEXT_PUBLIC_MEILISEARCH_API_KEY="your_meilisearch_api_key"
NEXT_PUBLIC_MEILISEARCH_INDEX_PREFIX="medusa"

# App URL
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=pk_3d67561dece2d466dc798c18c1f80523f84f3b2f01316e0bf915e51f3a59b98b
