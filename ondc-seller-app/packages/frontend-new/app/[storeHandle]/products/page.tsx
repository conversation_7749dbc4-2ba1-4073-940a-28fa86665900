'use client';

import React, { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { useStoreConfig } from '@/hooks/useStoreConfig';
import { GlobalProviders } from '@/components/layout/GlobalProviders';
import { ThemedStoreHeader } from '@/components/store/ThemedStoreHeader';
import { ProductListingPage } from '@/components/product/ProductListingPage';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ErrorMessage } from '@/components/ui/ErrorMessage';
import { ErrorBoundary } from '@/components/ui/ErrorBoundary';

interface ProductsPageProps {
  searchParams: {
    q?: string;
    page?: string;
    sort?: string;
    category?: string;
    collection?: string;
    price_min?: string;
    price_max?: string;
    brand?: string;
    color?: string;
    size?: string;
    rating?: string;
    in_stock?: string;
  };
}

const ProductsPageContent: React.FC<{ storeHandle: string; searchParams: ProductsPageProps['searchParams'] }> = ({ 
  storeHandle, 
  searchParams 
}) => {
  const { 
    storeData, 
    isLoading: storeLoading, 
    error: storeError
  } = useStoreConfig();
  
  // Create store object from store configuration data with memoization
  const store = React.useMemo(() => {
    if (!storeData) return null;
    
    return {
      id: storeData.id || storeHandle,
      name: storeData.name || storeHandle,
      handle: storeData.handle || storeHandle,
      description: storeData.description || '',
      logo: storeData.logo,
      address: storeData.address,
      contact: storeData.contact,
      theme: storeData.theme,
      ...storeData,
    };
  }, [storeData, storeHandle]);
  
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading time for demonstration
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);
  
    return () => clearTimeout(timer);
  }, []);

  if (storeLoading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">
            {storeLoading ? 'Loading store information...' : 'Loading products...'}
          </p>
        </div>
      </div>
    );
  }

  if (storeError && !storeData) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <ErrorMessage 
          message="Unable to load store information"
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen" style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}>
        <ThemedStoreHeader store={store} storeHandle={storeHandle} />
       
        <ProductListingPage 
          store={store}
          storeHandle={storeHandle} 
          searchParams={searchParams}
        />
      </div>
    </ErrorBoundary>
  );
};

export default function ProductsPage({ searchParams }: ProductsPageProps) {
  const params = useParams();
  const storeHandle = params?.storeHandle as string;

  // Early return if params are not available
  if (!storeHandle) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Invalid URL</h1>
          <p className="text-gray-600">Store handle is missing from the URL.</p>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <GlobalProviders storeHandle={storeHandle}>
        <ProductsPageContent storeHandle={storeHandle} searchParams={searchParams} />
      </GlobalProviders>
    </ErrorBoundary>
  );
}