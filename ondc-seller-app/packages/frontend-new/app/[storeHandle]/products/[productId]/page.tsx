'use client';

import React, { useMemo } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { GlobalProviders } from '@/components/layout/GlobalProviders';
import { ThemedStoreHeader } from '@/components/store/ThemedStoreHeader';
import { ProductDetailContent } from '@/components/product/ProductDetailContent';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ErrorMessage } from '@/components/ui/ErrorMessage';
import { useStoreWithMedusaConfig } from '@/hooks/useStoreWithMedusaConfig';
import { useProduct } from '@/hooks/useProduct';
import { useStoreSingleProduct } from '@/hooks/useStore';

const ProductDetailPageContent: React.FC<{ storeHandle: string; productId: string }> = ({ 
  storeHandle, 
  productId 
}) => {
  const router = useRouter();
  
  // Get store configuration and Medusa API config using proper API service
  const { 
    storeConfig, 
    medusaConfig,
    isLoading: storeLoading, 
    error: storeError,
    refetch: refetchStore
  } = useStoreWithMedusaConfig(storeHandle);

  // Fetch product from Medusa API
  // const { 
  //   product, 
  //   isLoading: productLoading, 
  //   error: productError,
  //   refetch: refetchProduct 
  // } = useProduct(medusaConfig, productId);
    const { 
      data: product, 
      isLoading: productLoading ,
      // error: productError,
    } = useStoreSingleProduct(storeHandle,productId);

  // Create store object from store configuration data
  const store = React.useMemo(() => {
    if (!storeConfig) return null;
    
    return {
      id: storeConfig.id || storeHandle,
      name: storeConfig.name || storeHandle,
      handle: storeConfig.handle || storeHandle,
      description: storeConfig.description || '',
      logo: storeConfig.logo,
      address: storeConfig.address,
      contact: storeConfig.contact,
      theme: storeConfig.theme,
      ...storeConfig,
    };
  }, [storeConfig, storeHandle]);

  const filteredProduct = useMemo(() => {
    if (!product) return null;
    
    // Safely handle images array
    const productImages = Array.isArray(product?.images) ? product.images : [];
    const images = product.thumbnail ? [product.thumbnail, ...productImages] : productImages;
    
    // Safely handle variants array
    const productVariants = Array.isArray(product?.variants) ? product.variants : [];
    
    // Safely handle categories array
    const productCategories = Array.isArray(product?.categories) ? product.categories : [];
    
    // Safely handle tags array
    const productTags = Array.isArray(product?.tags) ? product.tags : [];
    
    return {
      id: product.id,
      title: product.title,
      description: product.description,
      price: product?.variants?.[0]?.metadata?.sale_price,
      originalPrice: product?.variants?.[0]?.metadata?.original_price,
      images: images,
      variants: productVariants.map((val) => ({
        stock: val.metadata?.product_quantity,
        id: val.id,
        title: val.title,
        price: val.metadata?.sale_price,
        originalPrice: val.metadata?.original_price,
        metadata: val.metadata,
        sku: val.sku,
      })),
      categories: productCategories,
      tags: productTags,
      productOverview: product?.metadata?.additional_data?.product_overview,
      productFeatures: product?.metadata?.additional_data?.product_features,
      productSpecifications: product?.metadata?.additional_data?.product_specifications,
    }
  }, [product]);

  // Loading state
  if (storeLoading || productLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">
            {storeLoading ? 'Loading store information...' : 'Loading product details...'}
          </p>
        </div>
      </div>
    );
  }

  // Store error state
  if (storeError || !store) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <ErrorMessage 
          message="Unable to load store information"
          onRetry={refetchStore}
        />
      </div>
    );
  }

  // Product error or not found state
  if (!product) {
    return (
      <div className="min-h-screen" style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}>
        <ThemedStoreHeader store={store} storeHandle={storeHandle} />
        
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div 
            className="bg-white rounded-lg shadow-sm border p-8 text-center"
            style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
          >
            <div 
              className="w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-6"
              style={{ backgroundColor: 'var(--theme-text-secondary, #6b7280)' }}
            >
              <svg 
                className="w-8 h-8 text-white" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            </div>

            <h1 
              className="text-2xl font-bold mb-4"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              Product Not Found
            </h1>
            
            <p 
              className="text-lg mb-6"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
            >
              The product you are looking for could not be found.
            </p>

            {/* Debug information */}
            {process.env.NODE_ENV === 'development' && (
              <div className="mb-6 p-4 bg-gray-100 rounded-lg text-left">
                <h3 className="font-semibold mb-2">Debug Information:</h3>
                <pre className="text-xs text-gray-600">
                  {JSON.stringify({
                    productId,
                    storeHandle,
                    medusaConfig,
                  }, null, 2)}
                </pre>
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => window.location.reload()}
                className="px-6 py-3 rounded-lg font-semibold transition-all duration-200 hover:shadow-lg"
                style={{ 
                  backgroundColor: 'var(--btn-primary, #3b82f6)',
                  color: 'var(--btn-text, #ffffff)',
                }}
              >
                Try Again
              </button>
              
              <button
                onClick={() => router.push(`/${storeHandle}`)}
                className="px-6 py-3 rounded-lg font-semibold transition-all duration-200 hover:shadow-lg"
                style={{ 
                  backgroundColor: 'var(--btn-primary, #3b82f6)',
                  color: 'var(--btn-text, #ffffff)',
                }}
              >
                Continue Shopping
              </button>
              
              <button
                onClick={() => router.back()}
                className="px-6 py-3 rounded-lg font-semibold transition-colors border"
                style={{ 
                  borderColor: 'var(--theme-border, #d1d5db)',
                  color: 'var(--theme-text, #111827)',
                }}
              >
                Go Back
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Success state - render product details
  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}>
      <ThemedStoreHeader store={store} storeHandle={storeHandle} />
      
      <ProductDetailContent 
        product={filteredProduct}
        store={store}
        storeHandle={storeHandle}
      />
    </div>
  );
};

export default function ProductDetailPage() {
  const params = useParams();
  const storeHandle = params?.storeHandle as string;
  const productId = params?.productId as string;

  // Early return if params are not available
  if (!storeHandle || !productId) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Invalid URL</h1>
          <p className="text-gray-600">Store handle or product ID is missing from the URL.</p>
        </div>
      </div>
    );
  }

  return (
    <GlobalProviders storeHandle={storeHandle}>
      <ProductDetailPageContent storeHandle={storeHandle} productId={productId} />
    </GlobalProviders>
  );
}