'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useStoreConfig } from '@/hooks/useStoreConfig';
import { GlobalProviders } from '@/components/layout/GlobalProviders';
import { ThemedStoreHeader } from '@/components/store/ThemedStoreHeader';
import { ProductListingContent } from '@/components/product/ProductListingContent';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ErrorMessage } from '@/components/ui/ErrorMessage';

const CategoriesPageContent: React.FC<{ storeHandle: string; categorySlug: string }> = ({ 
  storeHandle, 
  categorySlug 
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { 
    storeData, 
    isLoading: storeLoading, 
    error: storeError
  } = useStoreConfig();
  
  // Create store object from store configuration data with memoization
  const store = React.useMemo(() => {
    if (!storeData) return null;
    
    return {
      id: storeData.id || storeHandle,
      name: storeData.name || storeHandle,
      handle: storeData.handle || storeHandle,
      description: storeData.description || '',
      logo: storeData.logo,
      address: storeData.address,
      contact: storeData.contact,
      theme: storeData.theme,
      ...storeData,
    };
  }, [storeData, storeHandle]);
  
  const subcategorySlug = searchParams?.get('subcategory');
  const [isLoading, setIsLoading] = useState(true);

  // Store configuration is automatically fetched by StoreConfigProvider
  
  useEffect(() => {
    // Simulate loading time for demonstration
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);
  
    return () => clearTimeout(timer);
  }, [categorySlug, subcategorySlug]);
  console.log("storeError::::::::<>",{storeError,store})

  if (storeLoading||isLoading ) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">
            {storeLoading ? 'Loading store information...' : 'Loading category...'}
          </p>
        </div>
      </div>
    );
  }

  if (storeError && !storeData) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <ErrorMessage 
          message="Unable to load store information"
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}>
      <ThemedStoreHeader store={store} storeHandle={storeHandle} />
     
      <ProductListingContent 
        store={store}
        storeHandle={storeHandle}
        categorySlug={categorySlug}
        subcategorySlug={subcategorySlug}
      />
    </div>
  );
};

export default function CategoriesPage() {
  const params = useParams();
  const storeHandle = params?.storeHandle as string;
  const categorySlug = params?.categorySlug as string;

  // Early return if params are not available
  if (!storeHandle || !categorySlug) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Invalid URL</h1>
          <p className="text-gray-600">Store handle or category slug is missing from the URL.</p>
          
          {/* Debug information in development */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-6 p-4 bg-gray-100 rounded-lg text-left max-w-md mx-auto">
              <h3 className="font-semibold mb-2">Debug Information:</h3>
              <pre className="text-xs text-gray-600">
                {JSON.stringify({
                  params,
                  storeHandle,
                  categorySlug,
                  paramsType: typeof params,
                  paramsKeys: params ? Object.keys(params) : 'null',
                }, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <GlobalProviders storeHandle={storeHandle}>
      <CategoriesPageContent storeHandle={storeHandle} categorySlug={categorySlug} />
    </GlobalProviders>
  );
}