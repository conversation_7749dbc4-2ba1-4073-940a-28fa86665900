import React from 'react';
import { ModernStoreHomePage } from '@/components/store/ModernStoreHomePage';
import { ErrorBoundary } from '@/components/ui/ErrorBoundary';
import { GlobalProviders } from '@/components/layout/GlobalProviders';

interface StorePageProps {
  params: {
    storeHandle: string;
  };
}

export default function StorePage({ params }: StorePageProps) {
  // Validate store handle
  if (!params.storeHandle || typeof params.storeHandle !== 'string') {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Invalid Store</h1>
          <p className="text-gray-600">The store handle provided is not valid.</p>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <GlobalProviders storeHandle={params.storeHandle}>
        <ModernStoreHomePage storeHandle={params.storeHandle} />
      </GlobalProviders>
    </ErrorBoundary>
  );
}

// Generate metadata for SEO
export async function generateMetadata({ params }: StorePageProps) {
  const { storeHandle } = params;
  
  try {
    // Use proper API service for fetching store data
    const { storeConfigApi } = await import('@/lib/api/store-config');
    const storeConfig = await storeConfigApi.getStoreConfig(storeHandle);
    
    if (!storeConfig) {
      throw new Error('Store not found');
    }
    
    return {
      title: `${storeConfig.name || storeHandle} - Online Store`,
      description: storeConfig.description || `Shop at ${storeConfig.name || storeHandle} - Browse our products and categories`,
      openGraph: {
        title: `${storeConfig.name || storeHandle} Store`,
        description: storeConfig.description || `Discover amazing products at ${storeConfig.name || storeHandle}`,
        type: 'website',
        images: storeConfig.logo ? [{ url: storeConfig.logo }] : [],
      },
      twitter: {
        card: 'summary_large_image',
        title: `${storeConfig.name || storeHandle} Store`,
        description: storeConfig.description || `Discover amazing products at ${storeConfig.name || storeHandle}`,
        images: storeConfig.logo ? [storeConfig.logo] : [],
      },
    };
  } catch (error) {
    // Fallback to basic metadata if store config fetch fails
    console.warn('Failed to fetch store config for metadata:', error);
    return {
      title: `${storeHandle} - Online Store`,
      description: `Shop at ${storeHandle} - Browse our products and categories`,
      openGraph: {
        title: `${storeHandle} Store`,
        description: `Discover amazing products at ${storeHandle}`,
        type: 'website',
      },
    };
  }
}