import { CMSPagesList } from '@/components/store/cms/CMSPagesList';

interface PagesIndexProps {
  params: {
    storeHandle: string;
  };
}

export default function PagesIndex({ params }: PagesIndexProps) {
  return <CMSPagesList />;
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PagesIndexProps) {
  const { storeHandle } = params;
  
  return {
    title: `Pages | ${storeHandle}`,
    description: `Browse all available pages and information for ${storeHandle}`,
  };
}