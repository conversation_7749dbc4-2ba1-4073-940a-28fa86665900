import { CMSPageView } from '@/components/store/cms/CMSPageView';

interface CMSPageProps {
  params: {
    storeHandle: string;
    pageSlug: string;
  };
}

export default function CMSPage({ params }: CMSPageProps) {
  return <CMSPageView pageSlug={params.pageSlug} />;
}

// Generate metadata for SEO
export async function generateMetadata({ params }: CMSPageProps) {
  const { storeHandle, pageSlug } = params;
  
  try {
    // You could fetch the page data here for better SEO metadata
    // For now, we'll use basic metadata
    return {
      title: `${pageSlug.replace(/-/g, ' ')} | ${storeHandle}`,
      description: `View ${pageSlug.replace(/-/g, ' ')} page`,
    };
  } catch (error) {
    return {
      title: `Page | ${storeHandle}`,
      description: 'Page content',
    };
  }
}