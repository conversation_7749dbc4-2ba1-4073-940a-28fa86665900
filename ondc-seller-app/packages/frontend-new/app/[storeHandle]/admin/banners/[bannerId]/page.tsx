import { AdminBannerForm } from '@/components/admin/banners';

interface EditBannerPageProps {
  params: {
    storeHandle: string;
    bannerId: string; // This is actually the documentId from Strapi
  };
}

export default function EditBannerPage({ params }: EditBannerPageProps) {
  return (
    <AdminBannerForm 
      mode="edit" 
      bannerId={params.bannerId} // documentId passed as bannerId
      storeHandle={params.storeHandle}
    />
  );
}

export const metadata = {
  title: 'Edit Banner | Admin Dashboard',
  description: 'Edit promotional banner settings and content',
};