'use client';

import React from 'react';
import { AdminDashboard } from '@/components/admin/AdminDashboard';
import { PageLoadingWrapper } from '@/components/loading/PageLoadingWrapper';

export default function AdminPage() {
  return (
    <PageLoadingWrapper 
      loadingMessage="Loading admin dashboard..."
      loadingSubMessage="Setting up your store management panel"
      pageId="store-admin-page"
      minLoadingTime={0}
    >
      <AdminDashboard />
    </PageLoadingWrapper>
  );
}