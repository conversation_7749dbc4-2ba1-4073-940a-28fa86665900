'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import { AdminSidebar } from '@/components/admin/AdminSidebar';
import { AdminHeader } from '@/components/admin/AdminHeader';
import { AdminProviders } from '@/components/admin/AdminProviders';
import { AdminNavigationLoader } from '@/components/admin/AdminNavigationLoader';
import { AdminAuthGuard } from '@/components/admin/AdminAuthGuard';

// Note: Removed useAdminTokenCheck and useAdminNavigationGuard from layout
// These are now handled more gracefully within the improved auth guard

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  console.log('admin layout<<<<<<>>>>>')
  return (
    <AdminProviders>
      <AdminAuthGuard>
        <div className="min-h-screen bg-gray-50">
          {/* Admin Header */}
          <AdminHeader storeHandle={storeHandle} />
          
          <div className="flex pt-20">
            {/* Admin Sidebar */}
            <AdminSidebar storeHandle={storeHandle} />
            
            {/* Main Content */}
            <main className="flex-1 p-6">
              <div className="">
                {children}
              </div>
            </main>
          </div>
          
          {/* Global Navigation Loader */}
          <AdminNavigationLoader />
        </div>
      </AdminAuthGuard>
    </AdminProviders>
  );
}