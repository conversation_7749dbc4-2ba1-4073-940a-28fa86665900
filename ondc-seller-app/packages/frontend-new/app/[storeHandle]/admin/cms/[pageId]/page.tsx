import { AdminCMSPageForm } from '@/components/admin/cms';

interface EditCMSPageProps {
  params: {
    storeHandle: string;
    pageId: string;
  };
}

export default function EditCMSPage({ params }: EditCMSPageProps) {
  return (
    <AdminCMSPageForm 
      mode="edit" 
      pageId={params.pageId}
      storeHandle={params.storeHandle}
    />
  );
}

export const metadata = {
  title: 'Edit CMS Page | Admin Dashboard',
  description: 'Edit content page settings and content',
};