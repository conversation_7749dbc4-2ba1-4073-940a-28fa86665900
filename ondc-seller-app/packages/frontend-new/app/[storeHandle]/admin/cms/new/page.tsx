import { AdminCMSPageForm } from '@/components/admin/cms';

interface NewCMSPageProps {
  params: {
    storeHandle: string;
  };
}

export default function NewCMSPage({ params }: NewCMSPageProps) {
  return (
    <AdminCMSPageForm 
      mode="create" 
      storeHandle={params.storeHandle}
    />
  );
}

export const metadata = {
  title: 'Create CMS Page | Admin Dashboard',
  description: 'Create a new content page for your store',
};