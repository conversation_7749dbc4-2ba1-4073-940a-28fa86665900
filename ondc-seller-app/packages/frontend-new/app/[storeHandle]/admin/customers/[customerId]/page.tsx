'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import { AdminCustomerDetailsMD3 } from '@/components/admin/customers/AdminCustomerDetailsMD3';

export default function AdminCustomerDetailsPage() {
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  const customerId = params.customerId as string;

  return <AdminCustomerDetailsMD3 storeHandle={storeHandle} customerId={customerId} />;
}