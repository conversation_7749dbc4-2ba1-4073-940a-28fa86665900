import React from 'react';
import { StoreConfigProvider } from '@/components/store/StoreConfigProvider';

interface StoreLayoutProps {
  children: React.ReactNode;
  params: {
    storeHandle: string;
  };
}

export default function StoreLayout({ children, params }: StoreLayoutProps) {
  return (
    <StoreConfigProvider storeHandle={params.storeHandle}>
      {children}
    </StoreConfigProvider>
  );
}