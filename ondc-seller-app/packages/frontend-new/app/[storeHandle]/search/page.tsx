import React from 'react';
import { Metadata } from 'next';
import { SearchResults } from '@/components/search/SearchResults';

interface SearchPageProps {
  params: {
    storeHandle: string;
  };
  searchParams: {
    q?: string;
    page?: string;
    sort?: string;
    category?: string;
    collection?: string;
  };
}

export async function generateMetadata({ 
  params, 
  searchParams 
}: SearchPageProps): Promise<Metadata> {
  const { storeHandle } = params;
  const { q } = searchParams;
  
  const title = q 
    ? `Search results for "${q}" - ${storeHandle}` 
    : `Search - ${storeHandle}`;
    
  const description = q
    ? `Find products matching "${q}" in our ${storeHandle} store. Browse through our search results and discover what you're looking for.`
    : `Search for products in our ${storeHandle} store. Find exactly what you're looking for with our powerful search functionality.`;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
    },
    robots: {
      index: false, // Don't index search result pages
      follow: true,
    },
  };
}

export default function SearchPage({ params }: SearchPageProps) {
  const { storeHandle } = params;

  return (
    <div className="min-h-screen bg-gray-50">
      <SearchResults storeHandle={storeHandle} />
    </div>
  );
}
