import React from 'react';
import { DynamicStorePageLayout } from '@/components/store/static-pages/DynamicStorePageLayout';
import { ErrorBoundary } from '@/components/ui/ErrorBoundary';
import { GlobalProviders } from '@/components/layout/GlobalProviders';

interface ShippingPolicyPageProps {
  params: {
    storeHandle: string;
  };
}

export default function ShippingPolicyPage({ params }: ShippingPolicyPageProps) {
  const { storeHandle } = params;
  
  // Mock store name from handle for fallback content
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  // Fallback content if CMS content is not available
  const fallbackContent = (
    <>
      <h2 style={{ 
        fontSize: '2rem',
        fontWeight: '600',
        marginBottom: '16px',
        color: '#3b82f6'
      }}>
        Shipping Policy
      </h2>
      
      <div style={{
        backgroundColor: '#f0f9ff',
        border: '1px solid #bae6fd',
        borderRadius: '8px',
        padding: '16px',
        marginBottom: '24px'
      }}>
        <p style={{ margin: '0', lineHeight: '1.7' }}>
          <strong>Effective Date:</strong> January 15, 2024<br />
          <strong>Last Updated:</strong> January 15, 2024
        </p>
      </div>

      <p style={{ marginBottom: '24px', lineHeight: '1.7' }}>
        At {storeName}, we are committed to delivering your orders quickly and safely. This Shipping 
        Policy outlines our shipping methods, delivery times, costs, and terms. Please review this 
        information before placing your order.
      </p>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '24px'
      }}>
        Shipping Overview
      </h3>

      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '24px',
        marginBottom: '32px'
      }}>
        <div style={{ 
          backgroundColor: '#f9fafb',
          padding: '24px',
          borderRadius: '12px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <div style={{ 
              backgroundColor: '#3b82f6',
              color: 'white',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '16px',
              fontSize: '18px'
            }}>
              🕒
            </div>
            <h4 style={{ 
              fontSize: '1.25rem',
              fontWeight: '600',
              margin: '0'
            }}>
              Processing Time
            </h4>
          </div>
          <p style={{ 
            fontSize: '14px',
            color: '#6b7280',
            margin: '0',
            lineHeight: '1.6'
          }}>
            Orders are processed within 1-2 business days (Monday-Friday, excluding holidays).
          </p>
        </div>
        
        <div style={{ 
          backgroundColor: '#f9fafb',
          padding: '24px',
          borderRadius: '12px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <div style={{ 
              backgroundColor: '#10b981',
              color: 'white',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '16px',
              fontSize: '18px'
            }}>
              🚚
            </div>
            <h4 style={{ 
              fontSize: '1.25rem',
              fontWeight: '600',
              margin: '0'
            }}>
              Free Shipping
            </h4>
          </div>
          <p style={{ 
            fontSize: '14px',
            color: '#6b7280',
            margin: '0',
            lineHeight: '1.6'
          }}>
            Free standard shipping on orders over $50 within the continental United States.
          </p>
        </div>
        
        <div style={{ 
          backgroundColor: '#f9fafb',
          padding: '24px',
          borderRadius: '12px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <div style={{ 
              backgroundColor: '#0ea5e9',
              color: 'white',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '16px',
              fontSize: '18px'
            }}>
              🌍
            </div>
            <h4 style={{ 
              fontSize: '1.25rem',
              fontWeight: '600',
              margin: '0'
            }}>
              International Shipping
            </h4>
          </div>
          <p style={{ 
            fontSize: '14px',
            color: '#6b7280',
            margin: '0',
            lineHeight: '1.6'
          }}>
            We ship to over 50 countries worldwide with competitive international rates.
          </p>
        </div>
        
        <div style={{ 
          backgroundColor: '#f9fafb',
          padding: '24px',
          borderRadius: '12px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <div style={{ 
              backgroundColor: '#dc2626',
              color: 'white',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '16px',
              fontSize: '18px'
            }}>
              📦
            </div>
            <h4 style={{ 
              fontSize: '1.25rem',
              fontWeight: '600',
              margin: '0'
            }}>
              Tracking Included
            </h4>
          </div>
          <p style={{ 
            fontSize: '14px',
            color: '#6b7280',
            margin: '0',
            lineHeight: '1.6'
          }}>
            All shipments include tracking information sent to your email address.
          </p>
        </div>
      </div>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        Domestic Shipping (United States)
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        We offer several shipping options for domestic orders within the United States:
      </p>

      <div style={{
        backgroundColor: '#ffffff',
        border: '1px solid #e5e7eb',
        borderRadius: '8px',
        overflow: 'hidden',
        marginBottom: '32px'
      }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ backgroundColor: '#f9fafb' }}>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb', fontWeight: '600' }}>
                Shipping Method
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb', fontWeight: '600' }}>
                Delivery Time
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb', fontWeight: '600' }}>
                Cost
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb', fontWeight: '600' }}>
                Tracking
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  🚗 Standard Shipping
                </div>
              </td>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>5-7 business days</td>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>$5.99 (Free on orders $50+)</td>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>✓ Included</td>
            </tr>
            <tr>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  ⚡ Expedited Shipping
                </div>
              </td>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>3-4 business days</td>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>$12.99</td>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>✓ Included</td>
            </tr>
            <tr>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  ✈️ Express Shipping
                </div>
              </td>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>1-2 business days</td>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>$24.99</td>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>✓ Included</td>
            </tr>
            <tr>
              <td style={{ padding: '12px' }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  🚀 Overnight Shipping
                </div>
              </td>
              <td style={{ padding: '12px' }}>Next business day</td>
              <td style={{ padding: '12px' }}>$39.99</td>
              <td style={{ padding: '12px' }}>✓ Included</td>
            </tr>
          </tbody>
        </table>
      </div>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        International Shipping
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        We proudly ship to customers worldwide. International shipping rates and delivery times 
        vary by destination and are calculated at checkout based on your location and order weight.
      </p>

      <h4 style={{ 
        fontSize: '1.25rem',
        fontWeight: '600',
        marginTop: '24px',
        marginBottom: '16px'
      }}>
        International Shipping Zones
      </h4>

      <div style={{
        backgroundColor: '#ffffff',
        border: '1px solid #e5e7eb',
        borderRadius: '8px',
        overflow: 'hidden',
        marginBottom: '32px'
      }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ backgroundColor: '#f9fafb' }}>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb', fontWeight: '600' }}>
                Zone
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb', fontWeight: '600' }}>
                Countries
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb', fontWeight: '600' }}>
                Delivery Time
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb', fontWeight: '600' }}>
                Starting Cost
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>Zone 1</td>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>Canada, Mexico</td>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>7-14 business days</td>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>$15.99</td>
            </tr>
            <tr>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>Zone 2</td>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>Europe, UK, Australia</td>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>10-21 business days</td>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>$24.99</td>
            </tr>
            <tr>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>Zone 3</td>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>Asia, South America</td>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>14-28 business days</td>
              <td style={{ padding: '12px', borderBottom: '1px solid #e5e7eb' }}>$29.99</td>
            </tr>
            <tr>
              <td style={{ padding: '12px' }}>Zone 4</td>
              <td style={{ padding: '12px' }}>Africa, Middle East</td>
              <td style={{ padding: '12px' }}>21-35 business days</td>
              <td style={{ padding: '12px' }}>$34.99</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div style={{
        backgroundColor: '#fef3c7',
        border: '1px solid #fbbf24',
        borderRadius: '8px',
        padding: '16px',
        marginBottom: '24px'
      }}>
        <h4 style={{ 
          fontSize: '1.125rem',
          fontWeight: '600',
          marginBottom: '12px',
          margin: '0 0 12px 0'
        }}>
          Important International Shipping Notes
        </h4>
        <ul style={{ paddingLeft: '20px', margin: '0' }}>
          <li style={{ marginBottom: '8px' }}>
            <strong>Customs and Duties:</strong> International customers are responsible for any 
            customs duties, taxes, or fees imposed by their country.
          </li>
          <li style={{ marginBottom: '8px' }}>
            <strong>Restricted Items:</strong> Some products may not be available for international 
            shipping due to regulations.
          </li>
          <li style={{ marginBottom: '8px' }}>
            <strong>Address Accuracy:</strong> Please ensure your shipping address is complete and 
            accurate to avoid delays.
          </li>
        </ul>
      </div>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        Order Processing
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        Orders are processed Monday through Friday, excluding holidays. Orders placed after 2:00 PM EST 
        will be processed the next business day.
      </p>
      
      <h4 style={{ 
        fontSize: '1.25rem',
        fontWeight: '600',
        marginTop: '24px',
        marginBottom: '12px'
      }}>
        Processing Timeline
      </h4>
      
      <ul style={{ paddingLeft: '24px', lineHeight: '1.7', marginBottom: '24px' }}>
        <li style={{ marginBottom: '8px' }}>
          <strong>In-Stock Items:</strong> 1-2 business days
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Pre-Order Items:</strong> Ships on or before the estimated release date
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Custom/Personalized Items:</strong> 3-5 business days
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Large/Oversized Items:</strong> 2-3 business days
        </li>
      </ul>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        Contact Information
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        For shipping-related questions or concerns, please contact us:
      </p>
      
      <div style={{ marginLeft: '16px', marginBottom: '24px' }}>
        <p style={{ marginBottom: '8px' }}>
          <strong>Shipping Support:</strong> shipping@{storeHandle}.com<br />
          <strong>Customer Service:</strong> support@{storeHandle}.com<br />
          <strong>Phone:</strong> +****************<br />
          <strong>Business Hours:</strong> Monday - Friday, 9:00 AM - 6:00 PM (EST)
        </p>
      </div>

      <div style={{
        backgroundColor: '#f0f9ff',
        border: '1px solid #bae6fd',
        borderRadius: '8px',
        padding: '16px',
        marginTop: '32px'
      }}>
        <p style={{ margin: '0', lineHeight: '1.7' }}>
          <strong>Note:</strong> This shipping policy is subject to change without notice. Any changes 
          will be posted on this page with an updated effective date. Shipping rates and delivery 
          times are estimates and may vary based on carrier performance and external factors.
        </p>
      </div>
    </>
  );

  return (
    <ErrorBoundary>
      <GlobalProviders storeHandle={storeHandle}>
        <DynamicStorePageLayout 
          pageName="Shipping policy"
          fallbackTitle="Shipping Policy"
          fallbackContent={fallbackContent}
        />
      </GlobalProviders>
    </ErrorBoundary>
  );
}

// Generate metadata for SEO
export async function generateMetadata({ params }: ShippingPolicyPageProps) {
  const { storeHandle } = params;
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  
  return {
    title: `Shipping Policy | ${storeName}`,
    description: `Learn about ${storeName}'s shipping policy including delivery times, shipping costs, international shipping, and tracking information.`,
    keywords: 'shipping policy, delivery, shipping costs, international shipping, tracking, shipping times',
  };
}