'use client';

import React, { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { GlobalProviders } from '@/components/layout/GlobalProviders';
import { ThemedStoreHeader } from '@/components/store/ThemedStoreHeader';
import { ProductListingContent } from '@/components/product/ProductListingContent';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ErrorMessage } from '@/components/ui/ErrorMessage';
import { useStoreConfig } from '@/hooks/useStoreConfig';

const SubcategoriesPageContent: React.FC<{ storeHandle: string; subcategorySlug: string }> = ({ 
  storeHandle, 
  subcategorySlug 
}) => {
  const [isLoading, setIsLoading] = useState(true);

  const { 
    storeData, 
    isLoading: storeLoading, 
    error: storeError
  } = useStoreConfig();

  // Create store object from store configuration data with memoization
  const store = React.useMemo(() => {
    if (!storeData) return null;
    
    return {
      id: storeData.id || storeHandle,
      name: storeData.name || storeHandle,
      handle: storeData.handle || storeHandle,
      description: storeData.description || '',
      logo: storeData.logo,
      address: storeData.address,
      contact: storeData.contact,
      theme: storeData.theme,
      ...storeData,
    };
  }, [storeData, storeHandle]);

  useEffect(() => {
    // Simulate loading time for demonstration
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, [subcategorySlug]);

  if (storeLoading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (storeError || !store) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <ErrorMessage 
          message="Unable to load store information"
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}>
      <ThemedStoreHeader store={store} storeHandle={storeHandle} />
      
      <ProductListingContent 
        store={store}
        storeHandle={storeHandle}
        subcategorySlug={subcategorySlug}
        isSubcategoryPage={true}
      />
    </div>
  );
};

export default function SubcategoriesPage() {
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  const subcategorySlug = params.subcategorySlug as string;

  return (
    <GlobalProviders storeHandle={storeHandle}>
      <SubcategoriesPageContent storeHandle={storeHandle} subcategorySlug={subcategorySlug} />
    </GlobalProviders>
  );
}