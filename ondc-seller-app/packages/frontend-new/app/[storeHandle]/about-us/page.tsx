import React from 'react';
import { DynamicStorePageLayout } from '@/components/store/static-pages/DynamicStorePageLayout';
import { ErrorBoundary } from '@/components/ui/ErrorBoundary';
import { GlobalProviders } from '@/components/layout/GlobalProviders';

interface AboutUsPageProps {
  params: {
    storeHandle: string;
  };
}

export default function AboutUsPage({ params }: AboutUsPageProps) {
  const { storeHandle } = params;
  
  // Mock store name from handle for fallback content
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  // Fallback content if CMS content is not available
  const fallbackContent = (
    <>
      <h2 style={{ 
        fontSize: '2rem',
        fontWeight: '600',
        marginBottom: '16px',
        color: '#3b82f6'
      }}>
        Welcome to {storeName}
      </h2>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        At {storeName}, we are passionate about providing our customers with the highest quality products 
        and exceptional service. Founded with a vision to make online shopping convenient, reliable, and 
        enjoyable, we have grown to become a trusted destination for customers seeking quality and value.
      </p>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        Our Story
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        {storeName} was established in 2020 with a simple mission: to bridge the gap between quality 
        products and customer satisfaction. What started as a small venture has evolved into a 
        comprehensive online marketplace, serving thousands of satisfied customers across the region.
      </p>

      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        Our journey began when our founders recognized the need for a reliable online platform that 
        prioritizes customer experience while offering competitive prices. Today, we continue to uphold 
        these values while expanding our product range and improving our services.
      </p>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '24px'
      }}>
        What We Offer
      </h3>

      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '24px',
        marginBottom: '32px'
      }}>
        <div style={{ 
          backgroundColor: '#f9fafb',
          padding: '24px',
          borderRadius: '12px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <div style={{ 
              backgroundColor: '#3b82f6',
              color: 'white',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '16px',
              fontSize: '18px'
            }}>
              🏪
            </div>
            <h4 style={{ 
              fontSize: '1.25rem',
              fontWeight: '600',
              margin: '0'
            }}>
              Quality Products
            </h4>
          </div>
          <p style={{ 
            fontSize: '14px',
            color: '#6b7280',
            margin: '0',
            lineHeight: '1.6'
          }}>
            We carefully curate our product selection to ensure that every item meets our high 
            standards for quality, durability, and value.
          </p>
        </div>
        
        <div style={{ 
          backgroundColor: '#f9fafb',
          padding: '24px',
          borderRadius: '12px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <div style={{ 
              backgroundColor: '#dc2626',
              color: 'white',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '16px',
              fontSize: '18px'
            }}>
              👥
            </div>
            <h4 style={{ 
              fontSize: '1.25rem',
              fontWeight: '600',
              margin: '0'
            }}>
              Customer Service
            </h4>
          </div>
          <p style={{ 
            fontSize: '14px',
            color: '#6b7280',
            margin: '0',
            lineHeight: '1.6'
          }}>
            Our dedicated customer service team is always ready to assist you with any questions 
            or concerns you may have.
          </p>
        </div>
        
        <div style={{ 
          backgroundColor: '#f9fafb',
          padding: '24px',
          borderRadius: '12px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <div style={{ 
              backgroundColor: '#059669',
              color: 'white',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '16px',
              fontSize: '18px'
            }}>
              🚚
            </div>
            <h4 style={{ 
              fontSize: '1.25rem',
              fontWeight: '600',
              margin: '0'
            }}>
              Fast Delivery
            </h4>
          </div>
          <p style={{ 
            fontSize: '14px',
            color: '#6b7280',
            margin: '0',
            lineHeight: '1.6'
          }}>
            We partner with reliable shipping providers to ensure your orders reach you quickly 
            and safely.
          </p>
        </div>
        
        <div style={{ 
          backgroundColor: '#f9fafb',
          padding: '24px',
          borderRadius: '12px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <div style={{ 
              backgroundColor: '#d97706',
              color: 'white',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '16px',
              fontSize: '18px'
            }}>
              🔒
            </div>
            <h4 style={{ 
              fontSize: '1.25rem',
              fontWeight: '600',
              margin: '0'
            }}>
              Secure Shopping
            </h4>
          </div>
          <p style={{ 
            fontSize: '14px',
            color: '#6b7280',
            margin: '0',
            lineHeight: '1.6'
          }}>
            Your privacy and security are our top priorities. We use advanced encryption to 
            protect your personal and payment information.
          </p>
        </div>
      </div>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        Our Mission
      </h3>
      
      <div style={{
        backgroundColor: '#eff6ff',
        border: '1px solid #bfdbfe',
        borderRadius: '8px',
        padding: '16px',
        margin: '16px 0'
      }}>
        <p style={{ 
          fontWeight: '500',
          margin: '0',
          lineHeight: '1.7'
        }}>
          "To provide an exceptional online shopping experience by offering quality products, 
          competitive prices, and outstanding customer service while building lasting relationships 
          with our customers."
        </p>
      </div>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        Our Values
      </h3>
      
      <ul style={{ paddingLeft: '24px', lineHeight: '1.7' }}>
        <li style={{ marginBottom: '8px' }}>
          <strong>Customer First:</strong> We prioritize our customers' needs and satisfaction above all else.
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Quality Assurance:</strong> We maintain strict quality standards for all our products.
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Transparency:</strong> We believe in honest communication and transparent business practices.
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Innovation:</strong> We continuously improve our services and embrace new technologies.
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Sustainability:</strong> We are committed to environmentally responsible business practices.
        </li>
      </ul>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        Contact Information
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        We'd love to hear from you! Whether you have questions about our products, need assistance 
        with an order, or simply want to share your feedback, our team is here to help.
      </p>
      
      <div style={{ marginTop: '16px' }}>
        <p style={{ marginBottom: '8px' }}>
          <strong>Email:</strong> support@{storeHandle}.com
        </p>
        <p style={{ marginBottom: '8px' }}>
          <strong>Phone:</strong> +****************
        </p>
        <p style={{ marginBottom: '8px' }}>
          <strong>Business Hours:</strong> Monday - Friday, 9:00 AM - 6:00 PM (EST)
        </p>
      </div>

      <div style={{
        backgroundColor: '#f0f9ff',
        border: '1px solid #bae6fd',
        borderRadius: '8px',
        padding: '16px',
        marginTop: '32px'
      }}>
        <p style={{ margin: '0', lineHeight: '1.7' }}>
          Thank you for choosing {storeName}. We look forward to serving you and providing you with 
          an exceptional shopping experience!
        </p>
      </div>
    </>
  );

  return (
    <ErrorBoundary>
      <GlobalProviders storeHandle={storeHandle}>
        <DynamicStorePageLayout 
          pageName="About us"
          fallbackTitle="About Us"
          fallbackContent={fallbackContent}
        />
      </GlobalProviders>
    </ErrorBoundary>
  );
}

// Generate metadata for SEO
export async function generateMetadata({ params }: AboutUsPageProps) {
  const { storeHandle } = params;
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  
  return {
    title: `About Us | ${storeName}`,
    description: `Learn more about ${storeName}, our story, mission, values, and commitment to providing quality products and exceptional customer service.`,
    keywords: 'about us, company story, mission, values, customer service',
  };
}