import React from 'react';
import { DynamicStorePageLayout } from '@/components/store/static-pages/DynamicStorePageLayout';
import { ErrorBoundary } from '@/components/ui/ErrorBoundary';
import { GlobalProviders } from '@/components/layout/GlobalProviders';

interface RefundPolicyPageProps {
  params: {
    storeHandle: string;
  };
}

export default function RefundPolicyPage({ params }: RefundPolicyPageProps) {
  const { storeHandle } = params;
  
  // Mock store name from handle for fallback content
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  // Fallback content if CMS content is not available
  const fallbackContent = (
    <>
      <h2 style={{ 
        fontSize: '2rem',
        fontWeight: '600',
        marginBottom: '16px',
        color: '#3b82f6'
      }}>
        Refund and Return Policy
      </h2>
      
      <div style={{
        backgroundColor: '#f0f9ff',
        border: '1px solid #bae6fd',
        borderRadius: '8px',
        padding: '16px',
        marginBottom: '24px'
      }}>
        <p style={{ margin: '0', lineHeight: '1.7' }}>
          <strong>Effective Date:</strong> January 15, 2024<br />
          <strong>Last Updated:</strong> January 15, 2024
        </p>
      </div>

      <p style={{ marginBottom: '24px', lineHeight: '1.7' }}>
        At {storeName}, we want you to be completely satisfied with your purchase. This Refund Policy 
        outlines the terms and conditions for returns, exchanges, and refunds. Please read this policy 
        carefully before making a purchase.
      </p>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '24px'
      }}>
        Return Policy Overview
      </h3>

      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '24px',
        marginBottom: '32px'
      }}>
        <div style={{ 
          backgroundColor: '#f9fafb',
          padding: '24px',
          borderRadius: '12px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <div style={{ 
              backgroundColor: '#3b82f6',
              color: 'white',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '16px',
              fontSize: '18px'
            }}>
              🕒
            </div>
            <h4 style={{ 
              fontSize: '1.25rem',
              fontWeight: '600',
              margin: '0'
            }}>
              30-Day Return Window
            </h4>
          </div>
          <p style={{ 
            fontSize: '14px',
            color: '#6b7280',
            margin: '0',
            lineHeight: '1.6'
          }}>
            You have 30 days from the date of delivery to initiate a return for most items.
          </p>
        </div>
        
        <div style={{ 
          backgroundColor: '#f9fafb',
          padding: '24px',
          borderRadius: '12px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <div style={{ 
              backgroundColor: '#10b981',
              color: 'white',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '16px',
              fontSize: '18px'
            }}>
              ✅
            </div>
            <h4 style={{ 
              fontSize: '1.25rem',
              fontWeight: '600',
              margin: '0'
            }}>
              Original Condition
            </h4>
          </div>
          <p style={{ 
            fontSize: '14px',
            color: '#6b7280',
            margin: '0',
            lineHeight: '1.6'
          }}>
            Items must be in original, unused condition with all tags and packaging intact.
          </p>
        </div>
        
        <div style={{ 
          backgroundColor: '#f9fafb',
          padding: '24px',
          borderRadius: '12px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <div style={{ 
              backgroundColor: '#0ea5e9',
              color: 'white',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '16px',
              fontSize: '18px'
            }}>
              🚚
            </div>
            <h4 style={{ 
              fontSize: '1.25rem',
              fontWeight: '600',
              margin: '0'
            }}>
              Free Return Shipping
            </h4>
          </div>
          <p style={{ 
            fontSize: '14px',
            color: '#6b7280',
            margin: '0',
            lineHeight: '1.6'
          }}>
            We provide prepaid return labels for eligible returns within our standard policy.
          </p>
        </div>
        
        <div style={{ 
          backgroundColor: '#f9fafb',
          padding: '24px',
          borderRadius: '12px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <div style={{ 
              backgroundColor: '#dc2626',
              color: 'white',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '16px',
              fontSize: '18px'
            }}>
              💳
            </div>
            <h4 style={{ 
              fontSize: '1.25rem',
              fontWeight: '600',
              margin: '0'
            }}>
              Quick Refunds
            </h4>
          </div>
          <p style={{ 
            fontSize: '14px',
            color: '#6b7280',
            margin: '0',
            lineHeight: '1.6'
          }}>
            Refunds are processed within 5-7 business days after we receive your return.
          </p>
        </div>
      </div>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        Eligible Items for Return
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        Most items purchased from {storeName} are eligible for return within 30 days of delivery, 
        provided they meet our return conditions:
      </p>
      
      <ul style={{ paddingLeft: '24px', lineHeight: '1.7', marginBottom: '24px' }}>
        <li style={{ marginBottom: '8px' }}>
          Items must be in original, unused condition
        </li>
        <li style={{ marginBottom: '8px' }}>
          All original tags, labels, and packaging must be included
        </li>
        <li style={{ marginBottom: '8px' }}>
          Items must not show signs of wear, damage, or alteration
        </li>
        <li style={{ marginBottom: '8px' }}>
          Original receipt or proof of purchase is required
        </li>
      </ul>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        Non-Returnable Items
      </h3>
      
      <div style={{
        backgroundColor: '#fef3c7',
        border: '1px solid #fbbf24',
        borderRadius: '8px',
        padding: '16px',
        marginBottom: '24px'
      }}>
        <p style={{ margin: '0 0 12px 0', lineHeight: '1.7', fontWeight: '600' }}>
          The following items cannot be returned:
        </p>
        
        <ul style={{ paddingLeft: '20px', margin: '0' }}>
          <li style={{ marginBottom: '8px' }}>
            Personalized or customized items
          </li>
          <li style={{ marginBottom: '8px' }}>
            Perishable goods (food, flowers, etc.)
          </li>
          <li style={{ marginBottom: '8px' }}>
            Intimate or sanitary goods
          </li>
          <li style={{ marginBottom: '8px' }}>
            Hazardous materials or flammable liquids
          </li>
          <li style={{ marginBottom: '8px' }}>
            Digital downloads or software
          </li>
          <li style={{ marginBottom: '8px' }}>
            Gift cards or vouchers
          </li>
          <li style={{ marginBottom: '8px' }}>
            Items returned after 30 days from delivery
          </li>
        </ul>
      </div>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        How to Initiate a Return
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        To start a return, please follow these simple steps:
      </p>
      
      <ol style={{ paddingLeft: '24px', lineHeight: '1.7', marginBottom: '24px' }}>
        <li style={{ marginBottom: '12px' }}>
          <strong>Contact Us:</strong> Email us at returns@{storeHandle}.com or call +**************** 
          with your order number and reason for return.
        </li>
        <li style={{ marginBottom: '12px' }}>
          <strong>Receive Return Authorization:</strong> We'll provide you with a Return Merchandise 
          Authorization (RMA) number and return instructions.
        </li>
        <li style={{ marginBottom: '12px' }}>
          <strong>Package Your Items:</strong> Securely package the items in their original packaging 
          with all accessories and documentation.
        </li>
        <li style={{ marginBottom: '12px' }}>
          <strong>Ship Your Return:</strong> Use the prepaid return label we provide and drop off 
          at any authorized shipping location.
        </li>
        <li style={{ marginBottom: '12px' }}>
          <strong>Receive Your Refund:</strong> Once we receive and inspect your return, we'll 
          process your refund within 5-7 business days.
        </li>
      </ol>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        Refund Processing
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        Once your return is received and inspected, we will send you an email to notify you that 
        we have received your returned item. We will also notify you of the approval or rejection 
        of your refund.
      </p>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        If your refund is approved, it will be processed and a credit will automatically be applied 
        to your original method of payment within 5-7 business days.
      </p>

      <div style={{
        backgroundColor: '#eff6ff',
        border: '1px solid #bfdbfe',
        borderRadius: '8px',
        padding: '16px',
        marginTop: '24px'
      }}>
        <h4 style={{ 
          fontSize: '1.125rem',
          fontWeight: '600',
          marginBottom: '12px',
          margin: '0 0 12px 0'
        }}>
          Refund Timeline by Payment Method
        </h4>
        <ul style={{ paddingLeft: '20px', margin: '0' }}>
          <li style={{ marginBottom: '8px' }}>
            <strong>Credit/Debit Cards:</strong> 3-5 business days
          </li>
          <li style={{ marginBottom: '8px' }}>
            <strong>PayPal:</strong> 1-2 business days
          </li>
          <li style={{ marginBottom: '8px' }}>
            <strong>Bank Transfer:</strong> 5-7 business days
          </li>
        </ul>
      </div>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        Contact Information
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        If you have any questions about our refund policy, please contact us:
      </p>
      
      <div style={{ marginLeft: '16px', marginBottom: '24px' }}>
        <p style={{ marginBottom: '8px' }}>
          <strong>Returns Email:</strong> returns@{storeHandle}.com<br />
          <strong>Customer Service:</strong> support@{storeHandle}.com<br />
          <strong>Phone:</strong> +****************<br />
          <strong>Business Hours:</strong> Monday - Friday, 9:00 AM - 6:00 PM (EST)
        </p>
      </div>

      <div style={{
        backgroundColor: '#f0f9ff',
        border: '1px solid #bae6fd',
        borderRadius: '8px',
        padding: '16px',
        marginTop: '32px'
      }}>
        <p style={{ margin: '0', lineHeight: '1.7' }}>
          <strong>Note:</strong> This refund policy is subject to change without notice. Any changes 
          will be posted on this page with an updated effective date. Your continued use of our 
          services after any changes constitutes acceptance of the new policy.
        </p>
      </div>
    </>
  );

  return (
    <ErrorBoundary>
      <GlobalProviders storeHandle={storeHandle}>
        <DynamicStorePageLayout 
          pageName="Refund policy"
          fallbackTitle="Refund Policy"
          fallbackContent={fallbackContent}
        />
      </GlobalProviders>
    </ErrorBoundary>
  );
}

// Generate metadata for SEO
export async function generateMetadata({ params }: RefundPolicyPageProps) {
  const { storeHandle } = params;
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  
  return {
    title: `Refund Policy | ${storeName}`,
    description: `Learn about ${storeName}'s refund and return policy. Understand our 30-day return window, eligible items, and refund process.`,
    keywords: 'refund policy, return policy, returns, refunds, exchange, money back guarantee',
  };
}