// app/[storeHandle]/onboarding/page.tsx

"use client";
import { useSearchParams } from "next/navigation";
import { useParams } from "next/navigation";
import StoreConfigurationForm from "@/components/onboarding/StoreConfigurationForm";
import AddProduct from "@/components/onboarding/AddProduct";
import BulkUpload from "@/components/onboarding/BulkUpload";

export default function OnboardingPage() {
  const { storeHandle } = useParams();
  const searchParams = useSearchParams();
  const stage = searchParams.get("stage");

  if (!stage || stage === "store-configuration") {
    return <StoreConfigurationForm storeHandle={storeHandle} />;
  }

  if (stage === "add-product") {
    return <AddProduct storeHandle={storeHandle} />;
  }

  if (stage === "import-products") {
    return <BulkUpload storeHandle={storeHandle} />;
  }

  return <p>Invalid onboarding stage.</p>;
}
