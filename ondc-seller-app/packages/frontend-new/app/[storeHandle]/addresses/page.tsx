'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useStoreConfig } from '@/hooks/useStoreConfig';
import { GlobalProviders } from '@/components/layout/GlobalProviders';
import { useStoreAuthContext } from '@/components/auth/StoreAuthProvider';
import { ThemedStoreHeader } from '@/components/store/ThemedStoreHeader';
import { AddressList } from '@/components/address/AddressList';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ErrorMessage } from '@/components/ui/ErrorMessage';
import { UserAddress } from '@/types/auth';

const AddressesPageContent: React.FC<{ storeHandle: string }> = ({ storeHandle }) => {
  const router = useRouter();
  const { 
    storeData, 
    isLoading: storeLoading, 
    error: storeError
  } = useStoreConfig();
  const { isAuthenticated, isLoading: authLoading, user } = useStoreAuthContext();
  const [addresses, setAddresses] = useState<UserAddress[]>([]);

  // Create store object from store configuration data
  const store = React.useMemo(() => {
    if (!storeData) return null;
    
    return {
      id: storeData.id || storeHandle,
      name: storeData.name || storeHandle,
      handle: storeData.handle || storeHandle,
      description: storeData.description || '',
      logo: storeData.logo,
      address: storeData.address,
      contact: storeData.contact,
      theme: storeData.theme,
      ...storeData,
    };
  }, [storeData, storeHandle]);

  // Redirect to home if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push(`/${storeHandle}`);
    }
  }, [isAuthenticated, authLoading, router, storeHandle]);

  // Load user addresses
  useEffect(() => {
    if (user && user.addresses) {
      setAddresses(user.addresses);
    }
  }, [user]);

  if (storeLoading || authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (storeError || !store) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <ErrorMessage 
          message="Unable to load store information"
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}>
      <ThemedStoreHeader store={store} storeHandle={storeHandle} />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="mb-8">
          <ol className="flex items-center space-x-2 text-sm">
            <li>
              <a 
                href={`/${storeHandle}`}
                className="transition-colors hover:opacity-80"
                style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
              >
                Home
              </a>
            </li>
            <li>
              <svg className="w-4 h-4 mx-2" fill="none" stroke="var(--theme-text-secondary, #6b7280)" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </li>
            <li>
              <span style={{ color: 'var(--theme-text, #111827)' }} className="font-medium">
                Addresses
              </span>
            </li>
          </ol>
        </nav>

        {/* Page Title */}
        <div className="mb-8">
          <h1 
            className="text-3xl font-bold"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            My Addresses
          </h1>
          <p 
            className="mt-2"
            style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
          >
            Manage your shipping and billing addresses
          </p>
        </div>

        {/* Address List */}
        <AddressList 
          addresses={addresses} 
          storeHandle={storeHandle}
          onUpdateAddresses={setAddresses}
        />
      </div>
    </div>
  );
};

export default function AddressesPage() {
  const params = useParams();
  const storeHandle = params.storeHandle as string;

  return (
    <GlobalProviders storeHandle={storeHandle}>
      <AddressesPageContent storeHandle={storeHandle} />
    </GlobalProviders>
  );
}