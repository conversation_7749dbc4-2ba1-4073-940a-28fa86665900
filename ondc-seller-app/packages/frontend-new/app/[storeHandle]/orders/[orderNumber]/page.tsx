'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useStoreConfig } from '@/hooks/useStoreConfig';
import { GlobalProviders } from '@/components/layout/GlobalProviders';
import { useStoreAuthContext } from '@/components/auth/StoreAuthProvider';
import { ThemedStoreHeader } from '@/components/store/ThemedStoreHeader';
import { OrderDetails } from '@/components/order/OrderDetails';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ErrorMessage } from '@/components/ui/ErrorMessage';
import { useOrder } from '@/hooks/medusa/useOrders';
import { Order } from '@/lib/api/medusa/orders';
import { 
  Box, 
  Container, 
  Typography, 
  Card, 
  CardContent, 
  Button, 
  Stack, 
  Breadcrumbs,
  Link as MuiLink,
  CircularProgress,
  Alert
} from '@mui/material';
import { 
  Home, 
  ShoppingBag, 
  NavigateNext,
  Refresh,
  ErrorOutline
} from '@mui/icons-material';
import Link from 'next/link';

const OrderPageContent: React.FC<{ storeHandle: string; orderNumber: string }> = ({ 
  storeHandle, 
  orderNumber 
}) => {
  const router = useRouter();
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const { 
    storeData, 
    isLoading: storeLoading, 
    error: storeError
  } = useStoreConfig();
  const { isAuthenticated, isLoading: authLoading, user, token } = useStoreAuthContext();
  
  // Use the useOrder hook to fetch order details
  const { 
    data: orderResponse, 
    isLoading: orderLoading, 
    error: orderError,
    refetch: refetchOrder
  } = useOrder(
    orderNumber,
    storeHandle,
    token || undefined,
    isAuthenticated && !!token && !!orderNumber
  );
  
  const order = orderResponse?.order || null;

  // Handle initial loading state
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitialLoading(false);
    }, 500); // Small delay to show loading state
    
    return () => clearTimeout(timer);
  }, []);

  // Create store object from store configuration data
  const store = React.useMemo(() => {
    if (!storeData) return null;
    
    return {
      id: storeData.id || storeHandle,
      name: storeData.name || storeHandle,
      handle: storeData.handle || storeHandle,
      description: storeData.description || '',
      logo: storeData.logo,
      address: storeData.address,
      contact: storeData.contact,
      theme: storeData.theme,
      ...storeData,
    };
  }, [storeData, storeHandle]);

  // Redirect to home if not authenticated
  useEffect(() => {
    if (!isInitialLoading && !authLoading && !isAuthenticated) {
      router.push(`/${storeHandle}`);
    }
  }, [isAuthenticated, authLoading, isInitialLoading, router, storeHandle]);

  if (storeLoading || authLoading || isInitialLoading || orderLoading) {
    return (
      <Box sx={{ minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress size={60} sx={{ mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            {storeLoading ? 'Loading store...' : isInitialLoading ? 'Loading...' : authLoading ? 'Checking authentication...' : 'Loading order details...'}
          </Typography>
        </Box>
      </Box>
    );
  }

  if (storeError || !store) {
    return (
      <Box sx={{ minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center', p: 2 }}>
        <Card elevation={2} sx={{ maxWidth: 400, width: '100%' }}>
          <CardContent sx={{ textAlign: 'center', p: 4 }}>
            <ErrorOutline sx={{ fontSize: 60, color: 'error.main', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              Unable to load store information
            </Typography>
            <Button 
              variant="contained" 
              onClick={() => window.location.reload()}
              sx={{ mt: 2 }}
            >
              Retry
            </Button>
          </CardContent>
        </Card>
      </Box>
    );
  }

  if (!isAuthenticated || !user) {
    return (
      <Box sx={{ minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress size={60} sx={{ mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            Redirecting to home...
          </Typography>
        </Box>
      </Box>
    );
  }

  if (orderError || !order) {
    return (
      <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
        <ThemedStoreHeader store={store} storeHandle={storeHandle} />
        
        <Container maxWidth="md" sx={{ py: 8 }}>
          <Card elevation={2}>
            <CardContent sx={{ p: 6, textAlign: 'center' }}>
              <ErrorOutline sx={{ fontSize: 80, color: 'error.main', mb: 3 }} />

              <Typography variant="h4" fontWeight="bold" gutterBottom>
                {orderError ? 'Error Loading Order' : 'Order Not Found'}
              </Typography>
              
              <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
                {orderError ? (orderError instanceof Error ? orderError.message : 'Failed to load order') : `We couldn't find order with ID: ${orderNumber}`}
              </Typography>

              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="center">
                {orderError && (
                  <Button
                    variant="contained"
                    startIcon={<Refresh />}
                    onClick={() => refetchOrder()}
                    size="large"
                  >
                    Try Again
                  </Button>
                )}
                
                <Button
                  variant={orderError ? "outlined" : "contained"}
                  startIcon={<Home />}
                  onClick={() => router.push(`/${storeHandle}`)}
                  size="large"
                >
                  Continue Shopping
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<ShoppingBag />}
                  onClick={() => router.push(`/${storeHandle}/orders`)}
                  size="large"
                >
                  View All Orders
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </Container>
      </Box>
    );
  }

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      <ThemedStoreHeader store={store} storeHandle={storeHandle} />
      
      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Breadcrumb */}
        <Breadcrumbs 
          separator={<NavigateNext fontSize="small" />} 
          sx={{ mb: 4 }}
          aria-label="breadcrumb"
        >
          <MuiLink 
            component={Link}
            href={`/${storeHandle}`}
            color="inherit"
            sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}
          >
            <Home sx={{ mr: 0.5 }} fontSize="inherit" />
            Home
          </MuiLink>
          <MuiLink 
            component={Link}
            href={`/${storeHandle}/orders`}
            color="inherit"
            sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}
          >
            <ShoppingBag sx={{ mr: 0.5 }} fontSize="inherit" />
            Orders
          </MuiLink>
          <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
            Order #{order.display_id}
          </Typography>
        </Breadcrumbs>

        {/* Order Details */}
        <OrderDetails order={order} storeHandle={storeHandle} />
      </Container>
    </Box>
  );
};

export default function OrderPage() {
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  const orderNumber = params.orderNumber as string;

  return (
    <GlobalProviders storeHandle={storeHandle}>
      <OrderPageContent storeHandle={storeHandle} orderNumber={orderNumber} />
    </GlobalProviders>
  );
}