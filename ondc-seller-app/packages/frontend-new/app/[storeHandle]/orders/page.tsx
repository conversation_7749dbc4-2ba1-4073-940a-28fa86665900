'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useStoreConfig } from '@/hooks/useStoreConfig';
import { GlobalProviders } from '@/components/layout/GlobalProviders';
import { useStoreAuthContext } from '@/components/auth/StoreAuthProvider';
import { ThemedStoreHeader } from '@/components/store/ThemedStoreHeader';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ErrorMessage } from '@/components/ui/ErrorMessage';
import { useOrders } from '@/hooks/medusa/useOrders';
import { OrderCard } from '@/components/order/OrderCard';
import { 
  Box, 
  Container, 
  Typography, 
  Card, 
  CardContent, 
  Button, 
  Stack, 
  Grid,
  Breadcrumbs,
  Link as MuiLink,
  CircularProgress,
  Alert
} from '@mui/material';
import { 
  Home, 
  ShoppingBag, 
  Refresh, 
  NavigateNext 
} from '@mui/icons-material';
import Link from 'next/link';

const OrdersPageContent: React.FC<{ storeHandle: string }> = ({ storeHandle }) => {
  const router = useRouter();
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const { 
    storeData, 
    isLoading: storeLoading, 
    error: storeError
  } = useStoreConfig();
  const { isAuthenticated, isLoading: authLoading, user, token } = useStoreAuthContext();
  
  // Handle initial loading state
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitialLoading(false);
    }, 500); // Small delay to show loading state
    
    return () => clearTimeout(timer);
  }, []);

  // Fetch orders using the API
  const { 
    data: ordersData, 
    isLoading: ordersLoading, 
    error: ordersError,
    refetch: refetchOrders
  } = useOrders(
    storeHandle,
    token || undefined,
    { limit: 20 },
    isAuthenticated && !!token
  );

  // Create store object from store configuration data
  const store = React.useMemo(() => {
    if (!storeData) return null;
    
    return {
      id: storeData.id || storeHandle,
      name: storeData.name || storeHandle,
      handle: storeData.handle || storeHandle,
      description: storeData.description || '',
      logo: storeData.logo,
      address: storeData.address,
      contact: storeData.contact,
      theme: storeData.theme,
      ...storeData,
    };
  }, [storeData, storeHandle]);

  // Redirect to home if not authenticated
  useEffect(() => {
    if (!isInitialLoading && !authLoading && !isAuthenticated) {
      router.push(`/${storeHandle}`);
    }
  }, [isAuthenticated, authLoading, isInitialLoading, router, storeHandle]);

  if (storeLoading || authLoading || isInitialLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p 
            className="mt-4 text-lg"
            style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
          >
            {storeLoading ? 'Loading store...' : isInitialLoading ? 'Loading orders...' : 'Checking authentication...'}
          </p>
        </div>
      </div>
    );
  }

  if (storeError || !store) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <ErrorMessage 
          message="Unable to load store information"
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p 
            className="mt-4 text-lg"
            style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
          >
            Redirecting to home...
          </p>
        </div>
      </div>
    );
  }

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      <ThemedStoreHeader store={store} storeHandle={storeHandle} />
      
      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Breadcrumb */}
        <Breadcrumbs 
          separator={<NavigateNext fontSize="small" />} 
          sx={{ mb: 4 }}
          aria-label="breadcrumb"
        >
          <MuiLink 
            component={Link}
            href={`/${storeHandle}`}
            color="inherit"
            sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}
          >
            <Home sx={{ mr: 0.5 }} fontSize="inherit" />
            Home
          </MuiLink>
          <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
            <ShoppingBag sx={{ mr: 0.5 }} fontSize="inherit" />
            My Orders
          </Typography>
        </Breadcrumbs>

        {/* Page Title */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h3" component="h1" fontWeight="bold" gutterBottom>
            My Orders
          </Typography>
          <Typography variant="h6" color="text.secondary">
            View and track your order history
          </Typography>
        </Box>

        {/* Orders Content */}
        {ordersLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
            <CircularProgress size={60} />
          </Box>
        ) : ordersError ? (
          <Card elevation={2}>
            <CardContent sx={{ p: 4 }}>
              <Alert 
                severity="error" 
                action={
                  <Button 
                    color="inherit" 
                    size="small" 
                    onClick={() => refetchOrders()}
                    startIcon={<Refresh />}
                  >
                    Retry
                  </Button>
                }
              >
                {ordersError.message || 'Failed to load orders'}
              </Alert>
            </CardContent>
          </Card>
        ) : ordersData && ordersData.orders && ordersData.orders.length > 0 ? (
          <Stack spacing={3}>
            {/* Orders Summary */}
            <Card elevation={2}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Box>
                    <Typography variant="h5" fontWeight="semibold" gutterBottom>
                      Order History
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {ordersData.count} {ordersData.count === 1 ? 'order' : 'orders'} found
                    </Typography>
                  </Box>
                  <Button
                    variant="outlined"
                    startIcon={<Refresh />}
                    onClick={() => refetchOrders()}
                  >
                    Refresh
                  </Button>
                </Box>
              </CardContent>
            </Card>

            {/* Orders List */}
            <Grid container spacing={3}>
              {ordersData.orders.map((order) => (
                <Grid item xs={12} md={6} lg={4} key={order.id}>
                  <OrderCard 
                    order={order} 
                    storeHandle={storeHandle} 
                  />
                </Grid>
              ))}
            </Grid>

            {/* Load More */}
            {ordersData.orders.length < ordersData.count && (
              <Box sx={{ textAlign: 'center', pt: 3 }}>
                <Button
                  variant="contained"
                  size="large"
                  sx={{ px: 4 }}
                >
                  Load More Orders
                </Button>
              </Box>
            )}
          </Stack>
        ) : (
          <Card elevation={2}>
            <CardContent sx={{ p: 6 }}>
              <Box sx={{ textAlign: 'center', py: 6 }}>
                <ShoppingBag 
                  sx={{ 
                    fontSize: 80, 
                    color: 'text.secondary', 
                    opacity: 0.5, 
                    mb: 2 
                  }} 
                />
                <Typography variant="h5" fontWeight="medium" gutterBottom>
                  No Orders Yet
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
                  You haven't placed any orders yet. Start shopping to see your orders here.
                </Typography>
                <Button
                  component={Link}
                  href={`/${storeHandle}`}
                  variant="contained"
                  size="large"
                  startIcon={<Home />}
                >
                  Start Shopping
                </Button>
              </Box>
            </CardContent>
          </Card>
        )}
      </Container>
    </Box>
  );
};

export default function OrdersPage() {
  const params = useParams();
  const storeHandle = params.storeHandle as string;

  return (
    <GlobalProviders storeHandle={storeHandle}>
      <OrdersPageContent storeHandle={storeHandle} />
    </GlobalProviders>
  );
}