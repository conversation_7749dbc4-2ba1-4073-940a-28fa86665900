import React from 'react';
import { DynamicStorePageLayout } from '@/components/store/static-pages/DynamicStorePageLayout';
import { ErrorBoundary } from '@/components/ui/ErrorBoundary';
import { GlobalProviders } from '@/components/layout/GlobalProviders';

interface ContactUsPageProps {
  params: {
    storeHandle: string;
  };
}

export default function ContactUsPage({ params }: ContactUsPageProps) {
  const { storeHandle } = params;
  
  // Mock store name from handle for fallback content
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  // Fallback content if CMS content is not available
  const fallbackContent = (
    <>
      <h2 style={{ 
        fontSize: '2rem',
        fontWeight: '600',
        marginBottom: '16px',
        color: '#3b82f6'
      }}>
        Get in Touch with {storeName}
      </h2>
      
      <p style={{ marginBottom: '24px', lineHeight: '1.7' }}>
        We're here to help! Whether you have questions about our products, need assistance with an order, 
        or want to provide feedback, our customer service team is ready to assist you. Choose the method 
        that works best for you.
      </p>

      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '32px',
        marginTop: '32px'
      }}>
        {/* Contact Information */}
        <div>
          <h3 style={{ 
            fontSize: '1.5rem',
            fontWeight: '600',
            marginBottom: '24px'
          }}>
            Contact Information
          </h3>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div style={{ 
              backgroundColor: '#f9fafb',
              padding: '20px',
              borderRadius: '12px',
              border: '1px solid #e5e7eb'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                <div style={{ 
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  width: '32px',
                  height: '32px',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '12px',
                  fontSize: '16px'
                }}>
                  📧
                </div>
                <div>
                  <h4 style={{ 
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    margin: '0 0 4px 0'
                  }}>
                    Email Support
                  </h4>
                  <p style={{ 
                    fontSize: '14px',
                    color: '#6b7280',
                    margin: '0'
                  }}>
                    support@{storeHandle}.com
                  </p>
                </div>
              </div>
              <p style={{ 
                fontSize: '14px',
                margin: '0',
                lineHeight: '1.5'
              }}>
                For general inquiries, order support, and product questions.
              </p>
            </div>
            
            <div style={{ 
              backgroundColor: '#f9fafb',
              padding: '20px',
              borderRadius: '12px',
              border: '1px solid #e5e7eb'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                <div style={{ 
                  backgroundColor: '#dc2626',
                  color: 'white',
                  width: '32px',
                  height: '32px',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '12px',
                  fontSize: '16px'
                }}>
                  📞
                </div>
                <div>
                  <h4 style={{ 
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    margin: '0 0 4px 0'
                  }}>
                    Phone Support
                  </h4>
                  <p style={{ 
                    fontSize: '14px',
                    color: '#6b7280',
                    margin: '0'
                  }}>
                    +****************
                  </p>
                </div>
              </div>
              <p style={{ 
                fontSize: '14px',
                margin: '0',
                lineHeight: '1.5'
              }}>
                Speak directly with our customer service representatives.
              </p>
            </div>
            
            <div style={{ 
              backgroundColor: '#f9fafb',
              padding: '20px',
              borderRadius: '12px',
              border: '1px solid #e5e7eb'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                <div style={{ 
                  backgroundColor: '#059669',
                  color: 'white',
                  width: '32px',
                  height: '32px',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '12px',
                  fontSize: '16px'
                }}>
                  📍
                </div>
                <div>
                  <h4 style={{ 
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    margin: '0 0 4px 0'
                  }}>
                    Business Address
                  </h4>
                  <p style={{ 
                    fontSize: '14px',
                    color: '#6b7280',
                    margin: '0'
                  }}>
                    123 Commerce Street<br />
                    Business District<br />
                    City, State 12345
                  </p>
                </div>
              </div>
              <p style={{ 
                fontSize: '14px',
                margin: '0',
                lineHeight: '1.5'
              }}>
                Our main office and customer service center.
              </p>
            </div>
            
            <div style={{ 
              backgroundColor: '#f9fafb',
              padding: '20px',
              borderRadius: '12px',
              border: '1px solid #e5e7eb'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                <div style={{ 
                  backgroundColor: '#d97706',
                  color: 'white',
                  width: '32px',
                  height: '32px',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '12px',
                  fontSize: '16px'
                }}>
                  🕒
                </div>
                <div>
                  <h4 style={{ 
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    margin: '0 0 4px 0'
                  }}>
                    Business Hours
                  </h4>
                  <p style={{ 
                    fontSize: '14px',
                    color: '#6b7280',
                    margin: '0'
                  }}>
                    Monday - Friday: 9:00 AM - 6:00 PM (EST)<br />
                    Saturday: 10:00 AM - 4:00 PM (EST)<br />
                    Sunday: Closed
                  </p>
                </div>
              </div>
              <p style={{ 
                fontSize: '14px',
                margin: '0',
                lineHeight: '1.5'
              }}>
                Our customer service team is available during these hours.
              </p>
            </div>
          </div>
        </div>

        {/* Contact Form */}
        <div>
          <h3 style={{ 
            fontSize: '1.5rem',
            fontWeight: '600',
            marginBottom: '24px'
          }}>
            Send Us a Message
          </h3>
          
          <div style={{ 
            backgroundColor: '#f9fafb',
            padding: '24px',
            borderRadius: '12px',
            border: '1px solid #e5e7eb'
          }}>
            <form style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              <input
                type="text"
                placeholder="Your Name"
                required
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontFamily: 'inherit'
                }}
              />
              
              <input
                type="email"
                placeholder="Email Address"
                required
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontFamily: 'inherit'
                }}
              />
              
              <input
                type="tel"
                placeholder="Phone Number (Optional)"
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontFamily: 'inherit'
                }}
              />
              
              <input
                type="text"
                placeholder="Order Number (If applicable)"
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontFamily: 'inherit'
                }}
              />
              
              <input
                type="text"
                placeholder="Subject"
                required
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontFamily: 'inherit'
                }}
              />
              
              <textarea
                placeholder="Please describe your inquiry or concern in detail..."
                rows={4}
                required
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontFamily: 'inherit',
                  resize: 'vertical'
                }}
              />
              
              <button
                type="submit"
                style={{
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  padding: '12px 24px',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '16px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  marginTop: '16px'
                }}
              >
                📤 Send Message
              </button>
            </form>
          </div>
        </div>
      </div>

      <div style={{ 
        borderTop: '1px solid #e5e7eb',
        marginTop: '48px',
        paddingTop: '32px'
      }}>
        <h3 style={{ 
          fontSize: '1.5rem',
          fontWeight: '600',
          marginBottom: '24px'
        }}>
          Frequently Asked Questions
        </h3>
        
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '16px'
        }}>
          <div style={{ 
            backgroundColor: '#f9fafb',
            padding: '16px',
            borderRadius: '8px',
            border: '1px solid #e5e7eb'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
              <div style={{ 
                backgroundColor: '#0ea5e9',
                color: 'white',
                width: '24px',
                height: '24px',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '8px',
                fontSize: '12px'
              }}>
                🛒
              </div>
              <h4 style={{ 
                fontSize: '1.125rem',
                fontWeight: '600',
                margin: '0'
              }}>
                Order Issues
              </h4>
            </div>
            <p style={{ 
              fontSize: '14px',
              color: '#6b7280',
              margin: '0',
              lineHeight: '1.5'
            }}>
              Questions about order status, shipping, returns, or exchanges.
            </p>
          </div>
          
          <div style={{ 
            backgroundColor: '#f9fafb',
            padding: '16px',
            borderRadius: '8px',
            border: '1px solid #e5e7eb'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
              <div style={{ 
                backgroundColor: '#059669',
                color: 'white',
                width: '24px',
                height: '24px',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '8px',
                fontSize: '12px'
              }}>
                🛠️
              </div>
              <h4 style={{ 
                fontSize: '1.125rem',
                fontWeight: '600',
                margin: '0'
              }}>
                Product Support
              </h4>
            </div>
            <p style={{ 
              fontSize: '14px',
              color: '#6b7280',
              margin: '0',
              lineHeight: '1.5'
            }}>
              Information about product specifications, compatibility, or usage.
            </p>
          </div>
          
          <div style={{ 
            backgroundColor: '#f9fafb',
            padding: '16px',
            borderRadius: '8px',
            border: '1px solid #e5e7eb'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
              <div style={{ 
                backgroundColor: '#d97706',
                color: 'white',
                width: '24px',
                height: '24px',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '8px',
                fontSize: '12px'
              }}>
                ❓
              </div>
              <h4 style={{ 
                fontSize: '1.125rem',
                fontWeight: '600',
                margin: '0'
              }}>
                General Inquiries
              </h4>
            </div>
            <p style={{ 
              fontSize: '14px',
              color: '#6b7280',
              margin: '0',
              lineHeight: '1.5'
            }}>
              Account questions, payment issues, or general store information.
            </p>
          </div>
        </div>
      </div>

      <div style={{
        backgroundColor: '#eff6ff',
        border: '1px solid #bfdbfe',
        borderRadius: '8px',
        padding: '16px',
        marginTop: '32px'
      }}>
        <h4 style={{ 
          fontSize: '1.125rem',
          fontWeight: '600',
          marginBottom: '8px',
          margin: '0 0 8px 0'
        }}>
          Response Time
        </h4>
        <p style={{ margin: '0', lineHeight: '1.7' }}>
          We strive to respond to all inquiries within 24 hours during business days. For urgent 
          matters, please call our phone support line during business hours for immediate assistance.
        </p>
      </div>

      <div style={{
        backgroundColor: '#f0f9ff',
        border: '1px solid #bae6fd',
        borderRadius: '8px',
        padding: '16px',
        marginTop: '16px'
      }}>
        <p style={{ margin: '0', lineHeight: '1.7' }}>
          <strong>Note:</strong> For order-related inquiries, please have your order number ready 
          to help us assist you more efficiently. You can find your order number in your confirmation 
          email or account dashboard.
        </p>
      </div>
    </>
  );

  return (
    <ErrorBoundary>
      <GlobalProviders storeHandle={storeHandle}>
        <DynamicStorePageLayout 
          pageName="Contact us"
          fallbackTitle="Contact Us"
          fallbackContent={fallbackContent}
        />
      </GlobalProviders>
    </ErrorBoundary>
  );
}

// Generate metadata for SEO
export async function generateMetadata({ params }: ContactUsPageProps) {
  const { storeHandle } = params;
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  
  return {
    title: `Contact Us | ${storeName}`,
    description: `Get in touch with ${storeName}. Find our contact information, business hours, and send us a message for customer support and inquiries.`,
    keywords: 'contact us, customer support, phone number, email, business hours, help',
  };
}