import React from 'react';
import { DynamicStorePageLayout } from '@/components/store/static-pages/DynamicStorePageLayout';
import { ErrorBoundary } from '@/components/ui/ErrorBoundary';
import { GlobalProviders } from '@/components/layout/GlobalProviders';

interface PrivacyPolicyPageProps {
  params: {
    storeHandle: string;
  };
}

export default function PrivacyPolicyPage({ params }: PrivacyPolicyPageProps) {
  const { storeHandle } = params;
  
  // Mock store name from handle for fallback content
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  // Fallback content if CMS content is not available
  const fallbackContent = (
    <>
      <h2 style={{ 
        fontSize: '2rem',
        fontWeight: '600',
        marginBottom: '16px',
        color: '#3b82f6'
      }}>
        Privacy Policy
      </h2>
      
      <div style={{
        backgroundColor: '#f0f9ff',
        border: '1px solid #bae6fd',
        borderRadius: '8px',
        padding: '16px',
        marginBottom: '24px'
      }}>
        <p style={{ margin: '0', lineHeight: '1.7' }}>
          <strong>Effective Date:</strong> January 15, 2024<br />
          <strong>Last Updated:</strong> January 15, 2024
        </p>
      </div>

      <p style={{ marginBottom: '24px', lineHeight: '1.7' }}>
        At {storeName}, we are committed to protecting your privacy and ensuring the security of your 
        personal information. This Privacy Policy explains how we collect, use, disclose, and safeguard 
        your information when you visit our website and use our services.
      </p>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '24px'
      }}>
        Privacy Principles
      </h3>

      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '16px',
        marginBottom: '32px'
      }}>
        <div style={{ 
          backgroundColor: '#f9fafb',
          padding: '20px',
          borderRadius: '12px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
            <div style={{ 
              backgroundColor: '#3b82f6',
              color: 'white',
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '12px',
              fontSize: '16px'
            }}>
              🔒
            </div>
            <h4 style={{ 
              fontSize: '1.125rem',
              fontWeight: '600',
              margin: '0'
            }}>
              Data Security
            </h4>
          </div>
          <p style={{ 
            fontSize: '14px',
            color: '#6b7280',
            margin: '0',
            lineHeight: '1.6'
          }}>
            We use industry-standard security measures to protect your personal information.
          </p>
        </div>
        
        <div style={{ 
          backgroundColor: '#f9fafb',
          padding: '20px',
          borderRadius: '12px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
            <div style={{ 
              backgroundColor: '#059669',
              color: 'white',
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '12px',
              fontSize: '16px'
            }}>
              👁️
            </div>
            <h4 style={{ 
              fontSize: '1.125rem',
              fontWeight: '600',
              margin: '0'
            }}>
              Transparency
            </h4>
          </div>
          <p style={{ 
            fontSize: '14px',
            color: '#6b7280',
            margin: '0',
            lineHeight: '1.6'
          }}>
            We are transparent about what data we collect and how we use it.
          </p>
        </div>
        
        <div style={{ 
          backgroundColor: '#f9fafb',
          padding: '20px',
          borderRadius: '12px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
            <div style={{ 
              backgroundColor: '#0ea5e9',
              color: 'white',
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '12px',
              fontSize: '16px'
            }}>
              ⚙️
            </div>
            <h4 style={{ 
              fontSize: '1.125rem',
              fontWeight: '600',
              margin: '0'
            }}>
              User Control
            </h4>
          </div>
          <p style={{ 
            fontSize: '14px',
            color: '#6b7280',
            margin: '0',
            lineHeight: '1.6'
          }}>
            You have control over your personal information and privacy settings.
          </p>
        </div>
        
        <div style={{ 
          backgroundColor: '#f9fafb',
          padding: '20px',
          borderRadius: '12px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
            <div style={{ 
              backgroundColor: '#dc2626',
              color: 'white',
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '12px',
              fontSize: '16px'
            }}>
              🛡️
            </div>
            <h4 style={{ 
              fontSize: '1.125rem',
              fontWeight: '600',
              margin: '0'
            }}>
              Minimal Collection
            </h4>
          </div>
          <p style={{ 
            fontSize: '14px',
            color: '#6b7280',
            margin: '0',
            lineHeight: '1.6'
          }}>
            We only collect information necessary to provide our services.
          </p>
        </div>
      </div>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        Information We Collect
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        We collect information you provide directly to us, information we obtain automatically when 
        you use our services, and information from third-party sources.
      </p>

      <h4 style={{ 
        fontSize: '1.25rem',
        fontWeight: '600',
        marginTop: '24px',
        marginBottom: '12px'
      }}>
        Personal Information You Provide
      </h4>
      
      <ul style={{ paddingLeft: '24px', lineHeight: '1.7', marginBottom: '24px' }}>
        <li style={{ marginBottom: '8px' }}>
          <strong>Account Information:</strong> Name, email address, phone number, and password when you create an account
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Order Information:</strong> Billing and shipping addresses, payment information, and order history
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Communication:</strong> Information you provide when contacting customer service or participating in surveys
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Reviews and Feedback:</strong> Product reviews, ratings, and other user-generated content
        </li>
      </ul>

      <h4 style={{ 
        fontSize: '1.25rem',
        fontWeight: '600',
        marginTop: '24px',
        marginBottom: '12px'
      }}>
        Information Collected Automatically
      </h4>
      
      <ul style={{ paddingLeft: '24px', lineHeight: '1.7', marginBottom: '24px' }}>
        <li style={{ marginBottom: '8px' }}>
          <strong>Device Information:</strong> IP address, browser type, operating system, and device identifiers
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Usage Information:</strong> Pages visited, time spent on site, click patterns, and referral sources
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Location Information:</strong> General geographic location based on IP address
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Cookies and Tracking:</strong> Information collected through cookies, web beacons, and similar technologies
        </li>
      </ul>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        How We Use Your Information
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        We use the information we collect for various purposes, including:
      </p>
      
      <ul style={{ paddingLeft: '24px', lineHeight: '1.7', marginBottom: '24px' }}>
        <li style={{ marginBottom: '8px' }}>
          <strong>Service Provision:</strong> Processing orders, managing accounts, and providing customer support
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Communication:</strong> Sending order confirmations, shipping updates, and promotional emails
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Personalization:</strong> Customizing your shopping experience and recommending products
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Analytics:</strong> Understanding how our website is used to improve our services
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Security:</strong> Detecting and preventing fraud, abuse, and security incidents
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Legal Compliance:</strong> Complying with applicable laws and regulations
        </li>
      </ul>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        Your Privacy Rights
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        Depending on your location, you may have certain rights regarding your personal information:
      </p>
      
      <ul style={{ paddingLeft: '24px', lineHeight: '1.7', marginBottom: '24px' }}>
        <li style={{ marginBottom: '8px' }}>
          <strong>Access:</strong> Request access to your personal information we hold
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Correction:</strong> Request correction of inaccurate or incomplete information
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Deletion:</strong> Request deletion of your personal information
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Portability:</strong> Request a copy of your data in a portable format
        </li>
        <li style={{ marginBottom: '8px' }}>
          <strong>Opt-out:</strong> Unsubscribe from marketing communications
        </li>
      </ul>

      <p style={{ marginBottom: '24px', lineHeight: '1.7' }}>
        To exercise these rights, please contact us at privacy@{storeHandle}.com.
      </p>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        Contact Information
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        If you have any questions, concerns, or requests regarding this Privacy Policy or our data 
        practices, please contact us:
      </p>
      
      <div style={{ marginLeft: '16px', marginBottom: '24px' }}>
        <p style={{ marginBottom: '8px' }}>
          <strong>Privacy Officer:</strong> privacy@{storeHandle}.com<br />
          <strong>Customer Service:</strong> support@{storeHandle}.com<br />
          <strong>Phone:</strong> +****************<br />
          <strong>Mailing Address:</strong><br />
          {storeName}<br />
          Attn: Privacy Officer<br />
          123 Commerce Street<br />
          Business District, City, State 12345
        </p>
      </div>

      <div style={{
        backgroundColor: '#fef3c7',
        border: '1px solid #fbbf24',
        borderRadius: '8px',
        padding: '16px',
        marginTop: '32px'
      }}>
        <p style={{ margin: '0', lineHeight: '1.7' }}>
          <strong>California Residents:</strong> If you are a California resident, you may have additional 
          rights under the California Consumer Privacy Act (CCPA). Please contact us for more information 
          about your rights and how to exercise them.
        </p>
      </div>

      <div style={{
        backgroundColor: '#f0f9ff',
        border: '1px solid #bae6fd',
        borderRadius: '8px',
        padding: '16px',
        marginTop: '16px'
      }}>
        <p style={{ margin: '0', lineHeight: '1.7' }}>
          <strong>Note:</strong> This Privacy Policy is effective as of the date listed above and applies 
          to all information collected by {storeName}. Your continued use of our services after any 
          changes to this Privacy Policy constitutes acceptance of those changes.
        </p>
      </div>
    </>
  );

  return (
    <ErrorBoundary>
      <GlobalProviders storeHandle={storeHandle}>
        <DynamicStorePageLayout 
          pageName="Privacy policy"
          fallbackTitle="Privacy Policy"
          fallbackContent={fallbackContent}
        />
      </GlobalProviders>
    </ErrorBoundary>
  );
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PrivacyPolicyPageProps) {
  const { storeHandle } = params;
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  
  return {
    title: `Privacy Policy | ${storeName}`,
    description: `Read ${storeName}'s privacy policy to understand how we collect, use, and protect your personal information when you use our services.`,
    keywords: 'privacy policy, data protection, personal information, cookies, data security, privacy rights',
  };
}