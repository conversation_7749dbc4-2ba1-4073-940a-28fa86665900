'use client';

import React from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useStoreConfig } from '@/hooks/useStoreConfig';
import { GlobalProviders } from '@/components/layout/GlobalProviders';
import { ThemedStoreHeader } from '@/components/store/ThemedStoreHeader';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ErrorMessage } from '@/components/ui/ErrorMessage';

const CheckoutSuccessContent: React.FC<{ storeHandle: string }> = ({ storeHandle }) => {
  const router = useRouter();
  const { 
    storeData, 
    isLoading: storeLoading, 
    error: storeError
  } = useStoreConfig();

  // Create store object from store configuration data
  const store = React.useMemo(() => {
    if (!storeData) return null;
    
    return {
      id: storeData.id || storeHandle,
      name: storeData.name || storeHandle,
      handle: storeData.handle || storeHandle,
      description: storeData.description || '',
      logo: storeData.logo,
      address: storeData.address,
      contact: storeData.contact,
      theme: storeData.theme,
      ...storeData,
    };
  }, [storeData, storeHandle]);

  // Generate mock order number
  const orderNumber = `ORD-${Date.now().toString().slice(-6)}`;

  if (storeLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (storeError || !store) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <ErrorMessage 
          message="Unable to load store information"
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}>
      {/* Header */}
      <ThemedStoreHeader store={store} storeHandle={storeHandle} />
      
      {/* Success Content */}
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div 
          className="bg-white rounded-lg shadow-sm border p-8 text-center"
          style={{ borderColor: 'var(--theme-border, #e5e7eb)' }}
        >
          {/* Success Icon */}
          <div 
            className="w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-6"
            style={{ backgroundColor: 'var(--theme-primary, #3b82f6)' }}
          >
            <svg 
              className="w-8 h-8 text-white" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>

          {/* Success Message */}
          <h1 
            className="text-3xl font-bold mb-4"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            Order Confirmed!
          </h1>
          
          <p 
            className="text-lg mb-6"
            style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
          >
            Thank you for your purchase. Your order has been successfully placed.
          </p>

          {/* Order Details */}
          <div 
            className="bg-gray-50 rounded-lg p-6 mb-8"
            style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
              <div>
                <h3 
                  className="font-semibold mb-2"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  Order Number
                </h3>
                <p 
                  className="font-mono text-lg"
                  style={{ color: 'var(--theme-primary, #3b82f6)' }}
                >
                  {orderNumber}
                </p>
              </div>
              
              <div>
                <h3 
                  className="font-semibold mb-2"
                  style={{ color: 'var(--theme-text, #111827)' }}
                >
                  Estimated Delivery
                </h3>
                <p style={{ color: 'var(--theme-text-secondary, #6b7280)' }}>
                  {new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
              </div>
            </div>
          </div>

          {/* What's Next */}
          <div className="text-left mb-8">
            <h3 
              className="font-semibold mb-4"
              style={{ color: 'var(--theme-text, #111827)' }}
            >
              What happens next?
            </h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div 
                  className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5"
                  style={{ backgroundColor: 'var(--theme-primary, #3b82f6)' }}
                >
                  <span className="text-white text-xs font-bold">1</span>
                </div>
                <div>
                  <p 
                    className="font-medium"
                    style={{ color: 'var(--theme-text, #111827)' }}
                  >
                    Order Confirmation
                  </p>
                  <p 
                    className="text-sm"
                    style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                  >
                    You'll receive an email confirmation with your order details shortly.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div 
                  className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5"
                  style={{ backgroundColor: 'var(--theme-primary, #3b82f6)' }}
                >
                  <span className="text-white text-xs font-bold">2</span>
                </div>
                <div>
                  <p 
                    className="font-medium"
                    style={{ color: 'var(--theme-text, #111827)' }}
                  >
                    Processing
                  </p>
                  <p 
                    className="text-sm"
                    style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                  >
                    We'll prepare your order for shipment within 1-2 business days.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div 
                  className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5"
                  style={{ backgroundColor: 'var(--theme-primary, #3b82f6)' }}
                >
                  <span className="text-white text-xs font-bold">3</span>
                </div>
                <div>
                  <p 
                    className="font-medium"
                    style={{ color: 'var(--theme-text, #111827)' }}
                  >
                    Shipping
                  </p>
                  <p 
                    className="text-sm"
                    style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
                  >
                    You'll receive tracking information once your order ships.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => router.push(`/${storeHandle}`)}
              className="px-6 py-3 rounded-lg font-semibold transition-all duration-200 hover:shadow-lg"
              style={{ 
                backgroundColor: 'var(--btn-primary, #3b82f6)',
                color: 'var(--btn-text, #ffffff)',
              }}
            >
              Continue Shopping
            </button>
            
            <button
              onClick={() => router.push(`/${storeHandle}/orders/${orderNumber}`)}
              className="px-6 py-3 rounded-lg font-semibold transition-colors border"
              style={{ 
                borderColor: 'var(--theme-border, #d1d5db)',
                color: 'var(--theme-text, #111827)',
              }}
            >
              Track Order
            </button>
          </div>

          {/* Support */}
          <div className="mt-8 pt-6 border-t" style={{ borderTopColor: 'var(--theme-border, #e5e7eb)' }}>
            <p 
              className="text-sm"
              style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
            >
              Need help? Contact our{' '}
              <a 
                href={`/${storeHandle}/contact-us`}
                className="underline transition-colors hover:opacity-80"
                style={{ color: 'var(--theme-primary, #3b82f6)' }}
              >
                customer support team
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function CheckoutSuccessPage() {
  const params = useParams();
  const storeHandle = params.storeHandle as string;

  return (
    <GlobalProviders storeHandle={storeHandle}>
      <CheckoutSuccessContent storeHandle={storeHandle} />
    </GlobalProviders>
  );
}