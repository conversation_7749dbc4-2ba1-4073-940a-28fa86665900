'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { GlobalProviders } from '@/components/layout/GlobalProviders';
import { getStoreCartAPI, Cart } from '@/lib/api/cart';
import { CheckoutProvider } from '@/components/checkout/CheckoutProvider';
import { ThemedStoreHeader } from '@/components/store/ThemedStoreHeader';
import { MultiStepCheckout } from '@/components/checkout/MultiStepCheckout';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ErrorMessage } from '@/components/ui/ErrorMessage';
import { CheckoutLoadingState } from '@/components/checkout/CheckoutLoadingState';
import { CheckoutPageWrapper } from '@/components/checkout/CheckoutPageWrapper';
import { useStoreConfig } from '@/hooks/useStoreConfig';
// Theme providers are handled by GlobalProviders

const CheckoutPageContent: React.FC<{ storeHandle: string }> = ({ storeHandle }) => {
  const router = useRouter();
  const [cart, setCart] = useState<Cart | null>(null);
  const [cartLoading, setCartLoading] = useState(true);
  const [cartError, setCartError] = useState<string | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);
  const { 
    storeData, 
    isLoading: storeLoading, 
    error: storeError
  } = useStoreConfig(storeHandle);
  
  // Get store-specific cart API
  const cartAPI = getStoreCartAPI(storeHandle);

  // Create store object from store configuration data
  const store = React.useMemo(() => {
    if (!storeData) return null;
    
    return {
      id: storeData.id || storeHandle,
      name: storeData.name || storeHandle,
      handle: storeData.handle || storeHandle,
      description: storeData.description || '',
      logo: storeData.logo,
      address: storeData.address,
      contact: storeData.contact,
      theme: storeData.theme,
      ...storeData,
    };
  }, [storeData, storeHandle]);

  // Load cart data
  useEffect(() => {
    const loadCart = async () => {
      console.log(`🛒 Loading checkout page for store: ${storeHandle}`);
      
      // Add a small delay to show initial loading state
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const cartId = cartAPI.getStoredCartId();
      if (!cartId) {
        console.log('❌ No cart found, redirecting to home');
        // No cart found, redirect to home
        router.push(`/${storeHandle}`);
        return;
      }
      
      try {
        setCartLoading(true);
        console.log(`📦 Fetching cart for checkout: ${cartId}`);
        const cartResponse = await cartAPI.getCart(cartId);
        setCart(cartResponse.cart);
        console.log(`✅ Cart loaded for checkout:`, cartResponse.cart.items.length, 'items');
        
        // Check if cart is empty
        if (!cartResponse.cart.items || cartResponse.cart.items.length === 0) {
          console.log('❌ Cart is empty, redirecting to home');
          router.push(`/${storeHandle}`);
          return;
        }
      } catch (error) {
        console.error('❌ Failed to load cart for checkout:', error);
        setCartError('Failed to load cart');
        // Redirect to home if cart can't be loaded
        setTimeout(() => {
          router.push(`/${storeHandle}`);
        }, 2000);
      } finally {
        setCartLoading(false);
        setInitialLoading(false);
      }
    };

    loadCart();
  }, [storeHandle, router, cartAPI]);

  // Show loading while store or cart is loading
  if (storeLoading || cartLoading || initialLoading) {
    const loadingMessage = storeLoading ? 'Loading store configuration...' : 
                          cartLoading ? 'Loading your cart...' : 
                          'Preparing checkout...';
    
    const loadingSubmessage = storeLoading ? 'Setting up your store theme and configuration' :
                             cartLoading ? 'Retrieving your cart items and calculating totals' :
                             'Please wait while we prepare your secure checkout experience';
    
    return (
      <CheckoutLoadingState 
        message={loadingMessage}
        submessage={loadingSubmessage}
      />
    );
  }

  // Show error if store failed to load
  if (storeError || !store) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <ErrorMessage 
          message="Unable to load store information"
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  // Show error if cart failed to load
  if (cartError) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <ErrorMessage 
          message={cartError}
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  // Show loading if cart is empty (will redirect)
  if (!cart || !cart.items || cart.items.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p 
            className="mt-4 text-lg"
            style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
          >
            Redirecting to store...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--theme-background, #f9fafb)' }}>
      {/* Header */}
      <ThemedStoreHeader store={store} storeHandle={storeHandle} />
      
      {/* Checkout Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="mb-8">
          <ol className="flex items-center space-x-2 text-sm">
            <li>
              <a 
                href={`/${storeHandle}`}
                className="transition-colors hover:opacity-80"
                style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
              >
                Home
              </a>
            </li>
            <li>
              <svg className="w-4 h-4 mx-2" fill="none" stroke="var(--theme-text-secondary, #6b7280)" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </li>
            <li>
              <span style={{ color: 'var(--theme-text, #111827)' }} className="font-medium">
                Checkout
              </span>
            </li>
          </ol>
        </nav>

        {/* Page Title */}
        <div className="mb-8">
          <h1 
            className="text-3xl font-bold"
            style={{ color: 'var(--theme-text, #111827)' }}
          >
            Checkout
          </h1>
          <p 
            className="mt-2"
            style={{ color: 'var(--theme-text-secondary, #6b7280)' }}
          >
            Complete your order below
          </p>
        </div>

        {/* Multi-Step Checkout */}
        <MultiStepCheckout storeHandle={storeHandle} cart={cart} />
      </div>
    </div>
  );
};

export default function CheckoutPage() {
  const params = useParams();
  const storeHandle = params.storeHandle as string;

  console.log(`📋 Checkout page initializing for store: ${storeHandle}`);

  return (
    <GlobalProviders 
      storeHandle={storeHandle}
      includeAuth={false}
      includeStoreAuth={true}
      includeCart={false} // Disable cart sidebar in checkout
    >
      <CheckoutPageWrapper storeHandle={storeHandle}>
        <CheckoutProvider>
          <CheckoutPageContent storeHandle={storeHandle} />
        </CheckoutProvider>
      </CheckoutPageWrapper>
    </GlobalProviders>
  );
}