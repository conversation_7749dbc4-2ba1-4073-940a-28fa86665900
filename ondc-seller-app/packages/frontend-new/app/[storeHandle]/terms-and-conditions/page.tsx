import React from 'react';
import { DynamicStorePageLayout } from '@/components/store/static-pages/DynamicStorePageLayout';
import { ErrorBoundary } from '@/components/ui/ErrorBoundary';
import { GlobalProviders } from '@/components/layout/GlobalProviders';

interface TermsAndConditionsPageProps {
  params: {
    storeHandle: string;
  };
}

export default function TermsAndConditionsPage({ params }: TermsAndConditionsPageProps) {
  const { storeHandle } = params;
  
  // Mock store name from handle for fallback content
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  // Fallback content if CMS content is not available
  const fallbackContent = (
    <>
      <h2 style={{ 
        fontSize: '2rem',
        fontWeight: '600',
        marginBottom: '16px',
        color: '#3b82f6'
      }}>
        Terms and Conditions
      </h2>
      
      <div style={{
        backgroundColor: '#f0f9ff',
        border: '1px solid #bae6fd',
        borderRadius: '8px',
        padding: '16px',
        marginBottom: '24px'
      }}>
        <p style={{ margin: '0', lineHeight: '1.7' }}>
          <strong>Effective Date:</strong> January 15, 2024<br />
          <strong>Last Updated:</strong> January 15, 2024
        </p>
      </div>

      <p style={{ marginBottom: '24px', lineHeight: '1.7' }}>
        Welcome to {storeName}. These Terms and Conditions ("Terms") govern your use of our website 
        and services. By accessing or using our website, you agree to be bound by these Terms. 
        If you do not agree with any part of these Terms, please do not use our services.
      </p>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        1. Acceptance of Terms
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        By accessing and using this website, you accept and agree to be bound by the terms and 
        provision of this agreement. Additionally, when using this website's particular services, 
        you shall be subject to any posted guidelines or rules applicable to such services.
      </p>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        2. Use License
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        Permission is granted to temporarily download one copy of the materials on {storeName}'s 
        website for personal, non-commercial transitory viewing only. This is the grant of a license, 
        not a transfer of title, and under this license you may not:
      </p>
      
      <ul style={{ paddingLeft: '24px', lineHeight: '1.7', marginBottom: '24px' }}>
        <li style={{ marginBottom: '8px' }}>
          modify or copy the materials
        </li>
        <li style={{ marginBottom: '8px' }}>
          use the materials for any commercial purpose or for any public display (commercial or non-commercial)
        </li>
        <li style={{ marginBottom: '8px' }}>
          attempt to decompile or reverse engineer any software contained on the website
        </li>
        <li style={{ marginBottom: '8px' }}>
          remove any copyright or other proprietary notations from the materials
        </li>
      </ul>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        3. User Accounts
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        When you create an account with us, you must provide information that is accurate, complete, 
        and current at all times. You are responsible for safeguarding the password and for all 
        activities that occur under your account.
      </p>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        You agree not to disclose your password to any third party and to take sole responsibility 
        for activities and actions under your password, whether or not you have authorized such activities.
      </p>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        4. Product Information and Pricing
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        We strive to provide accurate product descriptions and pricing information. However, we do not 
        warrant that product descriptions, pricing, or other content is accurate, complete, reliable, 
        current, or error-free.
      </p>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        We reserve the right to:
      </p>
      
      <ul style={{ paddingLeft: '24px', lineHeight: '1.7', marginBottom: '24px' }}>
        <li style={{ marginBottom: '8px' }}>
          Modify or discontinue products without prior notice
        </li>
        <li style={{ marginBottom: '8px' }}>
          Correct pricing errors, even after an order has been placed
        </li>
        <li style={{ marginBottom: '8px' }}>
          Limit quantities available for purchase
        </li>
      </ul>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        5. Orders and Payment
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        All orders are subject to acceptance and availability. We reserve the right to refuse or 
        cancel any order for any reason, including but not limited to product availability, errors 
        in product or pricing information, or problems identified by our fraud detection systems.
      </p>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        Payment must be received by us before we ship any products. We accept various payment methods 
        as indicated on our website. All payments are processed securely through our payment partners.
      </p>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        6. Shipping and Delivery
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        We will make every effort to ship products within the timeframes specified on our website. 
        However, delivery dates are estimates and we are not responsible for delays caused by shipping 
        carriers or circumstances beyond our control.
      </p>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        Risk of loss and title for products pass to you upon delivery to the shipping carrier.
      </p>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        7. Returns and Refunds
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        Our return and refund policy is detailed in our separate Refund Policy document. By making 
        a purchase, you agree to the terms outlined in our Refund Policy.
      </p>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        8. Prohibited Uses
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        You may not use our website for any unlawful purpose or to solicit others to perform unlawful acts. 
        You may not violate any local, state, national, or international law or regulation.
      </p>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        Prohibited activities include, but are not limited to:
      </p>
      
      <ul style={{ paddingLeft: '24px', lineHeight: '1.7', marginBottom: '24px' }}>
        <li style={{ marginBottom: '8px' }}>
          Transmitting or uploading viruses or malicious code
        </li>
        <li style={{ marginBottom: '8px' }}>
          Attempting to gain unauthorized access to our systems
        </li>
        <li style={{ marginBottom: '8px' }}>
          Using our website to spam or send unsolicited communications
        </li>
        <li style={{ marginBottom: '8px' }}>
          Impersonating another person or entity
        </li>
      </ul>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        9. Intellectual Property
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        The content, organization, graphics, design, compilation, magnetic translation, digital 
        conversion, and other matters related to the website are protected under applicable copyrights, 
        trademarks, and other proprietary rights.
      </p>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        10. Disclaimer of Warranties
      </h3>
      
      <div style={{
        backgroundColor: '#fef3c7',
        border: '1px solid #fbbf24',
        borderRadius: '8px',
        padding: '16px',
        marginBottom: '24px'
      }}>
        <p style={{ margin: '0', lineHeight: '1.7' }}>
          THE MATERIALS ON {storeName.toUpperCase()}'S WEBSITE ARE PROVIDED ON AN 'AS IS' BASIS. 
          {storeName.toUpperCase()} MAKES NO WARRANTIES, EXPRESSED OR IMPLIED, AND HEREBY DISCLAIMS 
          AND NEGATES ALL OTHER WARRANTIES INCLUDING WITHOUT LIMITATION, IMPLIED WARRANTIES OR 
          CONDITIONS OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, OR NON-INFRINGEMENT 
          OF INTELLECTUAL PROPERTY OR OTHER VIOLATION OF RIGHTS.
        </p>
      </div>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        11. Limitation of Liability
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        In no event shall {storeName} or its suppliers be liable for any damages (including, without 
        limitation, damages for loss of data or profit, or due to business interruption) arising out 
        of the use or inability to use the materials on {storeName}'s website, even if {storeName} 
        or an authorized representative has been notified orally or in writing of the possibility of such damage.
      </p>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        12. Privacy Policy
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        Your privacy is important to us. Please review our Privacy Policy, which also governs your 
        use of the website, to understand our practices.
      </p>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        13. Changes to Terms
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        We reserve the right to modify these Terms at any time. Changes will be effective immediately 
        upon posting on the website. Your continued use of the website after any changes constitutes 
        acceptance of the new Terms.
      </p>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        14. Governing Law
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        These Terms shall be governed by and construed in accordance with the laws of the jurisdiction 
        in which {storeName} operates, without regard to its conflict of law provisions.
      </p>

      <h3 style={{ 
        fontSize: '1.5rem',
        fontWeight: '600',
        marginTop: '32px',
        marginBottom: '16px'
      }}>
        15. Contact Information
      </h3>
      
      <p style={{ marginBottom: '16px', lineHeight: '1.7' }}>
        If you have any questions about these Terms and Conditions, please contact us at:
      </p>
      
      <div style={{ marginLeft: '16px', marginBottom: '24px' }}>
        <p style={{ marginBottom: '8px' }}>
          <strong>Email:</strong> legal@{storeHandle}.com<br />
          <strong>Phone:</strong> +****************<br />
          <strong>Address:</strong> 123 Commerce Street, Business District, City, State 12345
        </p>
      </div>

      <hr style={{ margin: '32px 0', border: 'none', borderTop: '1px solid #e5e7eb' }} />

      <div style={{
        backgroundColor: '#eff6ff',
        border: '1px solid #bfdbfe',
        borderRadius: '8px',
        padding: '16px'
      }}>
        <p style={{ margin: '0', lineHeight: '1.7' }}>
          <strong>Important:</strong> These terms and conditions constitute the entire agreement 
          between you and {storeName} regarding the use of our website and services. If any provision 
          of these Terms is found to be invalid or unenforceable, the remaining provisions will remain in full force and effect.
        </p>
      </div>
    </>
  );

  return (
    <ErrorBoundary>
      <GlobalProviders storeHandle={storeHandle}>
        <DynamicStorePageLayout 
          pageName="Terms and conditions"
          fallbackTitle="Terms and Conditions"
          fallbackContent={fallbackContent}
        />
      </GlobalProviders>
    </ErrorBoundary>
  );
}

// Generate metadata for SEO
export async function generateMetadata({ params }: TermsAndConditionsPageProps) {
  const { storeHandle } = params;
  const storeName = storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  
  return {
    title: `Terms and Conditions | ${storeName}`,
    description: `Read the terms and conditions for ${storeName}. Understand your rights and responsibilities when using our website and services.`,
    keywords: 'terms and conditions, terms of service, legal, user agreement, website terms',
  };
}