'use client';

import AuthScreen from '@/components/auth/AuthScreen';
import { Suspense } from 'react';
import { CircularProgress, Box } from '@mui/material';

function RegisterPageContent() {
  return <AuthScreen defaultTab={1} />;
}

export default function RegisterPage() {
  return (
    <Suspense fallback={
      <Box sx={{ 
        minHeight: '100vh', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <CircularProgress size={60} sx={{ color: 'white' }} />
      </Box>
    }>
      <RegisterPageContent />
    </Suspense>
  );
}