import React from 'react';
import { OnboardingWizard } from '@/components/onboarding/OnboardingWizard';
import { PageLoadingWrapper } from '@/components/loading/PageLoadingWrapper';

export default function OnboardingPage() {
  return (
    <PageLoadingWrapper 
      loadingMessage="Setting up onboarding..."
      loadingSubMessage="Preparing your store setup wizard"
      pageId="onboarding-page"
      minLoadingTime={500}
    >
      <OnboardingWizard />
    </PageLoadingWrapper>
  );
}

// Generate metadata for SEO
export async function generateMetadata() {
  return {
    title: 'Store Setup - Multi-Tenant Store Platform',
    description: 'Set up your online store in minutes with our guided onboarding process',
    robots: 'noindex, nofollow', // Don't index onboarding pages
  };
}