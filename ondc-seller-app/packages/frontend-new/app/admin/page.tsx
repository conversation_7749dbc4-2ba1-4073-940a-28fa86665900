'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import { PageLoadingWrapper } from '@/components/loading/PageLoadingWrapper';

import { GlobalAdminAuthGuard } from '@/components/admin/GlobalAdminAuthGuard';

export default function GenericAdminPage() {
  const router = useRouter();
  const { getStoreHandle, user } = useAuthStore();

  useEffect(() => {
    // Try to get store handle and redirect to proper admin page
    const storeHandle = getStoreHandle();
    
    if (storeHandle) {
      router.replace(`/${storeHandle}/admin`);
    }
  }, [router, getStoreHandle, user]);

  const storeHandle = getStoreHandle();

  return (
    <GlobalAdminAuthGuard>
      <PageLoadingWrapper 
        loadingMessage="Loading admin panel..."
        loadingSubMessage="Setting up your dashboard"
        pageId="admin-page"
        minLoadingTime={300}
      >
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 mb-4">
            <svg className="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            Admin Access
          </h1>
          
          {storeHandle ? (
            <div>
              <p className="text-gray-600 mb-4">
                Redirecting to your store admin panel...
              </p>
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            </div>
          ) : (
            <div>
              <p className="text-gray-600 mb-4">
                No store handle found in your profile. Please contact support or check your account setup.
              </p>
              
              <div className="space-y-3">
                <button
                  onClick={() => {
                    const handle = getStoreHandle();
                    if (handle) {
                      router.push(`/${handle}/admin`);
                    } else {
                      alert('No store handle found');
                    }
                  }}
                  className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  Test Store Handle Detection
                </button>
                
                <button
                  onClick={() => router.push('/onboarding')}
                  className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Complete Store Setup
                </button>
                
                <button
                  onClick={() => router.push('/login?tab=login')}
                  className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Back to Login
                </button>
              </div>
              

            </div>
          )}
        </div>
      </div>
    </div>
      </PageLoadingWrapper>
    </GlobalAdminAuthGuard>
  );
}