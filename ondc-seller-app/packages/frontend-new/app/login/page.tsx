'use client';

import AuthScreen from '@/components/auth/AuthScreen';
import { Suspense, useEffect } from 'react';
import { CircularProgress, Box } from '@mui/material';
import { useGlobalLoading } from '@/components/loading/GlobalLoadingProvider';

function LoginPageContent() {
  const { loadingState, stopLoading } = useGlobalLoading();
  
  useEffect(() => {
    // Stop logout loading when login page mounts
    if (loadingState.isLoading && loadingState.actionId === 'user-logout') {
      stopLoading('user-logout');
    }
  }, [loadingState.isLoading, loadingState.actionId, stopLoading]);
  
  // Also watch for loading state changes
  useEffect(() => {
    // Stop logout loading if it appears after mount
    if (loadingState.isLoading && loadingState.actionId === 'user-logout') {
      setTimeout(() => {
        stopLoading('user-logout');
      }, 100);
    }
  }, [loadingState.isLoading, loadingState.actionId, stopLoading]);
  
  return <AuthScreen defaultTab={0} />;
}

export default function LoginPage() {
  return (
    <Suspense fallback={
      <Box sx={{ 
        minHeight: '100vh', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <CircularProgress size={60} sx={{ color: 'white' }} />
      </Box>
    }>
      <LoginPageContent />
    </Suspense>
  );
}