"use client";

import { ReactNode, useEffect } from "react";
import ReactQueryProvider from "./providers/react-query-provider";
import MuiThemeProvider from "./providers/mui-theme-provider";
import ToastProvider from "./providers/toast-provider";
import { GlobalLoadingProvider } from "@/components/loading/GlobalLoadingProvider";
import { LoadingOverlay } from "@/components/loading/LoadingOverlay";
import { useAuthStore } from "@/stores/authStore";
import { setMedusaToken } from "@/lib/api/medusa";

export const Providers = ({ children }: { children: ReactNode }) => {
  const token = useAuthStore((state) => state.token);
  const hasHydrated = useAuthStore((state) => state.hasHydrated);

  useEffect(() => {
    // Log hydration status
    console.log('=== PROVIDERS INITIALIZING ===');
    console.log('Auth hydration status:', hasHydrated);
  }, [hasHydrated]);

  useEffect(() => {
    if (token) {
      setMedusaToken(token);
    }
  }, [token]);

  return (
    <GlobalLoadingProvider>
      <MuiThemeProvider>
        <ToastProvider>
          <ReactQueryProvider>
            {children}
            <LoadingOverlay />
          </ReactQueryProvider>
        </ToastProvider>
      </MuiThemeProvider>
    </GlobalLoadingProvider>
  );
};
