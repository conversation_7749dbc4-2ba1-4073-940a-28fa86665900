@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import store theme styles */
@import '../styles/store-theme.css';

/* Import rich text styles */
@import '../styles/rich-text.css';

/* Import Quill Editor Styles - Disabled until dependencies are installed */
/* @import '../styles/quill.css'; */

/* Theme CSS Variables */
:root {
  /* Default theme colors (will be overridden by theme system) */
  --theme-primary: #dc0467;
  --theme-secondary: #8f5095;
  --theme-accent: #d40464;
  --theme-background: #b7ddca;
  --theme-surface: #ffffff;
  --theme-text: #960246;
  --theme-text-secondary: #8f5095;
  --theme-border: #a4dac0;
  --theme-hover: #a4dac0;
  --theme-active: #d40464;

  /* Component-specific colors */
  --header-bg: var(--theme-surface);
  --header-text: var(--theme-text);
  --header-border: var(--theme-border);
  --header-search-bg: var(--theme-background);
  --header-search-border: var(--theme-primary);

  --nav-bg: var(--theme-surface);
  --nav-text: var(--theme-text);
  --nav-hover: var(--theme-hover);
  --nav-active: var(--theme-primary);
  --nav-border: var(--theme-border);

  --btn-primary: var(--theme-primary);
  --btn-primary-hover: var(--theme-accent);
  --btn-secondary: var(--theme-secondary);
  --btn-secondary-hover: var(--theme-text);
  --btn-text: var(--theme-surface);

  --card-bg: var(--theme-surface);
  --card-border: var(--theme-border);
  --card-shadow: 0 4px 6px -1px rgba(220, 4, 103, 0.1);
  --card-hover: var(--theme-hover);

  --dropdown-bg: var(--theme-surface);
  --dropdown-border: var(--theme-border);
  --dropdown-shadow: 0 25px 50px -12px rgba(220, 4, 103, 0.25);
  --dropdown-item-hover: var(--theme-hover);

  /* Footer specific colors */
  --footer-bg: var(--theme-text);
  --footer-text: var(--theme-surface);
  --footer-text-secondary: var(--theme-background);
  --footer-accent: var(--theme-primary);
  --footer-border: var(--theme-primary);
}

body {
  margin: 0px !important;
}

/* Custom utilities for line clamping */
@layer utilities {
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Hide scrollbar for carousel */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Smooth transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

/* Material Design 3 Dropdown Animations */
@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes dropdownSlideOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
}

.animate-in {
  animation: dropdownSlideIn 0.2s cubic-bezier(0.16, 1, 0.3, 1);
}

.animate-out {
  animation: dropdownSlideOut 0.15s cubic-bezier(0.4, 0, 1, 1);
}

.fade-in {
  animation: fadeIn 0.2s ease-out;
}

.slide-in-from-top-2 {
  animation: dropdownSlideIn 0.2s cubic-bezier(0.16, 1, 0.3, 1);
}

/* User menu specific animations */
.user-menu-enter {
  animation: dropdownSlideIn 0.2s cubic-bezier(0.16, 1, 0.3, 1);
}

.user-menu-exit {
  animation: dropdownSlideOut 0.15s cubic-bezier(0.4, 0, 1, 1);
}

/* Mega menu specific styles */
.mega-menu-dropdown {
  animation: fadeIn 0.2s ease-out;
}

/* Hover effects */
.category-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Mega menu specific fixes */
.mega-menu-container {
  position: relative;
  z-index: 1000;
}

.mega-menu-dropdown {
  position: absolute !important;
  z-index: 9999 !important;
  top: 100% !important;
  left: 0 !important;
  margin-top: 0.5rem !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

/* Ensure dropdowns appear above other content */
.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998;
  pointer-events: none;
}

/* Debug styles for development */
.debug-hover {
  background-color: rgba(255, 0, 0, 0.1) !important;
  border: 2px solid red !important;
}

/* Cart sidebar animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.cart-sidebar-enter {
  animation: slideInRight 0.3s ease-out;
}

.cart-sidebar-exit {
  animation: slideOutRight 0.3s ease-in;
}

.cart-overlay-enter {
  animation: fadeIn 0.3s ease-out;
}

.cart-overlay-exit {
  animation: fadeOut 0.3s ease-in;
}

/* Scrollbar styling for cart */
.cart-items::-webkit-scrollbar {
  width: 6px;
}

.cart-items::-webkit-scrollbar-track {
  background: var(--theme-background);
  border-radius: 3px;
}

.cart-items::-webkit-scrollbar-thumb {
  background: var(--theme-border);
  border-radius: 3px;
}

.cart-items::-webkit-scrollbar-thumb:hover {
  background: var(--theme-primary);
}

/* Global Table Gradient Styles */
/* Base table styling */
table {
  @apply w-full border-collapse;
}

/* Table header styling */
thead {
  @apply bg-gray-50;
}

thead th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200;
}

/* Table body styling with alternating gradient rows */
tbody {
  @apply bg-white divide-y divide-gray-200;
}

/* Normal white background for odd rows */
tbody tr:nth-child(odd) {
  @apply bg-white;
}

/* More visible shade for even rows */
tbody tr:nth-child(even) {
  @apply bg-gray-100;
}

/* Hover effects for table rows */
tbody tr:hover {
  @apply bg-gray-200 transition-colors duration-150;
}

/* Table cell styling */
tbody td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

/* Alternative single shade patterns for different table types */
/* More visible blue shade for admin tables */
.table-admin tbody tr:nth-child(even) {
  @apply bg-blue-100;
}

/* Admin table hover effect */
.table-admin tbody tr:hover {
  @apply bg-blue-200 transition-colors duration-150;
}

/* More visible green shade for success/active tables */
.table-success tbody tr:nth-child(even) {
  @apply bg-green-100;
}

/* Success table hover effect */
.table-success tbody tr:hover {
  @apply bg-green-200 transition-colors duration-150;
}

/* More visible purple shade for premium/featured tables */
.table-premium tbody tr:nth-child(even) {
  @apply bg-purple-100;
}

/* Premium table hover effect */
.table-premium tbody tr:hover {
  @apply bg-purple-200 transition-colors duration-150;
}

/* Responsive table wrapper */
.table-responsive {
  @apply overflow-x-auto shadow-sm border border-gray-200 rounded-lg;
}

/* Table container with border radius */
.table-container {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
}

/* Striped table variant (more pronounced stripes) */
.table-striped tbody tr:nth-child(even) {
  @apply bg-gray-200;
}

/* Compact table variant */
.table-compact thead th {
  @apply px-4 py-2 text-xs;
}

.table-compact tbody td {
  @apply px-4 py-2 text-sm;
}

/* Large table variant */
.table-large thead th {
  @apply px-8 py-4 text-sm;
}

.table-large tbody td {
  @apply px-8 py-5 text-base;
}


/*  */

.carousel-container {
  position: relative;
}

.carousel-container .react-multi-carousel-list {
  position: static;
}

.carousel-container .react-multi-carousel-track {
  display: flex;
  align-items: stretch;
}

.carousel-container .react-multi-carousel-item {
  display: flex;
  align-items: stretch;
}

.carousel-container .react-multi-carousel-item > div {
  width: 100%;
  height: 100%;
}

/* Hide default arrows on small screens */
@media (max-width: 480px) {
  .carousel-container .react-multi-carousel-arrow {
    display: none;
  }
}

/* Custom dot styles */
.custom-dot-list-style {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  gap: 0.5rem;
}

.custom-dot-list-style li button {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: none;
  background-color: var(--theme-text-secondary, #6b7280);
  opacity: 0.5;
  transition: all 0.3s ease;
}

.custom-dot-list-style li.react-multi-carousel-dot--active button {
  background-color: var(--theme-primary, #3b82f6);
  opacity: 1;
  transform: scale(1.2);
}