'use client';

import React, { createContext, useContext, useState, ReactNode, useEffect, useRef } from 'react';
import { Snackbar, Alert, AlertColor, LinearProgress, Box } from '@mui/material';

interface ToastContextType {
  showToast: (message: string, severity?: AlertColor, autoHide?: boolean) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

interface ToastProviderProps {
  children: ReactNode;
}

interface ToastState {
  open: boolean;
  message: string;
  severity: AlertColor;
  progress: number;
  autoHide: boolean;
}

export default function ToastProvider({ children }: ToastProviderProps) {
  const [toast, setToast] = useState<ToastState>({
    open: false,
    message: '',
    severity: 'info',
    progress: 100,
    autoHide: true,
  });
  
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const autoHideTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const showToast = (message: string, severity: AlertColor = 'info', autoHide: boolean = true) => {
    // Clear any existing timers
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
    }
    if (autoHideTimeoutRef.current) {
      clearTimeout(autoHideTimeoutRef.current);
    }
    
    setToast({
      open: true,
      message,
      severity,
      progress: 100,
      autoHide,
    });
    
    if (autoHide) {
      // Start progress bar animation
      const duration = 3000; // 3 seconds
      const interval = 50; // Update every 50ms
      const decrement = (100 * interval) / duration;
      
      progressIntervalRef.current = setInterval(() => {
        setToast(prev => {
          const newProgress = prev.progress - decrement;
          if (newProgress <= 0) {
            return { ...prev, progress: 0 };
          }
          return { ...prev, progress: newProgress };
        });
      }, interval);
      
      // Auto hide after 3 seconds
      autoHideTimeoutRef.current = setTimeout(() => {
        handleClose();
      }, duration);
    }
  };

  const handleClose = (event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    
    // Clear timers when closing
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
    if (autoHideTimeoutRef.current) {
      clearTimeout(autoHideTimeoutRef.current);
      autoHideTimeoutRef.current = null;
    }
    
    setToast(prev => ({ ...prev, open: false, progress: 100 }));
  };
  
  // Cleanup timers on unmount
  useEffect(() => {
    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
      if (autoHideTimeoutRef.current) {
        clearTimeout(autoHideTimeoutRef.current);
      }
    };
  }, []);

  return (
    <ToastContext.Provider value={{ showToast }}>
      {children}
      <Snackbar
        open={toast.open}
        autoHideDuration={null} // Disable default auto hide, we handle it manually
        onClose={handleClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Box sx={{ width: '100%' }}>
          <Alert
            onClose={handleClose}
            severity={toast.severity}
            variant="filled"
            sx={{ 
              width: '100%',
              borderRadius: '8px 8px 0 0', // Rounded top corners only
            }}
          >
            {toast.message}
          </Alert>
          {toast.autoHide && (
            <LinearProgress
              variant="determinate"
              value={toast.progress}
              sx={{
                height: 4,
                borderRadius: '0 0 8px 8px', // Rounded bottom corners only
                backgroundColor: 'rgba(255, 255, 255, 0.3)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                },
              }}
            />
          )}
        </Box>
      </Snackbar>
    </ToastContext.Provider>
  );
}