'use client';

import { ReactNode } from 'react';
import { ThemeProvider, createTheme, alpha } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

// Material Design 3 Color Scheme
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#6750A4', // Material Design 3 primary
      light: '#7F67BE',
      dark: '#4F378B',
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: '#625B71', // Material Design 3 secondary
      light: '#7A7289',
      dark: '#4A4458',
      contrastText: '#FFFFFF',
    },
    error: {
      main: '#BA1A1A', // Material Design 3 error
      light: '#DE3730',
      dark: '#93000A',
      contrastText: '#FFFFFF',
    },
    warning: {
      main: '#F57C00', // Material Design 3 warning
      light: '#FFB74D',
      dark: '#E65100',
      contrastText: '#000000',
    },
    info: {
      main: '#0288D1', // Material Design 3 info
      light: '#03DAC6',
      dark: '#018786',
      contrastText: '#FFFFFF',
    },
    success: {
      main: '#2E7D32', // Material Design 3 success
      light: '#4CAF50',
      dark: '#1B5E20',
      contrastText: '#FFFFFF',
    },
    background: {
      default: '#FFFBFE', // Material Design 3 surface
      paper: '#FFFFFF',
    },
    text: {
      primary: '#1C1B1F', // Material Design 3 on-surface
      secondary: '#49454F', // Material Design 3 on-surface-variant
    },
  },
  
  // Material Design 3 Typography Scale
  typography: {
    fontFamily: '"Roboto", "Inter", "Helvetica", "Arial", sans-serif',
    // Display styles
    h1: {
      fontSize: '3.5rem', // Display Large
      fontWeight: 400,
      lineHeight: 1.12,
      letterSpacing: '-0.25px',
    },
    h2: {
      fontSize: '2.8125rem', // Display Medium
      fontWeight: 400,
      lineHeight: 1.16,
      letterSpacing: '0px',
    },
    h3: {
      fontSize: '2.25rem', // Display Small
      fontWeight: 400,
      lineHeight: 1.22,
      letterSpacing: '0px',
    },
    // Headline styles
    h4: {
      fontSize: '2rem', // Headline Large
      fontWeight: 400,
      lineHeight: 1.25,
      letterSpacing: '0px',
    },
    h5: {
      fontSize: '1.75rem', // Headline Medium
      fontWeight: 400,
      lineHeight: 1.29,
      letterSpacing: '0px',
    },
    h6: {
      fontSize: '1.5rem', // Headline Small
      fontWeight: 400,
      lineHeight: 1.33,
      letterSpacing: '0px',
    },
    // Body styles
    body1: {
      fontSize: '1rem', // Body Large
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: '0.5px',
    },
    body2: {
      fontSize: '0.875rem', // Body Medium
      fontWeight: 400,
      lineHeight: 1.43,
      letterSpacing: '0.25px',
    },
    // Label styles
    button: {
      fontSize: '0.875rem', // Label Large
      fontWeight: 500,
      lineHeight: 1.43,
      letterSpacing: '0.1px',
      textTransform: 'none',
    },
    caption: {
      fontSize: '0.75rem', // Label Medium
      fontWeight: 500,
      lineHeight: 1.33,
      letterSpacing: '0.5px',
    },
    overline: {
      fontSize: '0.6875rem', // Label Small
      fontWeight: 500,
      lineHeight: 1.45,
      letterSpacing: '0.5px',
      textTransform: 'uppercase',
    },
  },
  
  // Material Design 3 Shape System
  shape: {
    borderRadius: 12, // Medium corner radius
  },
  
  // Material Design 3 Elevation System
  shadows: [
    'none',
    '0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15)', // Elevation 1
    '0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15)', // Elevation 2
    '0px 1px 3px 0px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15)', // Elevation 3
    '0px 2px 3px 0px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15)', // Elevation 4
    '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)', // Elevation 5
    '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
    '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
    '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
    '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
    '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
    '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
    '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
    '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
    '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
    '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
    '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
    '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
    '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
    '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
    '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
    '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
    '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
    '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
    '0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
  ],
  
  // Material Design 3 Component Customizations
  components: {
    // Button Components
    MuiButton: {
      styleOverrides: {
        root: ({ theme, ownerState }) => ({
          textTransform: 'none',
          fontWeight: 500,
          borderRadius: 20, // Full corner radius for buttons
          paddingX: 24,
          paddingY: 10,
          minHeight: 40,
          ...(ownerState.variant === 'contained' && {
            boxShadow: theme.shadows[1],
            '&:hover': {
              boxShadow: theme.shadows[2],
            },
            '&:active': {
              boxShadow: theme.shadows[1],
            },
          }),
          ...(ownerState.variant === 'outlined' && {
            borderWidth: 1,
            '&:hover': {
              borderWidth: 1,
            },
          }),
        }),
      },
    },
    
    // Floating Action Button
    MuiFab: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: 16, // Large corner radius for FAB
          boxShadow: theme.shadows[3],
          '&:hover': {
            boxShadow: theme.shadows[4],
          },
        }),
      },
    },
    
    // Text Field Components
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 4, // Extra small corner radius for text fields
            '& fieldset': {
              borderWidth: 1,
            },
            '&:hover fieldset': {
              borderWidth: 1,
            },
            '&.Mui-focused fieldset': {
              borderWidth: 2,
            },
          },
        },
      },
    },
    
    // Card Components
    MuiCard: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: 12, // Medium corner radius for cards
          boxShadow: theme.shadows[1],
          '&:hover': {
            boxShadow: theme.shadows[2],
          },
        }),
      },
    },
    
    // Paper Components
    MuiPaper: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: 12,
          boxShadow: theme.shadows[1],
        }),
      },
    },
    
    // Chip Components
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 8, // Small corner radius for chips
          fontWeight: 500,
        },
      },
    },
    
    // Dialog Components
    MuiDialog: {
      styleOverrides: {
        paper: {
          borderRadius: 28, // Extra large corner radius for dialogs
        },
      },
    },
    
    // Menu Components
    MuiMenu: {
      styleOverrides: {
        paper: {
          borderRadius: 4, // Extra small corner radius for menus
        },
      },
    },
    
    // Snackbar Components
    MuiSnackbar: {
      styleOverrides: {
        root: {
          '& .MuiSnackbarContent-root': {
            borderRadius: 4, // Extra small corner radius for snackbars
          },
        },
      },
    },
    
    // Switch Components
    MuiSwitch: {
      styleOverrides: {
        root: {
          '& .MuiSwitch-thumb': {
            borderRadius: '50%',
          },
          '& .MuiSwitch-track': {
            borderRadius: 16,
          },
        },
      },
    },
    
    // Slider Components
    MuiSlider: {
      styleOverrides: {
        thumb: {
          borderRadius: '50%',
        },
        track: {
          borderRadius: 4,
        },
        rail: {
          borderRadius: 4,
        },
      },
    },
  },
});

// Add alpha function to theme for components that might need it
(theme as any).alpha = alpha;

interface MuiThemeProviderProps {
  children: ReactNode;
}

export default function MuiThemeProvider({ children }: MuiThemeProviderProps) {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  );
}