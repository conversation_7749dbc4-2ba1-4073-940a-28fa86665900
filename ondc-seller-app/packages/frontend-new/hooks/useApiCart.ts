'use client';

import { useState, useCallback, useEffect } from 'react';
import { 
  getStoreCartAPI,
  Cart, 
  Region, 
  AddToCartPayload,
  calculateCartMetadata
} from '@/lib/api/cart';
import { useToast } from '@/app/providers/toast-provider';

export interface AddToCartItem {
  productId: string;
  variantId: string;
  quantity: number;
  product: {
    id: string;
    title: string;
    price: number;
    image: string;
    variant: string;
  };
}

interface UseApiCartReturn {
  cart: Cart | null;
  isOpen: boolean;
  isLoading: boolean;
  region: Region | null;
  addToCart: (item: AddToCartItem) => Promise<void>;
  removeItem: (lineItemId: string) => Promise<void>;
  updateQuantity: (lineItemId: string, quantity: number) => Promise<void>;
  clearCart: () => Promise<void>;
  openCart: () => void;
  closeCart: () => void;
  toggleCart: () => void;
  refreshCart: () => Promise<void>;
  initializeCart: () => Promise<void>;
}

export function useApiCart(storeHandle: string): UseApiCartReturn {
  const { showToast } = useToast();
  const [cart, setCart] = useState<Cart | null>(null);
  const [region, setRegion] = useState<Region | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Get store-specific cart API
  const getCartAPI = useCallback(() => {
    return getStoreCartAPI(storeHandle);
  }, [storeHandle]);

  // Initialize cart - get region and existing cart
  const initializeCart = useCallback(async () => {
    if (!storeHandle) {
      console.warn('No store handle provided for cart initialization');
      return;
    }

    try {
      setIsLoading(true);
      console.log(`🛒 Initializing cart for store: ${storeHandle}`);
      
      const cartAPI = getCartAPI();
      
      // Get Asia region
      const regionsResponse = await cartAPI.getRegions('Asia');
      const asiaRegion = regionsResponse.regions.find(r => 
        r.name.toLowerCase().includes('asia')
      ) || regionsResponse.regions[0]; // Fallback to first region
      
      if (asiaRegion) {
        setRegion(asiaRegion);
        console.log(`📍 Region set for store ${storeHandle}:`, asiaRegion.name);
        
        // Check for existing cart for this store
        const existingCartId = cartAPI.getStoredCartId();
        if (existingCartId) {
          try {
            console.log(`🔍 Found existing cart for store ${storeHandle}:`, existingCartId);
            const cartResponse = await cartAPI.getCart(existingCartId);
            setCart(cartResponse.cart);
            console.log(`✅ Loaded existing cart for store ${storeHandle}:`, cartResponse.cart.items.length, 'items');
          } catch (error) {
            console.warn(`Failed to load existing cart for store ${storeHandle}, will create new one:`, error);
            cartAPI.removeStoredCartId();
          }
        } else {
          console.log(`📝 No existing cart found for store ${storeHandle}`);
        }
      }
    } catch (error) {
      console.error(`Failed to initialize cart for store ${storeHandle}:`, error);
      showToast('Failed to initialize cart', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [showToast, storeHandle, getCartAPI]);

  // Initialize cart and region on mount
  useEffect(() => {
    initializeCart();
  }, [initializeCart]);

  // Create cart if it doesn't exist
  const ensureCartExists = useCallback(async (): Promise<string> => {
    if (cart?.id) {
      return cart.id;
    }

    if (!region) {
      throw new Error('Region not available');
    }

    try {
      console.log(`🆕 Creating new cart for store ${storeHandle}`);
      const cartAPI = getCartAPI();
      
      const cartResponse = await cartAPI.createCart({
        region_id: region.id,
      });
      
      setCart(cartResponse.cart);
      console.log(`✅ Created new cart for store ${storeHandle}:`, cartResponse.cart.id);
      
      return cartResponse.cart.id;
    } catch (error) {
      console.error(`Failed to create cart for store ${storeHandle}:`, error);
      throw new Error('Failed to create cart');
    }
  }, [cart, region, storeHandle, getCartAPI]);

  // Add item to cart
  const addToCart = useCallback(async (item: AddToCartItem) => {
    try {
      setIsLoading(true);
      console.log(`➡️ Adding item to cart for store ${storeHandle}:`, item.product.title);
      
      // Ensure cart exists
      const cartId = await ensureCartExists();
      const cartAPI = getCartAPI();
      
      // Calculate metadata for the item
      const metadata = calculateCartMetadata(
        item.product.price, 
        item.quantity,
        region?.tax_rate || 0.08
      );
      
      // Prepare payload
      const payload: AddToCartPayload = {
        variant_id: item.variantId,
        quantity: item.quantity,
        metadata,
      };
      
      // Add item to cart
      const cartResponse = await cartAPI.addLineItem(cartId, payload);
      setCart(cartResponse.cart);
      
      // Open cart sidebar
      setIsOpen(true);
      
      console.log(`✅ Added item to cart for store ${storeHandle}:`, cartResponse.cart.items.length, 'total items');
      showToast(`Product added to ${storeHandle} cart successfully!`, 'success');
    } catch (error) {
      console.error(`Failed to add item to cart for store ${storeHandle}:`, error);
      showToast('Failed to add product to cart', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [ensureCartExists, region, showToast, storeHandle, getCartAPI]);

  // Remove item from cart
  const removeItem = useCallback(async (lineItemId: string) => {
    if (!cart?.id) return;
    
    try {
      setIsLoading(true);
      console.log(`🗑️ Removing item from cart for store ${storeHandle}:`, lineItemId);
      
      const cartAPI = getCartAPI();
      const cartResponse = await cartAPI.removeLineItem(cart.id, lineItemId);
      setCart(cartResponse.cart);
      
      console.log(`✅ Removed item from cart for store ${storeHandle}:`, cartResponse.cart.items.length, 'items remaining');
      showToast('Item removed from cart', 'success');
    } catch (error) {
      console.error(`Failed to remove item from cart for store ${storeHandle}:`, error);
      showToast('Failed to remove item from cart', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [cart, showToast, storeHandle, getCartAPI]);

  // Update item quantity
  const updateQuantity = useCallback(async (lineItemId: string, quantity: number) => {
    if (!cart?.id) return;
    
    if (quantity <= 0) {
      await removeItem(lineItemId);
      return;
    }
    
    try {
      setIsLoading(true);
      console.log(`📝 Updating quantity for store ${storeHandle}:`, lineItemId, 'to', quantity);
      
      const cartAPI = getCartAPI();
      const cartResponse = await cartAPI.updateLineItem(cart.id, lineItemId, { quantity });
      setCart(cartResponse.cart);
      
      console.log(`✅ Updated quantity for store ${storeHandle}`);
    } catch (error) {
      console.error(`Failed to update quantity for store ${storeHandle}:`, error);
      showToast('Failed to update quantity', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [cart, removeItem, showToast, storeHandle, getCartAPI]);

  // Clear cart
  const clearCart = useCallback(async () => {
    try {
      setIsLoading(true);
      console.log(`🧹 Clearing cart for store ${storeHandle}`);
      
      if (cart?.id) {
        const cartAPI = getCartAPI();
        
        // Remove all items
        for (const item of cart.items) {
          await cartAPI.removeLineItem(cart.id, item.id);
        }
      }
      
      // Reset cart state
      setCart(null);
      const cartAPI = getCartAPI();
      cartAPI.removeStoredCartId();
      
      console.log(`✅ Cleared cart for store ${storeHandle}`);
      showToast(`${storeHandle} cart cleared`, 'success');
    } catch (error) {
      console.error(`Failed to clear cart for store ${storeHandle}:`, error);
      showToast('Failed to clear cart', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [cart, showToast, storeHandle, getCartAPI]);

  // Refresh cart from API
  const refreshCart = useCallback(async () => {
    const cartAPI = getCartAPI();
    const cartId = cartAPI.getStoredCartId();
    if (!cartId) return;
    
    try {
      setIsLoading(true);
      console.log(`🔄 Refreshing cart for store ${storeHandle}:`, cartId);
      
      const cartResponse = await cartAPI.getCart(cartId);
      setCart(cartResponse.cart);
      
      console.log(`✅ Refreshed cart for store ${storeHandle}:`, cartResponse.cart.items.length, 'items');
    } catch (error) {
      console.error(`Failed to refresh cart for store ${storeHandle}:`, error);
      // If cart doesn't exist, remove from storage
      cartAPI.removeStoredCartId();
      setCart(null);
    } finally {
      setIsLoading(false);
    }
  }, [storeHandle, getCartAPI]);

  // Cart visibility controls
  const openCart = useCallback(() => setIsOpen(true), []);
  const closeCart = useCallback(() => setIsOpen(false), []);
  const toggleCart = useCallback(() => setIsOpen(prev => !prev), []);

  // Close cart on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        closeCart();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeCart]);

  // Prevent body scroll when cart is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return {
    cart,
    isOpen,
    isLoading,
    region,
    addToCart,
    removeItem,
    updateQuantity,
    clearCart,
    openCart,
    closeCart,
    toggleCart,
    refreshCart,
    initializeCart,
  };
}
