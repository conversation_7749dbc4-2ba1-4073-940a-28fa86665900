/**
 * React Hook for Store Theme Management
 * Provides easy theme generation and application from store logos
 */

import { useState, useEffect, useCallback } from 'react';
import {
  AutoTheme,
  autoGenerateAndApplyTheme,
  loadStoredTheme,
  clearStoredTheme,
  getCurrentTheme,
  applyDefaultTheme,
  validateThemeAccessibility
} from '@/lib/utils/simpleTheme';

export interface UseStoreThemeReturn {
  // Current theme state
  currentTheme: AutoTheme | null;
  isGenerating: boolean;
  error: string | null;
  
  // Theme actions
  generateThemeFromLogo: (logoUrl: string) => Promise<AutoTheme | null>;
  applyTheme: (theme: AutoTheme) => void;
  resetToDefault: () => void;
  clearTheme: () => void;
  
  // Theme validation
  accessibility: {
    isValid: boolean;
    issues: string[];
  } | null;
}

/**
 * Hook for managing store theme
 */
export const useStoreTheme = (storeHandle?: string): UseStoreThemeReturn => {
  const [currentTheme, setCurrentTheme] = useState<AutoTheme | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [accessibility, setAccessibility] = useState<{
    isValid: boolean;
    issues: string[];
  } | null>(null);

  // Load stored theme on mount
  useEffect(() => {
    if (storeHandle) {
      const storedTheme = loadStoredTheme(storeHandle);
      if (storedTheme) {
        setCurrentTheme(storedTheme);
        setAccessibility(validateThemeAccessibility(storedTheme));
      }
    }
  }, [storeHandle]);

  // Generate theme from logo
  const generateThemeFromLogo = useCallback(async (logoUrl: string): Promise<AutoTheme | null> => {
    if (!logoUrl) {
      setError('Logo URL is required');
      return null;
    }

    setIsGenerating(true);
    setError(null);

    try {
      console.log('🎨 Generating theme from logo:', logoUrl);
      
      const theme = await autoGenerateAndApplyTheme(logoUrl, storeHandle);
      
      setCurrentTheme(theme);
      setAccessibility(validateThemeAccessibility(theme));
      
      console.log('✅ Theme generated successfully:', theme);
      return theme;
      
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to generate theme from logo';
      console.error('❌ Error generating theme:', err);
      setError(errorMessage);
      return null;
      
    } finally {
      setIsGenerating(false);
    }
  }, [storeHandle]);

  // Apply existing theme
  const applyTheme = useCallback((theme: AutoTheme) => {
    try {
      setCurrentTheme(theme);
      setAccessibility(validateThemeAccessibility(theme));
      
      // Apply to CSS variables
      if (typeof document !== 'undefined') {
        const root = document.documentElement;
        
        Object.entries(theme).forEach(([key, value]) => {
          const cssVar = `--store-${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
          root.style.setProperty(cssVar, value);
        });
      }
      
      console.log('✅ Theme applied:', theme);
      
    } catch (err: any) {
      console.error('❌ Error applying theme:', err);
      setError('Failed to apply theme');
    }
  }, []);

  // Reset to default theme
  const resetToDefault = useCallback(() => {
    try {
      applyDefaultTheme();
      const defaultTheme = getCurrentTheme();
      
      if (defaultTheme) {
        setCurrentTheme(defaultTheme);
        setAccessibility(validateThemeAccessibility(defaultTheme));
      }
      
      setError(null);
      console.log('✅ Reset to default theme');
      
    } catch (err: any) {
      console.error('❌ Error resetting theme:', err);
      setError('Failed to reset theme');
    }
  }, []);

  // Clear theme
  const clearTheme = useCallback(() => {
    if (storeHandle) {
      clearStoredTheme(storeHandle);
    }
    
    setCurrentTheme(null);
    setAccessibility(null);
    setError(null);
    
    console.log('🗑️ Theme cleared');
  }, [storeHandle]);

  return {
    currentTheme,
    isGenerating,
    error,
    generateThemeFromLogo,
    applyTheme,
    resetToDefault,
    clearTheme,
    accessibility
  };
};

/**
 * Hook for theme preview without applying
 */
export const useThemePreview = () => {
  const [previewTheme, setPreviewTheme] = useState<AutoTheme | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generatePreview = useCallback(async (logoUrl: string): Promise<AutoTheme | null> => {
    if (!logoUrl) {
      setError('Logo URL is required');
      return null;
    }

    setIsGenerating(true);
    setError(null);

    try {
      // Import theme generation functions
      const { extractDominantColor, generateSimpleTheme } = await import('@/lib/utils/simpleTheme');
      
      const dominantColor = await extractDominantColor(logoUrl);
      const theme = generateSimpleTheme(dominantColor);
      
      setPreviewTheme(theme);
      return theme;
      
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to generate theme preview';
      console.error('❌ Error generating theme preview:', err);
      setError(errorMessage);
      return null;
      
    } finally {
      setIsGenerating(false);
    }
  }, []);

  const clearPreview = useCallback(() => {
    setPreviewTheme(null);
    setError(null);
  }, []);

  return {
    previewTheme,
    isGenerating,
    error,
    generatePreview,
    clearPreview
  };
};

/**
 * Hook for theme accessibility validation
 */
export const useThemeAccessibility = (theme: AutoTheme | null) => {
  const [accessibility, setAccessibility] = useState<{
    isValid: boolean;
    issues: string[];
  } | null>(null);

  useEffect(() => {
    if (theme) {
      setAccessibility(validateThemeAccessibility(theme));
    } else {
      setAccessibility(null);
    }
  }, [theme]);

  return accessibility;
};