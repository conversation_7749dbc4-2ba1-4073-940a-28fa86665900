'use client';

/**
 * @deprecated This hook is no longer needed.
 * Authentication is now handled reactively through API response interceptors.
 * When any API call returns 401, the auth state is automatically cleared and user is redirected.
 * This is more efficient than periodic token checking.
 */

interface UseAdminTokenCheckOptions {
  storeHandle: string;
  checkInterval?: number;
  enabled?: boolean;
}

/**
 * @deprecated Use API response interceptors instead
 * This hook is kept for backward compatibility but does nothing
 */
export const useAdminTokenCheck = ({
  storeHandle,
  checkInterval = 10 * 60 * 1000,
  enabled = false // Disabled by default
}: UseAdminTokenCheckOptions) => {
  // Only log once in development
  if (process.env.NODE_ENV === 'development' && enabled) {
    console.log('⚠️ useAdminTokenCheck is deprecated. Using reactive auth instead.');
  }
  
  // Return empty functions for backward compatibility
  return { 
    stopTokenCheck: () => {
      // No-op for backward compatibility
    }
  };
};