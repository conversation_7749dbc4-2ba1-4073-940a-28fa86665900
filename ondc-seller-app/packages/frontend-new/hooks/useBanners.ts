import { useQuery } from "@tanstack/react-query";
import { strapiStoreService, type NormalizedBanner } from "@/lib/api/strapi-store";
import { demoBanners } from "@/lib/demo-data";

export const useBanners = (storeHandle?: string) => {
  return useQuery({
    queryKey: ["banners", storeHandle],
    queryFn: () => strapiStoreService.getBanners(storeHandle),
    staleTime: 10 * 60 * 1000, // 10 minutes
    cacheTime: 15 * 60 * 1000, // 15 minutes
    retry: 1, // Only retry once before falling back to demo data
    retryOnMount: false,
    refetchOnWindowFocus: false,
    // Transform the data to ensure it matches the expected Banner interface
    select: (data: NormalizedBanner[]) => {
      if (!data || data.length === 0) {
        console.warn("No banners from API, using demo banners");
        return demoBanners;
      }
      
      // Transform NormalizedBanner to Banner interface expected by HeroBanner
      return data.map(banner => ({
        id: banner.id,
        title: banner.title,
        subtitle: banner.subtitle,
        description: banner.description,
        image: banner.image,
        ctaText: banner.ctaText,
        ctaLink: banner.ctaLink,
        backgroundColor: banner.backgroundColor,
      }));
    },
    // Fallback to demo data on error
    onError: (error) => {
      console.error("Error fetching banners, will use demo data:", error);
    },
  });
};