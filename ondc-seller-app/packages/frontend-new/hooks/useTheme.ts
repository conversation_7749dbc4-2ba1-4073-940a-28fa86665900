'use client';

import { useState, useEffect, useCallback } from 'react';
import { StoreThemeConfig, ComponentTheme } from '@/lib/theme/types';
import { generateComponentTheme, generateCSSVariables, applyCSSVariables } from '@/lib/theme/themeGenerator';
import { getThemeForStore, defaultThemeConfig } from '@/lib/theme/mockThemes';

interface UseThemeReturn {
  theme: StoreThemeConfig;
  componentTheme: ComponentTheme;
  isLoading: boolean;
  error: string | null;
  applyTheme: (themeConfig: StoreThemeConfig) => void;
  resetTheme: () => void;
}

/**
 * Hook for managing store themes
 */
export function useTheme(storeHandle?: string): UseThemeReturn {
  const [theme, setTheme] = useState<StoreThemeConfig>(defaultThemeConfig);
  const [componentTheme, setComponentTheme] = useState<ComponentTheme>(
    generateComponentTheme(defaultThemeConfig.derivedColors)
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Apply theme to the document
   */
  const applyTheme = useCallback((themeConfig: StoreThemeConfig) => {
    try {
      setIsLoading(true);
      setError(null);

      // Generate component theme
      const newComponentTheme = generateComponentTheme(themeConfig.derivedColors);
      
      // Generate CSS variables
      const cssVariables = generateCSSVariables(themeConfig.derivedColors, newComponentTheme);
      
      // Apply CSS variables to document
      applyCSSVariables(cssVariables);
      
      // Update state
      setTheme(themeConfig);
      setComponentTheme(newComponentTheme);
      
      // Store theme in localStorage for persistence
      if (typeof window !== 'undefined') {
        localStorage.setItem('store-theme', JSON.stringify(themeConfig));
      }
      
      console.log('Theme applied successfully:', themeConfig.name);
    } catch (err) {
      console.error('Error applying theme:', err);
      setError('Failed to apply theme');
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Reset to default theme
   */
  const resetTheme = useCallback(() => {
    applyTheme(defaultThemeConfig);
    if (typeof window !== 'undefined') {
      localStorage.removeItem('store-theme');
    }
  }, [applyTheme]);

  /**
   * Load theme for store
   */
  useEffect(() => {
    if (!storeHandle) return;

    setIsLoading(true);
    
    try {
      // Check for saved theme first
      let savedTheme: StoreThemeConfig | null = null;
      if (typeof window !== 'undefined') {
        const saved = localStorage.getItem('store-theme');
        if (saved) {
          savedTheme = JSON.parse(saved);
        }
      }

      // Use saved theme or get theme for store
      const themeToApply = savedTheme || getThemeForStore(storeHandle);
      applyTheme(themeToApply);
      
    } catch (err) {
      console.error('Error loading theme:', err);
      setError('Failed to load theme');
      // Fallback to default theme
      applyTheme(defaultThemeConfig);
    }
  }, [storeHandle, applyTheme]);

  /**
   * Apply default theme on mount
   */
  useEffect(() => {
    if (!storeHandle) {
      applyTheme(defaultThemeConfig);
    }
  }, [storeHandle, applyTheme]);

  return {
    theme,
    componentTheme,
    isLoading,
    error,
    applyTheme,
    resetTheme,
  };
}

/**
 * Hook for getting theme colors as CSS variables
 */
export function useThemeColors() {
  const [colors, setColors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const updateColors = () => {
      const root = document.documentElement;
      const computedStyle = getComputedStyle(root);
      
      const themeColors = {
        primary: computedStyle.getPropertyValue('--theme-primary').trim(),
        secondary: computedStyle.getPropertyValue('--theme-secondary').trim(),
        accent: computedStyle.getPropertyValue('--theme-accent').trim(),
        background: computedStyle.getPropertyValue('--theme-background').trim(),
        surface: computedStyle.getPropertyValue('--theme-surface').trim(),
        text: computedStyle.getPropertyValue('--theme-text').trim(),
        textSecondary: computedStyle.getPropertyValue('--theme-text-secondary').trim(),
        border: computedStyle.getPropertyValue('--theme-border').trim(),
        hover: computedStyle.getPropertyValue('--theme-hover').trim(),
        active: computedStyle.getPropertyValue('--theme-active').trim(),
      };
      
      setColors(themeColors);
    };

    // Initial update
    updateColors();

    // Listen for theme changes
    const observer = new MutationObserver(updateColors);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['style'],
    });

    return () => observer.disconnect();
  }, []);

  return colors;
}