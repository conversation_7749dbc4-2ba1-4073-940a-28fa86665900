'use client';

/**
 * @deprecated This hook is no longer needed.
 * Authentication is now handled reactively through API response interceptors.
 * When any API call returns 401, the auth state is automatically cleared and user is redirected.
 * Navigation-based token checking is unnecessary and can cause performance issues.
 */

interface UseAdminNavigationGuardOptions {
  storeHandle: string;
  enabled?: boolean;
}

/**
 * @deprecated Use API response interceptors instead
 * This hook is kept for backward compatibility but does nothing
 */
export const useAdminNavigationGuard = ({
  storeHandle,
  enabled = false // Disabled by default
}: UseAdminNavigationGuardOptions) => {
  // Only log once in development
  if (process.env.NODE_ENV === 'development' && enabled) {
    console.log('⚠️ useAdminNavigationGuard is deprecated. Using reactive auth instead.');
  }
  
  // Hook does nothing now - authentication is handled reactively
  // No useEffect needed for backward compatibility
};