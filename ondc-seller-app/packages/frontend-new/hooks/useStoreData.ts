import { useEffect } from 'react';
import { useStoreConfigStore } from '@/stores/storeConfigStore';

/**
 * Enhanced hook for accessing store data with automatic fetching
 * Provides easy access to all store information with proper caching
 */
export const useStoreData = (storeHandle?: string) => {
  const {
    storeCache,
    currentStoreHandle,
    isLoading,
    error,
    fetchStoreConfig,
    setCurrentStore,
    getStoreFromCache,
    getRawResponseFromCache,
    isStoreCacheValid,
    // Getters
    getStoreName,
    getStoreHandle,
    getStoreDescription,
    getStoreAddress,
    getStoreContact,
    getStoreLogo,
    getStoreTheme,
    getStoreSettings,
    getSocialMedia,
    getBusinessHours,
  } = useStoreConfigStore();

  // Auto-fetch store data if handle is provided and not cached
  useEffect(() => {
    if (storeHandle) {
      // Set as current store
      if (currentStoreHandle !== storeHandle) {
        setCurrentStore(storeHandle);
      }
      
      // Fetch if not in cache or cache is invalid
      if (!isStoreCacheValid(storeHandle)) {
        fetchStoreConfig(storeHandle).catch((error) => {
          console.error('Failed to fetch store data:', error);
        });
      }
    }
  }, [storeHandle, currentStoreHandle, setCurrentStore, isStoreCacheValid, fetchStoreConfig]);

  // Get current store data
  const currentStoreData = currentStoreHandle ? getStoreFromCache(currentStoreHandle) : null;
  
  // Get specific store data if handle is provided
  const requestedStoreData = storeHandle ? getStoreFromCache(storeHandle) : null;
  
  // Use requested store data if available, otherwise use current
  const storeData = requestedStoreData || currentStoreData;

  return {
    // Store data
    storeData,
    storeCache,
    currentStoreHandle,
    isLoading,
    error,
    
    // Convenience getters (for current store)
    storeName: getStoreName(),
    storeHandle: getStoreHandle(),
    storeDescription: getStoreDescription(),
    storeAddress: getStoreAddress(),
    storeContact: getStoreContact(),
    storeLogo: getStoreLogo(),
    storeTheme: getStoreTheme(),
    storeSettings: getStoreSettings(),
    socialMedia: getSocialMedia(),
    businessHours: getBusinessHours(),
    
    // Actions
    fetchStoreConfig,
    setCurrentStore,
    
    // Cache utilities
    getStoreFromCache,
    getRawResponseFromCache,
    isStoreCacheValid,
    
    // Computed properties
    hasStoreData: !!storeData,
    isCurrentStore: storeHandle === currentStoreHandle,
    cachedStoreCount: Object.keys(storeCache).length,
    
    // Helper functions
    getStoreProperty: (property: keyof typeof storeData) => {
      return storeData?.[property] || null;
    },
    
    isStoreLoaded: (handle: string) => {
      return isStoreCacheValid(handle);
    },
    
    getAllCachedStores: () => {
      return Object.keys(storeCache).map(handle => ({
        handle,
        normalizedData: storeCache[handle].normalizedData,
        rawResponse: storeCache[handle].rawResponse,
        lastFetched: storeCache[handle].lastFetched,
        isValid: isStoreCacheValid(handle),
      }));
    },
    
    // Get raw API response for specific store
    getRawResponse: (handle?: string) => {
      const targetHandle = handle || storeHandle;
      return targetHandle ? getRawResponseFromCache(targetHandle) : null;
    },
  };
};

/**
 * Hook specifically for theme-related store data
 */
export const useStoreTheme = (storeHandle?: string) => {
  const { storeTheme, storeData, isLoading } = useStoreData(storeHandle);
  
  return {
    theme: storeTheme,
    hasTheme: !!storeTheme,
    isLoading,
    
    // Individual theme properties
    primaryColor: storeTheme?.primary_color,
    secondaryColor: storeTheme?.secondary_color,
    accentColor: storeTheme?.accent_color,
    backgroundColor: storeTheme?.background_color,
    textColor: storeTheme?.text_color,
    
    // CSS custom properties object
    cssVariables: storeTheme ? {
      '--store-primary': storeTheme.primary_color || '',
      '--store-secondary': storeTheme.secondary_color || '',
      '--store-accent': storeTheme.accent_color || '',
      '--store-background': storeTheme.background_color || '',
      '--store-text': storeTheme.text_color || '',
    } : {},
    
    // Apply theme to document
    applyTheme: () => {
      if (storeTheme && typeof document !== 'undefined') {
        const root = document.documentElement;
        Object.entries({
          '--store-primary': storeTheme.primary_color,
          '--store-secondary': storeTheme.secondary_color,
          '--store-accent': storeTheme.accent_color,
          '--store-background': storeTheme.background_color,
          '--store-text': storeTheme.text_color,
        }).forEach(([property, value]) => {
          if (value) {
            root.style.setProperty(property, value);
          }
        });
      }
    },
  };
};

/**
 * Hook for store contact and address information
 */
export const useStoreContact = (storeHandle?: string) => {
  const { storeContact, storeAddress, isLoading } = useStoreData(storeHandle);
  
  return {
    contact: storeContact,
    address: storeAddress,
    isLoading,
    
    // Contact details
    email: storeContact?.email,
    phone: storeContact?.phone,
    website: storeContact?.website,
    
    // Address details
    street: storeAddress?.street,
    city: storeAddress?.city,
    state: storeAddress?.state,
    postalCode: storeAddress?.postal_code,
    country: storeAddress?.country,
    
    // Formatted address
    formattedAddress: storeAddress ? [
      storeAddress.street,
      [storeAddress.city, storeAddress.state, storeAddress.postal_code]
        .filter(Boolean)
        .join(', '),
      storeAddress.country,
    ].filter(Boolean).join('\n') : '',
    
    // Helper functions
    hasContact: !!storeContact,
    hasAddress: !!storeAddress,
    hasContactInfo: !!(storeContact?.email || storeContact?.phone),
  };
};