'use client';

import { useEffect, useState } from 'react';
import { useStoreAuth } from './useStoreAuth';

/**
 * Hook that provides store auth with proper hydration handling
 * Ensures auth state is properly loaded from localStorage before rendering
 */
export function useStoreAuthWithHydration(storeHandle?: string) {
  const [isHydrated, setIsHydrated] = useState(false);
  const authData = useStoreAuth(storeHandle);

  useEffect(() => {
    // Wait for hydration to complete
    const checkHydration = () => {
      if (typeof window !== 'undefined') {
        // Give a small delay to ensure localStorage is read
        setTimeout(() => {
          setIsHydrated(true);
        }, 100);
      }
    };

    checkHydration();
  }, []);

  // Return loading state until hydrated
  if (!isHydrated) {
    return {
      ...authData,
      isLoading: true,
      loadingMessage: 'Loading authentication state...',
    };
  }

  return authData;
}