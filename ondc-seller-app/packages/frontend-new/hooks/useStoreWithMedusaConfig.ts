import { useState, useEffect } from 'react';
import { storeConfigApi, type StoreConfig, type MedusaApiConfig } from '@/lib/api/store-config';

interface UseStoreWithMedusaConfigOptions {
  enabled?: boolean;
}

interface UseStoreWithMedusaConfigResult {
  storeConfig: StoreConfig | null;
  medusaConfig: MedusaApiConfig | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

/**
 * Hook to fetch store configuration and build Medusa API config
 * Follows the same pattern as other API hooks in the project
 */
export const useStoreWithMedusaConfig = (
  storeHandle: string | undefined,
  options: UseStoreWithMedusaConfigOptions = {}
): UseStoreWithMedusaConfigResult => {
  const [storeConfig, setStoreConfig] = useState<StoreConfig | null>(null);
  const [medusaConfig, setMedusaConfig] = useState<MedusaApiConfig | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { enabled = true } = options;

  const fetchStoreConfig = async () => {
    if (!storeHandle || !enabled) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      console.log('Fetching store config with useStoreWithMedusaConfig hook:', storeHandle);
      
      const result = await storeConfigApi.getStoreWithMedusaConfig(storeHandle);
      
      setStoreConfig(result.storeConfig);
      setMedusaConfig(result.medusaConfig);
      
      console.log('Store config and Medusa config fetched successfully:', result);
    } catch (err: any) {
      console.error('Error in useStoreWithMedusaConfig hook:', err);
      setError(err.message || 'Failed to fetch store configuration');
      setStoreConfig(null);
      setMedusaConfig(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStoreConfig();
  }, [storeHandle, enabled]);

  const refetch = () => {
    fetchStoreConfig();
  };

  return {
    storeConfig,
    medusaConfig,
    isLoading,
    error,
    refetch,
  };
};