'use client';

import { useState, useCallback } from 'react';
import { CheckoutState, CheckoutStep, ContactInformation, ShippingAddress, BillingAddress, PaymentMethod, CreditCardDetails } from '@/types/checkout';

const initialSteps: CheckoutStep[] = [
  {
    id: 1,
    name: 'contact',
    title: 'Contact Information',
    description: 'Enter your contact details',
    isCompleted: false,
    isActive: true,
  },
  {
    id: 2,
    name: 'payment',
    title: 'Payment Method',
    description: 'Choose how to pay',
    isCompleted: false,
    isActive: false,
  },
  {
    id: 3,
    name: 'review',
    title: 'Review Order',
    description: 'Review and place your order',
    isCompleted: false,
    isActive: false,
  },
];

const initialState: CheckoutState = {
  currentStep: 1,
  steps: initialSteps,
  contactInfo: {
    email: '',
    firstName: '',
    lastName: '',
    phone: '',
  },
  shippingAddress: {
    address: '',
    address2: '',
    city: '',
    state: '',
    zipCode: '',
    country: '',
  },
  billingAddress: {
    address: '',
    address2: '',
    city: '',
    state: '',
    zipCode: '',
    country: '',
  },
  useSameAddressForBilling: true,
  selectedPaymentMethod: null,
  creditCardDetails: {
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
  },
  orderNotes: '',
  isProcessing: false,
};

export function useCheckout() {
  const [checkoutState, setCheckoutState] = useState<CheckoutState>(initialState);

  const updateContactInfo = useCallback((contactInfo: Partial<ContactInformation>) => {
    setCheckoutState(prev => ({
      ...prev,
      contactInfo: { ...prev.contactInfo, ...contactInfo },
    }));
  }, []);

  const updateShippingAddress = useCallback((shippingAddress: Partial<ShippingAddress>) => {
    setCheckoutState(prev => {
      const newState = {
        ...prev,
        shippingAddress: { ...prev.shippingAddress, ...shippingAddress },
      };
      
      // If using same address for billing, update billing address too
      if (prev.useSameAddressForBilling) {
        newState.billingAddress = { ...newState.shippingAddress };
      }
      
      return newState;
    });
  }, []);

  const updateBillingAddress = useCallback((billingAddress: Partial<BillingAddress>) => {
    setCheckoutState(prev => ({
      ...prev,
      billingAddress: { ...prev.billingAddress, ...billingAddress },
    }));
  }, []);

  const toggleSameAddressForBilling = useCallback((useSame: boolean) => {
    setCheckoutState(prev => {
      const newState = {
        ...prev,
        useSameAddressForBilling: useSame,
      };
      
      // If using same address, copy shipping to billing
      if (useSame) {
        newState.billingAddress = { ...prev.shippingAddress };
      }
      
      return newState;
    });
  }, []);

  const selectPaymentMethod = useCallback((paymentMethod: PaymentMethod) => {
    setCheckoutState(prev => ({
      ...prev,
      selectedPaymentMethod: paymentMethod,
    }));
  }, []);

  const updateCreditCardDetails = useCallback((creditCardDetails: Partial<CreditCardDetails>) => {
    setCheckoutState(prev => ({
      ...prev,
      creditCardDetails: { ...prev.creditCardDetails, ...creditCardDetails },
    }));
  }, []);

  const updateOrderNotes = useCallback((orderNotes: string) => {
    setCheckoutState(prev => ({
      ...prev,
      orderNotes,
    }));
  }, []);

  const goToNextStep = useCallback(() => {
    setCheckoutState(prev => {
      const newSteps = prev.steps.map(step => {
        if (step.id === prev.currentStep) {
          return { ...step, isCompleted: true, isActive: false };
        }
        if (step.id === prev.currentStep + 1) {
          return { ...step, isActive: true };
        }
        return step;
      });

      return {
        ...prev,
        currentStep: Math.min(prev.currentStep + 1, prev.steps.length),
        steps: newSteps,
      };
    });
  }, []);

  const goToPreviousStep = useCallback(() => {
    setCheckoutState(prev => {
      const newSteps = prev.steps.map(step => {
        if (step.id === prev.currentStep) {
          return { ...step, isActive: false };
        }
        if (step.id === prev.currentStep - 1) {
          return { ...step, isCompleted: false, isActive: true };
        }
        return step;
      });

      return {
        ...prev,
        currentStep: Math.max(prev.currentStep - 1, 1),
        steps: newSteps,
      };
    });
  }, []);

  const goToStep = useCallback((stepNumber: number) => {
    setCheckoutState(prev => {
      const newSteps = prev.steps.map(step => {
        if (step.id === stepNumber) {
          return { ...step, isActive: true };
        } else if (step.id < stepNumber) {
          return { ...step, isCompleted: true, isActive: false };
        } else {
          return { ...step, isCompleted: false, isActive: false };
        }
      });

      return {
        ...prev,
        currentStep: stepNumber,
        steps: newSteps,
      };
    });
  }, []);

  const setProcessing = useCallback((isProcessing: boolean) => {
    setCheckoutState(prev => ({
      ...prev,
      isProcessing,
    }));
  }, []);

  const resetCheckout = useCallback(() => {
    setCheckoutState(initialState);
  }, []);

  const validateCurrentStep = useCallback(() => {
    const { currentStep, contactInfo, shippingAddress, billingAddress, useSameAddressForBilling, selectedPaymentMethod, creditCardDetails } = checkoutState;

    switch (currentStep) {
      case 1:
        const shippingValid = (
          contactInfo.email.trim() !== '' &&
          contactInfo.firstName.trim() !== '' &&
          contactInfo.lastName.trim() !== '' &&
          shippingAddress.address.trim() !== '' &&
          shippingAddress.city.trim() !== '' &&
          shippingAddress.state.trim() !== '' &&
          shippingAddress.zipCode.trim() !== '' &&
          shippingAddress.country.trim() !== ''
        );
        
        if (useSameAddressForBilling) {
          return shippingValid;
        }
        
        const billingValid = (
          billingAddress.address.trim() !== '' &&
          billingAddress.city.trim() !== '' &&
          billingAddress.state.trim() !== '' &&
          billingAddress.zipCode.trim() !== '' &&
          billingAddress.country.trim() !== ''
        );
        
        return shippingValid && billingValid;
      case 2:
        if (!selectedPaymentMethod) return false;
        if (selectedPaymentMethod.type === 'credit_card') {
          return (
            creditCardDetails.cardNumber.trim() !== '' &&
            creditCardDetails.expiryDate.trim() !== '' &&
            creditCardDetails.cvv.trim() !== '' &&
            creditCardDetails.cardholderName.trim() !== ''
          );
        }
        return true;
      case 3:
        return true;
      default:
        return false;
    }
  }, [checkoutState]);

  return {
    checkoutState,
    updateContactInfo,
    updateShippingAddress,
    updateBillingAddress,
    toggleSameAddressForBilling,
    selectPaymentMethod,
    updateCreditCardDetails,
    updateOrderNotes,
    goToNextStep,
    goToPreviousStep,
    goToStep,
    setProcessing,
    resetCheckout,
    validateCurrentStep,
  };
}