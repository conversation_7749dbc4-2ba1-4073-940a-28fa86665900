/**
 * Auto Theme Loader Hook
 * Automatically fetches and applies themes for stores with existing logos
 * Sources: Zustand store, localStorage, and Strapi API
 */

import { useEffect, useState, useCallback } from 'react';
import { useStoreTheme } from './useStoreTheme';
import { useStoreConfigStore } from '@/stores/storeConfigStore';
import { AutoTheme } from '@/lib/utils/simpleTheme';

export interface UseAutoThemeLoaderReturn {
  isLoading: boolean;
  hasAutoApplied: boolean;
  logoSource: 'zustand' | 'localStorage' | 'strapi' | null;
  error: string | null;
  retryAutoLoad: () => Promise<void>;
}

/**
 * Hook to automatically load and apply themes from existing store data
 */
export const useAutoThemeLoader = (storeHandle: string): UseAutoThemeLoaderReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [hasAutoApplied, setHasAutoApplied] = useState(false);
  const [logoSource, setLogoSource] = useState<'zustand' | 'localStorage' | 'strapi' | null>(null);
  const [error, setError] = useState<string | null>(null);

  const { generateThemeFromLogo, currentTheme } = useStoreTheme(storeHandle);
  const storeConfigStore = useStoreConfigStore(storeHandle);

  /**
   * Get logo from localStorage store config
   */
  const getLogoFromLocalStorage = useCallback((): string | null => {
    if (typeof window === 'undefined') return null;

    try {
      const storeConfigKey = `store-config-${storeHandle}`;
      const storedConfig = localStorage.getItem(storeConfigKey);
      
      if (storedConfig) {
        const parsedConfig = JSON.parse(storedConfig);
        console.log('📦 Found localStorage store config:', parsedConfig);
        
        // Check different possible logo paths
        const logo = parsedConfig.storeData?.logo || 
                    parsedConfig.storeData?.store_logo_url ||
                    parsedConfig.logo ||
                    parsedConfig.store_logo_url;
        
        if (logo) {
          console.log('✅ Found logo in localStorage:', logo);
          return logo;
        }
      }
    } catch (error) {
      console.warn('⚠️ Error reading from localStorage:', error);
    }

    return null;
  }, [storeHandle]);

  /**
   * Get logo from Zustand store
   */
  const getLogoFromZustand = useCallback((): string | null => {
    try {
      // Check store data from Zustand
      const storeData = storeConfigStore.storeData;
      if (storeData?.logo) {
        console.log('✅ Found logo in Zustand store data:', storeData.logo);
        return storeData.logo;
      }

      // Check store config from Zustand
      const storeConfig = storeConfigStore.storeConfig;
      if (storeConfig?.store_logo_url) {
        console.log('✅ Found logo in Zustand store config:', storeConfig.store_logo_url);
        return storeConfig.store_logo_url;
      }

      console.log('⚠️ No logo found in Zustand store');
      return null;
    } catch (error) {
      console.warn('⚠️ Error reading from Zustand store:', error);
      return null;
    }
  }, [storeConfigStore]);

  /**
   * Auto-load theme from available sources
   */
  const autoLoadTheme = useCallback(async (): Promise<void> => {
    if (!storeHandle) {
      console.log('⚠️ No store handle provided for auto theme loading');
      return;
    }

    // Skip if theme is already applied
    if (currentTheme) {
      console.log('✅ Theme already applied, skipping auto-load');
      setHasAutoApplied(true);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('🎨 Starting auto theme loading for store:', storeHandle);

      let logoUrl: string | null = null;
      let source: 'zustand' | 'localStorage' | 'strapi' | null = null;

      // 1. Try to get logo from Zustand store first (most up-to-date)
      logoUrl = getLogoFromZustand();
      if (logoUrl) {
        source = 'zustand';
      }

      // 2. If not found, try localStorage
      if (!logoUrl) {
        logoUrl = getLogoFromLocalStorage();
        if (logoUrl) {
          source = 'localStorage';
        }
      }

      // 3. If still not found, try to fetch from Strapi
      if (!logoUrl) {
        console.log('🔄 Fetching store data from Strapi...');
        try {
          await storeConfigStore.fetchStoreConfig(storeHandle);
          logoUrl = getLogoFromZustand(); // Check again after fetch
          if (logoUrl) {
            source = 'strapi';
          }
        } catch (fetchError) {
          console.warn('⚠️ Failed to fetch from Strapi:', fetchError);
        }
      }

      // 4. Generate theme if logo found
      if (logoUrl && source) {
        console.log(`🎨 Generating theme from logo (source: ${source}):`, logoUrl);
        
        const generatedTheme = await generateThemeFromLogo(logoUrl);
        
        if (generatedTheme) {
          console.log('✅ Auto theme applied successfully:', generatedTheme);
          setHasAutoApplied(true);
          setLogoSource(source);
        } else {
          console.warn('⚠️ Failed to generate theme from logo');
          setError('Failed to generate theme from logo');
        }
      } else {
        console.log('ℹ️ No logo found in any source for store:', storeHandle);
        setError('No logo found for automatic theme generation');
      }

    } catch (error: any) {
      console.error('❌ Error in auto theme loading:', error);
      setError(error.message || 'Failed to auto-load theme');
    } finally {
      setIsLoading(false);
    }
  }, [storeHandle, currentTheme, generateThemeFromLogo, getLogoFromZustand, getLogoFromLocalStorage, storeConfigStore]);

  /**
   * Retry auto-loading
   */
  const retryAutoLoad = useCallback(async (): Promise<void> => {
    setHasAutoApplied(false);
    setLogoSource(null);
    setError(null);
    await autoLoadTheme();
  }, [autoLoadTheme]);

  // Auto-load theme on mount and when store handle changes
  useEffect(() => {
    if (storeHandle && !hasAutoApplied) {
      // Small delay to ensure other hooks are initialized
      const timer = setTimeout(() => {
        autoLoadTheme();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [storeHandle, hasAutoApplied, autoLoadTheme]);

  return {
    isLoading,
    hasAutoApplied,
    logoSource,
    error,
    retryAutoLoad,
  };
};