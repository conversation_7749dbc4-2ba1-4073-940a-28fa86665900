import { useQuery } from '@tanstack/react-query';
import { getCMSPageByStoreAndPageName, type CMSPageData, type StrapiResponse } from '@/lib/api/strapi/pages';

export interface UseCMSPageReturn {
  data: CMSPageData | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
}

/**
 * Hook to fetch CMS page content by store handle and page name
 * @param storeHandle - The store handle
 * @param pageName - The page name (e.g., "About us", "Contact us", etc.)
 * @param enabled - Whether to enable the query (default: true)
 */
export const useCMSPage = (
  storeHandle: string, 
  pageName: string,
  enabled: boolean = true
): UseCMSPageReturn => {
  const {
    data,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['cms-page', storeHandle, pageName],
    queryFn: async (): Promise<CMSPageData | null> => {
      try {
        console.log('🔄 Fetching CMS page:', { storeHandle, pageName });
        
        const response: StrapiResponse<CMSPageData[]> = await getCMSPageByStoreAndPageName(storeHandle, pageName);
        
        console.log('📄 CMS page response:', response);
        
        // Return the first page if found, or null if no pages
        if (response.data && response.data.length > 0) {
          return response.data[0];
        }
        
        return null;
      } catch (error) {
        console.error('❌ Error fetching CMS page:', error);
        throw error;
      }
    },
    enabled: enabled && !!storeHandle && !!pageName,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });

  return {
    data: data || null,
    isLoading,
    error: error as Error | null,
    refetch,
  };
};