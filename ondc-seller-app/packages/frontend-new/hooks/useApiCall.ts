'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useToast } from '@/app/providers/toast-provider';

interface UseApiCallOptions<T> {
  apiCall: () => Promise<T>;
  dependencies: any[];
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
  errorMessage?: string;
  skipInitialCall?: boolean;
}

interface UseApiCallReturn<T> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

export function useApiCall<T>({
  apiCall,
  dependencies,
  onSuccess,
  onError,
  errorMessage = 'An error occurred',
  skipInitialCall = false,
}: UseApiCallOptions<T>): UseApiCallReturn<T> {
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(!skipInitialCall);
  const [error, setError] = useState<Error | null>(null);
  const { showToast } = useToast();
  const abortControllerRef = useRef<AbortController | null>(null);
  const isFirstRender = useRef(true);
  const hasExecuted = useRef(false);
  const callId = useRef(Math.random().toString(36).substr(2, 9));

  const executeApiCall = useCallback(async () => {
    // Prevent duplicate calls
    if (abortControllerRef.current && !abortControllerRef.current.signal.aborted) {
      console.log(`[${callId.current}] API call already in progress, skipping duplicate`);
      return;
    }

    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    try {
      setIsLoading(true);
      setError(null);

      console.log(`[${callId.current}] Executing API call`);
      const result = await apiCall();

      // Check if request was aborted
      if (abortControllerRef.current.signal.aborted) {
        console.log(`[${callId.current}] API call was aborted`);
        return;
      }

      setData(result);
      onSuccess?.(result);
      console.log(`[${callId.current}] API call completed successfully`);
    } catch (err) {
      // Check if request was aborted
      if (abortControllerRef.current?.signal.aborted) {
        console.log(`[${callId.current}] API call was aborted during error handling`);
        return;
      }

      const error = err instanceof Error ? err : new Error(String(err));
      setError(error);
      
      // Show toast error message
      showToast(`${errorMessage}: ${error.message}`, 'error');
      
      onError?.(error);
      console.error(`[${callId.current}] API call failed:`, error);
    } finally {
      // Check if request was aborted
      if (!abortControllerRef.current?.signal.aborted) {
        setIsLoading(false);
      }
    }
  }, [apiCall, onSuccess, onError, errorMessage, showToast]);

  const refetch = useCallback(async () => {
    await executeApiCall();
  }, [executeApiCall]);

  useEffect(() => {
    // Skip initial call if requested
    if (skipInitialCall && isFirstRender.current) {
      isFirstRender.current = false;
      setIsLoading(false);
      return;
    }

    // Prevent duplicate execution
    if (hasExecuted.current) {
      console.log(`[${callId.current}] useEffect triggered but API call already executed, skipping`);
      return;
    }

    isFirstRender.current = false;
    hasExecuted.current = true;
    
    console.log(`[${callId.current}] useEffect triggering API call with dependencies:`, dependencies);
    executeApiCall();

    // Cleanup function to abort ongoing requests
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, dependencies); // eslint-disable-line react-hooks/exhaustive-deps

  // Reset execution flag when dependencies change
  useEffect(() => {
    hasExecuted.current = false;
  }, dependencies); // eslint-disable-line react-hooks/exhaustive-deps

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    data,
    isLoading,
    error,
    refetch,
  };
}