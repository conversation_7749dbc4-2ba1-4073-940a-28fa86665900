'use client';

import { useQuery } from '@tanstack/react-query';
import { ordersAPI, OrdersResponse } from '@/lib/api/medusa/orders';

export const useOrders = (
  storeHandle: string,
  token?: string,
  params?: {
    limit?: number;
    offset?: number;
    customer_id?: string;
  },
  enabled: boolean = true
) => {
  return useQuery<OrdersResponse>({
    queryKey: ['orders', storeHandle, token, params],
    queryFn: () => ordersAPI.getOrders(storeHandle, token, params),
    enabled: enabled && !!storeHandle && !!token,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

export const useOrder = (
  orderId: string,
  storeHandle: string,
  token?: string,
  enabled: boolean = true
) => {
  return useQuery({
    queryKey: ['order', orderId, storeHandle, token],
    queryFn: () => ordersAPI.getOrder(orderId, storeHandle, token),
    enabled: enabled && !!orderId && !!storeHandle && !!token,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};