// hooks/medusa/useAdminAuth.ts
"use client";

import { useMutation } from "@tanstack/react-query";
import { adminLogin, getAdminUser } from "@/lib/api/medusa/auth";
import { useAuthStore } from "@/stores/authStore";

export const useAdminLogin = () => {
  const setToken = useAuthStore((state) => state.setToken);
  const setUser = useAuthStore((state) => state.setUser);

  return useMutation({
    mutationFn: async (credentials: { email: string; password: string }) => {
      const loginRes = await adminLogin(credentials);
      const token = loginRes.token;

      setToken(token);
      localStorage.setItem("ondc_auth_token", token);

      const userRes = await getAdminUser(token);
      setUser(userRes);

      return userRes;
    },
  });
};
