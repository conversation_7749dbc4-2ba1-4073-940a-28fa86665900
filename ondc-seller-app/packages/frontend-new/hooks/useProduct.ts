import { useState, useEffect } from 'react';
import { medusaStoreProductsApi, MedusaApiConfig } from '@/lib/api/medusa/products';

interface UseProductOptions {
  enabled?: boolean;
}

interface UseProductResult {
  product: any | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

/**
 * Hook to fetch a single product from Medusa store API
 */
export const useProduct = (
  config: MedusaApiConfig | undefined,
  productId: string | undefined,
  options: UseProductOptions = {}
): UseProductResult => {
  const [product, setProduct] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { enabled = true } = options;

  const fetchProduct = async () => {
    if (!config || !config.baseUrl || !productId || !enabled) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      console.log('Fetching product with useProduct hook:', { config, productId });
      
      const fetchedProduct = await medusaStoreProductsApi.getProduct(config, productId);
      setProduct(fetchedProduct);
      
      console.log('Product fetched successfully:', fetchedProduct);
    } catch (err: any) {
      console.error('Error in useProduct hook:', err);
      setError(err.message || 'Failed to fetch product');
      setProduct(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchProduct();
  }, [config?.baseUrl, config?.publishableKey, config?.tenantId, productId, enabled]);

  const refetch = () => {
    fetchProduct();
  };

  return {
    product,
    isLoading,
    error,
    refetch,
  };
};

/**
 * Hook to fetch multiple products from Medusa store API
 */
export const useProducts = (
  config: MedusaApiConfig | undefined,
  params?: {
    limit?: number;
    offset?: number;
    category_id?: string[];
    collection_id?: string[];
    tags?: string[];
    title?: string;
    description?: string;
    handle?: string;
    is_giftcard?: boolean;
  },
  options: UseProductOptions = {}
) => {
  const [products, setProducts] = useState<any[]>([]);
  const [count, setCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { enabled = true } = options;

  const fetchProducts = async () => {
    if (!config || !config.baseUrl || !enabled) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      console.log('Fetching products with useProducts hook:', { config, params });
      
      const result = await medusaStoreProductsApi.getProducts(config, params);
      setProducts(result.products);
      setCount(result.count);
      
      console.log('Products fetched successfully:', result);
    } catch (err: any) {
      console.error('Error in useProducts hook:', err);
      setError(err.message || 'Failed to fetch products');
      setProducts([]);
      setCount(0);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, [config?.baseUrl, config?.publishableKey, config?.tenantId, JSON.stringify(params), enabled]);

  const refetch = () => {
    fetchProducts();
  };

  return {
    products,
    count,
    isLoading,
    error,
    refetch,
  };
};