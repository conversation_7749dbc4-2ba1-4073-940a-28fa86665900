'use client';

import { useCallback } from 'react';
import { useStoreAuthStore, getStoreAuthStore } from '@/stores/storeAuthStore';
import { storeAuthAPI, StoreLoginCredentials } from '@/lib/api/storeAuth';
import { useToast } from '@/app/providers/toast-provider';
import { useParams } from 'next/navigation';

export function useStoreAuth(storeHandle?: string) {
  const params = useParams();
  const handle = storeHandle || (params.storeHandle as string);
  
  console.log(`🔐 useStoreAuth called for store: ${handle}`);
  console.log('Params:', params);
  console.log('Provided storeHandle:', storeHandle);
  
  // Use store-specific auth store
  const authStore = getStoreAuthStore(handle || 'default');
  
  if (!authStore) {
    console.error('Failed to get auth store for:', handle);
    // Return fallback functions
    return {
      token: null,
      user: null,
      isAuthenticated: false,
      isLoading: false,
      loadingMessage: null,
      error: 'Failed to initialize auth store',
      login: async () => { throw new Error('Auth store not available'); },
      logout: () => {},
      clearError: () => {},
      refreshUserData: async () => { throw new Error('Auth store not available'); },
    };
  }
  
  const {
    token,
    user,
    isAuthenticated,
    isLoading,
    loadingMessage,
    error,
    hasHydrated,
    setAuth,
    setLoading,
    setError,
    clearAuth,
  } = authStore();
  
  // Log current auth state for debugging
  console.log(`📊 Auth state for store ${handle}:`, {
    token: token ? 'exists' : 'null',
    user: user ? 'exists' : 'null',
    isAuthenticated,
    hasHydrated
  });

  const { showToast } = useToast();

  const login = useCallback(async (credentials: StoreLoginCredentials, targetStoreHandle?: string) => {
    const loginStoreHandle = targetStoreHandle || handle;
    try {
      setError(null);
      
      console.log('=== STORE LOGIN PROCESS STARTED ===');
      console.log('Store handle:', loginStoreHandle);
      console.log('Email:', credentials.email);
      
      // Step 1: Login to get token
      console.log('🔐 Step 1: Authenticating user...');
      const authResponse = await storeAuthAPI.login(credentials, loginStoreHandle);
      console.log('authResponse::::::::<><><><>', authResponse)
      const token = authResponse.token;
      
      if (!token) {
        throw new Error('No access token received from login');
      }
      
      console.log('✅ Step 1: Authentication successful, token received');
      
      // Step 2: Get customer details using the token
      console.log('👤 Step 2: Fetching user details...');
      const customerResponse = await storeAuthAPI.getCustomerDetails(token, loginStoreHandle);

      const customer = customerResponse.customer;
      console.log("customer::::::",customer)
      if (!customer) {
        throw new Error('No customer details received');
      }
      
      console.log('✅ Step 2: User details fetched successfully');
      
      // Step 3: Store auth data in Zustand and localStorage (addresses come from user data)
      console.log('💾 Step 3: Storing user session...');
      console.log('📍 Addresses from user data:', customer.addresses?.length || 0);

      setAuth(token, customer);
      
      console.log('✅ Step 3: User session stored successfully');
      
      console.log('=== STORE LOGIN PROCESS COMPLETED ===');
      return customer;
      
    } catch (error) {
      console.error('Store login error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      setError(errorMessage);
      showToast(errorMessage, 'error');
      throw error;
    }
  }, [setAuth, setError, showToast, handle]);

  const logout = useCallback(() => {
    clearAuth();
    showToast('Logged out successfully', 'success');
  }, [clearAuth, showToast]);

  const clearError = useCallback(() => {
    setError(null);
  }, [setError]);

  const refreshUserData = useCallback(async (targetStoreHandle?: string) => {
    const refreshStoreHandle = targetStoreHandle || handle;
    try {
      const currentToken = token;
      if (!currentToken) {
        throw new Error('No authentication token available');
      }

      console.log('🔄 Refreshing user data...');
      
      // Fetch customer details (includes addresses)
      const customerResponse = await storeAuthAPI.getCustomerDetails(currentToken, refreshStoreHandle);
      const customer = customerResponse.customer;
      
      if (!customer) {
        throw new Error('No customer details received');
      }
      
      console.log('📍 Addresses from user data:', customer.addresses?.length || 0);
      
      // Update auth data with user (addresses included in user data)
      setAuth(currentToken, customer);
      console.log('✅ User data refreshed successfully');
      
      return customer;
    } catch (error) {
      console.error('Error refreshing user data:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to refresh user data';
      setError(errorMessage);
      throw error;
    }
  }, [token, setAuth, setError, handle]);

  return {
    token,
    user,
    isAuthenticated,
    isLoading,
    loadingMessage,
    error,
    login,
    logout,
    clearError,
    refreshUserData,
  };
}