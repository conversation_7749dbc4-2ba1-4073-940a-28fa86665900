import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getStoreDetails, updateStoreDetails } from "@/lib/api/strapi/store";

export const useStoreDetails = (storeHandle: string) =>
  useQuery({
    queryKey: ["store", storeHandle],
    queryFn: () => getStoreDetails(storeHandle),
    enabled: !!storeHandle,
  });

export const useUpdateStore = (id: number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) =>
      updateStoreDetails(id, data, localStorage.getItem("strapi_token") || ""),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["store"] });
    },
  });
};
