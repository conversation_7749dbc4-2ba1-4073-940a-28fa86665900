import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getStaticPages,
  getPageBySlug,
  createPage,
  updatePage,
  deletePage,
} from "@/lib/api/strapi/pages";

export const useStaticPages = (storeId: number) =>
  useQuery({
    queryKey: ["pages", storeId],
    queryFn: () => getStaticPages(storeId),
    enabled: !!storeId,
  });

export const usePageBySlug = (slug: string) =>
  useQuery({
    queryKey: ["page", slug],
    queryFn: () => getPageBySlug(slug),
    enabled: !!slug,
  });

export const useCreatePage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) =>
      createPage(data, localStorage.getItem("strapi_token") || ""),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ["pages"] }),
  });
};

export const useUpdatePage = (id: number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) =>
      updatePage(id, data, localStorage.getItem("strapi_token") || ""),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ["pages"] }),
  });
};

export const useDeletePage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) =>
      deletePage(id, localStorage.getItem("strapi_token") || ""),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ["pages"] }),
  });
};
