import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getBanners,
  createBanner,
  updateBanner,
  deleteBanner,
} from "@/lib/api/strapi/banners";

export const useBanners = (storeId: number) =>
  useQuery({
    queryKey: ["banners", storeId],
    queryFn: () => getBanners(storeId),
    enabled: !!storeId,
  });

export const useCreateBanner = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) =>
      createBanner(data, localStorage.getItem("strapi_token") || ""),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ["banners"] }),
  });
};

export const useUpdateBanner = (id: number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) =>
      updateBanner(id, data, localStorage.getItem("strapi_token") || ""),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ["banners"] }),
  });
};

export const useDeleteBanner = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) =>
      deleteBanner(id, localStorage.getItem("strapi_token") || ""),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ["banners"] }),
  });
};
