'use client';

import { useState, useCallback, useEffect } from 'react';
import { 
  getStoreCartAPI,
  calculateCartMetadata,
  type Cart,
  type Region,
  type AddToCartPayload
} from '@/lib/api/cart';
import { useToast } from '@/app/providers/toast-provider';

export interface AddToCartItem {
  productId: string;
  variantId: string;
  quantity: number;
  product: {
    id: string;
    title: string;
    price: number;
    image: string;
    variant: string;
  };
}

interface UseIsolatedCartReturn {
  cart: Cart | null;
  isOpen: boolean;
  isLoading: boolean;
  region: Region | null;
  addToCart: (item: AddToCartItem) => Promise<void>;
  removeItem: (lineItemId: string) => Promise<void>;
  updateQuantity: (lineItemId: string, quantity: number) => Promise<void>;
  clearCart: () => Promise<void>;
  openCart: () => void;
  closeCart: () => void;
  toggleCart: () => void;
  refreshCart: () => Promise<void>;
  initializeCart: () => Promise<void>;
  
  // Store isolation methods
  switchToStore: (newStoreHandle: string) => Promise<void>;
  getCurrentStore: () => string;
  clearStoreCart: () => Promise<void>;
}

export function useIsolatedCart(storeHandle: string): UseIsolatedCartReturn {
  const { showToast } = useToast();
  const [cart, setCart] = useState<Cart | null>(null);
  const [region, setRegion] = useState<Region | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentStoreHandle, setCurrentStoreHandle] = useState(storeHandle);

  // Get store-specific cart API
  const getCartAPI = useCallback(() => {
    return getStoreCartAPI(currentStoreHandle);
  }, [currentStoreHandle]);

  // Initialize cart - get region and existing cart for current store
  const initializeCart = useCallback(async () => {
    if (!currentStoreHandle) {
      console.warn('No store handle provided for cart initialization');
      return;
    }

    try {
      setIsLoading(true);
      console.log(`🛒 Initializing cart for store: ${currentStoreHandle}`);
      
      const cartAPI = getCartAPI();
      
      // Check if we're in mock mode or if backend is not available
      const useMockData = process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true' || 
                         process.env.NEXT_PUBLIC_FORCE_MOCK_DATA === 'true';
      
      if (useMockData) {
        console.log(`🎭 Using mock data for cart initialization in store: ${currentStoreHandle}`);
        
        // Create a mock region for development
        const mockRegion = {
          id: 'mock-asia-region',
          name: 'Asia',
          currency_code: 'INR',
          tax_rate: 0.08,
          countries: []
        };
        
        setRegion(mockRegion);
        console.log(`📍 Mock region set for store ${currentStoreHandle}:`, mockRegion.name);
        
        // Check for existing cart in localStorage
        const existingCartId = cartAPI.getStoredCartId();
        if (existingCartId) {
          console.log(`🔍 Found existing cart ID for store ${currentStoreHandle}:`, existingCartId);
          // In mock mode, we'll just use a basic cart structure
          const mockCart = {
            id: existingCartId,
            region_id: mockRegion.id,
            region: mockRegion,
            items: [],
            total: 0,
            subtotal: 0,
            tax_total: 0,
            shipping_total: 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };
          setCart(mockCart);
          console.log(`✅ Loaded mock cart for store ${currentStoreHandle}`);
        } else {
          console.log(`📝 No existing cart found for store ${currentStoreHandle}`);
        }
        
        return; // Exit early for mock mode
      }
      
      // Real API mode
      try {
        // Get Asia region
        const regionsResponse = await cartAPI.getRegions('Asia');
        const asiaRegion = regionsResponse.regions.find(r => 
          r.name.toLowerCase().includes('asia')
        ) || regionsResponse.regions[0]; // Fallback to first region
        
        if (asiaRegion) {
          setRegion(asiaRegion);
          console.log(`📍 Region set for store ${currentStoreHandle}:`, asiaRegion.name);
          
          // Check for existing cart for this store
          const existingCartId = cartAPI.getStoredCartId();
          if (existingCartId) {
            try {
              console.log(`🔍 Found existing cart for store ${currentStoreHandle}:`, existingCartId);
              const cartResponse = await cartAPI.getCart(existingCartId);
              setCart(cartResponse.cart);
              console.log(`✅ Loaded existing cart for store ${currentStoreHandle}:`, cartResponse.cart.items.length, 'items');
            } catch (error) {
              console.warn(`Failed to load existing cart for store ${currentStoreHandle}, will create new one:`, error);
              cartAPI.removeStoredCartId();
            }
          } else {
            console.log(`📝 No existing cart found for store ${currentStoreHandle}`);
          }
        }
      } catch (apiError) {
        console.warn(`API call failed for store ${currentStoreHandle}, falling back to mock mode:`, apiError);
        
        // Fallback to mock region if API fails
        const mockRegion = {
          id: 'fallback-asia-region',
          name: 'Asia (Offline)',
          currency_code: 'INR',
          tax_rate: 0.08,
          countries: []
        };
        
        setRegion(mockRegion);
        console.log(`📍 Fallback region set for store ${currentStoreHandle}:`, mockRegion.name);
        
        showToast(`Cart initialized in offline mode for ${currentStoreHandle}`, 'info');
      }
    } catch (error) {
      console.error(`Failed to initialize cart for store ${currentStoreHandle}:`, error);
      showToast('Failed to initialize cart', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [currentStoreHandle, getCartAPI, showToast]);

  // Switch to different store
  const switchToStore = useCallback(async (newStoreHandle: string) => {
    if (newStoreHandle === currentStoreHandle) {
      console.log(`Already on store ${newStoreHandle}, no switch needed`);
      return;
    }

    console.log(`🔄 Switching cart from store ${currentStoreHandle} to ${newStoreHandle}`);
    
    // Clear current cart state
    setCart(null);
    setRegion(null);
    setIsOpen(false);
    
    // Update store handle
    setCurrentStoreHandle(newStoreHandle);
    
    // Initialize cart for new store
    // Note: We'll let the useEffect handle this to avoid double initialization
  }, [currentStoreHandle]);

  // Initialize cart when store handle changes
  useEffect(() => {
    if (currentStoreHandle) {
      initializeCart();
    }
  }, [currentStoreHandle, initializeCart]);

  // Update current store handle when prop changes
  useEffect(() => {
    if (storeHandle !== currentStoreHandle) {
      switchToStore(storeHandle);
    }
  }, [storeHandle, currentStoreHandle, switchToStore]);

  // Create cart if it doesn't exist
  const ensureCartExists = useCallback(async (): Promise<string> => {
    if (cart?.id) {
      return cart.id;
    }

    if (!region) {
      throw new Error('Region not available');
    }

    try {
      console.log(`🆕 Creating new cart for store ${currentStoreHandle}`);
      const cartAPI = getCartAPI();
      
      // Check if we're in mock mode
      const useMockData = process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true' || 
                         process.env.NEXT_PUBLIC_FORCE_MOCK_DATA === 'true';
      
      if (useMockData || region.id.includes('mock') || region.id.includes('fallback')) {
        // Create a mock cart
        const mockCartId = `mock-cart-${currentStoreHandle}-${Date.now()}`;
        const mockCart = {
          id: mockCartId,
          region_id: region.id,
          region: region,
          items: [],
          total: 0,
          subtotal: 0,
          tax_total: 0,
          shipping_total: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        
        setCart(mockCart);
        cartAPI.setStoredCartId(mockCartId);
        console.log(`✅ Created mock cart for store ${currentStoreHandle}:`, mockCartId);
        
        return mockCartId;
      }
      
      // Real API mode
      const cartResponse = await cartAPI.createCart({
        region_id: region.id,
      });
      
      setCart(cartResponse.cart);
      console.log(`✅ Created new cart for store ${currentStoreHandle}:`, cartResponse.cart.id);
      
      return cartResponse.cart.id;
    } catch (error) {
      console.error(`Failed to create cart for store ${currentStoreHandle}:`, error);
      throw new Error('Failed to create cart');
    }
  }, [cart, region, currentStoreHandle, getCartAPI]);

  // Add item to cart
  const addToCart = useCallback(async (item: AddToCartItem) => {
    try {
      setIsLoading(true);
      console.log(`➡️ Adding item to cart for store ${currentStoreHandle}:`, item.product.title);
      
      // Ensure cart exists
      const cartId = await ensureCartExists();
      const cartAPI = getCartAPI();
      
      // Check if we're in mock mode
      const useMockData = process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true' || 
                         process.env.NEXT_PUBLIC_FORCE_MOCK_DATA === 'true';
      
      if (useMockData || cartId.includes('mock')) {
        // Mock mode - simulate adding item to cart
        const mockItem = {
          id: `mock-item-${Date.now()}`,
          title: item.product.title,
          description: `Mock item for ${item.product.title}`,
          thumbnail: item.product.image,
          variant_id: item.variantId,
          quantity: item.quantity,
          unit_price: item.product.price,
          total: item.product.price * item.quantity,
          metadata: calculateCartMetadata(
            item.product.price, 
            item.quantity,
            region?.tax_rate || 0.08
          )
        };
        
        // Update cart with new item
        const updatedCart = {
          ...cart!,
          items: [...(cart?.items || []), mockItem],
          updated_at: new Date().toISOString()
        };
        
        // Recalculate totals
        updatedCart.subtotal = updatedCart.items.reduce((sum, item) => sum + item.total, 0);
        updatedCart.tax_total = updatedCart.subtotal * (region?.tax_rate || 0.08);
        updatedCart.total = updatedCart.subtotal + updatedCart.tax_total + updatedCart.shipping_total;
        
        setCart(updatedCart);
        
        // Open cart sidebar
        setIsOpen(true);
        
        console.log(`✅ Added mock item to cart for store ${currentStoreHandle}:`, updatedCart.items.length, 'total items');
        showToast(`Product added to ${currentStoreHandle} cart successfully!`, 'success');
        
        return;
      }
      
      // Real API mode
      // Calculate metadata for the item
      const metadata = calculateCartMetadata(
        item.product.price, 
        item.quantity,
        region?.tax_rate || 0.08
      );
      
      // Prepare payload
      const payload: AddToCartPayload = {
        variant_id: item.variantId,
        quantity: item.quantity,
        metadata,
      };
      
      // Add item to cart
      const cartResponse = await cartAPI.addLineItem(cartId, payload);
      setCart(cartResponse.cart);
      
      // Open cart sidebar
      setIsOpen(true);
      
      console.log(`✅ Added item to cart for store ${currentStoreHandle}:`, cartResponse.cart.items.length, 'total items');
      showToast(`Product added to ${currentStoreHandle} cart successfully!`, 'success');
    } catch (error) {
      console.error(`Failed to add item to cart for store ${currentStoreHandle}:`, error);
      showToast('Failed to add product to cart', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [ensureCartExists, region, currentStoreHandle, getCartAPI, showToast, cart]);

  // Remove item from cart
  const removeItem = useCallback(async (lineItemId: string) => {
    if (!cart?.id) return;
    
    try {
      setIsLoading(true);
      console.log(`🗑️ Removing item from cart for store ${currentStoreHandle}:`, lineItemId);
      
      const cartAPI = getCartAPI();
      const cartResponse = await cartAPI.removeLineItem(cart.id, lineItemId);
      setCart(cartResponse.cart);
      
      console.log(`✅ Removed item from cart for store ${currentStoreHandle}:`, cartResponse.cart.items.length, 'items remaining');
      showToast('Item removed from cart', 'success');
    } catch (error) {
      console.error(`Failed to remove item from cart for store ${currentStoreHandle}:`, error);
      showToast('Failed to remove item from cart', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [cart, currentStoreHandle, getCartAPI, showToast]);

  // Update item quantity
  const updateQuantity = useCallback(async (lineItemId: string, quantity: number) => {
    if (!cart?.id) return;
    
    if (quantity <= 0) {
      await removeItem(lineItemId);
      return;
    }
    
    try {
      setIsLoading(true);
      console.log(`📝 Updating quantity for store ${currentStoreHandle}:`, lineItemId, 'to', quantity);
      
      const cartAPI = getCartAPI();
      const cartResponse = await cartAPI.updateLineItem(cart.id, lineItemId, { quantity });
      setCart(cartResponse.cart);
      
      console.log(`✅ Updated quantity for store ${currentStoreHandle}`);
    } catch (error) {
      console.error(`Failed to update quantity for store ${currentStoreHandle}:`, error);
      showToast('Failed to update quantity', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [cart, removeItem, currentStoreHandle, getCartAPI, showToast]);

  // Clear cart
  const clearCart = useCallback(async () => {
    try {
      setIsLoading(true);
      console.log(`🧹 Clearing cart for store ${currentStoreHandle}`);
      
      if (cart?.id) {
        const cartAPI = getCartAPI();
        
        // Remove all items
        for (const item of cart.items) {
          await cartAPI.removeLineItem(cart.id, item.id);
        }
      }
      
      // Reset cart state
      setCart(null);
      const cartAPI = getCartAPI();
      cartAPI.removeStoredCartId();
      
      console.log(`✅ Cleared cart for store ${currentStoreHandle}`);
      showToast(`${currentStoreHandle} cart cleared`, 'success');
    } catch (error) {
      console.error(`Failed to clear cart for store ${currentStoreHandle}:`, error);
      showToast('Failed to clear cart', 'error');
    } finally {
      setIsLoading(false);
    }
  }, [cart, currentStoreHandle, getCartAPI, showToast]);

  // Clear store-specific cart data
  const clearStoreCart = useCallback(async () => {
    try {
      console.log(`🧹 Clearing all cart data for store ${currentStoreHandle}`);
      
      const cartAPI = getCartAPI();
      cartAPI.clearAllCartData();
      
      // Reset state
      setCart(null);
      setRegion(null);
      setIsOpen(false);
      
      console.log(`✅ Cleared all cart data for store ${currentStoreHandle}`);
      showToast(`All ${currentStoreHandle} cart data cleared`, 'success');
    } catch (error) {
      console.error(`Failed to clear store cart data for ${currentStoreHandle}:`, error);
      showToast('Failed to clear cart data', 'error');
    }
  }, [currentStoreHandle, getCartAPI, showToast]);

  // Refresh cart from API
  const refreshCart = useCallback(async () => {
    const cartAPI = getCartAPI();
    const cartId = cartAPI.getStoredCartId();
    if (!cartId) return;
    
    try {
      setIsLoading(true);
      console.log(`🔄 Refreshing cart for store ${currentStoreHandle}:`, cartId);
      
      const cartResponse = await cartAPI.getCart(cartId);
      setCart(cartResponse.cart);
      
      console.log(`✅ Refreshed cart for store ${currentStoreHandle}:`, cartResponse.cart.items.length, 'items');
    } catch (error) {
      console.error(`Failed to refresh cart for store ${currentStoreHandle}:`, error);
      // If cart doesn't exist, remove from storage
      cartAPI.removeStoredCartId();
      setCart(null);
    } finally {
      setIsLoading(false);
    }
  }, [currentStoreHandle, getCartAPI]);

  // Cart visibility controls
  const openCart = useCallback(() => setIsOpen(true), []);
  const closeCart = useCallback(() => setIsOpen(false), []);
  const toggleCart = useCallback(() => setIsOpen(prev => !prev), []);

  // Get current store
  const getCurrentStore = useCallback(() => currentStoreHandle, [currentStoreHandle]);

  // Close cart on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        closeCart();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeCart]);

  // Prevent body scroll when cart is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return {
    cart,
    isOpen,
    isLoading,
    region,
    addToCart,
    removeItem,
    updateQuantity,
    clearCart,
    openCart,
    closeCart,
    toggleCart,
    refreshCart,
    initializeCart,
    
    // Store isolation methods
    switchToStore,
    getCurrentStore,
    clearStoreCart,
  };
}