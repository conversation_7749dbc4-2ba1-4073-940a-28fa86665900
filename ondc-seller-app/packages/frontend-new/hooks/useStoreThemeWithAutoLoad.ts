/**
 * Store Theme Hook with Auto-Loading
 * Combines useStoreTheme with useAutoThemeLoader for seamless theme management
 */

import { useStoreTheme, UseStoreThemeReturn } from './useStoreTheme';
import { useAutoThemeLoader, UseAutoThemeLoaderReturn } from './useAutoThemeLoader';

export interface UseStoreThemeWithAutoLoadReturn extends UseStoreThemeReturn {
  // Auto-loading specific properties
  autoLoader: {
    isLoading: boolean;
    hasAutoApplied: boolean;
    logoSource: 'zustand' | 'localStorage' | 'strapi' | null;
    error: string | null;
    retryAutoLoad: () => Promise<void>;
  };
  
  // Combined loading state
  isAnyLoading: boolean;
  
  // Combined error state
  combinedError: string | null;
}

/**
 * Enhanced store theme hook with automatic theme loading
 * This hook will automatically detect and apply themes from existing store logos
 */
export const useStoreThemeWithAutoLoad = (storeHandle: string): UseStoreThemeWithAutoLoadReturn => {
  // Get base theme functionality
  const storeTheme = useStoreTheme(storeHandle);
  
  // Get auto-loading functionality
  const autoLoader = useAutoThemeLoader(storeHandle);
  
  // Combined loading state
  const isAnyLoading = storeTheme.isGenerating || autoLoader.isLoading;
  
  // Combined error state
  const combinedError = storeTheme.error || autoLoader.error;
  
  return {
    // All original useStoreTheme functionality
    ...storeTheme,
    
    // Auto-loader functionality
    autoLoader,
    
    // Combined states
    isAnyLoading,
    combinedError,
  };
};