'use client';

import { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';

export const useAdminNavigation = () => {
  const [isNavigating, setIsNavigating] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Reset loading state when pathname changes
    console.log('Pathname changed to:', pathname);
    setIsNavigating(false);
    
    // Delay the navigation end event to ensure components have loaded
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('admin-navigation-end'));
    }, 100);
  }, [pathname]);

  const navigateWithLoading = (href: string, message?: string) => {
    setIsNavigating(true);
    
    // Trigger global loading event
    window.dispatchEvent(new CustomEvent('admin-navigation-start'));
    
    router.push(href);
    
    // Fallback to reset loading state after a timeout
    setTimeout(() => {
      setIsNavigating(false);
      window.dispatchEvent(new CustomEvent('admin-navigation-end'));
    }, 3000);
  };

  return {
    isNavigating,
    navigateWithLoading,
    setIsNavigating,
  };
};