'use client';

import { useEffect, useState } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';

export function useNavigationLoader() {
  const [isLoading, setIsLoading] = useState(false);
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleStart = () => {
      // Add a small delay to prevent flash for very fast navigations
      timeoutId = setTimeout(() => {
        setIsLoading(true);
      }, 150);
    };

    const handleComplete = () => {
      clearTimeout(timeoutId);
      setIsLoading(false);
    };

    // Listen for link clicks that would trigger navigation
    const handleLinkClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const link = target.closest('a');
      
      if (link && link.href && !link.href.startsWith('mailto:') && !link.href.startsWith('tel:')) {
        const url = new URL(link.href);
        const currentUrl = new URL(window.location.href);
        
        // Only show loader for different pages
        if (url.pathname !== currentUrl.pathname || url.search !== currentUrl.search) {
          handleStart();
        }
      }
    };

    // Listen for form submissions that might navigate
    const handleFormSubmit = (e: SubmitEvent) => {
      const form = e.target as HTMLFormElement;
      if (form.method === 'get' || form.action) {
        handleStart();
      }
    };

    // Listen for browser navigation
    const handlePopState = () => {
      handleStart();
      // Use a timeout for browser navigation since we can't hook into the promise
      setTimeout(handleComplete, 1000);
    };

    document.addEventListener('click', handleLinkClick);
    document.addEventListener('submit', handleFormSubmit);
    window.addEventListener('popstate', handlePopState);

    // Cleanup on route change
    handleComplete();

    return () => {
      clearTimeout(timeoutId);
      document.removeEventListener('click', handleLinkClick);
      document.removeEventListener('submit', handleFormSubmit);
      window.removeEventListener('popstate', handlePopState);
    };
  }, [pathname, searchParams]);

  return isLoading;
}