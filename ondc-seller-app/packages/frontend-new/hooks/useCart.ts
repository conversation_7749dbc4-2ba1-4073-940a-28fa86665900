'use client';

import { useState, useCallback, useEffect } from 'react';
import { Cart, CartItem, AddToCartItem } from '@/types/cart';
import { calculateCartTotals } from '@/lib/cart/mockCartData';

interface UseCartReturn {
  cart: Cart;
  isOpen: boolean;
  isLoading: boolean;
  addItem: (item: Omit<CartItem, 'id'>) => void;
  addToCart: (item: AddToCartItem) => Promise<void>;
  removeItem: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  openCart: () => void;
  closeCart: () => void;
  toggleCart: () => void;
}

export function useCart(): UseCartReturn {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [cartItems, setCartItems] = useState<CartItem[]>([]);

  // Calculate cart totals
  const totals = calculateCartTotals(cartItems);

  const cart: Cart = {
    id: 'demo-cart-1',
    items: cartItems,
    totalItems: totals.totalItems,
    totalAmount: totals.totalAmount,
    currency: 'USD',
    updatedAt: new Date().toISOString(),
  };

  // Add item to cart (legacy function)
  const addItem = useCallback((newItem: Omit<CartItem, 'id'>) => {
    setIsLoading(true);
    
    setTimeout(() => {
      setCartItems(prevItems => {
        const existingItemIndex = prevItems.findIndex(
          item => item.productId === newItem.productId && 
                  item.variant?.id === newItem.variant?.id
        );

        if (existingItemIndex >= 0) {
          // Update existing item quantity
          const updatedItems = [...prevItems];
          const existingItem = updatedItems[existingItemIndex];
          const newQuantity = existingItem.quantity + newItem.quantity;
          const maxQuantity = existingItem.maxQuantity || 99;
          
          updatedItems[existingItemIndex] = {
            ...existingItem,
            quantity: Math.min(newQuantity, maxQuantity)
          };
          
          return updatedItems;
        } else {
          // Add new item
          const cartItem: CartItem = {
            ...newItem,
            id: `cart-item-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          };
          return [...prevItems, cartItem];
        }
      });
      
      setIsLoading(false);
      setIsOpen(true); // Open cart when item is added
    }, 300); // Simulate API delay
  }, []);

  // Add to cart function (new interface)
  const addToCart = useCallback(async (item: AddToCartItem) => {
    setIsLoading(true);
    
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        const cartItem: Omit<CartItem, 'id'> = {
          productId: item.productId,
          title: item.product.title,
          image: item.product.image,
          price: item.product.price,
          quantity: item.quantity,
          variant: {
            id: item.variantId,
            title: item.product.variant,
            options: {}, // This would be populated from the actual variant data
          },
          maxQuantity: 99,
        };
        
        setCartItems(prevItems => {
          const existingItemIndex = prevItems.findIndex(
            cartItem => cartItem.productId === item.productId && 
                       cartItem.variant?.id === item.variantId
          );

          if (existingItemIndex >= 0) {
            // Update existing item quantity
            const updatedItems = [...prevItems];
            const existingItem = updatedItems[existingItemIndex];
            const newQuantity = existingItem.quantity + item.quantity;
            const maxQuantity = existingItem.maxQuantity || 99;
            
            updatedItems[existingItemIndex] = {
              ...existingItem,
              quantity: Math.min(newQuantity, maxQuantity)
            };
            
            return updatedItems;
          } else {
            // Add new item
            const newCartItem: CartItem = {
              ...cartItem,
              id: `cart-item-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            };
            return [...prevItems, newCartItem];
          }
        });
        
        setIsLoading(false);
        setIsOpen(true); // Open cart when item is added
        resolve();
      }, 300); // Simulate API delay
    });
  }, []);

  // Remove item from cart
  const removeItem = useCallback((itemId: string) => {
    setIsLoading(true);
    
    setTimeout(() => {
      setCartItems(prevItems => prevItems.filter(item => item.id !== itemId));
      setIsLoading(false);
    }, 200);
  }, []);

  // Update item quantity
  const updateQuantity = useCallback((itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeItem(itemId);
      return;
    }

    setCartItems(prevItems => 
      prevItems.map(item => {
        if (item.id === itemId) {
          const maxQuantity = item.maxQuantity || 99;
          return {
            ...item,
            quantity: Math.min(quantity, maxQuantity)
          };
        }
        return item;
      })
    );
  }, [removeItem]);

  // Clear cart
  const clearCart = useCallback(() => {
    setIsLoading(true);
    
    setTimeout(() => {
      setCartItems([]);
      setIsLoading(false);
    }, 300);
  }, []);

  // Cart visibility controls
  const openCart = useCallback(() => setIsOpen(true), []);
  const closeCart = useCallback(() => setIsOpen(false), []);
  const toggleCart = useCallback(() => setIsOpen(prev => !prev), []);

  // Close cart on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        closeCart();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeCart]);

  // Prevent body scroll when cart is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return {
    cart,
    isOpen,
    isLoading,
    addItem,
    addToCart,
    removeItem,
    updateQuantity,
    clearCart,
    openCart,
    closeCart,
    toggleCart,
  };
}