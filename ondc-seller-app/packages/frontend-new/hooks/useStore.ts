import { useQuery } from "@tanstack/react-query";
import {
  // fetchStoreByHandle,
  fetchStoreProducts,
  fetchStoreCategories,
  fetchFeaturedProducts,
  fetchProductsByCategory,
  fetchProductsBySubcategory,
  fetchStoreSingleProduct,
} from "@/lib/api/store";
import { demoStore, demoProducts, demoCategories } from "@/lib/demo-data";

// export const useStore = (handle: string) => {
//   return useQuery({
//     queryKey: ["store", handle],
//     queryFn: () => fetchStoreByHandle(handle),
//     enabled: !!handle,
//     staleTime: 5 * 60 * 1000, // 5 minutes
//     cacheTime: 10 * 60 * 1000, // 10 minutes
//     retry: 1, // Only retry once before falling back to mock data
//     retryOnMount: false,
//     refetchOnWindowFocus: false,
//   });
// };

export const useStoreProducts = (
  storeHandle: string,
  limit: number = 12,
  offset: number = 0
) => {
  return useQuery({
    queryKey: ["store-products", storeHandle, limit, offset],
    queryFn: () => fetchStoreProducts(storeHandle, limit, offset),
    enabled: !!storeHandle,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 1,
    retryOnMount: false,
    refetchOnWindowFocus: false,
  });
};

export const useStoreCategories = (storeHandle: string) => {
  return useQuery({
    queryKey: ["store-categories", storeHandle],
    queryFn: () => {
      console.log('🎯 React Query: Calling fetchStoreCategories for:', storeHandle);
      return fetchStoreCategories(storeHandle);
    },
    enabled: !!storeHandle,
    staleTime: 0, // Always fetch fresh data for debugging
    cacheTime: 0, // Don't cache for debugging
    retry: 1,
    retryOnMount: true, // Force refetch on mount
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      console.log('🎯 React Query: Categories fetch successful:', data);
    },
    onError: (error) => {
      console.error('🎯 React Query: Categories fetch error:', error);
    },
  });
};

export const useFeaturedProducts = (storeHandle: string, limit: number = 8) => {
  return useQuery({
    queryKey: ["featured-products", storeHandle, limit],
    queryFn: () => fetchFeaturedProducts(storeHandle, limit),
    enabled: !!storeHandle,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
    retryOnMount: false,
    refetchOnWindowFocus: false,
  });
};

export const useProductsByCategory = (
  storeHandle: string,
  categoryId: string,
  limit: number = 12
) => {
  return useQuery({
    queryKey: ["products-by-category", storeHandle, categoryId, limit],
    queryFn: () => {
      console.log('🎯 React Query: Calling fetchProductsByCategory for:', { storeHandle, categoryId, limit });
      return fetchProductsByCategory(storeHandle, categoryId, limit);
    },
    enabled: !!storeHandle && !!categoryId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 1,
    retryOnMount: false,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      console.log('🎯 React Query: Products by category fetch successful:', data?.length, 'products');
    },
    onError: (error) => {
      console.error('🎯 React Query: Products by category fetch error:', error);
    },
  });
};

export const useProductsBySubcategory = (
  storeHandle: string,
  subcategoryId: string,
  limit: number = 12
) => {
  return useQuery({
    queryKey: ["products-by-subcategory", storeHandle, subcategoryId, limit],
    queryFn: () => {
      console.log('🎯 React Query: Calling fetchProductsBySubcategory for:', { storeHandle, subcategoryId, limit });
      return fetchProductsBySubcategory(storeHandle, subcategoryId, limit);
    },
    enabled: !!storeHandle && !!subcategoryId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 1,
    retryOnMount: false,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      console.log('🎯 React Query: Products by subcategory fetch successful:', data?.length, 'products');
    },
    onError: (error) => {
      console.error('🎯 React Query: Products by subcategory fetch error:', error);
    },
  });
};

export const useStoreSingleProduct = (
  storeHandle: string,
  productId: string
) => {
  return useQuery({
    queryKey: ["store-single-product", storeHandle,productId],
    queryFn: () => fetchStoreSingleProduct(storeHandle,productId),
    enabled: !!storeHandle,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 1,
    retryOnMount: false,
  })
};