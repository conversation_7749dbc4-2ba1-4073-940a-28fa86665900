import { useEffect, useRef } from 'react';
import { useParams } from 'next/navigation';
import { useStoreConfigStore, getStoreConfigStore } from '@/stores/storeConfigStore';

/**
 * Hook to automatically fetch and manage store configuration
 * Should be used in the main store layout or pages
 */
export const useStoreConfig = (
  storeHandle?: string,
  options?: {
    autoFetch?: boolean;
    forceRefresh?: boolean;
  }
) => {
  const params = useParams();
  const handle = storeHandle || (params.storeHandle as string);
  const fetchingRef = useRef<string | null>(null); // Track which store handle is being fetched
  
  // Use store-specific config store
  const storeConfigStore = getStoreConfigStore(handle || 'default');
  
  if (!storeConfigStore) {
    console.error('Failed to get store config store for:', handle);
    // Return fallback values
    return {
      storeData: null,
      isLoading: false,
      error: 'Failed to initialize store config',
      storeHandle: handle,
      fetchStoreConfig: () => Promise.resolve(),
      updateStoreConfig: () => {},
      clearStoreData: () => {},
      setError: () => {},
      storeName: 'Store',
      storeDescription: '',
      storeAddress: null,
      storeContact: null,
      storeLogo: null,
      storeTheme: null,
      storeSettings: null,
      socialMedia: null,
      businessHours: null,
      storeColorPalette: null,
      rawStoreResponse: null,
      currentStoreHandle: null,
    };
  }
  
  const {
    storeData,
    isLoading,
    error,
    currentStoreHandle,
    fetchStoreConfig,
    updateStoreConfig,
    clearStoreData,
    setError,
    isStoreCacheValid,
  } = storeConfigStore();

  // Auto-fetch store config when store handle changes
  useEffect(() => {
    if (handle && (options?.autoFetch !== false)) {
      // Check if store handle has changed or if we need to force refresh
      const storeHandleChanged = currentStoreHandle !== handle;
      const needsRefresh = options?.forceRefresh || !isStoreCacheValid(handle);
      const notCurrentlyFetching = fetchingRef.current !== handle;
      
      const shouldFetch = (storeHandleChanged || needsRefresh) && !isLoading && notCurrentlyFetching;
      
      if (shouldFetch) {
        console.log('useStoreConfig: Fetching store config for', handle);
        
        fetchingRef.current = handle;
        
        fetchStoreConfig(handle, options?.forceRefresh)
          .then(() => {
            fetchingRef.current = null;
            console.log('Store config fetch completed for:', handle);
          })
          .catch((error) => {
            console.error('Failed to fetch store config:', error);
            fetchingRef.current = null;
          });
      }
    }
  }, [handle, currentStoreHandle, options?.autoFetch, options?.forceRefresh, isLoading]);

  // Cleanup when store handle changes
  useEffect(() => {
    return () => {
      if (storeData && storeData.handle !== handle) {
        // Clear data if switching to a different store
        clearStoreData();
      }
    };
  }, [handle, storeData, clearStoreData]);

  return {
    storeData,
    isLoading,
    error,
    storeHandle: handle,
    
    // Actions
    fetchStoreConfig: (forceRefresh?: boolean) => 
      fetchStoreConfig(handle, forceRefresh),
    updateStoreConfig,
    clearStoreData,
    setError,
    
    // Convenience getters
    storeName: storeData?.name || 'Store',
    storeDescription: storeData?.description || '',
    storeAddress: storeData?.address,
    storeContact: storeData?.contact,
    storeLogo: storeData?.logo,
    storeTheme: storeData?.theme,
    storeSettings: storeData?.settings,
    socialMedia: storeData?.social_media,
    businessHours: storeData?.business_hours,
    
    // Enhanced getters for dynamic theming
    storeColorPalette: storeData?.store_color_palette || storeData?.theme,
    rawStoreResponse: storeData?._rawResponse,
    currentStoreHandle,
  };
};