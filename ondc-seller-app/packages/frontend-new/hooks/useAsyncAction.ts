'use client';

import { useState, useCallback } from 'react';
import { useGlobalLoading } from '../components/loading/GlobalLoadingProvider';

interface UseAsyncActionOptions {
  loadingMessage?: string;
  loadingSubMessage?: string;
  loadingType?: 'button' | 'page' | 'navigation' | 'backdrop';
  actionId?: string;
  showGlobalLoader?: boolean;
  onSuccess?: (result: any) => void;
  onError?: (error: Error) => void;
  minLoadingTime?: number; // Minimum time to show loading (in ms)
}

interface UseAsyncActionReturn<T> {
  execute: (...args: any[]) => Promise<T | undefined>;
  isLoading: boolean;
  error: Error | null;
  result: T | null;
  reset: () => void;
}

export function useAsyncAction<T = any>(
  asyncFunction: (...args: any[]) => Promise<T>,
  options: UseAsyncActionOptions = {}
): UseAsyncActionReturn<T> {
  const {
    loadingMessage = 'Processing...',
    loadingSubMessage = 'Please wait',
    loadingType = 'backdrop',
    actionId,
    showGlobalLoader = true,
    onSuccess,
    onError,
    minLoadingTime = 0,
  } = options;

  const { startLoading, stopLoading } = useGlobalLoading();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [result, setResult] = useState<T | null>(null);

  const execute = useCallback(async (...args: any[]): Promise<T | undefined> => {
    if (isLoading) return;

    const currentActionId = actionId || `async-action-${Date.now()}`;
    const startTime = Date.now();

    try {
      setIsLoading(true);
      setError(null);
      setResult(null);

      // Start global loading if enabled
      if (showGlobalLoader) {
        startLoading(loadingType, loadingMessage, {
          subMessage: loadingSubMessage,
          actionId: currentActionId,
        });
      }

      // Execute the async function
      const actionResult = await asyncFunction(...args);

      // Ensure minimum loading time if specified
      if (minLoadingTime > 0) {
        const elapsed = Date.now() - startTime;
        const remainingTime = Math.max(0, minLoadingTime - elapsed);
        if (remainingTime > 0) {
          await new Promise(resolve => setTimeout(resolve, remainingTime));
        }
      }

      setResult(actionResult);
      
      if (onSuccess) {
        onSuccess(actionResult);
      }

      return actionResult;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('An error occurred');
      setError(error);
      
      if (onError) {
        onError(error);
      } else {
        console.error('Async action error:', error);
      }
    } finally {
      setIsLoading(false);
      
      // Stop global loading if enabled
      if (showGlobalLoader) {
        stopLoading(currentActionId);
      }
    }
  }, [
    asyncFunction,
    isLoading,
    actionId,
    showGlobalLoader,
    loadingType,
    loadingMessage,
    loadingSubMessage,
    onSuccess,
    onError,
    minLoadingTime,
    startLoading,
    stopLoading,
  ]);

  const reset = useCallback(() => {
    setIsLoading(false);
    setError(null);
    setResult(null);
  }, []);

  return {
    execute,
    isLoading,
    error,
    result,
    reset,
  };
}