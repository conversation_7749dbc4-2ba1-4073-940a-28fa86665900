import { PaymentMethod } from '@/types/checkout';

export const mockPaymentMethods: PaymentMethod[] = [
  {
    id: 'credit_card',
    type: 'credit_card',
    name: 'Credit or Debit Card',
    description: 'Pay securely with your credit or debit card',
    icon: '💳',
    isAvailable: true,
  },
  {
    id: 'paypal',
    type: 'paypal',
    name: 'PayPal',
    description: 'Pay with your PayPal account',
    icon: '🅿️',
    isAvailable: true,
  },
  {
    id: 'apple_pay',
    type: 'apple_pay',
    name: 'Apple Pay',
    description: 'Pay with Touch ID or Face ID',
    icon: '🍎',
    isAvailable: true,
  },
  {
    id: 'google_pay',
    type: 'google_pay',
    name: 'Google Pay',
    description: 'Pay with your Google account',
    icon: '🔵',
    isAvailable: true,
  },
  {
    id: 'bank_transfer',
    type: 'bank_transfer',
    name: 'Bank Transfer',
    description: 'Direct bank transfer (2-3 business days)',
    icon: '🏦',
    isAvailable: false, // Disabled for demo
  },
];

export const getAvailablePaymentMethods = (): PaymentMethod[] => {
  return mockPaymentMethods.filter(method => method.isAvailable);
};