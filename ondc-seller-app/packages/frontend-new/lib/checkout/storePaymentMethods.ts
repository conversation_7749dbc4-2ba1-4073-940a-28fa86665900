import { PaymentMethod } from '@/types/checkout';

// Default payment methods as fallback
const defaultPaymentMethods: PaymentMethod[] = [
  {
    id: 'credit_card',
    type: 'credit_card',
    name: 'Credit or Debit Card',
    description: 'Pay securely with your credit or debit card',
    icon: '💳',
    isAvailable: true,
  },
  {
    id: 'upi',
    type: 'upi',
    name: 'UPI',
    description: 'Pay using UPI apps like PhonePe, Google Pay, Paytm',
    icon: '📱',
    isAvailable: true,
  },
  {
    id: 'net_banking',
    type: 'net_banking',
    name: 'Net Banking',
    description: 'Pay directly from your bank account',
    icon: '🏦',
    isAvailable: true,
  },
];

// Payment method mapping from store config to PaymentMethod format
const paymentMethodMapping: Record<string, PaymentMethod> = {
  credit_card: {
    id: 'credit_card',
    type: 'credit_card',
    name: 'Credit or Debit Card',
    description: 'Pay securely with your credit or debit card',
    icon: '💳',
    isAvailable: true,
  },
  debit_card: {
    id: 'debit_card',
    type: 'credit_card', // Same type as credit card for processing
    name: 'Debit Card',
    description: 'Pay securely with your debit card',
    icon: '💳',
    isAvailable: true,
  },
  upi: {
    id: 'upi',
    type: 'upi',
    name: 'UPI',
    description: 'Pay using UPI apps like PhonePe, Google Pay, Paytm',
    icon: '📱',
    isAvailable: true,
  },
  net_banking: {
    id: 'net_banking',
    type: 'net_banking',
    name: 'Net Banking',
    description: 'Pay directly from your bank account',
    icon: '🏦',
    isAvailable: true,
  },
  wallet: {
    id: 'wallet',
    type: 'wallet',
    name: 'Digital Wallet',
    description: 'Pay using digital wallets like Paytm, PhonePe',
    icon: '👛',
    isAvailable: true,
  },
  bnpl: {
    id: 'bnpl',
    type: 'bnpl',
    name: 'Buy Now Pay Later',
    description: 'Pay in installments with BNPL services',
    icon: '📅',
    isAvailable: true,
  },
  cash_on_delivery: {
    id: 'cash_on_delivery',
    type: 'cash_on_delivery',
    name: 'Cash on Delivery',
    description: 'Pay when your order is delivered',
    icon: '💵',
    isAvailable: true,
  },
};

/**
 * Get available payment methods from store configuration
 * Falls back to default methods if store config is not available or empty
 */
export const getStorePaymentMethods = (storeData: any): PaymentMethod[] => {
  console.log('🔍 Getting store payment methods from store data:', storeData);
  
  // Try to get payment methods from _rawResponse first
  let paymentMethods = null;
  
  if (storeData?._rawResponse?.data?.[0]?.payment_methods) {
    paymentMethods = storeData._rawResponse.data[0].payment_methods;
    console.log('📦 Found payment methods in _rawResponse:', paymentMethods);
  } else if (storeData?.payment_methods) {
    paymentMethods = storeData.payment_methods;
    console.log('📦 Found payment methods in storeData:', paymentMethods);
  }
  
  // If no payment methods found or all are disabled, return default methods
  if (!paymentMethods) {
    console.log('⚠️ No payment methods found in store config, using defaults');
    return defaultPaymentMethods;
  }
  
  // Convert store config payment methods to PaymentMethod format
  const availableMethods: PaymentMethod[] = [];
  
  Object.entries(paymentMethods).forEach(([key, isEnabled]) => {
    if (isEnabled && paymentMethodMapping[key]) {
      availableMethods.push(paymentMethodMapping[key]);
      console.log(`✅ Added payment method: ${key}`);
    } else {
      console.log(`❌ Skipped payment method: ${key} (enabled: ${isEnabled})`);
    }
  });
  
  // If no methods are enabled, return default methods
  if (availableMethods.length === 0) {
    console.log('⚠️ No payment methods enabled in store config, using defaults');
    return defaultPaymentMethods;
  }
  
  console.log('🎯 Final available payment methods:', availableMethods.map(m => m.name));
  return availableMethods;
};

/**
 * Get default payment methods (fallback)
 */
export const getDefaultPaymentMethods = (): PaymentMethod[] => {
  return defaultPaymentMethods;
};