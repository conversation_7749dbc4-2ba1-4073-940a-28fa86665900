/**
 * Centralized API Configuration
 * This file manages all API base URLs and validates environment variables
 */

export interface ApiConfig {
  medusaBaseUrl: string;
  medusaStoreApiUrl: string;
  medusaAdminApiUrl: string;
  strapiBaseUrl: string;
  strapiApiUrl: string;
  medusaPublishableKey: string;
  strapiAccessToken?: string;
}

/**
 * Validates and returns API configuration from environment variables
 * Throws error if required environment variables are missing
 */
export const getApiConfig = (): ApiConfig => {
  const medusaBaseUrl = process.env.NEXT_PUBLIC_MEDUSA_BASE_URL;
  const medusaStoreApiUrl = process.env.NEXT_PUBLIC_MEDUSA_STORE_API_URL;
  const medusaAdminApiUrl = process.env.NEXT_PUBLIC_MEDUSA_API_URL;
  const strapiBaseUrl = process.env.NEXT_PUBLIC_STRAPI_BASE_URL;
  const strapiApiUrl = process.env.NEXT_PUBLIC_API_URL;
  const medusaPublishableKey = process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY;
  const strapiAccessToken = process.env.NEXT_PUBLIC_STRAPI_ACCESS_TOKEN;

  // Validate required environment variables
  const missingVars: string[] = [];
  
  if (!medusaBaseUrl) missingVars.push('NEXT_PUBLIC_MEDUSA_BASE_URL');
  if (!strapiBaseUrl) missingVars.push('NEXT_PUBLIC_STRAPI_BASE_URL');
  if (!medusaPublishableKey) missingVars.push('NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY');

  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingVars.join(', ')}. ` +
      'Please check your .env file and ensure all required API URLs are configured.'
    );
  }

  return {
    medusaBaseUrl: medusaBaseUrl!,
    medusaStoreApiUrl: medusaStoreApiUrl || medusaBaseUrl! + '/store',
    medusaAdminApiUrl: medusaAdminApiUrl || medusaBaseUrl! + '/admin',
    strapiBaseUrl: strapiBaseUrl!,
    strapiApiUrl: strapiApiUrl || strapiBaseUrl! + '/api',
    medusaPublishableKey: medusaPublishableKey!,
    strapiAccessToken,
  };
};

/**
 * Safe getter that returns default values if environment variables are missing
 * Use this for non-critical operations where you want to handle missing config gracefully
 */
export const getApiConfigSafe = (): Partial<ApiConfig> => {
  try {
    return getApiConfig();
  } catch (error) {
    console.warn('API configuration incomplete:', error);
    return {
      medusaBaseUrl: process.env.NEXT_PUBLIC_MEDUSA_BASE_URL,
      strapiBaseUrl: process.env.NEXT_PUBLIC_STRAPI_BASE_URL,
      medusaPublishableKey: process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY,
    };
  }
};

/**
 * Get Medusa base URL with validation
 */
export const getMedusaBaseUrl = (): string => {
  const config = getApiConfig();
  return config.medusaBaseUrl;
};

/**
 * Get Strapi base URL with validation
 */
export const getStrapiBaseUrl = (): string => {
  const config = getApiConfig();
  return config.strapiBaseUrl;
};

/**
 * Get Medusa publishable key with validation
 */
export const getMedusaPublishableKey = (): string => {
  const config = getApiConfig();
  return config.medusaPublishableKey;
};