import { StoreTheme, StoreThemeConfig } from './types';
import { generateThemeColors, generateComponentTheme } from './themeGenerator';

// Mock theme based on your provided colors
export const mockStoreTheme: StoreTheme = {
  Vibrant: "#dc0467",
  Muted: "#8f5095",
  DarkVibrant: "#d40464",
  DarkMuted: "#960246",
  LightVibrant: "#a4dac0",
  LightMuted: "#b7ddca",
};

// Additional mock themes for different store types
export const mockThemes: Record<string, StoreTheme> = {
  // Fashion Store (Pink/Purple theme)
  fashion: {
    Vibrant: "#dc0467",
    Muted: "#8f5095",
    DarkVibrant: "#d40464",
    DarkMuted: "#960246",
    LightVibrant: "#a4dac0",
    LightMuted: "#b7ddca",
  },

  // Tech Store (Blue theme)
  tech: {
    Vibrant: "#0066cc",
    Muted: "#4a90a4",
    DarkVibrant: "#004499",
    DarkMuted: "#2c5282",
    LightVibrant: "#87ceeb",
    LightMuted: "#b3d9f2",
  },

  // Nature Store (Green theme)
  nature: {
    Vibrant: "#22c55e",
    Muted: "#6b7280",
    DarkVibrant: "#16a34a",
    DarkMuted: "#374151",
    LightVibrant: "#86efac",
    LightMuted: "#d1fae5",
  },

  // Luxury Store (Gold theme)
  luxury: {
    Vibrant: "#f59e0b",
    Muted: "#92400e",
    DarkVibrant: "#d97706",
    DarkMuted: "#78350f",
    LightVibrant: "#fde68a",
    LightMuted: "#fef3c7",
  },

  // Minimalist Store (Monochrome theme)
  minimalist: {
    Vibrant: "#1f2937",
    Muted: "#6b7280",
    DarkVibrant: "#111827",
    DarkMuted: "#374151",
    LightVibrant: "#e5e7eb",
    LightMuted: "#f9fafb",
  },

  // Vibrant Store (Orange theme)
  vibrant: {
    Vibrant: "#ea580c",
    Muted: "#9a3412",
    DarkVibrant: "#c2410c",
    DarkMuted: "#7c2d12",
    LightVibrant: "#fed7aa",
    LightMuted: "#ffedd5",
  },
};

// Generate complete theme configurations
export const mockThemeConfigs: Record<string, StoreThemeConfig> = Object.entries(mockThemes).reduce(
  (configs, [key, theme]) => {
    const derivedColors = generateThemeColors(theme);
    configs[key] = {
      id: key,
      name: key.charAt(0).toUpperCase() + key.slice(1),
      colors: theme,
      derivedColors,
    };
    return configs;
  },
  {} as Record<string, StoreThemeConfig>
);

// Default theme (fashion theme)
export const defaultThemeConfig = mockThemeConfigs.fashion;

/**
 * Get theme configuration by store handle or ID
 */
export function getThemeForStore(storeHandle: string): StoreThemeConfig {
  // In a real implementation, this would fetch from an API or database
  // For now, we'll use a simple mapping based on store handle
  
  const themeMapping: Record<string, string> = {
    'fashion-store': 'fashion',
    'tech-hub': 'tech',
    'eco-store': 'nature',
    'luxury-boutique': 'luxury',
    'minimal-store': 'minimalist',
    'vibrant-shop': 'vibrant',
  };

  const themeKey = themeMapping[storeHandle] || 'fashion';
  return mockThemeConfigs[themeKey] || defaultThemeConfig;
}

/**
 * Get theme configuration by theme name
 */
export function getThemeByName(themeName: string): StoreThemeConfig {
  return mockThemeConfigs[themeName] || defaultThemeConfig;
}

/**
 * Get all available themes
 */
export function getAllThemes(): StoreThemeConfig[] {
  return Object.values(mockThemeConfigs);
}