import { StoreTheme, ThemeColors, ComponentTheme } from './types';

/**
 * Generates derived colors from the base store theme
 */
export function generateThemeColors(storeTheme: StoreTheme): ThemeColors {
  return {
    primary: storeTheme.Vibrant,
    secondary: storeTheme.Muted,
    accent: storeTheme.DarkVibrant,
    background: storeTheme.LightMuted,
    surface: '#ffffff',
    text: storeTheme.DarkMuted,
    textSecondary: storeTheme.Muted,
    border: storeTheme.LightVibrant,
    hover: storeTheme.LightVibrant,
    active: storeTheme.DarkVibrant,
  };
}

/**
 * Generates component-specific theme colors
 */
export function generateComponentTheme(colors: ThemeColors): ComponentTheme {
  return {
    header: {
      background: colors.surface,
      text: colors.text,
      border: colors.border,
      searchBackground: colors.background,
      searchBorder: colors.primary,
    },
    navigation: {
      background: colors.surface,
      text: colors.text,
      hover: colors.hover,
      active: colors.primary,
      border: colors.border,
    },
    button: {
      primary: colors.primary,
      primaryHover: colors.accent,
      secondary: colors.secondary,
      secondaryHover: colors.text,
      text: colors.surface,
    },
    card: {
      background: colors.surface,
      border: colors.border,
      shadow: `0 4px 6px -1px ${colors.primary}20`,
      hover: colors.hover,
    },
    dropdown: {
      background: colors.surface,
      border: colors.border,
      shadow: `0 25px 50px -12px ${colors.primary}40`,
      itemHover: colors.hover,
    },
  };
}

/**
 * Converts hex color to RGB values
 */
export function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

/**
 * Generates CSS custom properties for the theme
 */
export function generateCSSVariables(colors: ThemeColors, componentTheme: ComponentTheme): Record<string, string> {
  return {
    // Base colors
    '--theme-primary': colors.primary,
    '--theme-secondary': colors.secondary,
    '--theme-accent': colors.accent,
    '--theme-background': colors.background,
    '--theme-surface': colors.surface,
    '--theme-text': colors.text,
    '--theme-text-secondary': colors.textSecondary,
    '--theme-border': colors.border,
    '--theme-hover': colors.hover,
    '--theme-active': colors.active,

    // Header
    '--header-bg': componentTheme.header.background,
    '--header-text': componentTheme.header.text,
    '--header-border': componentTheme.header.border,
    '--header-search-bg': componentTheme.header.searchBackground,
    '--header-search-border': componentTheme.header.searchBorder,

    // Navigation
    '--nav-bg': componentTheme.navigation.background,
    '--nav-text': componentTheme.navigation.text,
    '--nav-hover': componentTheme.navigation.hover,
    '--nav-active': componentTheme.navigation.active,
    '--nav-border': componentTheme.navigation.border,

    // Buttons
    '--btn-primary': componentTheme.button.primary,
    '--btn-primary-hover': componentTheme.button.primaryHover,
    '--btn-secondary': componentTheme.button.secondary,
    '--btn-secondary-hover': componentTheme.button.secondaryHover,
    '--btn-text': componentTheme.button.text,

    // Cards
    '--card-bg': componentTheme.card.background,
    '--card-border': componentTheme.card.border,
    '--card-shadow': componentTheme.card.shadow,
    '--card-hover': componentTheme.card.hover,

    // Dropdowns
    '--dropdown-bg': componentTheme.dropdown.background,
    '--dropdown-border': componentTheme.dropdown.border,
    '--dropdown-shadow': componentTheme.dropdown.shadow,
    '--dropdown-item-hover': componentTheme.dropdown.itemHover,
  };
}

/**
 * Applies CSS variables to the document root
 */
export function applyCSSVariables(variables: Record<string, string>): void {
  const root = document.documentElement;
  Object.entries(variables).forEach(([property, value]) => {
    root.style.setProperty(property, value);
  });
}

/**
 * Lightens a color by a percentage
 */
export function lightenColor(color: string, percent: number): string {
  const rgb = hexToRgb(color);
  if (!rgb) return color;

  const { r, g, b } = rgb;
  const newR = Math.min(255, Math.floor(r + (255 - r) * (percent / 100)));
  const newG = Math.min(255, Math.floor(g + (255 - g) * (percent / 100)));
  const newB = Math.min(255, Math.floor(b + (255 - b) * (percent / 100)));

  return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
}

/**
 * Darkens a color by a percentage
 */
export function darkenColor(color: string, percent: number): string {
  const rgb = hexToRgb(color);
  if (!rgb) return color;

  const { r, g, b } = rgb;
  const newR = Math.max(0, Math.floor(r * (1 - percent / 100)));
  const newG = Math.max(0, Math.floor(g * (1 - percent / 100)));
  const newB = Math.max(0, Math.floor(b * (1 - percent / 100)));

  return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
}