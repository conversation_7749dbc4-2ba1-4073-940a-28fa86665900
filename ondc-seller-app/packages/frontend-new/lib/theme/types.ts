// Theme types for store customization
export interface StoreTheme {
  Vibrant: string;
  Muted: string;
  DarkVibrant: string;
  DarkMuted: string;
  LightVibrant: string;
  LightMuted: string;
}

export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  hover: string;
  active: string;
}

export interface StoreThemeConfig {
  id: string;
  name: string;
  colors: StoreTheme;
  derivedColors: ThemeColors;
}

// Theme application modes
export type ThemeMode = 'light' | 'dark' | 'auto';

// Component theme variants
export interface ComponentTheme {
  header: {
    background: string;
    text: string;
    border: string;
    searchBackground: string;
    searchBorder: string;
  };
  navigation: {
    background: string;
    text: string;
    hover: string;
    active: string;
    border: string;
  };
  button: {
    primary: string;
    primaryHover: string;
    secondary: string;
    secondaryHover: string;
    text: string;
  };
  card: {
    background: string;
    border: string;
    shadow: string;
    hover: string;
  };
  dropdown: {
    background: string;
    border: string;
    shadow: string;
    itemHover: string;
  };
}