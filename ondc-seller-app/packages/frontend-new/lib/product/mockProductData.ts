import { Product } from '@/types/product';

export const mockProducts: Product[] = [
  // Women's Clothing - Dresses
  {
    id: 'prod-1',
    title: 'Elegant Summer Dress',
    description: 'A beautiful flowing summer dress perfect for any occasion. Made with premium cotton blend fabric.',
    price: 7999,
    originalPrice: 9999,
    images: [
      '/images/products/dress-1.jpg',
      '/images/products/dress-1-alt.jpg',
    ],
    category: 'womens-clothing',
    subcategory: 'womens-dresses',
    brand: 'Fashion Forward',
    rating: 4.5,
    reviewCount: 128,
    variants: [
      {
        id: 'var-1-1',
        title: 'Small / Blue',
        stock: 15,
        sku: 'ESD-S-BLU',
        options: { size: 'Small', color: 'Blue' },
      },
      {
        id: 'var-1-2',
        title: 'Medium / Blue',
        stock: 20,
        sku: 'ESD-M-BLU',
        options: { size: 'Medium', color: 'Blue' },
      },
      {
        id: 'var-1-3',
        title: 'Large / Blue',
        stock: 12,
        sku: 'ESD-L-BLU',
        options: { size: 'Large', color: 'Blue' },
      },
      {
        id: 'var-1-4',
        title: 'Small / Red',
        stock: 8,
        sku: 'ESD-S-RED',
        options: { size: 'Small', color: 'Red' },
      },
    ],
    tags: ['summer', 'casual', 'elegant'],
    features: ['Breathable fabric', 'Machine washable', 'Wrinkle resistant'],
    specifications: {
      'Material': '60% Cotton, 40% Polyester',
      'Care': 'Machine wash cold',
      'Origin': 'Made in USA',
    },
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-20T14:20:00Z',
  },
  {
    id: 'prod-2',
    title: 'Classic Maxi Dress',
    description: 'Timeless maxi dress with elegant draping and comfortable fit.',
    price: 12999,
    images: ['/images/products/dress-2.jpg'],
    category: 'womens-clothing',
    subcategory: 'womens-dresses',
    brand: 'Elegant Styles',
    rating: 4.7,
    reviewCount: 89,
    variants: [
      {
        id: 'var-2-1',
        title: 'Small / Black',
        stock: 10,
        sku: 'CMD-S-BLK',
        options: { size: 'Small', color: 'Black' },
      },
      {
        id: 'var-2-2',
        title: 'Medium / Black',
        stock: 15,
        sku: 'CMD-M-BLK',
        options: { size: 'Medium', color: 'Black' },
      },
    ],
    tags: ['formal', 'elegant', 'evening'],
    features: ['Floor length', 'Adjustable straps', 'Lined'],
    specifications: {
      'Material': '95% Polyester, 5% Spandex',
      'Care': 'Dry clean only',
      'Origin': 'Imported',
    },
    createdAt: '2024-01-10T09:15:00Z',
    updatedAt: '2024-01-18T11:30:00Z',
  },

  // Women's Clothing - Tops
  {
    id: 'prod-3',
    title: 'Casual Cotton Blouse',
    description: 'Comfortable and stylish cotton blouse perfect for everyday wear.',
    price: 4999,
    originalPrice: 6999,
    images: ['/images/products/blouse-1.jpg'],
    category: 'womens-clothing',
    subcategory: 'womens-tops',
    brand: 'Comfort Wear',
    rating: 4.3,
    reviewCount: 156,
    variants: [
      {
        id: 'var-3-1',
        title: 'Small / White',
        stock: 25,
        sku: 'CCB-S-WHT',
        options: { size: 'Small', color: 'White' },
      },
      {
        id: 'var-3-2',
        title: 'Medium / White',
        stock: 30,
        sku: 'CCB-M-WHT',
        options: { size: 'Medium', color: 'White' },
      },
      {
        id: 'var-3-3',
        title: 'Large / White',
        stock: 18,
        sku: 'CCB-L-WHT',
        options: { size: 'Large', color: 'White' },
      },
    ],
    tags: ['casual', 'cotton', 'comfortable'],
    features: ['100% Cotton', 'Button front', 'Machine washable'],
    specifications: {
      'Material': '100% Cotton',
      'Care': 'Machine wash warm',
      'Origin': 'Made in India',
    },
    createdAt: '2024-01-12T14:45:00Z',
    updatedAt: '2024-01-22T16:10:00Z',
  },

  // Men's Clothing - Shirts
  {
    id: 'prod-4',
    title: 'Classic Dress Shirt',
    description: 'Professional dress shirt with modern fit and premium cotton fabric.',
    price: 8999,
    images: ['/images/products/shirt-1.jpg'],
    category: 'mens-clothing',
    subcategory: 'mens-shirts',
    brand: 'Professional Wear',
    rating: 4.6,
    reviewCount: 203,
    variants: [
      {
        id: 'var-4-1',
        title: 'Medium / White',
        stock: 20,
        sku: 'CDS-M-WHT',
        options: { size: 'Medium', color: 'White' },
      },
      {
        id: 'var-4-2',
        title: 'Large / White',
        stock: 25,
        sku: 'CDS-L-WHT',
        options: { size: 'Large', color: 'White' },
      },
      {
        id: 'var-4-3',
        title: 'Medium / Blue',
        stock: 15,
        sku: 'CDS-M-BLU',
        options: { size: 'Medium', color: 'Blue' },
      },
    ],
    tags: ['formal', 'professional', 'cotton'],
    features: ['Wrinkle resistant', 'Modern fit', 'Button cuffs'],
    specifications: {
      'Material': '100% Cotton',
      'Care': 'Machine wash cold',
      'Origin': 'Made in Turkey',
    },
    createdAt: '2024-01-08T11:20:00Z',
    updatedAt: '2024-01-25T13:45:00Z',
  },

  // Men's Clothing - Pants
  {
    id: 'prod-5',
    title: 'Slim Fit Chinos',
    description: 'Versatile chino pants with slim fit and comfortable stretch fabric.',
    price: 6999,
    originalPrice: 8999,
    images: ['/images/products/chinos-1.jpg'],
    category: 'mens-clothing',
    subcategory: 'mens-pants',
    brand: 'Urban Style',
    rating: 4.4,
    reviewCount: 174,
    variants: [
      {
        id: 'var-5-1',
        title: '32 / Khaki',
        stock: 12,
        sku: 'SFC-32-KHA',
        options: { size: '32', color: 'Khaki' },
      },
      {
        id: 'var-5-2',
        title: '34 / Khaki',
        stock: 18,
        sku: 'SFC-34-KHA',
        options: { size: '34', color: 'Khaki' },
      },
      {
        id: 'var-5-3',
        title: '36 / Khaki',
        stock: 14,
        sku: 'SFC-36-KHA',
        options: { size: '36', color: 'Khaki' },
      },
    ],
    tags: ['casual', 'slim-fit', 'versatile'],
    features: ['Stretch fabric', 'Slim fit', 'Multiple pockets'],
    specifications: {
      'Material': '97% Cotton, 3% Elastane',
      'Care': 'Machine wash cold',
      'Origin': 'Made in Bangladesh',
    },
    createdAt: '2024-01-05T16:30:00Z',
    updatedAt: '2024-01-19T10:15:00Z',
  },

  // Electronics - Smartphones
  {
    id: 'prod-6',
    title: 'Premium Smartphone 128GB',
    description: 'Latest smartphone with advanced camera system and long-lasting battery.',
    price: 79999,
    originalPrice: 89999,
    images: ['/images/products/phone-1.jpg'],
    category: 'electronics',
    subcategory: 'smartphones',
    brand: 'TechPro',
    rating: 4.8,
    reviewCount: 342,
    variants: [
      {
        id: 'var-6-1',
        title: '128GB / Black',
        stock: 8,
        sku: 'PS-128-BLK',
        options: { storage: '128GB', color: 'Black' },
      },
      {
        id: 'var-6-2',
        title: '128GB / White',
        stock: 5,
        sku: 'PS-128-WHT',
        options: { storage: '128GB', color: 'White' },
      },
      {
        id: 'var-6-3',
        title: '256GB / Black',
        price: 89999,
        originalPrice: 99999,
        stock: 3,
        sku: 'PS-256-BLK',
        options: { storage: '256GB', color: 'Black' },
      },
    ],
    tags: ['smartphone', 'premium', 'camera'],
    features: ['5G Ready', 'Wireless charging', 'Water resistant'],
    specifications: {
      'Display': '6.1" OLED',
      'Camera': '48MP Triple camera',
      'Battery': '4000mAh',
      'OS': 'Android 14',
    },
    createdAt: '2024-01-01T08:00:00Z',
    updatedAt: '2024-01-28T12:30:00Z',
  },

  // Electronics - Laptops
  {
    id: 'prod-7',
    title: 'Professional Laptop 16GB RAM',
    description: 'High-performance laptop perfect for work and creative tasks.',
    price: 124999,
    images: ['/images/products/laptop-1.jpg'],
    category: 'electronics',
    subcategory: 'laptops',
    brand: 'CompuTech',
    rating: 4.7,
    reviewCount: 89,
    variants: [
      {
        id: 'var-7-1',
        title: '16GB / 512GB SSD',
        stock: 6,
        sku: 'PL-16-512',
        options: { ram: '16GB', storage: '512GB SSD' },
      },
      {
        id: 'var-7-2',
        title: '32GB / 1TB SSD',
        price: 149999,
        stock: 3,
        sku: 'PL-32-1TB',
        options: { ram: '32GB', storage: '1TB SSD' },
      },
    ],
    tags: ['laptop', 'professional', 'high-performance'],
    features: ['Intel i7 processor', 'Backlit keyboard', 'Thunderbolt ports'],
    specifications: {
      'Processor': 'Intel Core i7',
      'Display': '15.6" 4K',
      'Graphics': 'Integrated Intel Iris',
      'Weight': '1.8kg',
    },
    createdAt: '2024-01-03T12:15:00Z',
    updatedAt: '2024-01-26T15:20:00Z',
  },

  // Home & Garden - Furniture
  {
    id: 'prod-8',
    title: 'Modern Office Chair',
    description: 'Ergonomic office chair with lumbar support and adjustable height.',
    price: 24999,
    originalPrice: 29999,
    images: ['/images/products/chair-1.jpg'],
    category: 'home-garden',
    subcategory: 'furniture',
    brand: 'ComfortSeating',
    rating: 4.5,
    reviewCount: 267,
    variants: [
      {
        id: 'var-8-1',
        title: 'Standard / Black',
        stock: 15,
        sku: 'MOC-STD-BLK',
        options: { type: 'Standard', color: 'Black' },
      },
      {
        id: 'var-8-2',
        title: 'Executive / Black',
        price: 34999,
        originalPrice: 39999,
        stock: 8,
        sku: 'MOC-EXE-BLK',
        options: { type: 'Executive', color: 'Black' },
      },
    ],
    tags: ['office', 'ergonomic', 'furniture'],
    features: ['Lumbar support', 'Adjustable height', '360° swivel'],
    specifications: {
      'Material': 'Mesh and plastic',
      'Weight capacity': '120kg',
      'Warranty': '2 years',
    },
    createdAt: '2024-01-07T09:45:00Z',
    updatedAt: '2024-01-24T11:10:00Z',
  },
];

// Helper functions to get products by category/subcategory
export function getAllProducts(): Product[] {
  return mockProducts;
}

export function getProductsByCategory(categorySlug: string): Product[] {
  return mockProducts.filter(product => product.category === categorySlug);
}

export function getProductsBySubcategory(subcategorySlug: string): Product[] {
  return mockProducts.filter(product => product.subcategory === subcategorySlug);
}

export function getProductById(productId: string): Product | undefined {
  return mockProducts.find(product => product.id === productId);
}

// Get unique filter options from products
export function getFilterOptions(products: Product[]) {
  const brands = [...new Set(products.map(p => p.brand).filter(Boolean))].sort();
  const colors = [...new Set(
    products.flatMap(p => 
      (p.variants || []).flatMap(v => (v?.options?.color) ? [v.options.color] : [])
    )
  )].sort();
  const sizes = [...new Set(
    products.flatMap(p => 
      (p.variants || []).flatMap(v => (v?.options?.size) ? [v.options.size] : [])
    )
  )].sort();

  return { brands, colors, sizes };
}