import { User, WishlistItem } from '@/types/auth';

export const mockUser: User = {
  id: 'user-123',
  email: '<EMAIL>',
  firstName: '<PERSON>',
  lastName: 'Doe',
  phone: '+****************',
  avatar: '/images/default-avatar.png',
  dateOfBirth: '1990-05-15',
  gender: 'male',
  preferences: {
    newsletter: true,
    smsNotifications: false,
    emailNotifications: true,
    language: 'en',
    currency: 'USD',
    theme: 'light',
  },
  addresses: [
    {
      id: 'addr-1',
      type: 'home',
      firstName: '<PERSON>',
      lastName: 'Doe',
      address: '123 Main Street',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'US',
      phone: '+****************',
      isDefault: true,
    },
    {
      id: 'addr-2',
      type: 'work',
      firstName: 'John',
      lastName: 'Doe',
      company: 'Tech Corp',
      address: '456 Business Ave',
      address2: 'Suite 200',
      city: 'New York',
      state: 'NY',
      zipCode: '10002',
      country: 'US',
      isDefault: false,
    },
  ],
  createdAt: '2023-01-15T10:30:00Z',
  updatedAt: '2024-01-15T10:30:00Z',
};

export const mockWishlistItems: WishlistItem[] = [
  {
    id: 'wish-1',
    productId: 'prod-1',
    title: 'Elegant Summer Dress',
    image: '/images/products/dress-1.jpg',
    price: 7999,
    originalPrice: 9999,
    isInStock: true,
    addedAt: '2024-01-10T10:30:00Z',
    variant: {
      id: 'var-1',
      title: 'Small / Blue',
      options: { size: 'Small', color: 'Blue' },
    },
  },
  {
    id: 'wish-2',
    productId: 'prod-2',
    title: 'Classic Denim Jacket',
    image: '/images/products/jacket-1.jpg',
    price: 12999,
    isInStock: true,
    addedAt: '2024-01-08T14:20:00Z',
    variant: {
      id: 'var-2',
      title: 'Medium / Light Blue',
      options: { size: 'Medium', color: 'Light Blue' },
    },
  },
  {
    id: 'wish-3',
    productId: 'prod-3',
    title: 'Leather Crossbody Bag',
    image: '/images/products/bag-1.jpg',
    price: 8999,
    isInStock: false,
    addedAt: '2024-01-05T09:15:00Z',
    variant: {
      id: 'var-3',
      title: 'One Size / Black',
      options: { size: 'One Size', color: 'Black' },
    },
  },
  {
    id: 'wish-4',
    productId: 'prod-4',
    title: 'Casual Cotton T-Shirt',
    image: '/images/products/tshirt-1.jpg',
    price: 2999,
    originalPrice: 3999,
    isInStock: true,
    addedAt: '2024-01-03T16:45:00Z',
    variant: {
      id: 'var-4',
      title: 'Medium / Navy',
      options: { size: 'Medium', color: 'Navy' },
    },
  },
];

// Authentication functions
export function saveUserToStorage(user: User): void {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem('auth-user', JSON.stringify(user));
      localStorage.setItem('auth-token', 'mock-jwt-token-' + user.id);
    } catch (error) {
      console.error('Error saving user to storage:', error);
    }
  }
}

export function getUserFromStorage(): User | null {
  if (typeof window !== 'undefined') {
    try {
      const user = localStorage.getItem('auth-user');
      return user ? JSON.parse(user) : null;
    } catch (error) {
      console.error('Error getting user from storage:', error);
      return null;
    }
  }
  return null;
}

export function getAuthTokenFromStorage(): string | null {
  if (typeof window !== 'undefined') {
    try {
      return localStorage.getItem('auth-token');
    } catch (error) {
      console.error('Error getting auth token from storage:', error);
      return null;
    }
  }
  return null;
}

export function clearAuthFromStorage(): void {
  if (typeof window !== 'undefined') {
    try {
      localStorage.removeItem('auth-user');
      localStorage.removeItem('auth-token');
    } catch (error) {
      console.error('Error clearing auth from storage:', error);
    }
  }
}

// Wishlist functions
export function saveWishlistToStorage(items: WishlistItem[]): void {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem('user-wishlist', JSON.stringify(items));
    } catch (error) {
      console.error('Error saving wishlist to storage:', error);
    }
  }
}

export function getWishlistFromStorage(): WishlistItem[] {
  if (typeof window !== 'undefined') {
    try {
      const wishlist = localStorage.getItem('user-wishlist');
      return wishlist ? JSON.parse(wishlist) : mockWishlistItems;
    } catch (error) {
      console.error('Error getting wishlist from storage:', error);
      return mockWishlistItems;
    }
  }
  return mockWishlistItems;
}

// Mock authentication functions
export async function mockLogin(email: string, password: string): Promise<User> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Mock validation
  if (email === '<EMAIL>' && password === 'password123') {
    const user = { ...mockUser, email };
    saveUserToStorage(user);
    return user;
  } else {
    throw new Error('Invalid email or password');
  }
}

export async function mockSignup(signupData: any): Promise<User> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  // Create new user from signup data
  const newUser: User = {
    id: 'user-' + Date.now(),
    email: signupData.email,
    firstName: signupData.firstName,
    lastName: signupData.lastName,
    preferences: {
      newsletter: signupData.subscribeNewsletter || false,
      smsNotifications: false,
      emailNotifications: true,
      language: 'en',
      currency: 'USD',
      theme: 'light',
    },
    addresses: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
  
  saveUserToStorage(newUser);
  return newUser;
}