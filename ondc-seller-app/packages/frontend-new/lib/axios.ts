// lib/axios.ts
import axios from "axios";
import { medusaClient } from './api/medusa-client';
import { handleUnauthorizedWithSmartRedirect } from './utils/authUtils';

// Create axios instance with base configuration
const baseURL = medusaClient.getBaseUrl();

const axiosInstance = axios.create({
  baseURL,
  headers: {
    "Content-Type": "application/json",
  },
});

axiosInstance.interceptors.request.use((config) => {
  if (typeof window !== "undefined") {
    const token = localStorage.getItem("ondc_auth_token");
    // Don't add Authorization header for login/signup endpoints
    const isAuthEndpoint = config.url?.includes('/auth/') || config.url?.includes('/public/');
    if (token && !isAuthEndpoint) {
      config.headers.Authorization = `Bearer ${token}`;
    }
  }
  return config;
});

// Add response interceptor for 401 handling
axiosInstance.interceptors.response.use(
  (response) => {
    // Return successful responses as-is
    return response;
  },
  (error) => {
    // Handle 401 Unauthorized responses
    if (error.response?.status === 401) {
      console.log('🚨 Main Axios instance returned 401 Unauthorized:', error.config?.url);
      
      // Extract store handle from headers if available
      const storeHandle = error.config?.headers?.['x-tenant-id'];
      
      // Clear auth and redirect
      handleUnauthorizedWithSmartRedirect(storeHandle);
    }
    
    // Re-throw the error for the calling code to handle
    return Promise.reject(error);
  }
);

export default axiosInstance;
