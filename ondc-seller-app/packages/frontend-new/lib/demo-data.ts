import { Store, Product, ProductCategory } from '@/types';

// Banner/Hero data
export interface Banner {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  image: string;
  ctaText: string;
  ctaLink: string;
  backgroundColor: string;
}

export const demoBanners: Banner[] = [
  {
    id: 'banner-1',
    title: 'Summer Collection 2024',
    subtitle: 'Up to 50% Off',
    description: 'Discover our latest summer collection with amazing discounts on trending styles.',
    image: '/images/banner-summer.jpg',
    ctaText: 'Shop Now',
    ctaLink: '/products?collection=summer-2024',
    backgroundColor: '#3B82F6'
  },
  {
    id: 'banner-2',
    title: 'New Arrivals',
    subtitle: 'Fresh Styles Weekly',
    description: 'Stay ahead of the trends with our weekly new arrivals and exclusive designs.',
    image: '/images/banner-new-arrivals.jpg',
    ctaText: 'Explore',
    ctaLink: '/products?filter=latest',
    backgroundColor: '#10B981'
  },
  {
    id: 'banner-3',
    title: 'Premium Quality',
    subtitle: 'Best Materials',
    description: 'Experience luxury with our premium quality products crafted from the finest materials.',
    image: '/images/banner-premium.jpg',
    ctaText: 'Discover',
    ctaLink: '/products?filter=premium',
    backgroundColor: '#8B5CF6'
  }
];

// Demo store data for testing
export const demoStore: Store = {
  id: 'demo-store-1',
  handle: 'demo-store',
  name: 'Demo Fashion Store',
  description: 'Your one-stop destination for trendy fashion and accessories. We offer high-quality products at affordable prices.',
  logo: '/images/demo-store-logo.png',
  banner: '/images/demo-store-banner.jpg',
  category: {
    id: 'cat-1',
    name: 'Fashion & Apparel',
    description: 'Clothing and fashion accessories',
    icon: '👗'
  },
  subcategory: {
    id: 'subcat-1',
    name: 'Women\'s Fashion',
    description: 'Women\'s clothing and accessories',
    category_id: 'cat-1'
  },
  contact_info: {
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Fashion Street, Style City, SC 12345',
    website: 'https://demofashionstore.com'
  },
  social_links: {
    facebook: 'https://facebook.com/demofashionstore',
    instagram: 'https://instagram.com/demofashionstore',
    twitter: 'https://twitter.com/demofashionstore'
  },
  settings: {
    theme_color: '#3B82F6',
    currency: 'USD',
    timezone: 'America/New_York',
    language: 'en'
  },
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-15T00:00:00Z'
};

// Demo categories with subcategories
export const demoCategories: ProductCategory[] = [
  {
    id: 'cat-1',
    name: 'Women\'s Clothing',
    description: 'Trendy women\'s fashion for every occasion',
    handle: 'womens-clothing',
    is_active: true,
    is_internal: false,
    metadata: {},
    category_children: [
      {
        id: 'subcat-1-1',
        name: 'Dresses',
        description: 'Elegant dresses for all occasions',
        handle: 'womens-dresses',
        is_active: true,
        is_internal: false,
        parent_category_id: 'cat-1',
        metadata: {}
      },
      {
        id: 'subcat-1-2',
        name: 'Tops & Blouses',
        description: 'Stylish tops and blouses',
        handle: 'womens-tops',
        is_active: true,
        is_internal: false,
        parent_category_id: 'cat-1',
        metadata: {}
      },
      {
        id: 'subcat-1-3',
        name: 'Pants & Jeans',
        description: 'Comfortable pants and jeans',
        handle: 'womens-pants',
        is_active: true,
        is_internal: false,
        parent_category_id: 'cat-1',
        metadata: {}
      },
      {
        id: 'subcat-1-4',
        name: 'Skirts',
        description: 'Fashionable skirts',
        handle: 'womens-skirts',
        is_active: true,
        is_internal: false,
        parent_category_id: 'cat-1',
        metadata: {}
      }
    ]
  },
  {
    id: 'cat-2',
    name: 'Men\'s Clothing',
    description: 'Stylish men\'s apparel and casual wear',
    handle: 'mens-clothing',
    is_active: true,
    is_internal: false,
    metadata: {},
    category_children: [
      {
        id: 'subcat-2-1',
        name: 'Shirts',
        description: 'Formal and casual shirts',
        handle: 'mens-shirts',
        is_active: true,
        is_internal: false,
        parent_category_id: 'cat-2',
        metadata: {}
      },
      {
        id: 'subcat-2-2',
        name: 'T-Shirts',
        description: 'Comfortable t-shirts',
        handle: 'mens-tshirts',
        is_active: true,
        is_internal: false,
        parent_category_id: 'cat-2',
        metadata: {}
      },
      {
        id: 'subcat-2-3',
        name: 'Pants & Jeans',
        description: 'Men\'s pants and jeans',
        handle: 'mens-pants',
        is_active: true,
        is_internal: false,
        parent_category_id: 'cat-2',
        metadata: {}
      },
      {
        id: 'subcat-2-4',
        name: 'Jackets',
        description: 'Stylish jackets and outerwear',
        handle: 'mens-jackets',
        is_active: true,
        is_internal: false,
        parent_category_id: 'cat-2',
        metadata: {}
      }
    ]
  },
  {
    id: 'cat-3',
    name: 'Accessories',
    description: 'Fashion accessories to complete your look',
    handle: 'accessories',
    is_active: true,
    is_internal: false,
    metadata: {},
    category_children: [
      {
        id: 'subcat-3-1',
        name: 'Watches',
        description: 'Stylish watches',
        handle: 'watches',
        is_active: true,
        is_internal: false,
        parent_category_id: 'cat-3',
        metadata: {}
      },
      {
        id: 'subcat-3-2',
        name: 'Belts',
        description: 'Leather and fabric belts',
        handle: 'belts',
        is_active: true,
        is_internal: false,
        parent_category_id: 'cat-3',
        metadata: {}
      },
      {
        id: 'subcat-3-3',
        name: 'Sunglasses',
        description: 'Trendy sunglasses',
        handle: 'sunglasses',
        is_active: true,
        is_internal: false,
        parent_category_id: 'cat-3',
        metadata: {}
      }
    ]
  },
  {
    id: 'cat-4',
    name: 'Shoes',
    description: 'Footwear for all occasions and styles',
    handle: 'shoes',
    is_active: true,
    is_internal: false,
    metadata: {},
    category_children: [
      {
        id: 'subcat-4-1',
        name: 'Sneakers',
        description: 'Comfortable sneakers',
        handle: 'sneakers',
        is_active: true,
        is_internal: false,
        parent_category_id: 'cat-4',
        metadata: {}
      },
      {
        id: 'subcat-4-2',
        name: 'Formal Shoes',
        description: 'Elegant formal footwear',
        handle: 'formal-shoes',
        is_active: true,
        is_internal: false,
        parent_category_id: 'cat-4',
        metadata: {}
      },
      {
        id: 'subcat-4-3',
        name: 'Boots',
        description: 'Stylish boots',
        handle: 'boots',
        is_active: true,
        is_internal: false,
        parent_category_id: 'cat-4',
        metadata: {}
      }
    ]
  },
  {
    id: 'cat-5',
    name: 'Bags',
    description: 'Handbags, backpacks, and travel bags',
    handle: 'bags',
    is_active: true,
    is_internal: false,
    metadata: {},
    category_children: [
      {
        id: 'subcat-5-1',
        name: 'Handbags',
        description: 'Elegant handbags',
        handle: 'handbags',
        is_active: true,
        is_internal: false,
        parent_category_id: 'cat-5',
        metadata: {}
      },
      {
        id: 'subcat-5-2',
        name: 'Backpacks',
        description: 'Practical backpacks',
        handle: 'backpacks',
        is_active: true,
        is_internal: false,
        parent_category_id: 'cat-5',
        metadata: {}
      },
      {
        id: 'subcat-5-3',
        name: 'Travel Bags',
        description: 'Durable travel bags',
        handle: 'travel-bags',
        is_active: true,
        is_internal: false,
        parent_category_id: 'cat-5',
        metadata: {}
      }
    ]
  },
  {
    id: 'cat-6',
    name: 'Jewelry',
    description: 'Beautiful jewelry pieces and accessories',
    handle: 'jewelry',
    is_active: true,
    is_internal: false,
    metadata: {},
    category_children: [
      {
        id: 'subcat-6-1',
        name: 'Necklaces',
        description: 'Elegant necklaces',
        handle: 'necklaces',
        is_active: true,
        is_internal: false,
        parent_category_id: 'cat-6',
        metadata: {}
      },
      {
        id: 'subcat-6-2',
        name: 'Earrings',
        description: 'Beautiful earrings',
        handle: 'earrings',
        is_active: true,
        is_internal: false,
        parent_category_id: 'cat-6',
        metadata: {}
      },
      {
        id: 'subcat-6-3',
        name: 'Rings',
        description: 'Stylish rings',
        handle: 'rings',
        is_active: true,
        is_internal: false,
        parent_category_id: 'cat-6',
        metadata: {}
      }
    ]
  }
];

// Extended demo products with more variety
export const demoProducts: Product[] = [
  {
    id: 'prod-1',
    title: 'Elegant Summer Dress',
    description: 'A beautiful flowy summer dress perfect for any occasion. Made with premium cotton blend fabric.',
    handle: 'elegant-summer-dress',
    status: 'published',
    thumbnail: '/images/demo-product-1.jpg',
    images: [
      { id: 'img-1', url: '/images/demo-product-1.jpg', metadata: {} },
      { id: 'img-2', url: '/images/demo-product-1-alt.jpg', metadata: {} }
    ],
    variants: [
      {
        id: 'var-1',
        title: 'Small / Blue',
        sku: 'ESD-S-BLU',
        inventory_quantity: 10,
        allow_backorder: false,
        manage_inventory: true,
        prices: [
          { id: 'price-1', currency_code: 'USD', amount: 7999 }
        ],
        options: [
          { id: 'opt-val-1', value: 'Small', option_id: 'opt-1' },
          { id: 'opt-val-2', value: 'Blue', option_id: 'opt-2' }
        ]
      },
      {
        id: 'var-2',
        title: 'Medium / Blue',
        sku: 'ESD-M-BLU',
        inventory_quantity: 15,
        allow_backorder: false,
        manage_inventory: true,
        prices: [
          { id: 'price-2', currency_code: 'USD', amount: 7999 }
        ],
        options: [
          { id: 'opt-val-3', value: 'Medium', option_id: 'opt-1' },
          { id: 'opt-val-4', value: 'Blue', option_id: 'opt-2' }
        ]
      }
    ],
    options: [
      {
        id: 'opt-1',
        title: 'Size',
        values: [
          { id: 'opt-val-1', value: 'Small', option_id: 'opt-1' },
          { id: 'opt-val-3', value: 'Medium', option_id: 'opt-1' },
          { id: 'opt-val-5', value: 'Large', option_id: 'opt-1' }
        ]
      },
      {
        id: 'opt-2',
        title: 'Color',
        values: [
          { id: 'opt-val-2', value: 'Blue', option_id: 'opt-2' },
          { id: 'opt-val-6', value: 'Red', option_id: 'opt-2' },
          { id: 'opt-val-7', value: 'Green', option_id: 'opt-2' }
        ]
      }
    ],
    tags: [
      { id: 'tag-1', value: 'summer' },
      { id: 'tag-2', value: 'casual' },
      { id: 'tag-3', value: 'cotton' }
    ],
    type: { id: 'type-1', value: 'dress' },
    categories: [demoCategories[0]],
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-15T00:00:00Z'
  },
  {
    id: 'prod-2',
    title: 'Classic Denim Jacket',
    description: 'Timeless denim jacket that goes with everything. Premium quality denim with vintage wash.',
    handle: 'classic-denim-jacket',
    status: 'published',
    thumbnail: '/images/demo-product-2.jpg',
    images: [
      { id: 'img-3', url: '/images/demo-product-2.jpg', metadata: {} }
    ],
    variants: [
      {
        id: 'var-3',
        title: 'Medium / Light Blue',
        sku: 'CDJ-M-LB',
        inventory_quantity: 8,
        allow_backorder: false,
        manage_inventory: true,
        prices: [
          { id: 'price-3', currency_code: 'USD', amount: 12999 }
        ],
        options: [
          { id: 'opt-val-8', value: 'Medium', option_id: 'opt-3' },
          { id: 'opt-val-9', value: 'Light Blue', option_id: 'opt-4' }
        ]
      }
    ],
    options: [
      {
        id: 'opt-3',
        title: 'Size',
        values: [
          { id: 'opt-val-8', value: 'Medium', option_id: 'opt-3' },
          { id: 'opt-val-10', value: 'Large', option_id: 'opt-3' }
        ]
      },
      {
        id: 'opt-4',
        title: 'Wash',
        values: [
          { id: 'opt-val-9', value: 'Light Blue', option_id: 'opt-4' },
          { id: 'opt-val-11', value: 'Dark Blue', option_id: 'opt-4' }
        ]
      }
    ],
    tags: [
      { id: 'tag-4', value: 'denim' },
      { id: 'tag-5', value: 'jacket' },
      { id: 'tag-6', value: 'vintage' }
    ],
    type: { id: 'type-2', value: 'jacket' },
    categories: [demoCategories[1]],
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-16T00:00:00Z'
  },
  {
    id: 'prod-3',
    title: 'Leather Crossbody Bag',
    description: 'Stylish leather crossbody bag perfect for everyday use. Multiple compartments for organization.',
    handle: 'leather-crossbody-bag',
    status: 'published',
    thumbnail: '/images/demo-product-3.jpg',
    images: [
      { id: 'img-4', url: '/images/demo-product-3.jpg', metadata: {} }
    ],
    variants: [
      {
        id: 'var-4',
        title: 'One Size / Black',
        sku: 'LCB-OS-BLK',
        inventory_quantity: 12,
        allow_backorder: true,
        manage_inventory: true,
        prices: [
          { id: 'price-4', currency_code: 'USD', amount: 8999 }
        ],
        options: [
          { id: 'opt-val-12', value: 'One Size', option_id: 'opt-5' },
          { id: 'opt-val-13', value: 'Black', option_id: 'opt-6' }
        ]
      }
    ],
    options: [
      {
        id: 'opt-5',
        title: 'Size',
        values: [
          { id: 'opt-val-12', value: 'One Size', option_id: 'opt-5' }
        ]
      },
      {
        id: 'opt-6',
        title: 'Color',
        values: [
          { id: 'opt-val-13', value: 'Black', option_id: 'opt-6' },
          { id: 'opt-val-14', value: 'Brown', option_id: 'opt-6' }
        ]
      }
    ],
    tags: [
      { id: 'tag-7', value: 'leather' },
      { id: 'tag-8', value: 'bag' },
      { id: 'tag-9', value: 'crossbody' }
    ],
    type: { id: 'type-3', value: 'bag' },
    categories: [demoCategories[4]],
    created_at: '2024-01-03T00:00:00Z',
    updated_at: '2024-01-17T00:00:00Z'
  },
  {
    id: 'prod-4',
    title: 'Premium Wireless Headphones',
    description: 'High-quality wireless headphones with noise cancellation and premium sound quality.',
    handle: 'premium-wireless-headphones',
    status: 'published',
    thumbnail: '/images/demo-product-4.jpg',
    images: [
      { id: 'img-5', url: '/images/demo-product-4.jpg', metadata: {} }
    ],
    variants: [
      {
        id: 'var-5',
        title: 'Black',
        sku: 'PWH-BLK',
        inventory_quantity: 25,
        allow_backorder: false,
        manage_inventory: true,
        prices: [
          { id: 'price-5', currency_code: 'USD', amount: 19999 }
        ],
        options: [
          { id: 'opt-val-15', value: 'Black', option_id: 'opt-7' }
        ]
      }
    ],
    options: [
      {
        id: 'opt-7',
        title: 'Color',
        values: [
          { id: 'opt-val-15', value: 'Black', option_id: 'opt-7' },
          { id: 'opt-val-16', value: 'White', option_id: 'opt-7' }
        ]
      }
    ],
    tags: [
      { id: 'tag-10', value: 'electronics' },
      { id: 'tag-11', value: 'wireless' },
      { id: 'tag-12', value: 'premium' }
    ],
    type: { id: 'type-4', value: 'electronics' },
    categories: [demoCategories[2]],
    created_at: '2024-01-04T00:00:00Z',
    updated_at: '2024-01-18T00:00:00Z'
  },
  {
    id: 'prod-5',
    title: 'Casual Cotton T-Shirt',
    description: 'Comfortable cotton t-shirt perfect for everyday wear. Soft fabric and modern fit.',
    handle: 'casual-cotton-tshirt',
    status: 'published',
    thumbnail: '/images/demo-product-5.jpg',
    images: [
      { id: 'img-6', url: '/images/demo-product-5.jpg', metadata: {} }
    ],
    variants: [
      {
        id: 'var-6',
        title: 'Medium / Navy',
        sku: 'CCT-M-NAV',
        inventory_quantity: 50,
        allow_backorder: true,
        manage_inventory: true,
        prices: [
          { id: 'price-6', currency_code: 'USD', amount: 2999 }
        ],
        options: [
          { id: 'opt-val-17', value: 'Medium', option_id: 'opt-8' },
          { id: 'opt-val-18', value: 'Navy', option_id: 'opt-9' }
        ]
      }
    ],
    options: [
      {
        id: 'opt-8',
        title: 'Size',
        values: [
          { id: 'opt-val-17', value: 'Medium', option_id: 'opt-8' },
          { id: 'opt-val-19', value: 'Large', option_id: 'opt-8' }
        ]
      },
      {
        id: 'opt-9',
        title: 'Color',
        values: [
          { id: 'opt-val-18', value: 'Navy', option_id: 'opt-9' },
          { id: 'opt-val-20', value: 'White', option_id: 'opt-9' }
        ]
      }
    ],
    tags: [
      { id: 'tag-13', value: 'casual' },
      { id: 'tag-14', value: 'cotton' },
      { id: 'tag-15', value: 'basic' }
    ],
    type: { id: 'type-5', value: 'clothing' },
    categories: [demoCategories[1]],
    created_at: '2024-01-05T00:00:00Z',
    updated_at: '2024-01-19T00:00:00Z'
  },
  {
    id: 'prod-6',
    title: 'Sport Running Shoes',
    description: 'Lightweight running shoes with excellent cushioning and breathable material.',
    handle: 'sport-running-shoes',
    status: 'published',
    thumbnail: '/images/demo-product-6.jpg',
    images: [
      { id: 'img-7', url: '/images/demo-product-6.jpg', metadata: {} }
    ],
    variants: [
      {
        id: 'var-7',
        title: 'Size 9 / Blue',
        sku: 'SRS-9-BLU',
        inventory_quantity: 15,
        allow_backorder: false,
        manage_inventory: true,
        prices: [
          { id: 'price-7', currency_code: 'USD', amount: 8999 }
        ],
        options: [
          { id: 'opt-val-21', value: '9', option_id: 'opt-10' },
          { id: 'opt-val-22', value: 'Blue', option_id: 'opt-11' }
        ]
      }
    ],
    options: [
      {
        id: 'opt-10',
        title: 'Size',
        values: [
          { id: 'opt-val-21', value: '9', option_id: 'opt-10' },
          { id: 'opt-val-23', value: '10', option_id: 'opt-10' }
        ]
      },
      {
        id: 'opt-11',
        title: 'Color',
        values: [
          { id: 'opt-val-22', value: 'Blue', option_id: 'opt-11' },
          { id: 'opt-val-24', value: 'Black', option_id: 'opt-11' }
        ]
      }
    ],
    tags: [
      { id: 'tag-16', value: 'sport' },
      { id: 'tag-17', value: 'running' },
      { id: 'tag-18', value: 'athletic' }
    ],
    type: { id: 'type-6', value: 'shoes' },
    categories: [demoCategories[3]],
    created_at: '2024-01-06T00:00:00Z',
    updated_at: '2024-01-20T00:00:00Z'
  }
];

// Latest arrivals (most recent products)
export const latestArrivals = demoProducts.slice(-10).reverse();

// Top selling products (simulated bestsellers)
export const topSellingProducts = [
  demoProducts[1], // Classic Denim Jacket
  demoProducts[4], // Casual Cotton T-Shirt
  demoProducts[0], // Elegant Summer Dress
  demoProducts[5], // Sport Running Shoes
  demoProducts[2], // Leather Crossbody Bag
  demoProducts[3], // Premium Wireless Headphones
  ...demoProducts.slice(0, 4) // Fill remaining slots
];

// Helper function to get demo data (useful for development/testing)
export const getDemoStoreData = (storeHandle: string) => {
  return {
    store: { ...demoStore, handle: storeHandle },
    categories: demoCategories,
    products: demoProducts,
    featuredProducts: demoProducts.slice(0, 3),
    latestArrivals: latestArrivals,
    topSellingProducts: topSellingProducts,
    banners: demoBanners
  };
};