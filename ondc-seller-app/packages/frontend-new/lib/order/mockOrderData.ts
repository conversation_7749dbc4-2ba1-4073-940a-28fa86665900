import { Order, OrderItem, OrderTimelineEvent } from '@/types/order';
import { CartItem } from '@/types/cart';
import { ContactInformation, ShippingAddress, PaymentMethod } from '@/types/checkout';
import { formatPrice } from '@/lib/cart/mockCartData';

export function generateOrderFromCheckout(
  cartItems: CartItem[],
  contactInfo: ContactInformation,
  shippingAddress: ShippingAddress,
  paymentMethod: PaymentMethod,
  totals: any,
  orderNotes?: string
): Order {
  const orderNumber = `ORD-${Date.now().toString().slice(-8)}`;
  const orderId = `order-${Date.now()}`;
  
  // Convert cart items to order items
  const orderItems: OrderItem[] = cartItems.map(item => ({
    id: `order-item-${item.id}`,
    productId: item.productId,
    title: item.title,
    image: item.image,
    price: item.price,
    quantity: item.quantity,
    variant: item.variant,
    subtotal: item.price * item.quantity,
  }));

  // Create timeline events
  const timeline: OrderTimelineEvent[] = [
    {
      id: 'timeline-1',
      status: 'confirmed',
      title: 'Order Confirmed',
      description: 'Your order has been confirmed and is being prepared.',
      timestamp: new Date().toISOString(),
      isCompleted: true,
      isCurrent: false,
    },
    {
      id: 'timeline-2',
      status: 'processing',
      title: 'Processing',
      description: 'We are preparing your items for shipment.',
      timestamp: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
      isCompleted: false,
      isCurrent: true,
    },
    {
      id: 'timeline-3',
      status: 'shipped',
      title: 'Shipped',
      description: 'Your order has been shipped and is on its way.',
      timestamp: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 1 day from now
      isCompleted: false,
      isCurrent: false,
    },
    {
      id: 'timeline-4',
      status: 'delivered',
      title: 'Delivered',
      description: 'Your order has been delivered successfully.',
      timestamp: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
      isCompleted: false,
      isCurrent: false,
    },
  ];

  const order: Order = {
    id: orderId,
    orderNumber,
    status: 'confirmed',
    items: orderItems,
    customer: {
      email: contactInfo.email,
      firstName: contactInfo.firstName,
      lastName: contactInfo.lastName,
      phone: contactInfo.phone,
    },
    shippingAddress: {
      firstName: contactInfo.firstName,
      lastName: contactInfo.lastName,
      address: shippingAddress.address,
      city: shippingAddress.city,
      state: shippingAddress.state,
      zipCode: shippingAddress.zipCode,
      country: shippingAddress.country,
      phone: contactInfo.phone,
    },
    paymentMethod: {
      id: paymentMethod.id,
      type: paymentMethod.type,
      name: paymentMethod.name,
      last4: paymentMethod.type === 'credit_card' ? '3456' : undefined,
      brand: paymentMethod.type === 'credit_card' ? 'Visa' : undefined,
    },
    totals: {
      subtotal: totals.subtotal,
      tax: totals.tax,
      shipping: totals.shipping,
      discount: 0,
      total: totals.finalTotal,
    },
    orderNotes,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
    timeline,
  };

  return order;
}

export function saveOrderToStorage(order: Order): void {
  if (typeof window !== 'undefined') {
    try {
      const existingOrders = getOrdersFromStorage();
      const updatedOrders = [order, ...existingOrders];
      localStorage.setItem('user-orders', JSON.stringify(updatedOrders));
      localStorage.setItem('latest-order', JSON.stringify(order));
    } catch (error) {
      console.error('Error saving order to storage:', error);
    }
  }
}

export function getOrdersFromStorage(): Order[] {
  if (typeof window !== 'undefined') {
    try {
      const orders = localStorage.getItem('user-orders');
      return orders ? JSON.parse(orders) : [];
    } catch (error) {
      console.error('Error getting orders from storage:', error);
      return [];
    }
  }
  return [];
}

export function getLatestOrderFromStorage(): Order | null {
  if (typeof window !== 'undefined') {
    try {
      const order = localStorage.getItem('latest-order');
      return order ? JSON.parse(order) : null;
    } catch (error) {
      console.error('Error getting latest order from storage:', error);
      return null;
    }
  }
  return null;
}

export function getOrderByNumber(orderNumber: string): Order | null {
  const orders = getOrdersFromStorage();
  return orders.find(order => order.orderNumber === orderNumber) || null;
}

export function clearLatestOrder(): void {
  if (typeof window !== 'undefined') {
    try {
      localStorage.removeItem('latest-order');
    } catch (error) {
      console.error('Error clearing latest order:', error);
    }
  }
}