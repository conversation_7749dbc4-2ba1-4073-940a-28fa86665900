'use client';

/**
 * Store isolation utilities for localStorage
 * Ensures each store has its own isolated storage space
 */

/**
 * Generate a store-specific key for localStorage
 * Format: {storeHandle}-{keyName}-{category}
 */
export const getStoreKey = (storeHandle: string, keyName: string, category: string = 'general'): string => {
  return `${storeHandle}-${keyName}-${category}`;
};

/**
 * Store-specific localStorage wrapper
 * Automatically prefixes all keys with store handle
 */
export class StoreLocalStorage {
  private storeHandle: string;
  private category: string;

  constructor(storeHandle: string, category: string = 'general') {
    this.storeHandle = storeHandle;
    this.category = category;
  }

  private getKey(key: string): string {
    return getStoreKey(this.storeHandle, key, this.category);
  }

  getItem(key: string): string | null {
    if (typeof window === 'undefined') return null;
    
    try {
      const storeKey = this.getKey(key);
      const value = localStorage.getItem(storeKey);
      console.log(`🔓 StoreLocalStorage getItem for ${this.storeHandle}:`, storeKey, value ? 'exists' : 'null');
      return value;
    } catch (error) {
      console.error('Error getting item from store localStorage:', error);
      return null;
    }
  }

  setItem(key: string, value: string): void {
    if (typeof window === 'undefined') return;
    
    try {
      const storeKey = this.getKey(key);
      localStorage.setItem(storeKey, value);
      console.log(`🔒 StoreLocalStorage setItem for ${this.storeHandle}:`, storeKey);
    } catch (error) {
      console.error('Error setting item in store localStorage:', error);
    }
  }

  removeItem(key: string): void {
    if (typeof window === 'undefined') return;
    
    try {
      const storeKey = this.getKey(key);
      localStorage.removeItem(storeKey);
      console.log(`🗑️ StoreLocalStorage removeItem for ${this.storeHandle}:`, storeKey);
    } catch (error) {
      console.error('Error removing item from store localStorage:', error);
    }
  }

  clear(): void {
    if (typeof window === 'undefined') return;
    
    try {
      const keysToRemove: string[] = [];
      const storePrefix = `${this.storeHandle}-`;
      
      // Find all keys for this store
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(storePrefix)) {
          keysToRemove.push(key);
        }
      }
      
      // Remove all store-specific keys
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
      });
      
      console.log(`🧹 StoreLocalStorage cleared for ${this.storeHandle}:`, keysToRemove.length, 'items');
    } catch (error) {
      console.error('Error clearing store localStorage:', error);
    }
  }

  /**
   * Get all keys for this store
   */
  getAllKeys(): string[] {
    if (typeof window === 'undefined') return [];
    
    try {
      const storeKeys: string[] = [];
      const storePrefix = `${this.storeHandle}-`;
      
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(storePrefix)) {
          storeKeys.push(key);
        }
      }
      
      return storeKeys;
    } catch (error) {
      console.error('Error getting store keys:', error);
      return [];
    }
  }
}

/**
 * Clear all data for a specific store
 */
export const clearStoreData = (storeHandle: string): void => {
  if (typeof window === 'undefined') return;
  
  try {
    const keysToRemove: string[] = [];
    const storePrefix = `${storeHandle}-`;
    
    // Find all keys for this store
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(storePrefix)) {
        keysToRemove.push(key);
      }
    }
    
    // Remove all store-specific keys
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });
    
    console.log(`🧹 Cleared all data for store ${storeHandle}:`, keysToRemove.length, 'items');
  } catch (error) {
    console.error('Error clearing store data:', error);
  }
};

/**
 * Get all stores that have data in localStorage
 */
export const getAllStoreHandles = (): string[] => {
  if (typeof window === 'undefined') return [];
  
  try {
    const storeHandles = new Set<string>();
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.includes('-')) {
        const storeHandle = key.split('-')[0];
        if (storeHandle) {
          storeHandles.add(storeHandle);
        }
      }
    }
    
    return Array.from(storeHandles);
  } catch (error) {
    console.error('Error getting store handles:', error);
    return [];
  }
};