'use client';

import { StateCreator, StoreMutatorIdentifier } from 'zustand';
import { PersistOptions, persist } from 'zustand/middleware';
import { getStoreKey } from './storageUtils';

/**
 * Store-specific persist middleware for Zustand
 * Ensures each store's Zustand state is isolated using store handle
 */

type StoreSpecificPersist = <
  T,
  Mps extends [StoreMutatorIdentifier, unknown][] = [],
  M<PERSON> extends [StoreMutatorIdentifier, unknown][] = []
>(
  storeHandle: string,
  config: StateCreator<T, Mps, Mcs>,
  options: PersistOptions<T>
) => StateCreator<T, Mps, Mcs>;

export const storeSpecificPersist: StoreSpecificPersist = (storeHandle, config, options) => {
  // Generate store-specific storage name
  const storeSpecificName = getStoreKey(storeHandle, options.name || 'store', 'zustand');
  
  console.log(`🔒 Creating store-specific Zustand store for ${storeHandle}:`, storeSpecificName);
  
  return persist(config, {
    ...options,
    name: storeSpecificName,
    // Add store handle to partialize function if needed
    partialize: options.partialize ? (state) => {
      const partializedState = options.partialize!(state);
      return {
        ...partializedState,
        _storeHandle: storeHandle, // Track which store this belongs to
      };
    } : undefined,
  });
};

/**
 * Create a store-specific storage interface for Zustand persist
 */
export const createStoreSpecificStorage = (storeHandle: string) => {
  const storageKey = (name: string) => getStoreKey(storeHandle, name, 'zustand');
  
  return {
    getItem: (name: string) => {
      try {
        const key = storageKey(name);
        const value = localStorage.getItem(key);
        console.log(`🔓 Zustand getItem for store ${storeHandle}:`, key, value ? 'exists' : 'null');
        return value;
      } catch (error) {
        console.error('Error getting Zustand item:', error);
        return null;
      }
    },
    setItem: (name: string, value: string) => {
      try {
        const key = storageKey(name);
        localStorage.setItem(key, value);
        console.log(`🔒 Zustand setItem for store ${storeHandle}:`, key);
      } catch (error) {
        console.error('Error setting Zustand item:', error);
      }
    },
    removeItem: (name: string) => {
      try {
        const key = storageKey(name);
        localStorage.removeItem(key);
        console.log(`🗑️ Zustand removeItem for store ${storeHandle}:`, key);
      } catch (error) {
        console.error('Error removing Zustand item:', error);
      }
    },
  };
};

/**
 * Clear all Zustand stores for a specific store handle
 */
export const clearStoreZustandData = (storeHandle: string): void => {
  if (typeof window === 'undefined') return;
  
  try {
    const keysToRemove: string[] = [];
    const storePrefix = `${storeHandle}-`;
    
    // Find all Zustand keys for this store
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(storePrefix) && key.includes('-zustand')) {
        keysToRemove.push(key);
      }
    }
    
    // Remove all store-specific Zustand keys
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });
    
    console.log(`🧹 Cleared all Zustand data for store ${storeHandle}:`, keysToRemove.length, 'stores');
  } catch (error) {
    console.error('Error clearing store Zustand data:', error);
  }
};

/**
 * Utility to create store-aware Zustand stores
 */
export const createStoreAwareZustand = <T>(
  storeHandle: string,
  stateCreator: StateCreator<T>,
  persistOptions?: Omit<PersistOptions<T>, 'name'> & { name: string }
) => {
  if (persistOptions) {
    return storeSpecificPersist(storeHandle, stateCreator, persistOptions);
  }
  
  // For non-persisted stores, we can still add store handle tracking
  return (set: any, get: any, api: any) => {
    const state = stateCreator(set, get, api);
    return {
      ...state,
      _storeHandle: storeHandle,
    };
  };
};