'use client';

/**
 * Enhanced token utility with hydration-aware authentication checks
 * Addresses race conditions between Zustand hydration and auth validation
 */

export interface TokenInfo {
  token: string | null;
  source: 'store-auth' | 'global-auth' | 'none';
  storeHandle?: string;
}

export interface AuthValidationResult {
  isValid: boolean;
  token: string | null;
  reason?: string;
  shouldRetry?: boolean;
}

/**
 * Get authentication token with automatic fallback logic
 * Priority: store-specific auth > global auth > none
 */
export const getAuthToken = (storeHandle?: string): TokenInfo => {
  if (typeof window === 'undefined') {
    return { token: null, source: 'none' };
  }

  try {
    // 1. Try store-specific auth first if storeHandle is provided
    if (storeHandle) {
      const storeAuthKey = `${storeHandle}-auth`;
      const storeAuthStorage = localStorage.getItem(storeAuthKey);
      
      if (storeAuthStorage) {
        const storeAuthData = JSON.parse(storeAuthStorage);
        const storeToken = storeAuthData.state?.token;
        
        if (storeToken) {
          console.log(`🔐 Found store-specific token for ${storeHandle}`);
          return { 
            token: storeToken, 
            source: 'store-auth', 
            storeHandle 
          };
        }
      }
    }

    // 2. Fallback to global auth storage
    const globalAuthStorage = localStorage.getItem('auth-storage');
    if (globalAuthStorage) {
      const globalAuthData = JSON.parse(globalAuthStorage);
      const globalToken = globalAuthData.state?.token;
      
      if (globalToken) {
        console.log('🔐 Found global auth token');
        return { 
          token: globalToken, 
          source: 'global-auth' 
        };
      }
    }

    // 3. Check legacy token storage
    const legacyToken = localStorage.getItem('ondc_auth_token');
    if (legacyToken) {
      console.log('🔐 Found legacy auth token');
      return { 
        token: legacyToken, 
        source: 'global-auth' 
      };
    }

  } catch (error) {
    console.warn('Error retrieving auth token:', error);
  }

  return { token: null, source: 'none' };
};

/**
 * Enhanced authentication validation with hydration awareness
 */
export const validateAuthentication = async (
  storeHandle?: string,
  retryCount = 0,
  maxRetries = 2
): Promise<AuthValidationResult> => {
  console.log(`🔍 Validating authentication (attempt ${retryCount + 1}/${maxRetries + 1})`);
  
  try {
    // Get token from localStorage (most reliable during hydration)
    const tokenInfo = getAuthToken(storeHandle);
    
    if (!tokenInfo.token) {
      // If no token found and we haven't retried much, suggest retry
      if (retryCount < maxRetries) {
        return {
          isValid: false,
          token: null,
          reason: 'No token found - might be hydration timing',
          shouldRetry: true
        };
      }
      
      return {
        isValid: false,
        token: null,
        reason: 'No authentication token found'
      };
    }

    // Validate token format
    if (!isValidTokenFormat(tokenInfo.token)) {
      return {
        isValid: false,
        token: tokenInfo.token,
        reason: 'Invalid token format'
      };
    }

    // Check if token is expired
    if (isTokenExpired(tokenInfo.token)) {
      return {
        isValid: false,
        token: tokenInfo.token,
        reason: 'Token is expired'
      };
    }

    // Check if we have user data in localStorage
    const hasUserData = checkUserDataInStorage(storeHandle);
    if (!hasUserData && retryCount < maxRetries) {
      return {
        isValid: false,
        token: tokenInfo.token,
        reason: 'No user data found - might be hydration timing',
        shouldRetry: true
      };
    }

    console.log('✅ Authentication validation passed');
    return {
      isValid: true,
      token: tokenInfo.token
    };

  } catch (error) {
    console.error('Error during authentication validation:', error);
    return {
      isValid: false,
      token: null,
      reason: `Validation error: ${error.message}`
    };
  }
};

/**
 * Check if user data exists in localStorage
 */
const checkUserDataInStorage = (storeHandle?: string): boolean => {
  try {
    // Check store-specific auth first
    if (storeHandle) {
      const storeAuthKey = `${storeHandle}-auth`;
      const storeAuthStorage = localStorage.getItem(storeAuthKey);
      
      if (storeAuthStorage) {
        const storeAuthData = JSON.parse(storeAuthStorage);
        if (storeAuthData.state?.user) {
          return true;
        }
      }
    }

    // Check global auth
    const globalAuthStorage = localStorage.getItem('auth-storage');
    if (globalAuthStorage) {
      const globalAuthData = JSON.parse(globalAuthStorage);
      if (globalAuthData.state?.user) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.warn('Error checking user data in storage:', error);
    return false;
  }
};

/**
 * Retry-aware authentication check
 */
export const checkAuthenticationWithRetry = async (
  storeHandle?: string,
  onRetry?: () => void
): Promise<AuthValidationResult> => {
  let result = await validateAuthentication(storeHandle, 0);
  
  // If validation suggests retry, wait a bit and try again
  if (result.shouldRetry) {
    console.log('⏳ Retrying authentication validation after delay...');
    
    if (onRetry) {
      onRetry();
    }
    
    // Wait for potential hydration completion
    await new Promise(resolve => setTimeout(resolve, 150));
    
    result = await validateAuthentication(storeHandle, 1);
    
    // One more retry if still suggested
    if (result.shouldRetry) {
      console.log('⏳ Final retry for authentication validation...');
      await new Promise(resolve => setTimeout(resolve, 100));
      result = await validateAuthentication(storeHandle, 2);
    }
  }
  
  return result;
};

/**
 * Get headers with automatic token inclusion
 */
export const getAuthHeaders = (
  storeHandle?: string, 
  additionalHeaders: Record<string, string> = {}
): HeadersInit => {
  const baseHeaders: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...additionalHeaders,
  };

  // Add store-specific headers if storeHandle is provided
  if (storeHandle) {
    baseHeaders['x-tenant-id'] = storeHandle;
    baseHeaders['x-publishable-api-key'] = process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || '';
  }

  // Get and add authentication token
  const tokenInfo = getAuthToken(storeHandle);
  if (tokenInfo.token) {
    baseHeaders['Authorization'] = `Bearer ${tokenInfo.token}`;
    console.log(`🔐 Added ${tokenInfo.source} token to headers${tokenInfo.storeHandle ? ` for ${tokenInfo.storeHandle}` : ''}`);
  }

  return baseHeaders;
};

/**
 * Enhanced fetch wrapper with automatic token handling
 */
export const fetchWithAuth = async (
  url: string,
  options: RequestInit = {},
  storeHandle?: string
): Promise<Response> => {
  const headers = getAuthHeaders(storeHandle, options.headers as Record<string, string>);
  
  const enhancedOptions: RequestInit = {
    ...options,
    headers,
  };

  console.log(`🌐 Making authenticated request to: ${url}`);
  console.log(`🔐 Using headers:`, Object.keys(headers).reduce((acc, key) => {
    acc[key] = key === 'Authorization' ? 'Bearer [REDACTED]' : headers[key as keyof HeadersInit];
    return acc;
  }, {} as Record<string, any>));

  return fetch(url, enhancedOptions);
};

/**
 * Check if user is authenticated (has valid token)
 */
export const isAuthenticated = (storeHandle?: string): boolean => {
  const tokenInfo = getAuthToken(storeHandle);
  return tokenInfo.token !== null;
};

/**
 * Get user information from stored auth data
 */
export const getStoredUser = (storeHandle?: string): any | null => {
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    // Try store-specific auth first
    if (storeHandle) {
      const storeAuthKey = `${storeHandle}-auth`;
      const storeAuthStorage = localStorage.getItem(storeAuthKey);
      
      if (storeAuthStorage) {
        const storeAuthData = JSON.parse(storeAuthStorage);
        const user = storeAuthData.state?.user;
        
        if (user) {
          return user;
        }
      }
    }

    // Fallback to global auth
    const globalAuthStorage = localStorage.getItem('auth-storage');
    if (globalAuthStorage) {
      const globalAuthData = JSON.parse(globalAuthStorage);
      return globalAuthData.state?.user || null;
    }

  } catch (error) {
    console.warn('Error retrieving stored user:', error);
  }

  return null;
};

/**
 * Clear all authentication data
 */
export const clearAllAuth = (storeHandle?: string): void => {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    // Clear store-specific auth if storeHandle provided
    if (storeHandle) {
      const storeAuthKey = `${storeHandle}-auth`;
      localStorage.removeItem(storeAuthKey);
      console.log(`🧹 Cleared store-specific auth for ${storeHandle}`);
    }

    // Clear global auth
    localStorage.removeItem('auth-storage');
    localStorage.removeItem('ondc_auth_token');
    console.log('🧹 Cleared global auth storage');

  } catch (error) {
    console.warn('Error clearing auth data:', error);
  }
};

/**
 * Token refresh utility (placeholder for future implementation)
 */
export const refreshToken = async (storeHandle?: string): Promise<string | null> => {
  // TODO: Implement token refresh logic when refresh tokens are available
  console.log('🔄 Token refresh not yet implemented');
  return null;
};

/**
 * Validate token format
 */
export const isValidTokenFormat = (token: string): boolean => {
  if (!token || typeof token !== 'string') {
    return false;
  }
  
  // Basic JWT format check (three parts separated by dots)
  const parts = token.split('.');
  return parts.length === 3;
};

/**
 * Get token expiration info (if JWT)
 */
export const getTokenExpiration = (token: string): Date | null => {
  try {
    if (!isValidTokenFormat(token)) {
      return null;
    }

    const payload = JSON.parse(atob(token.split('.')[1]));
    if (payload.exp) {
      return new Date(payload.exp * 1000);
    }
  } catch (error) {
    console.warn('Error parsing token expiration:', error);
  }
  
  return null;
};

/**
 * Check if token is expired
 */
export const isTokenExpired = (token: string): boolean => {
  const expiration = getTokenExpiration(token);
  if (!expiration) {
    return false; // If we can't determine expiration, assume it's valid
  }
  
  return new Date() >= expiration;
};