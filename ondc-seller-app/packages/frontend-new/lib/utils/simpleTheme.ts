/**
 * Simple Theme Generation from Store Logo
 * Automatically generates a cohesive theme from a single dominant color
 */

export interface AutoTheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
  surface: string;
  onSurface: string;
  primaryLight: string;
  primaryDark: string;
  secondaryLight: string;
  secondaryDark: string;
}

export interface HSL {
  h: number;
  s: number;
  l: number;
}

export interface RGB {
  r: number;
  g: number;
  b: number;
}

/**
 * Extract dominant color from logo image
 */
export const extractDominantColor = async (imageUrl: string): Promise<string> => {
  try {
    console.log('🎨 Extracting dominant color from:', imageUrl);
    
    // Try dynamic import, fallback if not available
    let Vibrant;
    try {
      const vibrantModule = await import('node-vibrant/browser');
      Vibrant = vibrantModule.Vibrant;
    } catch (importError) {
      console.warn('node-vibrant not available, using fallback');
      return await extractColorFromImageFallback(imageUrl);
    }
    
    const vibrant = new Vibrant(imageUrl, {
      colorCount: 16,
      quality: 5,
    });
    
    const palette = await vibrant.getPalette();
    
    // Priority order for color selection
    const dominantColor = 
      palette.Vibrant?.hex ||
      palette.DarkVibrant?.hex ||
      palette.LightVibrant?.hex ||
      palette.Muted?.hex ||
      palette.DarkMuted?.hex ||
      palette.LightMuted?.hex ||
      '#1976d2'; // Default Material Design blue
    
    console.log('✅ Dominant color extracted:', dominantColor);
    return dominantColor;
    
  } catch (error) {
    console.error('❌ Error extracting dominant color:', error);
    return '#1976d2'; // Default fallback
  }
};

/**
 * Fallback color extraction method using Canvas API
 */
const extractColorFromImageFallback = async (imageUrl: string): Promise<string> => {
  return new Promise((resolve) => {
    try {
      if (typeof window === 'undefined') {
        resolve('#1976d2');
        return;
      }
      
      // Create image element
      const img = new Image();
      img.crossOrigin = 'anonymous';
      
      img.onload = () => {
        try {
          // Create canvas
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          
          if (!ctx) {
            resolve('#1976d2');
            return;
          }
          
          // Set canvas size (smaller for performance)
          const size = Math.min(img.width, img.height, 100);
          canvas.width = size;
          canvas.height = size;
          
          // Draw image
          ctx.drawImage(img, 0, 0, size, size);
          
          // Get image data
          const imageData = ctx.getImageData(0, 0, size, size);
          const data = imageData.data;
          
          // Simple color extraction - get average color
          let r = 0, g = 0, b = 0;
          let pixelCount = 0;
          
          // Sample every 4th pixel for performance
          for (let i = 0; i < data.length; i += 16) {
            r += data[i];
            g += data[i + 1];
            b += data[i + 2];
            pixelCount++;
          }
          
          // Calculate average
          r = Math.round(r / pixelCount);
          g = Math.round(g / pixelCount);
          b = Math.round(b / pixelCount);
          
          // Convert to hex
          const hex = `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
          
          console.log('✅ Fallback color extracted:', hex);
          resolve(hex);
          
        } catch (canvasError) {
          console.warn('⚠️ Canvas extraction failed:', canvasError);
          resolve('#1976d2');
        }
      };
      
      img.onerror = () => {
        console.warn('⚠️ Image load failed, using default color');
        resolve('#1976d2');
      };
      
      img.src = imageUrl;
      
    } catch (error) {
      console.warn('⚠️ Fallback extraction failed:', error);
      resolve('#1976d2');
    }
  });
};

/**
 * Convert hex color to HSL
 */
export const hexToHsl = (hex: string): HSL => {
  const r = parseInt(hex.slice(1, 3), 16) / 255;
  const g = parseInt(hex.slice(3, 5), 16) / 255;
  const b = parseInt(hex.slice(5, 7), 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  return {
    h: Math.round(h * 360),
    s: Math.round(s * 100),
    l: Math.round(l * 100)
  };
};

/**
 * Convert HSL to hex color
 */
export const hslToHex = (h: number, s: number, l: number): string => {
  h = h % 360;
  s = Math.max(0, Math.min(100, s)) / 100;
  l = Math.max(0, Math.min(100, l)) / 100;

  const c = (1 - Math.abs(2 * l - 1)) * s;
  const x = c * (1 - Math.abs((h / 60) % 2 - 1));
  const m = l - c / 2;
  let r = 0, g = 0, b = 0;

  if (0 <= h && h < 60) {
    r = c; g = x; b = 0;
  } else if (60 <= h && h < 120) {
    r = x; g = c; b = 0;
  } else if (120 <= h && h < 180) {
    r = 0; g = c; b = x;
  } else if (180 <= h && h < 240) {
    r = 0; g = x; b = c;
  } else if (240 <= h && h < 300) {
    r = x; g = 0; b = c;
  } else if (300 <= h && h < 360) {
    r = c; g = 0; b = x;
  }

  r = Math.round((r + m) * 255);
  g = Math.round((g + m) * 255);
  b = Math.round((b + m) * 255);

  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
};

/**
 * Generate complementary color (180° hue shift)
 */
export const generateComplementary = (hex: string): string => {
  const hsl = hexToHsl(hex);
  return hslToHex(
    (hsl.h + 180) % 360,
    Math.max(20, hsl.s - 10), // Slightly reduce saturation
    Math.max(30, hsl.l - 5)   // Slightly reduce lightness
  );
};

/**
 * Generate accent color (60° hue shift)
 */
export const generateAccent = (hex: string): string => {
  const hsl = hexToHsl(hex);
  return hslToHex(
    (hsl.h + 60) % 360,
    Math.max(25, hsl.s - 15), // Moderate saturation
    Math.max(35, hsl.l - 10)  // Balanced lightness
  );
};

/**
 * Generate lighter variant of a color
 */
export const generateLighter = (hex: string, amount: number = 20): string => {
  const hsl = hexToHsl(hex);
  return hslToHex(hsl.h, hsl.s, Math.min(95, hsl.l + amount));
};

/**
 * Generate darker variant of a color
 */
export const generateDarker = (hex: string, amount: number = 20): string => {
  const hsl = hexToHsl(hex);
  return hslToHex(hsl.h, hsl.s, Math.max(10, hsl.l - amount));
};

/**
 * Calculate contrast ratio between two colors
 */
export const calculateContrast = (color1: string, color2: string): number => {
  const getLuminance = (hex: string): number => {
    const rgb = {
      r: parseInt(hex.slice(1, 3), 16) / 255,
      g: parseInt(hex.slice(3, 5), 16) / 255,
      b: parseInt(hex.slice(5, 7), 16) / 255
    };

    const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  };

  const l1 = getLuminance(color1);
  const l2 = getLuminance(color2);
  const lighter = Math.max(l1, l2);
  const darker = Math.min(l1, l2);

  return (lighter + 0.05) / (darker + 0.05);
};

/**
 * Get contrasting text color (black or white)
 */
export const getContrastingText = (backgroundColor: string): string => {
  const contrast = calculateContrast(backgroundColor, '#ffffff');
  return contrast >= 4.5 ? '#ffffff' : '#000000';
};

/**
 * Generate complete theme from dominant color
 */
export const generateSimpleTheme = (dominantColor: string): AutoTheme => {
  console.log('🎨 Generating theme from dominant color:', dominantColor);
  
  const primary = dominantColor;
  const secondary = generateComplementary(dominantColor);
  const accent = generateAccent(dominantColor);
  
  // Ensure good contrast for text
  const background = '#ffffff';
  const surface = '#fafafa';
  const text = '#1a1a1a';
  const onSurface = '#424242';
  
  const theme: AutoTheme = {
    primary,
    secondary,
    accent,
    background,
    text,
    surface,
    onSurface,
    primaryLight: generateLighter(primary, 30),
    primaryDark: generateDarker(primary, 20),
    secondaryLight: generateLighter(secondary, 30),
    secondaryDark: generateDarker(secondary, 20),
  };
  
  console.log('✅ Theme generated:', theme);
  return theme;
};

/**
 * Apply theme to CSS variables for immediate use
 */
export const applyThemeToStore = (theme: AutoTheme, storeHandle?: string): void => {
  if (typeof document === 'undefined') return;
  
  console.log('🎨 Applying theme to store:', storeHandle || 'current');
  
  const root = document.documentElement;
  
  // Store-specific theme variables
  root.style.setProperty('--store-primary', theme.primary);
  root.style.setProperty('--store-secondary', theme.secondary);
  root.style.setProperty('--store-accent', theme.accent);
  root.style.setProperty('--store-background', theme.background);
  root.style.setProperty('--store-text', theme.text);
  root.style.setProperty('--store-surface', theme.surface);
  root.style.setProperty('--store-on-surface', theme.onSurface);
  root.style.setProperty('--store-primary-light', theme.primaryLight);
  root.style.setProperty('--store-primary-dark', theme.primaryDark);
  root.style.setProperty('--store-secondary-light', theme.secondaryLight);
  root.style.setProperty('--store-secondary-dark', theme.secondaryDark);
  
  // MUI theme integration
  root.style.setProperty('--mui-palette-primary-main', theme.primary);
  root.style.setProperty('--mui-palette-secondary-main', theme.secondary);
  root.style.setProperty('--mui-palette-background-default', theme.background);
  root.style.setProperty('--mui-palette-background-paper', theme.surface);
  root.style.setProperty('--mui-palette-text-primary', theme.text);
  root.style.setProperty('--mui-palette-text-secondary', theme.onSurface);
  
  // Legacy theme variables for backward compatibility
  root.style.setProperty('--theme-primary', theme.primary);
  root.style.setProperty('--theme-secondary', theme.secondary);
  root.style.setProperty('--theme-accent', theme.accent);
  root.style.setProperty('--theme-background', theme.background);
  root.style.setProperty('--theme-text', theme.text);
  root.style.setProperty('--theme-text-secondary', theme.onSurface);
  root.style.setProperty('--theme-surface', theme.surface);
  
  // Button and component variables
  root.style.setProperty('--btn-primary', theme.primary);
  root.style.setProperty('--btn-primary-hover', theme.primaryDark);
  root.style.setProperty('--btn-text', getContrastingText(theme.primary));
  
  // Text color variables
  root.style.setProperty('--text-primary', theme.text);
  root.style.setProperty('--text-secondary', theme.onSurface);
  root.style.setProperty('--text-muted', theme.onSurface);
  
  // Header variables
  root.style.setProperty('--header-bg', theme.background);
  root.style.setProperty('--header-text', theme.text);
  root.style.setProperty('--header-border', theme.surface);
  
  // Navigation and breadcrumb variables
  root.style.setProperty('--nav-text', theme.text);
  root.style.setProperty('--nav-text-secondary', theme.onSurface);
  root.style.setProperty('--breadcrumb-text', theme.onSurface);
  root.style.setProperty('--breadcrumb-active', theme.text);
  
  console.log('✅ Theme applied to CSS variables');
};

/**
 * Apply default theme when no logo is available
 */
export const applyDefaultTheme = (): void => {
  const defaultTheme = generateSimpleTheme('#1976d2'); // Material Design blue
  applyThemeToStore(defaultTheme);
  console.log('✅ Default theme applied');
};

/**
 * Auto-generate and apply theme from logo URL
 */
export const autoGenerateAndApplyTheme = async (logoUrl: string, storeHandle?: string): Promise<AutoTheme> => {
  try {
    console.log('🚀 Auto-generating theme from logo:', logoUrl);
    
    // Extract dominant color
    const dominantColor = await extractDominantColor(logoUrl);
    
    // Generate theme
    const theme = generateSimpleTheme(dominantColor);
    
    // Apply theme immediately
    applyThemeToStore(theme, storeHandle);
    
    // Store theme in localStorage for persistence
    if (storeHandle && typeof window !== 'undefined') {
      try {
        localStorage.setItem(`store-theme-${storeHandle}`, JSON.stringify(theme));
        console.log('💾 Theme saved to localStorage');
      } catch (error) {
        console.warn('Failed to save theme to localStorage:', error);
      }
    }
    
    console.log('✅ Theme auto-generated and applied successfully');
    return theme;
    
  } catch (error) {
    console.error('❌ Error auto-generating theme:', error);
    
    // Fallback to default theme
    applyDefaultTheme();
    return generateSimpleTheme('#1976d2');
  }
};

/**
 * Load and apply theme from localStorage
 */
export const loadStoredTheme = (storeHandle: string): AutoTheme | null => {
  if (typeof window === 'undefined') return null;
  
  try {
    const storedTheme = localStorage.getItem(`store-theme-${storeHandle}`);
    if (storedTheme) {
      const theme: AutoTheme = JSON.parse(storedTheme);
      applyThemeToStore(theme, storeHandle);
      console.log('✅ Stored theme loaded and applied:', theme);
      return theme;
    }
  } catch (error) {
    console.warn('Failed to load stored theme:', error);
  }
  
  return null;
};

/**
 * Clear stored theme
 */
export const clearStoredTheme = (storeHandle: string): void => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem(`store-theme-${storeHandle}`);
    console.log('🗑️ Stored theme cleared for:', storeHandle);
  } catch (error) {
    console.warn('Failed to clear stored theme:', error);
  }
};

/**
 * Get current applied theme from CSS variables
 */
export const getCurrentTheme = (): AutoTheme | null => {
  if (typeof document === 'undefined') return null;
  
  const root = document.documentElement;
  const computedStyle = getComputedStyle(root);
  
  const primary = computedStyle.getPropertyValue('--store-primary').trim();
  if (!primary) return null;
  
  return {
    primary,
    secondary: computedStyle.getPropertyValue('--store-secondary').trim(),
    accent: computedStyle.getPropertyValue('--store-accent').trim(),
    background: computedStyle.getPropertyValue('--store-background').trim(),
    text: computedStyle.getPropertyValue('--store-text').trim(),
    surface: computedStyle.getPropertyValue('--store-surface').trim(),
    onSurface: computedStyle.getPropertyValue('--store-on-surface').trim(),
    primaryLight: computedStyle.getPropertyValue('--store-primary-light').trim(),
    primaryDark: computedStyle.getPropertyValue('--store-primary-dark').trim(),
    secondaryLight: computedStyle.getPropertyValue('--store-secondary-light').trim(),
    secondaryDark: computedStyle.getPropertyValue('--store-secondary-dark').trim(),
  };
};

/**
 * Validate if a theme has good accessibility
 */
export const validateThemeAccessibility = (theme: AutoTheme): {
  isValid: boolean;
  issues: string[];
} => {
  const issues: string[] = [];
  
  // Check primary button contrast
  const primaryContrast = calculateContrast(theme.primary, getContrastingText(theme.primary));
  if (primaryContrast < 4.5) {
    issues.push('Primary button text contrast is too low');
  }
  
  // Check text on background contrast
  const textContrast = calculateContrast(theme.background, theme.text);
  if (textContrast < 4.5) {
    issues.push('Text on background contrast is too low');
  }
  
  // Check surface text contrast
  const surfaceContrast = calculateContrast(theme.surface, theme.onSurface);
  if (surfaceContrast < 4.5) {
    issues.push('Surface text contrast is too low');
  }
  
  return {
    isValid: issues.length === 0,
    issues
  };
};