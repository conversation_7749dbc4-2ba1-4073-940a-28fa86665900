// Import the extractor function and types
import { extractColorsFromUrl, ColorPalette as ExtractorColorPalette } from './colorPaletteExtractor';

// Define our ColorPalette interface to match the expected format
export interface ColorPalette {
  vibrant: string;
  vibrantLight: string;
  vibrantDark: string;
  muted: string;
  mutedLight: string;
  mutedDark: string;
  dominant: string;
  population: number;
  extractedAt: string;
}

// Use the robust color extraction from colorPaletteExtractor
export const extractColorPalette = async (imageUrl: string): Promise<ColorPalette | null> => {
  try {
    console.log('=== EXTRACTING COLOR PALETTE ===');
    console.log('Image URL:', imageUrl);
    
    // Use the existing extractColorsFromUrl function
    const result = await extractColorsFromUrl(imageUrl);
    
    if (result.success && result.palette) {
      console.log('✅ Color extraction successful:', result.palette);
      
      // Convert the palette format to match our interface with fallbacks
      const convertedPalette: ColorPalette = {
        vibrant: result.palette.vibrant || '#da1e06',
        vibrantLight: result.palette.vibrantLight || '#dfab8e', 
        vibrantDark: result.palette.vibrantDark || '#095c44',
        muted: result.palette.muted || '#64ac7c',
        mutedLight: result.palette.mutedLight || '#dcd5cb',
        mutedDark: result.palette.mutedDark || '#528d74',
        dominant: result.palette.dominant || result.palette.vibrant || '#da1e06',
        population: result.palette.population,
        extractedAt: result.palette.extractedAt,
      };
      
      console.log('Converted palette with fallbacks:', convertedPalette);
      
      return convertedPalette;
    } else {
      console.error('❌ Color extraction failed:', result.error);
      throw new Error(result.error || 'Failed to extract colors');
    }
    
  } catch (error) {
    console.error('Error extracting color palette:', error);
    throw error;
  }
};

export const uploadImageToCloudinary = async (file: File): Promise<string | null> => {
  try {
    console.log('=== UPLOADING IMAGE TO CLOUDINARY ===');
    console.log('File:', file.name, file.size, file.type);
    
    // For demo purposes, we'll create a local URL
    // In production, you would upload to Cloudinary or another service
    const localUrl = URL.createObjectURL(file);
    
    console.log('Generated local URL:', localUrl);
    
    // TODO: Implement actual Cloudinary upload
    // const formData = new FormData();
    // formData.append('file', file);
    // formData.append('upload_preset', 'your_upload_preset');
    // 
    // const response = await fetch('https://api.cloudinary.com/v1_1/your_cloud_name/image/upload', {
    //   method: 'POST',
    //   body: formData,
    // });
    // 
    // const data = await response.json();
    // return data.secure_url;
    
    return localUrl;
    
  } catch (error) {
    console.error('Error uploading image:', error);
    return null;
  }
};