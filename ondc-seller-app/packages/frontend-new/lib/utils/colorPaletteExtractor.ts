/**
 * Color Palette Extraction Utility using node-vibrant
 * Extracts dominant colors from store logo images for theme customization
 */

import { Vibrant } from 'node-vibrant/browser';

export interface ColorPalette {
  vibrant: string | null;
  vibrantLight: string | null;
  vibrantDark: string | null;
  muted: string | null;
  mutedLight: string | null;
  mutedDark: string | null;
  dominant: string | null;
  population: number;
  extractedAt: string;
}

export interface ColorExtractionResult {
  success: boolean;
  palette?: ColorPalette;
  error?: string;
  processingTime?: number;
}

/**
 * Extract color palette from an image file (File object)
 */
export async function extractColorsFromFile(
  file: File
): Promise<ColorExtractionResult> {
  const startTime = performance.now();

  try {
    console.log(
      '🎨 [ColorExtractor] Starting color extraction from file:',
      file.name
    );

    // Create object URL for the file
    const imageUrl = URL.createObjectURL(file);

    try {
      const result = await extractColorsFromUrl(imageUrl);

      // Clean up object URL
      URL.revokeObjectURL(imageUrl);

      return result;
    } catch (error) {
      // Clean up object URL on error
      URL.revokeObjectURL(imageUrl);
      throw error;
    }
  } catch (error) {
    const processingTime = performance.now() - startTime;
    console.error(
      '❌ [ColorExtractor] Error extracting colors from file:',
      error
    );

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      processingTime,
    };
  }
}

/**
 * Extract color palette from an image URL
 */
export async function extractColorsFromUrl(
  imageUrl: string
): Promise<ColorExtractionResult> {
  const startTime = performance.now();

  try {
    console.log(
      '🎨 [ColorExtractor] Starting color extraction from URL:',
      imageUrl
    );

    // Validate URL format
    if (!isValidImageUrl(imageUrl)) {
      throw new Error('Invalid image URL format');
    }

    // Extract colors using Vibrant
    const vibrant = new Vibrant(imageUrl, {
      colorCount: 64,
      quality: 5,
    });

    const palette = await vibrant.getPalette();
    const processingTime = performance.now() - startTime;

    // Convert Vibrant palette to our format
    const colorPalette: ColorPalette = {
      vibrant: palette.Vibrant?.hex || null,
      vibrantLight: palette.LightVibrant?.hex || null,
      vibrantDark: palette.DarkVibrant?.hex || null,
      muted: palette.Muted?.hex || null,
      mutedLight: palette.LightMuted?.hex || null,
      mutedDark: palette.DarkMuted?.hex || null,
      dominant: getDominantColor(palette),
      population: getTotalPopulation(palette),
      extractedAt: new Date().toISOString(),
    };

    console.log('✅ [ColorExtractor] Color extraction completed:', {
      processingTime: `${processingTime.toFixed(2)}ms`,
      palette: colorPalette,
    });

    return {
      success: true,
      palette: colorPalette,
      processingTime,
    };
  } catch (error) {
    const processingTime = performance.now() - startTime;
    console.error(
      '❌ [ColorExtractor] Error extracting colors from URL:',
      error
    );

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      processingTime,
    };
  }
}

/**
 * Get the dominant color from the palette (highest population)
 */
function getDominantColor(palette: any): string | null {
  let dominantColor = null;
  let maxPopulation = 0;

  Object.values(palette).forEach((swatch: any) => {
    if (swatch && swatch.population > maxPopulation) {
      maxPopulation = swatch.population;
      dominantColor = swatch.hex;
    }
  });

  return dominantColor;
}

/**
 * Get total population of all colors
 */
function getTotalPopulation(palette: any): number {
  return Object.values(palette).reduce((total: number, swatch: any) => {
    return total + (swatch?.population || 0);
  }, 0);
}

/**
 * Validate if URL is a valid image URL
 */
function isValidImageUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);

    // Check if it's a blob URL (for uploaded files)
    if (urlObj.protocol === 'blob:') {
      return true;
    }

    // Check if it's a valid HTTP/HTTPS URL
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return false;
    }

    // Check if URL has image extension (optional, as many URLs don't have extensions)
    const imageExtensions = [
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.bmp',
      '.webp',
      '.svg',
    ];
    const pathname = urlObj.pathname.toLowerCase();

    // If it has an extension, it should be an image extension
    const hasExtension = pathname.includes('.');
    if (hasExtension) {
      return imageExtensions.some(ext => pathname.endsWith(ext));
    }

    // If no extension, assume it's valid (many CDN URLs don't have extensions)
    return true;
  } catch {
    return false;
  }
}

/**
 * Generate CSS custom properties from color palette
 */
export function generateCSSCustomProperties(
  palette: ColorPalette
): Record<string, string> {
  const cssProps: Record<string, string> = {};

  if (palette.vibrant) cssProps['--color-vibrant'] = palette.vibrant;
  if (palette.vibrantLight)
    cssProps['--color-vibrant-light'] = palette.vibrantLight;
  if (palette.vibrantDark)
    cssProps['--color-vibrant-dark'] = palette.vibrantDark;
  if (palette.muted) cssProps['--color-muted'] = palette.muted;
  if (palette.mutedLight) cssProps['--color-muted-light'] = palette.mutedLight;
  if (palette.mutedDark) cssProps['--color-muted-dark'] = palette.mutedDark;
  if (palette.dominant) cssProps['--color-dominant'] = palette.dominant;

  return cssProps;
}

/**
 * Generate theme colors for Material-UI or other UI frameworks
 */
export function generateThemeColors(palette: ColorPalette) {
  return {
    primary: {
      main: palette.vibrant || palette.dominant || '#1976d2',
      light: palette.vibrantLight || '#42a5f5',
      dark: palette.vibrantDark || '#1565c0',
    },
    secondary: {
      main: palette.muted || '#dc004e',
      light: palette.mutedLight || '#f5325b',
      dark: palette.mutedDark || '#9a0036',
    },
    accent: {
      main: palette.vibrantDark || palette.mutedDark || '#ff5722',
    },
  };
}

/**
 * Check if color palette has sufficient contrast for accessibility
 */
export function validateColorAccessibility(palette: ColorPalette): {
  isAccessible: boolean;
  warnings: string[];
} {
  const warnings: string[] = [];

  // Check if we have at least one vibrant color
  if (!palette.vibrant && !palette.vibrantDark) {
    warnings.push(
      'No vibrant colors found - may result in low contrast themes'
    );
  }

  // Check if we have muted colors for backgrounds
  if (!palette.muted && !palette.mutedLight) {
    warnings.push('No muted colors found - may lack subtle background options');
  }

  // Check if dominant color exists
  if (!palette.dominant) {
    warnings.push('No dominant color identified - theme may lack cohesion');
  }

  return {
    isAccessible: warnings.length === 0,
    warnings,
  };
}
