/**
 * Utility functions for managing store themes and debugging theme issues
 * Updated to work with simplified Logo-to-Theme approach
 */

import { AutoTheme, applyThemeToStore, loadStoredTheme, applyDefaultTheme } from './simpleTheme';
import { storeThemeManager } from './storeThemeManager';

// Legacy interfaces for backward compatibility
export interface ExtractedColorPalette {
  muted: string;
  vibrant: string;
  mutedDark: string;
  mutedLight: string;
  population: number;
  extractedAt: string;
  vibrantDark: string;
  vibrantLight: string;
}

export interface NormalizedTheme {
  primary_color: string;
  secondary_color: string;
  accent_color: string;
  background_color: string;
  text_color: string;
}

// Default theme using new AutoTheme format
export const DEFAULT_AUTO_THEME: AutoTheme = {
  primary: '#1976d2',
  secondary: '#dc004e', 
  accent: '#ff5722',
  background: '#ffffff',
  text: '#1a1a1a',
  surface: '#fafafa',
  onSurface: '#424242',
  primaryLight: '#42a5f5',
  primaryDark: '#1565c0',
  secondaryLight: '#f5325b',
  secondaryDark: '#9a0036',
};

// Legacy default for backward compatibility
export const DEFAULT_MINIMALIST_THEME: NormalizedTheme = {
  primary_color: DEFAULT_AUTO_THEME.primary,
  secondary_color: DEFAULT_AUTO_THEME.secondary,
  accent_color: DEFAULT_AUTO_THEME.accent,
  background_color: DEFAULT_AUTO_THEME.background,
  text_color: DEFAULT_AUTO_THEME.text,
};

/**
 * Convert extracted color palette to normalized theme (Legacy support)
 */
export const convertExtractedPaletteToTheme = (palette: ExtractedColorPalette): NormalizedTheme => {
  console.log('🎨 Converting extracted palette to theme (legacy):', palette);
  
  const normalizedTheme: NormalizedTheme = {
    primary_color: palette.vibrant || DEFAULT_MINIMALIST_THEME.primary_color,
    secondary_color: palette.muted || DEFAULT_MINIMALIST_THEME.secondary_color,
    accent_color: palette.vibrantDark || DEFAULT_MINIMALIST_THEME.accent_color,
    background_color: palette.mutedLight || DEFAULT_MINIMALIST_THEME.background_color,
    text_color: palette.mutedDark || DEFAULT_MINIMALIST_THEME.text_color,
  };
  
  console.log('✅ Converted to normalized theme (legacy):', normalizedTheme);
  return normalizedTheme;
};

/**
 * Convert AutoTheme to NormalizedTheme for backward compatibility
 */
export const convertAutoThemeToNormalized = (autoTheme: AutoTheme): NormalizedTheme => {
  return {
    primary_color: autoTheme.primary,
    secondary_color: autoTheme.secondary,
    accent_color: autoTheme.accent,
    background_color: autoTheme.background,
    text_color: autoTheme.text,
  };
};

/**
 * Helper function to adjust color lightness (moved up for use in convertNormalizedToAutoTheme)
 */
const adjustColorLightness = (hex: string, factor: number): string => {
  // Simple fallback - just return a lighter/darker version
  if (factor < 1) {
    // Make darker
    return hex.replace(/^#/, '#').replace(/(.{2})/g, (match) => {
      const val = Math.max(0, parseInt(match, 16) - 30);
      return val.toString(16).padStart(2, '0');
    });
  } else {
    // Make lighter  
    return hex.replace(/^#/, '#').replace(/(.{2})/g, (match) => {
      const val = Math.min(255, parseInt(match, 16) + 30);
      return val.toString(16).padStart(2, '0');
    });
  }
};

/**
 * Convert NormalizedTheme to AutoTheme
 */
export const convertNormalizedToAutoTheme = (normalized: NormalizedTheme): AutoTheme => {
  return {
    primary: normalized.primary_color,
    secondary: normalized.secondary_color,
    accent: normalized.accent_color,
    background: normalized.background_color,
    text: normalized.text_color,
    surface: '#fafafa',
    onSurface: '#424242',
    primaryLight: adjustColorLightness(normalized.primary_color, 1.2),
    primaryDark: adjustColorLightness(normalized.primary_color, 0.8),
    secondaryLight: adjustColorLightness(normalized.secondary_color, 1.2),
    secondaryDark: adjustColorLightness(normalized.secondary_color, 0.8),
  };
};

/**
 * Get the appropriate theme based on store data (Updated for AutoTheme)
 */
export const getStoreTheme = (storeData: any): NormalizedTheme => {
  console.log('🔍 Getting store theme from data:', storeData);
  
  // Check for new auto_theme format first
  const autoTheme = storeData?.auto_theme;
  if (autoTheme && autoTheme.primary) {
    console.log('🎯 Using auto theme format:', autoTheme);
    return convertAutoThemeToNormalized(autoTheme);
  }
  
  // Legacy: Check if we have store_color_palette from API
  const colorPalette = storeData?.store_color_palette;
  if (colorPalette) {
    console.log('📦 Found legacy color palette in store data:', colorPalette);
    
    // Check if it's the old extracted format
    if (colorPalette.vibrant && colorPalette.muted) {
      console.log('🎯 Using extracted color palette format (legacy)');
      return convertExtractedPaletteToTheme(colorPalette as ExtractedColorPalette);
    }
    
    // Check if it's already in normalized format
    if (colorPalette.primary_color) {
      console.log('🎯 Using normalized color palette format (legacy)');
      return {
        primary_color: colorPalette.primary_color || DEFAULT_MINIMALIST_THEME.primary_color,
        secondary_color: colorPalette.secondary_color || DEFAULT_MINIMALIST_THEME.secondary_color,
        accent_color: colorPalette.accent_color || DEFAULT_MINIMALIST_THEME.accent_color,
        background_color: colorPalette.background_color || DEFAULT_MINIMALIST_THEME.background_color,
        text_color: colorPalette.text_color || DEFAULT_MINIMALIST_THEME.text_color,
      };
    }
  }
  
  // Check fallback theme
  const theme = storeData?.theme;
  if (theme && theme.primary_color) {
    console.log('🎯 Using fallback theme');
    return {
      primary_color: theme.primary_color || DEFAULT_MINIMALIST_THEME.primary_color,
      secondary_color: theme.secondary_color || DEFAULT_MINIMALIST_THEME.secondary_color,
      accent_color: theme.accent_color || DEFAULT_MINIMALIST_THEME.accent_color,
      background_color: theme.background_color || DEFAULT_MINIMALIST_THEME.background_color,
      text_color: theme.text_color || DEFAULT_MINIMALIST_THEME.text_color,
    };
  }
  
  // Use default minimalist theme
  console.log('🎯 Using default minimalist theme');
  return DEFAULT_MINIMALIST_THEME;
};

/**
 * Get AutoTheme from store data (New simplified approach)
 */
export const getStoreAutoTheme = (storeData: any): AutoTheme | null => {
  console.log('🔍 Getting auto theme from store data:', storeData);
  
  // Check for auto_theme format
  const autoTheme = storeData?.auto_theme;
  if (autoTheme && autoTheme.primary) {
    console.log('✅ Found auto theme:', autoTheme);
    return autoTheme;
  }
  
  // Try to load from theme manager
  const storeHandle = storeData?.store_handle;
  if (storeHandle) {
    const storedTheme = storeThemeManager.getTheme(storeHandle);
    if (storedTheme) {
      console.log('✅ Found stored theme:', storedTheme);
      return storedTheme;
    }
  }
  
  console.log('⚠️ No auto theme found');
  return null;
};

/**
 * Preserve and restore scroll position during theme operations
 */
const preserveScrollPosition = (callback: () => void) => {
  if (typeof window !== 'undefined') {
    // Save current scroll position
    const scrollX = window.scrollX || window.pageXOffset;
    const scrollY = window.scrollY || window.pageYOffset;
    
    // Execute the callback
    callback();
    
    // Restore scroll position after a brief delay
    requestAnimationFrame(() => {
      window.scrollTo(scrollX, scrollY);
    });
  } else {
    callback();
  }
};

/**
 * Force clear all theme CSS variables (without affecting scroll position)
 */
export const clearAllThemeVariables = () => {
  if (typeof document !== 'undefined') {
    preserveScrollPosition(() => {
      const root = document.documentElement;
      
      console.log('🧹 Clearing all theme variables');
      
      // Remove store theme variables
      root.style.removeProperty('--store-primary-color');
      root.style.removeProperty('--store-secondary-color');
      root.style.removeProperty('--store-accent-color');
      root.style.removeProperty('--store-background-color');
      root.style.removeProperty('--store-text-color');
      
      // Remove UI component theme variables
      root.style.removeProperty('--theme-primary');
      root.style.removeProperty('--theme-secondary');
      root.style.removeProperty('--theme-accent');
      root.style.removeProperty('--theme-background');
      root.style.removeProperty('--theme-text');
      root.style.removeProperty('--theme-text-secondary');
      root.style.removeProperty('--theme-surface');
      root.style.removeProperty('--theme-hover');
      root.style.removeProperty('--theme-border');
      root.style.removeProperty('--theme-dark-muted');
      
      // Remove header-specific variables
      root.style.removeProperty('--header-bg');
      root.style.removeProperty('--header-text');
      root.style.removeProperty('--header-border');
      root.style.removeProperty('--header-search-bg');
      root.style.removeProperty('--header-search-border');
      
      // Remove button variables
      root.style.removeProperty('--btn-primary');
      root.style.removeProperty('--btn-primary-hover');
      root.style.removeProperty('--btn-text');
      
      console.log('✅ Theme variables cleared');
    });
  }
};

/**
 * Apply theme colors to CSS variables (Updated for AutoTheme)
 */
export const applyThemeColors = (storeData: any, storeHandle: string) => {
  if (typeof document !== 'undefined') {
    return preserveScrollPosition(() => {
      console.log(`🎨 Applying theme for store: ${storeHandle}`);
      
      // Try to get AutoTheme first
      const autoTheme = getStoreAutoTheme(storeData);
      if (autoTheme) {
        console.log('✅ Applying AutoTheme:', autoTheme);
        applyThemeToStore(autoTheme, storeHandle);
        return convertAutoThemeToNormalized(autoTheme);
      }
      
      // Fallback to legacy theme
      const legacyTheme = getStoreTheme(storeData);
      console.log('⚠️ Falling back to legacy theme:', legacyTheme);
      
      // Convert legacy theme to AutoTheme and apply
      const convertedAutoTheme = convertNormalizedToAutoTheme(legacyTheme);
      applyThemeToStore(convertedAutoTheme, storeHandle);
      
      return legacyTheme;
    });
  }
  
  return null;
};

/**
 * Apply AutoTheme directly (New simplified method)
 */
export const applyAutoTheme = (autoTheme: AutoTheme, storeHandle: string) => {
  if (typeof document !== 'undefined') {
    return preserveScrollPosition(() => {
      console.log(`🎨 Applying AutoTheme for store: ${storeHandle}`);
      applyThemeToStore(autoTheme, storeHandle);
      console.log('✅ AutoTheme applied successfully');
      return autoTheme;
    });
  }
  return null;
};

/**
 * Get current CSS variable values
 */
export const getCurrentThemeVariables = () => {
  if (typeof document !== 'undefined') {
    const root = document.documentElement;
    const computedStyle = getComputedStyle(root);
    
    return {
      // Store variables
      primaryColor: computedStyle.getPropertyValue('--store-primary-color').trim(),
      secondaryColor: computedStyle.getPropertyValue('--store-secondary-color').trim(),
      accentColor: computedStyle.getPropertyValue('--store-accent-color').trim(),
      backgroundColor: computedStyle.getPropertyValue('--store-background-color').trim(),
      textColor: computedStyle.getPropertyValue('--store-text-color').trim(),
      
      // UI component variables
      themePrimary: computedStyle.getPropertyValue('--theme-primary').trim(),
      themeSecondary: computedStyle.getPropertyValue('--theme-secondary').trim(),
      themeAccent: computedStyle.getPropertyValue('--theme-accent').trim(),
      themeBackground: computedStyle.getPropertyValue('--theme-background').trim(),
      themeText: computedStyle.getPropertyValue('--theme-text').trim(),
      
      // Button variables
      btnPrimary: computedStyle.getPropertyValue('--btn-primary').trim(),
      btnText: computedStyle.getPropertyValue('--btn-text').trim(),
      
      // Header variables
      headerBg: computedStyle.getPropertyValue('--header-bg').trim(),
      headerText: computedStyle.getPropertyValue('--header-text').trim(),
    };
  }
  return {};
};

/**
 * Clear localStorage theme data for a specific store (Updated)
 */
export const clearStoreThemeStorage = (storeHandle: string) => {
  if (typeof window !== 'undefined') {
    try {
      localStorage.removeItem(`store-theme-${storeHandle}`);
      localStorage.removeItem(`store-config-${storeHandle}`);
      
      // Clear from theme manager
      storeThemeManager.clearTheme(storeHandle);
      
      console.log(`🗑️ Cleared localStorage and theme manager for store: ${storeHandle}`);
    } catch (error) {
      console.warn('Error clearing localStorage:', error);
    }
  }
};

/**
 * Get current theme for a store (New method)
 */
export const getCurrentStoreTheme = (storeHandle: string): AutoTheme | null => {
  return storeThemeManager.getTheme(storeHandle);
};

/**
 * Check if store theme is initialized (New method)
 */
export const isStoreThemeInitialized = (storeHandle: string): boolean => {
  return storeThemeManager.isInitialized(storeHandle);
};

/**
 * Helper function to convert hex color to RGB
 */
export const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};

/**
 * Helper function to adjust color opacity
 */
export const adjustColorOpacity = (hex: string, opacity: number): string => {
  const rgb = hexToRgb(hex);
  if (!rgb) return hex;
  return `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${opacity})`;
};

/**
 * Helper function to adjust color lightness (enhanced version)
 */
export const adjustColorLightnessAdvanced = (hex: string, factor: number): string => {
  const rgb = hexToRgb(hex);
  if (!rgb) return hex;
  
  const adjust = (color: number) => {
    return Math.round(Math.min(255, Math.max(0, color + (255 - color) * (1 - factor))));
  };
  
  const newR = adjust(rgb.r);
  const newG = adjust(rgb.g);
  const newB = adjust(rgb.b);
  
  return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
};

/**
 * Helper function to get contrasting color (white or black)
 */
export const getContrastColor = (hex: string): string => {
  const rgb = hexToRgb(hex);
  if (!rgb) return '#ffffff';
  
  // Calculate luminance
  const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;
  
  // Return black for light colors, white for dark colors
  return luminance > 0.5 ? '#000000' : '#ffffff';
};

/**
 * Force refresh theme application (Updated for AutoTheme)
 */
export const forceRefreshTheme = (storeData: any, storeHandle: string) => {
  console.log('🔄 Force refreshing theme for store:', storeHandle);
  
  // Try AutoTheme first
  const autoTheme = getStoreAutoTheme(storeData);
  if (autoTheme) {
    const appliedTheme = applyAutoTheme(autoTheme, storeHandle);
    console.log('✅ AutoTheme force refreshed:', appliedTheme);
    return appliedTheme;
  }
  
  // Fallback to legacy theme
  const appliedTheme = applyThemeColors(storeData, storeHandle);
  console.log('✅ Legacy theme force refreshed:', appliedTheme);
  return appliedTheme;
};

/**
 * Initialize theme for store (New method)
 */
export const initializeStoreTheme = async (storeHandle: string, logoUrl?: string) => {
  console.log('🚀 Initializing theme for store:', storeHandle);
  
  try {
    await storeThemeManager.initializeTheme(storeHandle, logoUrl);
    console.log('✅ Theme initialized successfully');
  } catch (error) {
    console.error('❌ Error initializing theme:', error);
    applyDefaultTheme();
  }
};

/**
 * Update theme when logo changes (New method)
 */
export const updateStoreThemeFromLogo = async (storeHandle: string, logoUrl: string) => {
  console.log('🔄 Updating theme from logo for store:', storeHandle);
  
  try {
    const newTheme = await storeThemeManager.updateTheme(storeHandle, logoUrl);
    console.log('✅ Theme updated from logo:', newTheme);
    return newTheme;
  } catch (error) {
    console.error('❌ Error updating theme from logo:', error);
    return null;
  }
};

export const debugThemeState = (storeHandle: string, storeData: any) => {
  console.log('🐛 THEME DEBUG STATE (Updated)');
  console.log('Store Handle:', storeHandle);
  console.log('Store Data:', storeData);
  console.log('Auto Theme:', storeData?.auto_theme);
  console.log('Legacy Color Palette:', storeData?.store_color_palette);
  console.log('Fallback Theme:', storeData?.theme);
  
  // Check AutoTheme
  const autoTheme = getStoreAutoTheme(storeData);
  console.log('Resolved AutoTheme:', autoTheme);
  
  // Check legacy theme
  const legacyTheme = getStoreTheme(storeData);
  console.log('Resolved Legacy Theme:', legacyTheme);
  
  console.log('Current CSS Variables:', getCurrentThemeVariables());
  
  // Check theme manager
  const managerTheme = storeThemeManager.getTheme(storeHandle);
  console.log('Theme Manager Theme:', managerTheme);
  console.log('Theme Manager Initialized:', storeThemeManager.isInitialized(storeHandle));
  
  // Check localStorage
  if (typeof window !== 'undefined') {
    try {
      const storedTheme = localStorage.getItem(`store-theme-${storeHandle}`);
      console.log('Stored Theme:', storedTheme ? JSON.parse(storedTheme) : 'None');
    } catch (error) {
      console.log('Error reading stored theme:', error);
    }
  }
};