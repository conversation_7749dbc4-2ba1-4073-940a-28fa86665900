/**
 * Generates a store handle from store name
 * Converts to lowercase, replaces spaces with hyphens, removes special characters
 */
export function generateStoreHandle(storeName: string): string {
  return storeName
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-+|-+$/g, ''); // Remove leading and trailing hyphens
}

/**
 * Validates if a store handle is available (placeholder function)
 * In a real app, this would make an API call to check availability
 */
export async function checkStoreHandleAvailability(handle: string): Promise<boolean> {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // For demo purposes, consider some handles as taken
  const takenHandles = ['admin', 'api', 'www', 'mail', 'support', 'help', 'store', 'shop'];
  return !takenHandles.includes(handle.toLowerCase());
}