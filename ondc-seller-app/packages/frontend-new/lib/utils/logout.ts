import { useAuthStore } from '@/stores/authStore';
import { clearStoreAuthStore } from '@/stores/storeAuthStore';
import { clearStoreConfigStore } from '@/stores/storeConfigStore';

// Global loading functions - we'll import these from the provider
let globalStartLoading: any = null;
let globalStopLoading: any = null;

// Function to set global loading functions
export const setGlobalLoadingFunctions = (startLoading: any, stopLoading: any) => {
  globalStartLoading = startLoading;
  globalStopLoading = stopLoading;
};

export const performLogout = (router: any, storeHandle?: string) => {
  console.log('=== PERFORMING COMPLETE LOGOUT ===');
  console.log('Store handle:', storeHandle);
  
  // Start logout loading
  if (globalStartLoading) {
    globalStartLoading('navigation', 'Signing you out...', {
      subMessage: 'Please wait while we securely log you out',
      actionId: 'user-logout'
    });
  }
  
  try {
    // Step 1: Clear main auth store
    const { clearAuth, getStoreHandle } = useAuthStore.getState();
    
    // Get store handle from auth store if not provided
    const currentStoreHandle = storeHandle || getStoreHandle();
    console.log('Current store handle for cleanup:', currentStoreHandle);
    
    // Clear main auth store and localStorage
    clearAuth();
    
    // Step 2: Clear store-specific auth store if we have a store handle
    if (currentStoreHandle) {
      try {
        clearStoreAuthStore(currentStoreHandle);
        console.log(`Cleared store auth for: ${currentStoreHandle}`);
      } catch (error) {
        console.error(`Error clearing store auth for ${currentStoreHandle}:`, error);
      }
      
      // Step 3: Clear store-specific config store
      try {
        clearStoreConfigStore(currentStoreHandle);
        console.log(`Cleared store config for: ${currentStoreHandle}`);
      } catch (error) {
        console.error(`Error clearing store config for ${currentStoreHandle}:`, error);
      }
    }
    
    // Step 4: Additional cleanup - clear any remaining items
    if (typeof window !== 'undefined') {
      // Clear session storage
      try {
        sessionStorage.clear();
        console.log('Session storage cleared');
      } catch (error) {
        console.error('Error clearing session storage:', error);
      }
      
      // Clear localStorage items that might be store-specific
      try {
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (
            key.includes('-auth') || 
            key.includes('-config') || 
            key.includes('store-theme') ||
            key.includes('ondc_auth_token') ||
            (currentStoreHandle && key.includes(currentStoreHandle))
          )) {
            keysToRemove.push(key);
          }
        }
        
        keysToRemove.forEach(key => {
          localStorage.removeItem(key);
          console.log(`Removed localStorage key: ${key}`);
        });
        
        console.log('Store-specific localStorage items cleared');
      } catch (error) {
        console.error('Error clearing store-specific localStorage:', error);
      }
      
      // Clear any cookies (if you're using them)
      try {
        document.cookie.split(";").forEach((c) => {
          document.cookie = c
            .replace(/^ +/, "")
            .replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
        });
        console.log('Cookies cleared');
      } catch (error) {
        console.error('Error clearing cookies:', error);
      }
    }
    
    // Step 5: Update loading message and redirect
    if (globalStartLoading) {
      globalStartLoading('navigation', 'Redirecting to login...', {
        subMessage: 'Taking you to the login screen',
        actionId: 'user-logout'
      });
    }
    
    console.log('Redirecting to login page');
    
    // Add a small delay to show the logout message
    setTimeout(() => {
      router.push('/login?tab=login');
      
      // Note: Login page will stop the logout loading when it's ready
      // Safety timeout in case login page doesn't load
      setTimeout(() => {
        if (globalStopLoading) {
          globalStopLoading('user-logout');
        }
      }, 5000); // Longer safety timeout
    }, 800);
    
    console.log('=== LOGOUT COMPLETED SUCCESSFULLY ===');
    
    return true;
  } catch (error) {
    console.error('Error during logout process:', error);
    
    // Stop loading on error
    if (globalStopLoading) {
      globalStopLoading('user-logout');
    }
    
    // Fallback: still try to redirect
    try {
      router.push('/login?tab=login');
    } catch (routerError) {
      console.error('Error redirecting to login:', routerError);
      // Last resort: reload the page to clear everything
      if (typeof window !== 'undefined') {
        window.location.href = '/login?tab=login';
      }
    }
    
    return false;
  }
};

// Hook version for use in components
export const useLogout = () => {
  const { clearAuth, getStoreHandle } = useAuthStore();
  
  return (router: any, storeHandle?: string) => {
    const currentStoreHandle = storeHandle || getStoreHandle();
    return performLogout(router, currentStoreHandle);
  };
};

// Admin-specific logout function that ensures store handle is passed
export const performAdminLogout = (router: any, storeHandle: string) => {
  console.log('=== PERFORMING ADMIN LOGOUT ===');
  console.log('Admin store handle:', storeHandle);
  
  return performLogout(router, storeHandle);
};