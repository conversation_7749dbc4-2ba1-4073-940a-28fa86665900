/**
 * Store Theme Manager
 * Centralized theme management for all store screens
 */

import { AutoTheme, autoGenerateAndApplyTheme, loadStoredTheme, applyDefaultTheme } from './simpleTheme';

export interface StoreThemeManager {
  initializeTheme: (storeHandle: string, logoUrl?: string) => Promise<AutoTheme | null>;
  updateTheme: (storeHandle: string, logoUrl: string) => Promise<AutoTheme | null>;
  getTheme: (storeHandle: string) => AutoTheme | null;
  clearTheme: (storeHandle: string) => void;
  applyThemeToAllScreens: (theme: AutoTheme) => void;
}

/**
 * Global theme manager instance
 */
class ThemeManager implements StoreThemeManager {
  private themes: Map<string, AutoTheme> = new Map();
  private initialized: Set<string> = new Set();

  /**
   * Initialize theme for a store
   */
  async initializeTheme(storeHandle: string, logoUrl?: string): Promise<AutoTheme | null> {
    try {
      console.log('🎨 Initializing theme for store:', storeHandle);

      // Check if already initialized
      if (this.initialized.has(storeHandle)) {
        const existingTheme = this.themes.get(storeHandle);
        if (existingTheme) {
          console.log('✅ Theme already initialized:', existingTheme);
          return existingTheme;
        }
      }

      // Try to load stored theme first
      const storedTheme = loadStoredTheme(storeHandle);
      if (storedTheme) {
        console.log('✅ Loaded stored theme:', storedTheme);
        this.themes.set(storeHandle, storedTheme);
        this.applyThemeToAllScreens(storedTheme);
        this.initialized.add(storeHandle);
        return storedTheme;
      }

      // Generate theme from logo if available
      if (logoUrl) {
        console.log('🎨 Generating theme from logo:', logoUrl);
        const generatedTheme = await autoGenerateAndApplyTheme(logoUrl, storeHandle);
        this.themes.set(storeHandle, generatedTheme);
        this.applyThemeToAllScreens(generatedTheme);
        this.initialized.add(storeHandle);
        return generatedTheme;
      }

      // Fallback to default theme
      console.log('⚠️ No logo available, using default theme');
      applyDefaultTheme();
      this.initialized.add(storeHandle);
      return null;

    } catch (error) {
      console.error('❌ Error initializing theme:', error);
      applyDefaultTheme();
      return null;
    }
  }

  /**
   * Update theme with new logo
   */
  async updateTheme(storeHandle: string, logoUrl: string): Promise<AutoTheme | null> {
    try {
      console.log('🔄 Updating theme for store:', storeHandle, 'with logo:', logoUrl);

      const generatedTheme = await autoGenerateAndApplyTheme(logoUrl, storeHandle);
      this.themes.set(storeHandle, generatedTheme);
      this.applyThemeToAllScreens(generatedTheme);

      // Notify other components about theme change
      this.notifyThemeChange(storeHandle, generatedTheme);

      console.log('✅ Theme updated successfully:', generatedTheme);
      return generatedTheme;

    } catch (error) {
      console.error('❌ Error updating theme:', error);
      return null;
    }
  }

  /**
   * Get current theme for a store
   */
  getTheme(storeHandle: string): AutoTheme | null {
    return this.themes.get(storeHandle) || null;
  }

  /**
   * Clear theme for a store
   */
  clearTheme(storeHandle: string): void {
    this.themes.delete(storeHandle);
    this.initialized.delete(storeHandle);
    
    // Clear from localStorage
    if (typeof window !== 'undefined') {
      try {
        localStorage.removeItem(`store-theme-${storeHandle}`);
      } catch (error) {
        console.warn('Failed to clear theme from localStorage:', error);
      }
    }

    // Apply default theme
    applyDefaultTheme();
    console.log('🗑️ Theme cleared for store:', storeHandle);
  }

  /**
   * Apply theme to all store screens
   */
  applyThemeToAllScreens(theme: AutoTheme): void {
    if (typeof document === 'undefined') return;

    console.log('🎨 Applying theme to all screens:', theme);

    const root = document.documentElement;

    // Apply all theme variables
    Object.entries(theme).forEach(([key, value]) => {
      const cssVar = `--store-${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
      root.style.setProperty(cssVar, value);
    });

    // Apply additional computed variables
    root.style.setProperty('--store-primary-rgb', this.hexToRgb(theme.primary));
    root.style.setProperty('--store-secondary-rgb', this.hexToRgb(theme.secondary));
    root.style.setProperty('--store-accent-rgb', this.hexToRgb(theme.accent));

    // Apply to body class for global styling
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add('theme-applied');

    // Trigger custom event for components to react to theme changes
    const themeChangeEvent = new CustomEvent('storeThemeChanged', {
      detail: { theme }
    });
    window.dispatchEvent(themeChangeEvent);

    console.log('✅ Theme applied to all screens');
  }

  /**
   * Notify components about theme changes
   */
  private notifyThemeChange(storeHandle: string, theme: AutoTheme): void {
    // Update localStorage to trigger storage event
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(`store-theme-${storeHandle}`, JSON.stringify(theme));
      } catch (error) {
        console.warn('Failed to save theme to localStorage:', error);
      }
    }

    // Dispatch custom event
    if (typeof window !== 'undefined') {
      const event = new CustomEvent('storeThemeUpdated', {
        detail: { storeHandle, theme }
      });
      window.dispatchEvent(event);
    }
  }

  /**
   * Convert hex to RGB string for CSS variables
   */
  private hexToRgb(hex: string): string {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `${r}, ${g}, ${b}`;
  }

  /**
   * Get all initialized themes
   */
  getAllThemes(): Map<string, AutoTheme> {
    return new Map(this.themes);
  }

  /**
   * Check if theme is initialized for a store
   */
  isInitialized(storeHandle: string): boolean {
    return this.initialized.has(storeHandle);
  }
}

// Global theme manager instance
export const storeThemeManager = new ThemeManager();

/**
 * React hook for theme manager
 */
export const useStoreThemeManager = () => {
  return {
    initializeTheme: storeThemeManager.initializeTheme.bind(storeThemeManager),
    updateTheme: storeThemeManager.updateTheme.bind(storeThemeManager),
    getTheme: storeThemeManager.getTheme.bind(storeThemeManager),
    clearTheme: storeThemeManager.clearTheme.bind(storeThemeManager),
    applyThemeToAllScreens: storeThemeManager.applyThemeToAllScreens.bind(storeThemeManager),
    getAllThemes: storeThemeManager.getAllThemes.bind(storeThemeManager),
    isInitialized: storeThemeManager.isInitialized.bind(storeThemeManager),
  };
};

/**
 * Initialize theme for store pages
 */
export const initializeStoreTheme = async (storeHandle: string, logoUrl?: string): Promise<void> => {
  try {
    await storeThemeManager.initializeTheme(storeHandle, logoUrl);
  } catch (error) {
    console.error('Failed to initialize store theme:', error);
  }
};

/**
 * Update theme when logo changes
 */
export const updateStoreTheme = async (storeHandle: string, logoUrl: string): Promise<void> => {
  try {
    await storeThemeManager.updateTheme(storeHandle, logoUrl);
  } catch (error) {
    console.error('Failed to update store theme:', error);
  }
};

/**
 * Apply theme to current page
 */
export const applyCurrentTheme = (storeHandle: string): void => {
  const theme = storeThemeManager.getTheme(storeHandle);
  if (theme) {
    storeThemeManager.applyThemeToAllScreens(theme);
  } else {
    applyDefaultTheme();
  }
};

/**
 * Listen for theme changes across tabs/windows
 */
export const listenForThemeChanges = (callback: (theme: AutoTheme) => void): (() => void) => {
  const handleThemeChange = (event: CustomEvent<{ theme: AutoTheme }>) => {
    callback(event.detail.theme);
  };

  const handleStorageChange = (event: StorageEvent) => {
    if (event.key?.startsWith('store-theme-') && event.newValue) {
      try {
        const theme: AutoTheme = JSON.parse(event.newValue);
        callback(theme);
      } catch (error) {
        console.error('Error parsing theme from storage:', error);
      }
    }
  };

  // Listen for custom events
  window.addEventListener('storeThemeChanged', handleThemeChange as EventListener);
  window.addEventListener('storeThemeUpdated', handleThemeChange as EventListener);
  
  // Listen for storage changes (cross-tab)
  window.addEventListener('storage', handleStorageChange);

  // Return cleanup function
  return () => {
    window.removeEventListener('storeThemeChanged', handleThemeChange as EventListener);
    window.removeEventListener('storeThemeUpdated', handleThemeChange as EventListener);
    window.removeEventListener('storage', handleStorageChange);
  };
};