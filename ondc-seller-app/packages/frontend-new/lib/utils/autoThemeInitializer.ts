/**
 * Auto Theme Initializer Utility
 * Global utility functions for initializing themes across the application
 */

import { autoGenerateAndApplyTheme } from './simpleTheme';
import { storeThemeManager } from './storeThemeManager';

export interface StoreDataSource {
  logo?: string;
  store_logo_url?: string;
  store_logo?: string;
}

export interface AutoThemeInitResult {
  success: boolean;
  themeApplied: boolean;
  logoSource: 'zustand' | 'localStorage' | 'strapi' | 'manual' | null;
  logoUrl: string | null;
  error: string | null;
}

/**
 * Initialize theme from multiple data sources
 */
export const initializeThemeFromSources = async (
  storeHandle: string,
  sources: {
    zustandData?: StoreDataSource;
    localStorageKey?: string;
    manualLogoUrl?: string;
  }
): Promise<AutoThemeInitResult> => {
  console.log('🎨 Initializing theme from sources for store:', storeHandle);
  
  let logoUrl: string | null = null;
  let logoSource: 'zustand' | 'localStorage' | 'strapi' | 'manual' | null = null;

  try {
    // 1. Check manual logo URL first (highest priority)
    if (sources.manualLogoUrl) {
      logoUrl = sources.manualLogoUrl;
      logoSource = 'manual';
      console.log('✅ Using manual logo URL:', logoUrl);
    }

    // 2. Check Zustand data
    if (!logoUrl && sources.zustandData) {
      logoUrl = sources.zustandData.logo || 
                sources.zustandData.store_logo_url || 
                sources.zustandData.store_logo;
      if (logoUrl) {
        logoSource = 'zustand';
        console.log('✅ Found logo in Zustand data:', logoUrl);
      }
    }

    // 3. Check localStorage
    if (!logoUrl && sources.localStorageKey && typeof window !== 'undefined') {
      try {
        const storedData = localStorage.getItem(sources.localStorageKey);
        if (storedData) {
          const parsedData = JSON.parse(storedData);
          logoUrl = parsedData.storeData?.logo || 
                   parsedData.storeData?.store_logo_url ||
                   parsedData.logo ||
                   parsedData.store_logo_url;
          if (logoUrl) {
            logoSource = 'localStorage';
            console.log('✅ Found logo in localStorage:', logoUrl);
          }
        }
      } catch (error) {
        console.warn('⚠️ Error reading from localStorage:', error);
      }
    }

    // 4. Generate and apply theme if logo found
    if (logoUrl && logoSource) {
      console.log(`🎨 Generating theme from logo (source: ${logoSource}):`, logoUrl);
      
      const theme = await autoGenerateAndApplyTheme(logoUrl, storeHandle);
      
      if (theme) {
        console.log('✅ Theme generated and applied successfully:', theme);
        return {
          success: true,
          themeApplied: true,
          logoSource,
          logoUrl,
          error: null,
        };
      } else {
        console.warn('⚠️ Failed to generate theme from logo');
        return {
          success: false,
          themeApplied: false,
          logoSource,
          logoUrl,
          error: 'Failed to generate theme from logo',
        };
      }
    } else {
      console.log('ℹ️ No logo found in any source');
      return {
        success: true,
        themeApplied: false,
        logoSource: null,
        logoUrl: null,
        error: 'No logo found for theme generation',
      };
    }

  } catch (error: any) {
    console.error('❌ Error initializing theme:', error);
    return {
      success: false,
      themeApplied: false,
      logoSource,
      logoUrl,
      error: error.message || 'Unknown error occurred',
    };
  }
};

/**
 * Quick theme initialization from localStorage store config
 */
export const quickInitFromLocalStorage = async (storeHandle: string): Promise<AutoThemeInitResult> => {
  return initializeThemeFromSources(storeHandle, {
    localStorageKey: `store-config-${storeHandle}`,
  });
};

/**
 * Initialize theme from Zustand store data
 */
export const initFromZustandStore = async (
  storeHandle: string, 
  storeData: StoreDataSource
): Promise<AutoThemeInitResult> => {
  return initializeThemeFromSources(storeHandle, {
    zustandData: storeData,
  });
};

/**
 * Initialize theme with manual logo URL
 */
export const initFromManualLogo = async (
  storeHandle: string, 
  logoUrl: string
): Promise<AutoThemeInitResult> => {
  return initializeThemeFromSources(storeHandle, {
    manualLogoUrl: logoUrl,
  });
};

/**
 * Comprehensive theme initialization (tries all sources)
 */
export const comprehensiveThemeInit = async (
  storeHandle: string,
  options: {
    zustandData?: StoreDataSource;
    manualLogoUrl?: string;
    skipLocalStorage?: boolean;
  } = {}
): Promise<AutoThemeInitResult> => {
  console.log('🚀 Starting comprehensive theme initialization for:', storeHandle);

  return initializeThemeFromSources(storeHandle, {
    manualLogoUrl: options.manualLogoUrl,
    zustandData: options.zustandData,
    localStorageKey: options.skipLocalStorage ? undefined : `store-config-${storeHandle}`,
  });
};

/**
 * Check if theme is already applied for a store
 */
export const isThemeAlreadyApplied = (storeHandle: string): boolean => {
  return storeThemeManager.isInitialized(storeHandle);
};

/**
 * Get current theme status for a store
 */
export const getThemeStatus = (storeHandle: string) => {
  const isInitialized = storeThemeManager.isInitialized(storeHandle);
  const currentTheme = storeThemeManager.getTheme(storeHandle);
  
  return {
    isInitialized,
    hasTheme: !!currentTheme,
    currentTheme,
  };
};

/**
 * Auto-initialize theme on page load (can be called from layout or page components)
 */
export const autoInitializeOnPageLoad = async (storeHandle: string): Promise<void> => {
  if (!storeHandle) {
    console.log('⚠️ No store handle provided for auto-initialization');
    return;
  }

  // Skip if already initialized
  if (isThemeAlreadyApplied(storeHandle)) {
    console.log('✅ Theme already applied for store:', storeHandle);
    return;
  }

  console.log('🎨 Auto-initializing theme on page load for:', storeHandle);

  try {
    const result = await quickInitFromLocalStorage(storeHandle);
    
    if (result.success && result.themeApplied) {
      console.log('✅ Theme auto-initialized successfully from:', result.logoSource);
    } else {
      console.log('ℹ️ No theme auto-initialized:', result.error);
    }
  } catch (error) {
    console.warn('⚠️ Error in auto-initialization:', error);
  }
};