/**
 * API Fallback Utility
 * Provides mock data when API calls fail or return empty responses
 */

import { Store, Product, ProductCategory } from '@/types';
import { demoStore, demoProducts, demoCategories } from '../demo-data';

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  source: 'api' | 'mock';
  message?: string;
}

/**
 * Wrapper function that handles API calls with automatic fallback to mock data
 */
export async function withMockFallback<T>(
  apiCall: () => Promise<T>,
  mockData: T,
  context: string
): Promise<ApiResponse<T>> {
  try {
    const result = await apiCall();
    
    // Check if result is empty or null
    if (!result || (Array.isArray(result) && result.length === 0)) {
      console.warn(`API returned empty data for ${context}, using mock data`);
      return {
        data: mockData,
        success: true,
        source: 'mock',
        message: `Empty API response, using mock data for ${context}`
      };
    }
    
    return {
      data: result,
      success: true,
      source: 'api'
    };
  } catch (error) {
    console.error(`API error for ${context}, using mock data:`, error);
    return {
      data: mockData,
      success: true,
      source: 'mock',
      message: `API error, using mock data for ${context}`
    };
  }
}

/**
 * Check if data is empty or invalid
 */
export function isEmptyResponse<T>(data: T): boolean {
  if (!data) return true;
  if (Array.isArray(data) && data.length === 0) return true;
  if (typeof data === 'object' && Object.keys(data).length === 0) return true;
  return false;
}

/**
 * Get mock store data for a specific handle
 */
export function getMockStore(handle: string): Store {
  return {
    ...demoStore,
    handle,
    name: `${handle.charAt(0).toUpperCase() + handle.slice(1)} Store`,
    description: `Welcome to ${handle} - your trusted online store for quality products.`
  };
}

/**
 * Get mock products with store-specific variations
 */
export function getMockProducts(storeHandle: string, limit?: number): Product[] {
  const products = demoProducts.map(product => ({
    ...product,
    id: `${storeHandle}-${product.id}`,
    title: `${storeHandle} ${product.title}`
  }));
  
  return limit ? products.slice(0, limit) : products;
}

/**
 * Get mock categories with store-specific variations
 */
export function getMockCategories(storeHandle: string): ProductCategory[] {
  return demoCategories.map(category => ({
    ...category,
    id: `${storeHandle}-${category.id}`,
    handle: `${storeHandle}-${category.handle}`
  }));
}

/**
 * Development mode flag to force mock data usage
 */
export const FORCE_MOCK_DATA = process.env.NODE_ENV === 'development' && 
  process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true';

/**
 * Check if we should use mock data based on environment
 */
export function shouldUseMockData(): boolean {
  return FORCE_MOCK_DATA || 
    !process.env.NEXT_PUBLIC_MEDUSA_STORE_API_URL || 
    !process.env.NEXT_PUBLIC_STRAPI_BASE_URL;
}

/**
 * Log API usage for debugging
 */
export function logApiUsage(context: string, source: 'api' | 'mock', data?: any) {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[${context.toUpperCase()}] Using ${source} data:`, {
      source,
      dataType: Array.isArray(data) ? `array(${data.length})` : typeof data,
      timestamp: new Date().toISOString()
    });
  }
}