import { z } from 'zod';

// User Profile Update Schema
export const userProfileUpdateSchema = z.object({
  first_name: z
    .string()
    .min(1, 'First name is required')
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters')
    .regex(/^[a-zA-Z\s]+$/, 'First name can only contain letters and spaces'),
  
  last_name: z
    .string()
    .min(1, 'Last name is required')
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters')
    .regex(/^[a-zA-Z\s]+$/, 'Last name can only contain letters and spaces'),
  
  metadata: z.object({
    profile_image: z
      .string()
      .url('Profile image must be a valid URL')
      .optional()
      .or(z.literal('')),
    
    contact_number: z
      .string()
      .regex(/^[+]?[\d\s\-\(\)]+$/, 'Contact number must be a valid phone number')
      .refine((val) => {
        if (!val || val === '') return true; // Allow empty for optional field
        // Extract only digits from the phone number
        const digitsOnly = val.replace(/\D/g, '');
        return digitsOnly.length === 10;
      }, 'Contact number must be exactly 10 digits')
      .optional()
      .or(z.literal('')),
    
    bio: z
      .string()
      .max(500, 'Bio must be less than 500 characters')
      .optional()
      .or(z.literal(''))
  })
});

// Type inference for TypeScript
export type UserProfileUpdateData = z.infer<typeof userProfileUpdateSchema>;

// Validation function with detailed error handling
export const validateUserProfile = (data: unknown) => {
  return userProfileUpdateSchema.safeParse(data);
};

// Helper function to format validation errors
export const formatValidationErrors = (errors: z.ZodError) => {
  const formattedErrors: Record<string, string> = {};
  
  errors.errors.forEach((error) => {
    const path = error.path.join('.');
    formattedErrors[path] = error.message;
  });
  
  return formattedErrors;
};

// Helper function to extract field-specific errors
export const getFieldError = (errors: z.ZodError, fieldPath: string): string | undefined => {
  const error = errors.errors.find(err => err.path.join('.') === fieldPath);
  return error?.message;
};