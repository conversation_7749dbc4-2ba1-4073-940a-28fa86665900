import { z } from 'zod';

// Product Option Schema
export const productOptionSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Option name is required'),
  values: z.array(z.string().min(1, 'Option value cannot be empty')).min(1, 'At least one option value is required'),
});

// Product Variant Schema
export const productVariantSchema = z.object({
  id: z.string(),
  title: z.string().min(1, 'Variant title is required'),
  sku: z.string().min(1, 'SKU is required'),
  height: z.number().min(0, 'Height must be positive').optional(),
  weight: z.number().min(0, 'Weight must be positive').optional(),
  length: z.number().min(0, 'Length must be positive').optional(),
  width: z.number().min(0, 'Width must be positive').optional(),
  originalPrice: z.number().min(0, 'Original price must be positive'),
  salePrice: z.number().min(0, 'Sale price must be positive'),
  stock: z.number().min(0, 'Stock must be positive'),
  stockStatus: z.enum(['in_stock', 'out_of_stock', 'low_stock']),
  options: z.record(z.string(), z.string()).optional(), // e.g., { color: 'red', size: 'M' }
});

// Product Image Schema
export const productImageSchema = z.object({
  id: z.string(),
  url: z.string().url('Invalid image URL'),
  alt: z.string().optional(),
  file: z.any().optional(), // For file uploads
});

// Main Product Schema
export const productSchema = z.object({
  // Basic Information
  productName: z.string().optional(),
  productHandle: z.string().optional(),
  productDescription: z.string().optional(),
  productSubtitle: z.string().optional(),
  
  // Categories and Collections
  productCategory: z.string().optional(),
  productCollection: z.string().optional(),
  productTags: z.array(z.string()).optional(),
  
  // Images
  productImages: z.array(productImageSchema).optional(),
  productThumbnail: productImageSchema.optional(),
  
  // Options and Variants
  productOptions: z.array(productOptionSchema).optional(),
  productVariants: z.array(productVariantSchema).optional(),
  
  // Pricing (derived from first variant)
  productOriginalPrice: z.number().min(0, 'Original price must be positive').optional(),
  productSalePrice: z.number().min(0, 'Sale price must be positive').optional(),
  productStock: z.number().min(0, 'Stock must be positive').optional(),
  
  // Rich Text Content
  productOverview: z.string().optional(),
  productFeatures: z.string().optional(),
  productSpecifications: z.string().optional(),
  
  // Status
  status: z.enum(['draft', 'published', 'archived']).default('draft'),
});

export type ProductFormData = z.infer<typeof productSchema>;
export type ProductOption = z.infer<typeof productOptionSchema>;
export type ProductVariant = z.infer<typeof productVariantSchema>;
export type ProductImage = z.infer<typeof productImageSchema>;

// Validation helpers
export const validateProduct = (data: unknown) => {
  return productSchema.safeParse(data);
};

export const validateProductVariant = (data: unknown) => {
  return productVariantSchema.safeParse(data);
};

export const validateProductOption = (data: unknown) => {
  return productOptionSchema.safeParse(data);
};