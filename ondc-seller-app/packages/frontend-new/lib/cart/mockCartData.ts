import { CartItem } from '@/types/cart';

// Mock cart items for demonstration
export const mockCartItems: CartItem[] = [
  {
    id: 'cart-item-1',
    productId: 'prod-1',
    title: 'Elegant Summer Dress',
    image: '/images/demo-product-1.jpg',
    price: 7999, // Price in cents
    quantity: 1,
    variant: {
      id: 'var-1',
      title: 'Small / Blue',
      options: {
        size: 'Small',
        color: 'Blue'
      }
    },
    maxQuantity: 10
  },
  {
    id: 'cart-item-2',
    productId: 'prod-2',
    title: 'Classic Denim Jacket',
    image: '/images/demo-product-2.jpg',
    price: 12999,
    quantity: 2,
    variant: {
      id: 'var-3',
      title: 'Medium / Light Blue',
      options: {
        size: 'Medium',
        wash: 'Light Blue'
      }
    },
    maxQuantity: 8
  },
  {
    id: 'cart-item-3',
    productId: 'prod-3',
    title: 'Leather Crossbody Bag',
    image: '/images/demo-product-3.jpg',
    price: 8999,
    quantity: 1,
    variant: {
      id: 'var-4',
      title: 'One Size / Black',
      options: {
        size: 'One Size',
        color: 'Black'
      }
    },
    maxQuantity: 12
  },
  {
    id: 'cart-item-4',
    productId: 'prod-5',
    title: 'Casual Cotton T-Shirt',
    image: '/images/demo-product-5.jpg',
    price: 2999,
    quantity: 3,
    variant: {
      id: 'var-6',
      title: 'Medium / Navy',
      options: {
        size: 'Medium',
        color: 'Navy'
      }
    },
    maxQuantity: 50
  }
];

// Calculate totals
export const calculateCartTotals = (items: CartItem[]) => {
  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
  const totalAmount = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  
  return {
    totalItems,
    totalAmount,
    subtotal: totalAmount,
    tax: Math.round(totalAmount * 0.08), // 8% tax
    shipping: totalAmount > 5000 ? 0 : 50, // Free shipping over $50
    finalTotal: totalAmount + Math.round(totalAmount * 0.08) + (totalAmount > 5000 ? 0 : 50)
  };
};

// Format price helper
export const formatPrice = (priceInCents: number, currency: string = 'INR'): string => {
  const price = priceInCents;
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(price);
};