// lib/api/index.ts
// Main API exports with clear separation and naming conventions

// ===== MEDUSA ADMIN API SERVICES =====
export * from './services/medusa-admin-auth';

// ===== MEDUSA STORE API SERVICES =====
export * from './services/medusa-store-auth';

// ===== STRAPI API SERVICES =====
export * from './services/strapi-store-config';
export * from './services/strapi-banners';

// ===== API CLIENTS =====
export { medusaAdminClient } from './clients/medusa-admin-client';
export { medusaStoreClient } from './clients/medusa-store-client';
export { strapiClient } from './clients/strapi-client';

// ===== LEGACY EXPORTS (for backward compatibility) =====
// These can be gradually removed as components are updated
export { medusaClient, strapiClient as strapiClientLegacy } from './client';

// ===== NAMING CONVENTION GUIDE =====
/*
All API functions follow this naming pattern:

MEDUSA ADMIN API:
- adminLogin()
- adminSignup()
- adminGetUserDetails()
- adminUpdateOnboardingStatus()
- adminCompleteOnboarding()
- adminUpdateUserProfile()

MEDUSA STORE API:
- storeCustomerLogin()
- storeCustomerRegister()
- storeGetCustomerDetails()
- storeUpdateCustomerProfile()
- storeGetCustomerAddresses()
- storeCreateCustomerAddress()
- storeUpdateCustomerAddress()
- storeDeleteCustomerAddress()

STRAPI API:
- strapiSaveStoreConfiguration()
- strapiUpdateStoreConfiguration()
- strapiGetStoreConfigurationByHandle()
- strapiGetStoreConfigurationByUserId()
- strapiGetStoreConfigurationByDocumentId()
- strapiDeleteStoreConfiguration()
- strapiGetBanners()
- strapiGetBannerByDocumentId()
- strapiCreateBanner()
- strapiUpdateBannerByDocumentId()
- strapiDeleteBannerByDocumentId()
- strapiUploadFile()
- strapiGetActiveBannersForStore()

This ensures:
1. No function name overlap between services
2. Clear identification of which API is being used
3. Consistent naming patterns within each service
4. Easy to understand and maintain
*/