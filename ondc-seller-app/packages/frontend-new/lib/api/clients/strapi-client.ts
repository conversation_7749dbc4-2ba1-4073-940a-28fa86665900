// lib/api/clients/strapi-client.ts
import { handleUnauthorizedWithSmartRedirect } from '../../utils/authUtils';

// Create a dedicated Strapi API client using fetch
export const createStrapiClient = () => {
  const baseURL = process.env.NEXT_PUBLIC_STRAPI_API_URL || process.env.NEXT_PUBLIC_STRAPI_BASE_URL + '/api';
  
  if (!baseURL) {
    throw new Error('Strapi API URL not configured. Please set NEXT_PUBLIC_STRAPI_API_URL or NEXT_PUBLIC_STRAPI_BASE_URL');
  }

  const request = async <T>(
    endpoint: string,
    options: RequestInit = {},
    storeHandle?: string
  ): Promise<T> => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    try {
      const url = `${baseURL}${endpoint}`;
      
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${config.strapiAccessToken}`,
          ...options.headers,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Handle 401 Unauthorized responses
      if (response.status === 401) {
        console.log('🚨 Strapi API returned 401 Unauthorized:', endpoint);
        handleUnauthorizedWithSmartRedirect(storeHandle);
        
        const errorMessage = 'Authentication failed - redirecting to login';
        throw new Error(errorMessage);
      }

      if (!response.ok) {
        let errorMessage = 'Strapi API error';
        try {
          const error = await response.json();
          errorMessage = error.message || `HTTP ${response.status}: ${response.statusText}`;
        } catch {
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      return response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout - API took too long to respond');
      }
      throw error;
    }
  };

  // Helper method for file uploads
  const uploadFile = async (
    file: File,
    endpoint: string = '/upload',
    storeHandle?: string
  ): Promise<any> => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout for uploads

    try {
      const url = `${baseURL}${endpoint}`;
      const formData = new FormData();
      formData.append('files', file);
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${config.strapiAccessToken}`,
        },
        body: formData,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.status === 401) {
        console.log('🚨 Strapi Upload API returned 401 Unauthorized:', endpoint);
        handleUnauthorizedWithSmartRedirect(storeHandle);
        
        const errorMessage = 'Authentication failed - redirecting to login';
        throw new Error(errorMessage);
      }

      if (!response.ok) {
        let errorMessage = 'Strapi Upload API error';
        try {
          const error = await response.json();
          errorMessage = error.message || `HTTP ${response.status}: ${response.statusText}`;
        } catch {
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      return response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Upload timeout - file took too long to upload');
      }
      throw error;
    }
  };

  return { request, uploadFile };
};

export const strapiClient = createStrapiClient();