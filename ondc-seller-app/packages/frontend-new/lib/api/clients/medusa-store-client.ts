// lib/api/clients/medusa-store-client.ts
import { handleUnauthorizedWithSmartRedirect } from '../../utils/authUtils';
import { medusaClient } from '../medusa-client';

// Create a dedicated Medusa Store API client using fetch
export const createMedusaStoreClient = () => {
  const baseURL = `${medusaClient.getBaseUrl()}`;
  
  if (!baseURL) {
    throw new Error('Medusa Store API URL not configured. Please set NEXT_PUBLIC_MEDUSA_STORE_API_URL or NEXT_PUBLIC_MEDUSA_BASE_URL');
  }

  const request = async <T>(
    endpoint: string,
    options: RequestInit = {},
    storeHandle?: string
  ): Promise<T> => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    try {
      const url = `${baseURL}${endpoint}`;
      
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'x-tenant-id': storeHandle || 'default',
          'x-publishable-api-key': process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || '',
          ...options.headers,
        },
        credentials: 'include',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Handle 401 Unauthorized responses
      if (response.status === 401) {
        console.log('🚨 Medusa Store API returned 401 Unauthorized:', endpoint);
        
        // Don't auto-redirect for registration/signup endpoints
        const isRegistrationEndpoint = endpoint.includes('/register') || 
                                       endpoint.includes('/signup') ||
                                       endpoint.includes('/auth/customer/emailpass/register');
        
        if (!isRegistrationEndpoint) {
          handleUnauthorizedWithSmartRedirect(storeHandle);
          const errorMessage = 'Authentication failed - redirecting to login';
          throw new Error(errorMessage);
        }
        
        // For registration endpoints, return the actual error message without redirecting
        console.log('📝 Registration endpoint 401 - extracting error message without redirect');
        let errorMessage = 'Registration failed';
        try {
          const error = await response.json();
          errorMessage = error.message || error.error || error.details || errorMessage;
          console.log('📝 Registration error message:', errorMessage);
        } catch {
          errorMessage = `Registration failed: ${response.status} ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      if (!response.ok) {
        let errorMessage = 'Medusa Store API error';
        try {
          const error = await response.json();
          errorMessage = error.message || `HTTP ${response.status}: ${response.statusText}`;
        } catch {
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      return response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout - API took too long to respond');
      }
      throw error;
    }
  };

  return { request };
};

export const medusaStoreClient = createMedusaStoreClient();