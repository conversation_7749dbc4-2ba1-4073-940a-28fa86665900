// lib/api/clients/medusa-admin-client.ts
import axios from 'axios';
import { handleUnauthorizedWithSmartRedirect } from '../../utils/authUtils';
import { medusaClient } from '../medusa-client';

// Create a dedicated Medusa Admin API client
const createMedusaAdminClient = () => {
  const baseURL = `${medusaClient.getBaseUrl()}/admin`;
  
  if (!baseURL) {
    throw new Error('Medusa Admin API URL not configured. Please set NEXT_PUBLIC_MEDUSA_API_URL or NEXT_PUBLIC_MEDUSA_BASE_URL');
  }

  const client = axios.create({
    baseURL,
    headers: {
      'Content-Type': 'application/json',
    },
    timeout: 30000, // 30 second timeout
  });

  // Request interceptor for authentication
  client.interceptors.request.use((config) => {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('ondc_auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  });

  // Response interceptor for 401 handling
  client.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response?.status === 401) {
        console.log('🚨 Medusa Admin API returned 401 Unauthorized:', error.config?.url);
        
        const storeHandle = error.config?.headers?.['x-tenant-id'];
        handleUnauthorizedWithSmartRedirect(storeHandle);
      }
      
      return Promise.reject(error);
    }
  );

  return client;
};

export const medusaAdminClient = createMedusaAdminClient();