// lib/api/services/strapi-banners.ts
import { strapiClient } from '../clients/strapi-client';

export interface StrapiBannerData {
  title: string;
  description: string;
  image_url?: string;
  image?: {
    id: number;
    url: string;
    name: string;
    alternativeText?: string;
    caption?: string;
    width?: number;
    height?: number;
    formats?: any;
    hash: string;
    ext: string;
    mime: string;
    size: number;
    previewUrl?: string;
    provider: string;
    provider_metadata?: any;
    createdAt: string;
    updatedAt: string;
  };
  buttonText?: string;
  buttonLink?: string;
  link?: string;
  active: boolean;
  tenant_store?: string;
  position?: number;
}

export interface StrapiBannerResponse {
  data: {
    id: number;
    documentId: string;
    title: string;
    description: string;
    image_url?: string;
    image?: StrapiBannerData['image'];
    buttonText?: string;
    buttonLink?: string;
    link?: string;
    active: boolean;
    tenant_store?: string;
    position?: number;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
  };
  meta: any;
}

export interface StrapiBannerListResponse {
  data: StrapiBannerResponse['data'][];
  meta: any;
}

export interface StrapiBannerCreatePayload {
  title: string;
  description: string;
  image_url?: string;
  buttonText?: string;
  buttonLink?: string;
  active: boolean;
  tenant_store: string;
  position?: number;
}

export interface StrapiBannerUpdatePayload extends Partial<StrapiBannerCreatePayload> {}

// Strapi Banners Service
export const strapiBannersService = {
  // Get all banners from Strapi
  strapiGetBanners: async (storeHandle?: string): Promise<StrapiBannerListResponse> => {
    try {
      console.log('=== STRAPI GET BANNERS ===');
      console.log('Store handle:', storeHandle);
      
      let endpoint = '/api/banners?populate=*&sort=position:asc';
      
      // Filter by store handle if provided
      if (storeHandle) {
        endpoint += `&filters[tenant_store][$eq]=${storeHandle}`;
      }
      
      const response = await strapiClient.request<StrapiBannerListResponse>(endpoint);
      
      console.log('✅ Strapi banners fetched:', { count: response.data?.length || 0 });
      return response;
    } catch (error: any) {
      console.error('❌ Strapi get banners error:', error.message);
      throw error;
    }
  },

  // Get banner by document ID from Strapi
  strapiGetBannerByDocumentId: async (documentId: string): Promise<StrapiBannerResponse> => {
    try {
      console.log('=== STRAPI GET BANNER BY DOCUMENT ID ===');
      console.log('Document ID:', documentId);
      
      const response = await strapiClient.request<StrapiBannerResponse>(
        `/api/banners/${documentId}?populate=*`
      );
      
      console.log('✅ Strapi banner by document ID fetched:', response);
      return response;
    } catch (error: any) {
      console.error('❌ Strapi get banner by document ID error:', error.message);
      throw error;
    }
  },

  // Create banner in Strapi
  strapiCreateBanner: async (payload: StrapiBannerCreatePayload): Promise<StrapiBannerResponse> => {
    try {
      console.log('=== STRAPI CREATE BANNER ===');
      console.log('Payload:', JSON.stringify(payload, null, 2));
      
      const response = await strapiClient.request<StrapiBannerResponse>(
        '/api/banners',
        {
          method: 'POST',
          body: JSON.stringify({
            data: payload
          }),
        }
      );
      
      console.log('✅ Strapi banner created:', response);
      return response;
    } catch (error: any) {
      console.error('❌ Strapi create banner error:', error.message);
      throw error;
    }
  },

  // Update banner in Strapi by document ID
  strapiUpdateBannerByDocumentId: async (
    documentId: string,
    payload: StrapiBannerUpdatePayload
  ): Promise<StrapiBannerResponse> => {
    try {
      console.log('=== STRAPI UPDATE BANNER BY DOCUMENT ID ===');
      console.log('Document ID:', documentId);
      console.log('Payload:', JSON.stringify(payload, null, 2));
      
      const response = await strapiClient.request<StrapiBannerResponse>(
        `/api/banners/${documentId}`,
        {
          method: 'PUT',
          body: JSON.stringify({
            data: payload
          }),
        }
      );
      
      console.log('✅ Strapi banner updated:', response);
      return response;
    } catch (error: any) {
      console.error('❌ Strapi update banner by document ID error:', error.message);
      throw error;
    }
  },

  // Delete banner from Strapi by document ID
  strapiDeleteBannerByDocumentId: async (documentId: string): Promise<void> => {
    try {
      console.log('=== STRAPI DELETE BANNER BY DOCUMENT ID ===');
      console.log('Document ID:', documentId);
      
      await strapiClient.request<void>(
        `/api/banners/${documentId}`,
        {
          method: 'DELETE',
        }
      );
      
      console.log('✅ Strapi banner deleted');
    } catch (error: any) {
      console.error('❌ Strapi delete banner by document ID error:', error.message);
      throw error;
    }
  },

  // Upload file to Strapi
  strapiUploadFile: async (file: File): Promise<any> => {
    try {
      console.log('=== STRAPI UPLOAD FILE ===');
      console.log('File name:', file.name);
      console.log('File size:', file.size);
      console.log('File type:', file.type);
      
      const response = await strapiClient.uploadFile(file);
      
      console.log('✅ Strapi file uploaded:', response);
      return response;
    } catch (error: any) {
      console.error('❌ Strapi upload file error:', error.message);
      throw error;
    }
  },

  // Get active banners for store
  strapiGetActiveBannersForStore: async (storeHandle: string): Promise<StrapiBannerListResponse> => {
    try {
      console.log('=== STRAPI GET ACTIVE BANNERS FOR STORE ===');
      console.log('Store handle:', storeHandle);
      
      const endpoint = `/api/banners?populate=*&sort=position:asc&filters[tenant_store][$eq]=${storeHandle}&filters[active][$eq]=true`;
      
      const response = await strapiClient.request<StrapiBannerListResponse>(endpoint);
      
      console.log('✅ Strapi active banners for store fetched:', { 
        storeHandle, 
        count: response.data?.length || 0 
      });
      return response;
    } catch (error: any) {
      console.error('❌ Strapi get active banners for store error:', error.message);
      throw error;
    }
  },
};