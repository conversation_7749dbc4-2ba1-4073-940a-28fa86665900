// lib/api/services/strapi-store-config.ts
import { strapiClient } from '../clients/strapi-client';
import { ColorPalette } from '../../utils/colorPalette';

export interface StrapiStoreConfigurationPayload {
  country: string;
  onboarding_completed: boolean;
  onboarding_step: number;
  store_status: string;
  store_handle: string;
  store_name: string;
  store_description: string;
  gst_number?: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  pincode: string;
  phone: string;
  email: string;
  store_email: string;
  business_type: string;
  business_category: string;
  user_id: string;
  created_by_user: string;
  payment_methods: {
    upi: boolean;
    bnpl: boolean;
    wallet: boolean;
    debit_card: boolean;
    credit_card: boolean;
    net_banking: boolean;
    cash_on_delivery: boolean;
  };
  store_color_palette?: ColorPalette;
  store_logo_url?: string;
  website?: string;
}

export interface StrapiStoreConfigurationResponse {
  data: {
    id: number;
    documentId: string;
    country: string;
    onboarding_completed: boolean;
    onboarding_step: number;
    store_status: string;
    store_handle: string;
    store_name: string;
    store_description: string;
    gst_number?: string;
    address_line_1?: string;
    address_line_2?: string;
    city?: string;
    state?: string;
    pincode?: string;
    phone?: string;
    email?: string;
    store_email?: string;
    business_type: string;
    business_category: string;
    user_id: string;
    website?: string;
    created_by_user: string;
    payment_methods: {
      upi: boolean;
      bnpl: boolean;
      wallet: boolean;
      debit_card: boolean;
      credit_card: boolean;
      net_banking: boolean;
      cash_on_delivery: boolean;
    };
    store_color_palette?: ColorPalette;
    store_logo_url?: string;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
  };
  meta: any;
}

export interface StrapiStoreConfigurationListResponse {
  data: StrapiStoreConfigurationResponse['data'][];
  meta: any;
}

// Strapi Store Configuration Service
export const strapiStoreConfigService = {
  // Save store configuration to Strapi
  strapiSaveStoreConfiguration: async (
    payload: StrapiStoreConfigurationPayload
  ): Promise<StrapiStoreConfigurationResponse> => {
    try {
      console.log('=== STRAPI SAVE STORE CONFIGURATION ===');
      console.log('Payload:', JSON.stringify(payload, null, 2));
      
      const response = await strapiClient.request<StrapiStoreConfigurationResponse>(
        '/api/store-configurations',
        {
          method: 'POST',
          body: JSON.stringify({
            data: payload
          }),
        }
      );
      
      console.log('✅ Strapi store configuration saved:', response);
      return response;
    } catch (error: any) {
      console.error('❌ Strapi save store configuration error:', error.message);
      throw error;
    }
  },

  // Update store configuration in Strapi
  strapiUpdateStoreConfiguration: async (
    documentId: string,
    payload: StrapiStoreConfigurationPayload
  ): Promise<StrapiStoreConfigurationResponse> => {
    try {
      console.log('=== STRAPI UPDATE STORE CONFIGURATION ===');
      console.log('Document ID:', documentId);
      console.log('Payload:', JSON.stringify(payload, null, 2));
      
      const response = await strapiClient.request<StrapiStoreConfigurationResponse>(
        `/api/store-configurations/${documentId}`,
        {
          method: 'PUT',
          body: JSON.stringify({
            data: payload
          }),
        }
      );
      
      console.log('✅ Strapi store configuration updated:', response);
      return response;
    } catch (error: any) {
      console.error('❌ Strapi update store configuration error:', error.message);
      throw error;
    }
  },

  // Get store configuration by handle from Strapi
  strapiGetStoreConfigurationByHandle: async (
    storeHandle: string
  ): Promise<StrapiStoreConfigurationResponse | null> => {
    try {
      console.log('=== STRAPI GET STORE CONFIGURATION BY HANDLE ===');
      console.log('Store handle:', storeHandle);
      
      const response = await strapiClient.request<StrapiStoreConfigurationListResponse>(
        `/api/store-configurations?filters[store_handle][$eq]=${storeHandle}&populate=*`
      );
      
      console.log('✅ Strapi store configuration fetched:', response);
      
      // Return first matching store configuration
      if (response.data && response.data.length > 0) {
        return { data: response.data[0], meta: response.meta };
      }
      
      return null;
    } catch (error: any) {
      console.error('❌ Strapi get store configuration by handle error:', error.message);
      throw error;
    }
  },

  // Get store configuration by user ID from Strapi
  strapiGetStoreConfigurationByUserId: async (
    userId: string
  ): Promise<StrapiStoreConfigurationResponse | null> => {
    try {
      console.log('=== STRAPI GET STORE CONFIGURATION BY USER ID ===');
      console.log('User ID:', userId);
      
      const response = await strapiClient.request<StrapiStoreConfigurationListResponse>(
        `/api/store-configurations?filters[user_id][$eq]=${userId}&populate=*`
      );
      
      console.log('✅ Strapi store configuration by user ID fetched:', response);
      
      // Return first matching store configuration
      if (response.data && Array.isArray(response.data) && response.data.length > 0) {
        console.log('Found store configuration data:', response.data[0]);
        return { data: response.data[0], meta: response.meta };
      }
      
      console.log('No store configuration data found in response');
      return null;
    } catch (error: any) {
      console.error('❌ Strapi get store configuration by user ID error:', error.message);
      
      // Don't throw error for 404 or other \"not found\" cases
      if (error.message.includes('404')) {
        console.log('No store configuration found (404)');
        return null;
      }
      
      throw error;
    }
  },

  // Get store configuration by document ID from Strapi
  strapiGetStoreConfigurationByDocumentId: async (
    documentId: string
  ): Promise<StrapiStoreConfigurationResponse> => {
    try {
      console.log('=== STRAPI GET STORE CONFIGURATION BY DOCUMENT ID ===');
      console.log('Document ID:', documentId);
      
      const response = await strapiClient.request<StrapiStoreConfigurationResponse>(
        `/api/store-configurations/${documentId}?populate=*`
      );
      
      console.log('✅ Strapi store configuration by document ID fetched:', response);
      return response;
    } catch (error: any) {
      console.error('❌ Strapi get store configuration by document ID error:', error.message);
      throw error;
    }
  },

  // Delete store configuration from Strapi
  strapiDeleteStoreConfiguration: async (
    documentId: string
  ): Promise<void> => {
    try {
      console.log('=== STRAPI DELETE STORE CONFIGURATION ===');
      console.log('Document ID:', documentId);
      
      await strapiClient.request<void>(
        `/api/store-configurations/${documentId}`,
        {
          method: 'DELETE',
        }
      );
      
      console.log('✅ Strapi store configuration deleted');
    } catch (error: any) {
      console.error('❌ Strapi delete store configuration error:', error.message);
      throw error;
    }
  },
};