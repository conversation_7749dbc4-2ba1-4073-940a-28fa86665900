// lib/api/services/medusa-store-auth.ts
import { medusaStoreClient } from '../clients/medusa-store-client';

export interface StoreLoginCredentials {
  email: string;
  password: string;
}

export interface StoreAuthResponse {
  access_token: string;
}

export interface StoreCustomer {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  has_account?: boolean;
  created_at?: string;
  updated_at?: string;
  addresses?: StoreAddress[];
  metadata?: {
    [key: string]: any;
  };
}

export interface StoreCustomerResponse {
  customer: StoreCustomer;
}

export interface StoreAddress {
  id: string;
  first_name: string;
  last_name: string;
  phone?: string;
  company?: string;
  address_1: string;
  address_2?: string;
  city: string;
  country_code: string;
  province: string;
  postal_code: string;
  address_name?: string;
  metadata?: {
    [key: string]: any;
  };
}

export interface StoreAddressesResponse {
  addresses: StoreAddress[];
}

export interface StoreCustomerRegistrationData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone?: string;
}

export interface StoreCustomerRegistrationResponse {
  token: string;
  customer?: StoreCustomer;
}

export interface StoreCustomerProfileUpdateData {
  phone?: string;
  metadata?: {
    date_of_birth?: string;
    gender?: string;
    [key: string]: any;
  };
}

export interface StoreAddressCreateData {
  first_name: string;
  last_name: string;
  phone?: string;
  company?: string;
  address_1: string;
  address_2?: string;
  city: string;
  country_code: string;
  province: string;
  postal_code: string;
  address_name?: string;
  metadata?: {
    [key: string]: any;
  };
}

export interface StoreAddressResponse {
  address: StoreAddress;
}

// Medusa Store Authentication Service
export const medusaStoreAuthService = {
  // Store customer login
  storeCustomerLogin: async (
    credentials: StoreLoginCredentials, 
    storeHandle: string
  ): Promise<StoreAuthResponse> => {
    try {
      console.log('🔐 Store customer login:', { 
        endpoint: '/auth/customer/emailpass',
        email: credentials.email,
        storeHandle 
      });
      
      const response = await medusaStoreClient.request<StoreAuthResponse>(
        '/auth/customer/emailpass',
        {
          method: 'POST',
          body: JSON.stringify(credentials),
        },
        storeHandle
      );
      
      console.log('✅ Store customer login successful:', { hasToken: !!response.access_token });
      return response;
    } catch (error: any) {
      console.error('❌ Store customer login error:', error.message);
      throw error;
    }
  },

  // Store customer registration
  storeCustomerRegister: async (
    registrationData: StoreCustomerRegistrationData,
    storeHandle: string
  ): Promise<StoreCustomerRegistrationResponse> => {
    try {
      console.log('📝 Store customer registration:', { 
        endpoint: '/auth/customer/emailpass/register',
        email: registrationData.email,
        storeHandle 
      });
      const userData={
        email:registrationData.email,
        password: registrationData.password
      }
      const response = await medusaStoreClient.request<StoreCustomerRegistrationResponse>(
        '/auth/customer/emailpass/register',
        {
          method: 'POST',
          body: JSON.stringify(userData),
        },
        storeHandle
      );
      
      console.log('✅ Store customer registration successful:', { hasToken: !!response.token });
      
      // Step 2: Complete registration with customer profile data
      if (response.token) {
        console.log('📝 Completing customer registration with profile data:', {
          endpoint: '/store/customers',
          storeHandle
        });
        
        // Create payload without password and add metadata
        const { password, ...registrationDataWithoutPassword } = registrationData;
        const customerProfilePayload = {
          ...registrationDataWithoutPassword,
          metadata: {
            user_type: "customer",
            registration_completed: true,
            registration_date: new Date().toISOString()
          }
        };
        
        console.log('📝 Customer profile payload:', {
          email: customerProfilePayload.email,
          first_name: customerProfilePayload.first_name,
          last_name: customerProfilePayload.last_name,
          phone: customerProfilePayload.phone,
          metadata: customerProfilePayload.metadata
        });
        
        try {
          const customerProfileResponse = await medusaStoreClient.request<StoreCustomerResponse>(
            '/store/customers',
            {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${response.token}`,
              },
              body: JSON.stringify(customerProfilePayload),
            },
            storeHandle
          );
          
          console.log('✅ Customer profile creation successful:', { 
            customerId: customerProfileResponse.customer?.id 
          });
          
          // Add the customer data to the response
          response.customer = customerProfileResponse.customer;
          
        } catch (profileError: any) {
          console.error('❌ Customer profile creation error:', profileError.message);
          // Don't throw here - registration was successful, profile creation is secondary
          console.warn('⚠️ Registration successful but profile creation failed. User can complete profile later.');
        }
      }
      
      return response;
    } catch (error: any) {
      console.error('❌ Store customer registration error:', error.message);
      throw error;
    }
  },

  // Get store customer details
  storeGetCustomerDetails: async (
    token: string, 
    storeHandle: string
  ): Promise<StoreCustomerResponse> => {
    try {
      console.log('👤 Fetching store customer details:', { 
        endpoint: '/store/customers/me',
        storeHandle 
      });
      
      const response = await medusaStoreClient.request<StoreCustomerResponse>(
        '/store/customers/me',
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        },
        storeHandle
      );
      
      console.log('✅ Store customer details fetched:', { customerId: response.customer?.id });
      return response;
    } catch (error: any) {
      console.error('❌ Store get customer details error:', error.message);
      throw error;
    }
  },

  // Update store customer profile
  storeUpdateCustomerProfile: async (
    token: string,
    profileData: StoreCustomerProfileUpdateData,
    storeHandle: string
  ): Promise<StoreCustomerResponse> => {
    try {
      console.log('📝 Updating store customer profile:', { 
        endpoint: '/store/customers/me',
        storeHandle,
        profileData 
      });
      
      const response = await medusaStoreClient.request<StoreCustomerResponse>(
        '/store/customers/me',
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(profileData),
        },
        storeHandle
      );
      
      console.log('✅ Store customer profile updated:', { customerId: response.customer?.id });
      return response;
    } catch (error: any) {
      console.error('❌ Store update customer profile error:', error.message);
      throw error;
    }
  },

  // Get store customer addresses
  storeGetCustomerAddresses: async (
    token: string, 
    storeHandle: string
  ): Promise<StoreAddressesResponse> => {
    try {
      console.log('📍 Fetching store customer addresses:', { 
        endpoint: '/store/customers/me/addresses',
        storeHandle 
      });
      
      const response = await medusaStoreClient.request<StoreAddressesResponse>(
        '/store/customers/me/addresses',
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        },
        storeHandle
      );
      
      console.log('✅ Store customer addresses fetched:', { count: response.addresses?.length || 0 });
      return response;
    } catch (error: any) {
      console.error('❌ Store get customer addresses error:', error.message);
      throw error;
    }
  },

  // Create store customer address
  storeCreateCustomerAddress: async (
    token: string,
    addressData: StoreAddressCreateData,
    storeHandle: string
  ): Promise<StoreAddressResponse> => {
    try {
      console.log('📍 Creating store customer address:', { 
        endpoint: '/store/customers/me/addresses',
        storeHandle,
        addressData 
      });
      
      const response = await medusaStoreClient.request<StoreAddressResponse>(
        '/store/customers/me/addresses',
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(addressData),
        },
        storeHandle
      );
      
      console.log('✅ Store customer address created:', { addressId: response.address?.id });
      return response;
    } catch (error: any) {
      console.error('❌ Store create customer address error:', error.message);
      throw error;
    }
  },

  // Update store customer address
  storeUpdateCustomerAddress: async (
    token: string,
    addressId: string,
    addressData: StoreAddressCreateData,
    storeHandle: string
  ): Promise<StoreAddressResponse> => {
    try {
      console.log('📍 Updating store customer address:', { 
        endpoint: `/store/customers/me/addresses/${addressId}`,
        storeHandle,
        addressId,
        addressData 
      });
      
      const response = await medusaStoreClient.request<StoreAddressResponse>(
        `/store/customers/me/addresses/${addressId}`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(addressData),
        },
        storeHandle
      );
      
      console.log('✅ Store customer address updated:', { addressId: response.address?.id });
      return response;
    } catch (error: any) {
      console.error('❌ Store update customer address error:', error.message);
      throw error;
    }
  },

  // Delete store customer address
  storeDeleteCustomerAddress: async (
    token: string,
    addressId: string,
    storeHandle: string
  ): Promise<void> => {
    try {
      console.log('🗑️ Deleting store customer address:', { 
        endpoint: `/store/customers/me/addresses/${addressId}`,
        storeHandle,
        addressId 
      });
      
      await medusaStoreClient.request<void>(
        `/store/customers/me/addresses/${addressId}`,
        {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        },
        storeHandle
      );
      
      console.log('✅ Store customer address deleted:', { addressId });
    } catch (error: any) {
      console.error('❌ Store delete customer address error:', error.message);
      throw error;
    }
  },
};