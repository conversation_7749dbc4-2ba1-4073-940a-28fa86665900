import { medusaClient } from './medusa-client';
import { getAuthHeaders } from '@/lib/utils/tokenUtils';

export interface CategoryPayload {
  name: string;
  handle: string;
  description: string;
  parent_category_id?: string;
}

export interface CategoryResponse {
  id: string;
  name: string;
  handle: string;
  description: string;
  parent_category_id?: string;
  created_at: string;
  updated_at: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Helper function to get tenant ID from storage
const getTenantId = (): string => {
  let tenantId = 'default-tenant';
  
  if (typeof window !== 'undefined') {
    try {
      // First, try to get from store-config-storage (preferred for onboarding)
      const storeConfigStorage = localStorage.getItem('store-config-storage');
      if (storeConfigStorage) {
        const storeConfigData = JSON.parse(storeConfigStorage);
        const storeHandle = storeConfigData.state?.storeData?.handle || 
                           storeConfigData.state?.storeConfig?.store_handle ||
                           storeConfigData.state?.currentStoreHandle;
        
        if (storeHandle) {
          console.log('Tenant ID from store-config-storage:', storeHandle);
          return storeHandle;
        }
      }
      
      // Fallback to auth-storage
      const authStorage = localStorage.getItem('auth-storage');
      if (authStorage) {
        const authData = JSON.parse(authStorage);
        const user = authData.state?.user;
        
        // Check multiple possible locations for store handle
        const storeHandle = 
          user?.user?.metadata?.store_handle || // From nested API response structure
          user?.user?.store_handle || // From nested API response structure
          user?.store_handle || 
          user?.metadata?.store_handle ||
          null;
        
        if (storeHandle) {
          console.log('Tenant ID from auth-storage:', storeHandle);
          return storeHandle;
        }
      }
    } catch (error) {
      console.error('Error getting tenant ID from storage:', error);
    }
  }
  
  console.log('Using default tenant ID:', tenantId);
  return tenantId;
};

// Helper function to get auth headers with auto token
const getCategoriesAuthHeaders = (storeHandle?: string) => {
  const tenantId = storeHandle || getTenantId();
  
  // Use the centralized auth headers utility (it already sets x-tenant-id)
  const headers = getAuthHeaders(tenantId);
  
  console.log('Categories API - Auth headers - Token exists:', !!headers.Authorization);
  console.log('Categories API - Auth headers - Tenant ID:', tenantId);
  
  return headers;
};

export const categoriesApi = {
  createCategory: async (payload: CategoryPayload, storeHandle?: string): Promise<ApiResponse<CategoryResponse>> => {
    try {
      console.log('=== CREATING CATEGORY ===');
      console.log('Payload:', JSON.stringify(payload, null, 2));
      
      const headers = getCategoriesAuthHeaders(storeHandle);
      
      console.log('Request headers:', {
        ...headers,
        'Authorization': headers.Authorization ? 'Bearer [REDACTED]' : 'No token'
      });
      
      const response = await medusaClient.admin.post('/product-categories', payload, {
        headers,
      });
      
      console.log('API response status:', response.status);
      
      const data = response.data;
      console.log('API response data:', data);
      
      return {
        success: true,
        data: data,
        message: payload.parent_category_id ? 'Subcategory created successfully!' : 'Category created successfully!',
      };
      
    } catch (error) {
      console.error('Error creating category:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create category. Please try again.',
      };
    }
  },
  
  getCategories: async (storeHandle?: string): Promise<ApiResponse<CategoryResponse[]>> => {
    try {
      console.log('=== FETCHING CATEGORIES ===');
      
      const headers = getCategoriesAuthHeaders(storeHandle);
      
      console.log('Request headers:', {
        ...headers,
        'Authorization': headers.Authorization ? 'Bearer [REDACTED]' : 'No token'
      });
      
      const response = await medusaClient.admin.get('/product-categories', {
        headers,
      });
      
      const data = response.data;
      console.log('=== RAW API RESPONSE ===');
      console.log('Response data:', data);
      console.log('Data type:', typeof data);
      console.log('Is array:', Array.isArray(data));
      
      let categories = [];
      
      // Handle different possible response structures
      if (Array.isArray(data)) {
        categories = data;
      } else if (data.product_categories && Array.isArray(data.product_categories)) {
        categories = data.product_categories;
      } else if (data.categories && Array.isArray(data.categories)) {
        categories = data.categories;
      } else if (data.data && Array.isArray(data.data)) {
        categories = data.data;
      } else {
        console.log('Unexpected response structure, treating as empty array:', data);
        categories = [];
      }
      
      console.log('Extracted categories:', categories);
      console.log('Categories count:', categories.length);
      
      return {
        success: true,
        data: categories,
      };
      
    } catch (error) {
      console.error('Error fetching categories:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch categories.',
      };
    }
  },
};