import { medusaClient } from './medusa-client';

export interface Coupon {
  id: string;
  name: string;
  code: string;
  description?: string;
  type: 'percentage' | 'fixed_amount' | 'free_shipping';
  value: number;
  minimum_amount?: number;
  maximum_discount?: number;
  usage_limit?: number;
  used_count?: number;
  status: 'active' | 'inactive' | 'expired';
  start_date?: string;
  end_date?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface CouponsResponse {
  coupons: Coupon[];
  count: number;
  limit: number;
  offset: number;
}

export const couponsApi = {
  getCoupons: async (storeHandle: string, params?: {
    limit?: number;
    offset?: number;
    search?: string;
    status?: string;
    type?: string;
  }): Promise<CouponsResponse> => {
    console.log('=== COUPONS API CALL ===');
    console.log('Store handle (x-tenant-id):', storeHandle);
    console.log('Request params:', params);
    
    try {
      const response = await medusaClient.admin.get('/promotions', {
        headers: {
          'x-tenant-id': storeHandle,
        },
        params,
      });
      
      console.log('Coupons API response status:', response.status);
      console.log('Coupons API response headers:', response.headers);
      console.log('Coupons API response data:', response.data);
      console.log('Coupons API response structure:', {
        dataType: typeof response.data,
        dataKeys: response.data ? Object.keys(response.data) : 'No data',
        couponsArray: Array.isArray(response.data?.coupons),
        couponsCount: response.data?.coupons?.length || 0,
        sampleCoupon: response.data?.coupons?.[0] || 'No coupons'
      });
      
      return response.data;
    } catch (error: any) {
      console.error('=== COUPONS API ERROR ===');
      console.error('Error details:', error);
      
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
        console.error('Error response headers:', error.response.headers);
      }
      
      throw error;
    }
  },

  getCoupon: async (storeHandle: string, couponId: string): Promise<Coupon> => {
    console.log('=== GET COUPON API CALL ===');
    console.log('Store handle (x-tenant-id):', storeHandle);
    console.log('Coupon ID:', couponId);
    
    try {
      const response = await medusaClient.admin.get(`/promotions/${couponId}`, {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Get coupon API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching coupon:', error);
      throw error;
    }
  },

  createCoupon: async (storeHandle: string, couponData: Partial<Coupon>): Promise<Coupon> => {
    console.log('=== CREATE COUPON API CALL ===');
    console.log('Store handle (x-tenant-id):', storeHandle);
    console.log('Coupon data:', couponData);
    
    try {
      const response = await medusaClient.admin.post('/promotions', couponData, {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Create coupon API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error creating coupon:', error);
      throw error;
    }
  },

  updateCoupon: async (storeHandle: string, couponId: string, couponData: Partial<Coupon>): Promise<Coupon> => {
    console.log('=== UPDATE COUPON API CALL ===');
    console.log('Store handle (x-tenant-id):', storeHandle);
    console.log('Coupon ID:', couponId);
    console.log('Coupon data:', couponData);
    
    try {
      const response = await medusaClient.admin.put(`/promotions/${couponId}`, couponData, {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Update coupon API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error updating coupon:', error);
      throw error;
    }
  },

  deleteCoupon: async (storeHandle: string, couponId: string): Promise<void> => {
    console.log('=== DELETE COUPON API CALL ===');
    console.log('Store handle (x-tenant-id):', storeHandle);
    console.log('Coupon ID:', couponId);
    
    try {
      await medusaClient.admin.delete(`/promotions/${couponId}`, {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Coupon deleted successfully');
    } catch (error: any) {
      console.error('Error deleting coupon:', error);
      throw error;
    }
  },
};

export default couponsApi;