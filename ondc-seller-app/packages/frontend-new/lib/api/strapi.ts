import { ColorPalette } from '../utils/colorPalette';

export interface StoreConfigurationPayload {
  country: string;
  onboarding_completed: boolean;
  onboarding_step: number;
  store_status: string;
  store_handle: string;
  store_name: string;
  store_description: string;
  gst_number?: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  pincode: string;
  phone: string;
  email: string;
  store_email: string;
  business_type: string;
  business_category: string;
  user_id: string;
  created_by_user: string;
  payment_methods: {
    upi: boolean;
    bnpl: boolean;
    wallet: boolean;
    debit_card: boolean;
    credit_card: boolean;
    net_banking: boolean;
    cash_on_delivery: boolean;
  };
  store_color_palette?: ColorPalette;
  store_logo_url?: string;
}

export interface StrapiResponse {
  data: {
    id: number;
    documentId: string;
    country: string;
    onboarding_completed: boolean;
    onboarding_step: number;
    store_status: string;
    store_handle: string;
    store_name: string;
    store_description: string;
    gst_number?: string;
    address_line_1?: string;
    address_line_2?: string;
    city?: string;
    state?: string;
    pincode?: string;
    phone?: string;
    email?: string;
    store_email?: string;
    business_type: string;
    business_category: string;
    user_id: string;
    website?:string;
    created_by_user: string;
    payment_methods: {
      upi: boolean;
      bnpl: boolean;
      wallet: boolean;
      debit_card: boolean;
      credit_card: boolean;
      net_banking: boolean;
      cash_on_delivery: boolean;
    };
    store_color_palette?: ColorPalette;
    store_logo_url?: string;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    };

  meta: any;
}

export const strapiApi = {
  saveStoreConfiguration: async (payload: StoreConfigurationPayload): Promise<StrapiResponse> => {
    try {
      console.log('=== SAVING STORE CONFIGURATION TO STRAPI ===');
      console.log('Payload:', JSON.stringify(payload, null, 2));
      
      const baseUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || process.env.NEXT_PUBLIC_STRAPI_BASE_URL;
      if (!baseUrl) {
        throw new Error('Strapi API URL not configured');
      }
      
      const response = await fetch(`${baseUrl}/api/store-configurations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          data: payload
        }),
      });
      
      console.log('Strapi response status:', response.status);
      console.log('Strapi response ok:', response.ok);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Strapi error response:', errorText);
        throw new Error(`Strapi API error: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('Strapi response data:', data);
      
      return data;
      
    } catch (error) {
      console.error('Error saving store configuration to Strapi:', error);
      throw error;
    }
  },
  
  updateStoreConfiguration: async (documentId: string, payload: StoreConfigurationPayload): Promise<StrapiResponse> => {
    try {
      console.log('=== UPDATING STORE CONFIGURATION IN STRAPI ===');
      console.log('Document ID:', documentId);
      console.log('Payload:', JSON.stringify(payload, null, 2));
      
      const baseUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || process.env.NEXT_PUBLIC_STRAPI_BASE_URL;
      if (!baseUrl) {
        throw new Error('Strapi API URL not configured');
      }
      
      const response = await fetch(`${baseUrl}/api/store-configurations/${documentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          data: payload
        }),
      });
      
      console.log('Strapi update response status:', response.status);
      console.log('Strapi update response ok:', response.ok);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Strapi update error response:', errorText);
        throw new Error(`Strapi API update error: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('Strapi update response data:', data);
      
      return data;
      
    } catch (error) {
      console.error('Error updating store configuration in Strapi:', error);
      throw error;
    }
  },
  
  getStoreConfiguration: async (storeHandle: string): Promise<StrapiResponse | null> => {
    try {
      console.log('=== FETCHING STORE CONFIGURATION FROM STRAPI ===');
      console.log('Store handle:', storeHandle);
      
      const baseUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || process.env.NEXT_PUBLIC_STRAPI_BASE_URL;
      if (!baseUrl) {
        throw new Error('Strapi API URL not configured');
      }
      
      const response = await fetch(`${baseUrl}/api/store-configurations?filters[store_handle][$eq]=${storeHandle}`);
      
      if (!response.ok) {
        throw new Error(`Strapi API error: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('Strapi fetch response:', data);
      
      // Return first matching store configuration
      if (data.data && data.data.length > 0) {
        return { data: data.data[0], meta: data.meta };
      }
      
      return null;
      
    } catch (error) {
      console.error('Error fetching store configuration from Strapi:', error);
      throw error;
    }
  },
  
  getStoreConfigurationByUserId: async (userId: string): Promise<StrapiResponse | null> => {
    try {
      console.log('=== FETCHING STORE CONFIGURATION BY USER ID ===');
      console.log('User ID:', userId);
      
      const baseUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || process.env.NEXT_PUBLIC_STRAPI_BASE_URL;
      if (!baseUrl) {
        throw new Error('Strapi API URL not configured');
      }
      
      const response = await fetch(`${baseUrl}/api/store-configurations?filters[user_id][$eq]=${userId}`);
      
      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);
      
      if (!response.ok) {
        console.error('Strapi API error:', response.status, response.statusText);
        // Don't throw error for 404 or other "not found" cases
        if (response.status === 404) {
          console.log('No store configuration found (404)');
          return null;
        }
        throw new Error(`Strapi API error: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('Strapi fetch response:', JSON.stringify(data, null, 2));
      
      // Return first matching store configuration
      if (data.data && Array.isArray(data.data) && data.data.length > 0) {
        console.log('Found store configuration data:', data.data[0]);
        return { data: data.data[0], meta: data.meta };
      }
      
      console.log('No store configuration data found in response');
      return null;
      
    } catch (error) {
      console.error('Error fetching store configuration from Strapi:', error);
      throw error; // Re-throw to be handled by caller
    }
  },
};