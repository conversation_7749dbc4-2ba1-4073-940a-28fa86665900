// lib/api/strapi-store.ts
import axios from 'axios';
import { handleUnauthorizedWithSmartRedirect } from '../utils/authUtils';
// Strapi API configuration
const strapiBaseURL = process.env.NEXT_PUBLIC_STRAPI_BASE_URL || 'http://localhost:1337';

// Create axios instance for Strapi API
const strapiApi = axios.create({
  baseURL: strapiBaseURL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for authentication if needed
strapiApi.interceptors.request.use((config) => {
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('strapi_auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
  }
  return config;
});

// Add response interceptor for 401 handling
strapiApi.interceptors.response.use(
  (response) => {
    // Return successful responses as-is
    return response;
  },
  (error) => {
    // Handle 401 Unauthorized responses
    if (error.response?.status === 401) {
      console.log('🚨 Strapi API returned 401 Unauthorized:', error.config?.url);
      
      // Extract store handle from headers if available
      const storeHandle = error.config?.headers?.['x-tenant-id'];
      
      // Clear auth and redirect
      handleUnauthorizedWithSmartRedirect(storeHandle);
    }
    
    // Re-throw the error for the calling code to handle
    return Promise.reject(error);
  }
);

// Complete Strapi API response interface
export interface StrapiStoreResponse {
  data: any[]; // Raw store data from Strapi
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export interface StrapiSingleStoreResponse {
  data: any; // Raw store data from Strapi
  meta: {};
}

export interface StrapiSingleStoreConfigResponse {
  data: any; // Raw store configuration data from Strapi
  meta: {};
}

// Enhanced cache entry to store complete API response
export interface StoreApiCache {
  [storeHandle: string]: {
    rawResponse: StrapiStoreResponse; // Complete API response
    normalizedData: NormalizedStore; // Processed data for easy access
    lastFetched: number;
    version: string;
  };
}

// Normalized store interface (compatible with existing code)
export interface NormalizedStore {
  id: string;
  name: string;
  handle: string;
  description?: string;
  logo?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    postal_code?: string;
    country?: string;
  };
  contact?: {
    email?: string;
    phone?: string;
    website?: string;
  };
  social_media?: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
  };
  business_hours?: {
    monday?: string;
    tuesday?: string;
    wednesday?: string;
    thursday?: string;
    friday?: string;
    saturday?: string;
    sunday?: string;
  };
  theme?: {
    primary_color?: string;
    secondary_color?: string;
    accent_color?: string;
    background_color?: string;
    text_color?: string;
  };
  settings?: {
    currency?: string;
    timezone?: string;
    language?: string;
    tax_inclusive?: boolean;
    shipping_enabled?: boolean;
  };
  created_at: string;
  updated_at: string;
}

// Helper function to normalize Strapi response to our store interface
const normalizeStrapiStore = (strapiStore: any): NormalizedStore => {
  console.log('Normalizing Strapi store data:', JSON.stringify(strapiStore, null, 2));
  
  // The response is a flat object, not nested in attributes
  const data = strapiStore;
  
  // Extract ID (could be id or documentId)
  const id = data.id || data.documentId || '1';
  
  // Extract basic store information
  const storeName = data.store_name || 'Unnamed Store';
  const storeHandle = data.store_handle || '';
  const storeDescription = data.store_description || '';
  
  console.log('Extracted values:', { id, storeName, storeHandle, storeDescription });
  
  // Handle logo URL - check if store_logo exists and has url
  let logoUrl = undefined;
  if (data.store_logo?.url) {
    logoUrl = data.store_logo.url.startsWith('http') 
      ? data.store_logo.url 
      : `${strapiBaseURL}${data.store_logo.url}`;
  } else if (data.store_logo_url) {
    logoUrl = data.store_logo_url.startsWith('http') 
      ? data.store_logo_url 
      : `${strapiBaseURL}${data.store_logo_url}`;
  }
  
  // Build address object from flat fields
  const address = {
    street: data.address_line_1 || '',
    city: data.city || '',
    state: data.state || '',
    postal_code: data.pincode || '',
    country: data.country || '',
  };
  
  // Build contact object
  const contact = {
    email: data.email || data.store_email || '',
    phone: data.phone || '',
    website: data.website || '',
  };
  
  // Handle theme colors
  const theme = data.store_color_palette ? {
    primary_color: data.store_color_palette.primary_color,
    secondary_color: data.store_color_palette.secondary_color,
    accent_color: data.store_color_palette.accent_color,
    background_color: data.store_color_palette.background_color,
    text_color: data.store_color_palette.text_color,
  } : undefined;
  
  // Build settings object
  const settings = data.store_settings || {
    currency: 'INR',
    timezone: 'Asia/Kolkata',
    language: 'en',
    tax_inclusive: true,
    shipping_enabled: true,
  };
  
  console.log('Built normalized store:', {
    id: id.toString(),
    name: storeName,
    handle: storeHandle,
    description: storeDescription,
    logo: logoUrl,
    address,
    contact,
    theme,
    settings
  });
  
  return {
    id: id.toString(),
    name: storeName,
    handle: storeHandle,
    description: storeDescription,
    logo: logoUrl,
    address,
    contact,
    social_media: data.social_media,
    business_hours: data.operating_hours,
    theme,
    settings,
    created_at: data.createdAt || new Date().toISOString(),
    updated_at: data.updatedAt || new Date().toISOString(),
  };
};

// Banner interface for Strapi banners
export interface StrapiBanner {
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  image?: {
    url: string;
    alternativeText?: string;
  };
  cta_text?: string;
  cta_link?: string;
  background_color?: string;
  active: boolean;
  position: number;
  created_at: string;
  updated_at: string;
}

// Normalized banner interface
export interface NormalizedBanner {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  image: string;
  ctaText: string;
  ctaLink: string;
  backgroundColor: string;
  position: number;
}

// Helper function to normalize Strapi banner response
const normalizeStrapiBanner = (strapiBanner: any): NormalizedBanner => {
  console.log('Normalizing Strapi banner data:', JSON.stringify(strapiBanner, null, 2));
  
  const data = strapiBanner;
  const strapiBaseURL = process.env.NEXT_PUBLIC_STRAPI_BASE_URL || 'http://localhost:1337';
  
  // Handle image URL
  let imageUrl = '/images/default-banner.jpg'; // Default fallback
  if (data.image?.url) {
    imageUrl = data.image.url.startsWith('http') 
      ? data.image.url 
      : `${strapiBaseURL}${data.image.url}`;
  }
  
  return {
    id: data.id?.toString() || '1',
    title: data.title || 'Welcome to Our Store',
    subtitle: data.subtitle || '',
    description: data.description || '',
    image: imageUrl,
    ctaText: data.cta_text || 'Shop Now',
    ctaLink: data.cta_link || '/products',
    backgroundColor: data.background_color || '#3B82F6',
    position: data.position || 0,
  };
};

// Strapi store configuration service
export const strapiStoreService = {
  // Test Strapi API connection
  testConnection: async (): Promise<boolean> => {
    try {
      console.log('Testing Strapi API connection...');
      const response = await strapiApi.get('/api/store-configurations?pagination[limit]=1');
      console.log('Strapi API connection test successful:', response.status);
      return true;
    } catch (error: any) {
      console.error('Strapi API connection test failed:', error);
      if (error.response) {
        console.error('Connection test error response:', error.response.status, error.response.data);
      }
      return false;
    }
  },
  // Get all store configurations
  getAllStoreConfigs: async (): Promise<NormalizedStore[]> => {
    try {
      console.log('Fetching all store configurations from Strapi...');
      
      const response = await strapiApi.get('/api/store-configurations', {
        params: {
          populate: ['store_logo'],
        },
      });
      
      console.log('Store configurations response:', response.data);
      
      // Handle response - could be array directly or wrapped
      let data = response.data;
      if (data.data) {
        data = data.data;
      }
      
      if (!Array.isArray(data)) {
        data = [data];
      }
      
      return data.map(normalizeStrapiStore);
    } catch (error: any) {
      console.error('Error fetching store configurations:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Get store configuration by handle with complete API response
  getStoreConfigByHandle: async (storeHandle: string): Promise<{ rawResponse: StrapiStoreResponse; normalizedData: NormalizedStore } | null> => {
    try {
      console.log('Fetching store configuration for handle:', storeHandle);
      
      // Use the store-configurations endpoint
      const params = {
        'filters[store_handle][$eq]': storeHandle,
        'populate': '*', // Populate all relations
      };
      
      console.log('Fetching from /api/store-configurations with params:', params);
      const response = await strapiApi.get('/api/store-configurations', { params });
      
      // console.log('Complete API response:', JSON.stringify(response.data, null, 2));
      
      const apiResponse: StrapiStoreResponse = response.data;
      
      if (!apiResponse.data || apiResponse.data.length === 0) {
        console.warn('No store configuration found for handle:', storeHandle);
        return null;
      }
      
      // Find the store by handle
      let targetStore = apiResponse.data[0]; // Default to first
      
      if (apiResponse.data.length > 1) {
        const foundStore = apiResponse.data.find((store: any) => {
          return (
            store.store_handle === storeHandle ||
            store.handle === storeHandle ||
            store.slug === storeHandle
          );
        });
        
        if (foundStore) {
          targetStore = foundStore;
        }
      }
      
      console.log('Target store configuration data:', targetStore);
      
      // Normalize the data for easy access
      const normalizedData = normalizeStrapiStore(targetStore);
      
      return {
        rawResponse: apiResponse,
        normalizedData
      };
    } catch (error: any) {
      console.error('Error fetching store configuration:', error);
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
        console.error('Error response headers:', error.response.headers);
      }
      if (error.request) {
        console.error('Error request:', error.request);
      }
      throw error;
    }
  },

  // Get store configuration by ID
  getStoreConfigById: async (id: string): Promise<NormalizedStore> => {
    try {
      console.log('Fetching store configuration by ID:', id);
      
      const response = await strapiApi.get<StrapiSingleStoreConfigResponse>(`/api/store-configurations/${id}`, {
        params: {
          populate: ['store_logo'],
        },
      });
      
      console.log('Store configuration response:', response.data);
      
      return normalizeStrapiStore(response.data.data);
    } catch (error: any) {
      console.error('Error fetching store configuration by ID:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Create new store configuration
  createStoreConfig: async (storeData: Partial<NormalizedStore>): Promise<NormalizedStore> => {
    try {
      console.log('Creating store configuration:', storeData);
      
      const payload = {
        data: {
          store_handle: storeData.handle,
          store_name: storeData.name,
          store_description: storeData.description,
          store_address: storeData.address,
          store_contact: storeData.contact,
          social_media: storeData.social_media,
          business_hours: storeData.business_hours,
          theme_settings: storeData.theme,
          store_settings: storeData.settings,
        },
      };
      
      const response = await strapiApi.post<StrapiSingleStoreConfigResponse>('/api/store-configurations', payload);
      
      console.log('Create store configuration response:', response.data);
      
      return normalizeStrapiStore(response.data.data);
    } catch (error: any) {
      console.error('Error creating store configuration:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Update store configuration
  updateStoreConfig: async (id: string, storeData: Partial<NormalizedStore>): Promise<NormalizedStore> => {
    try {
      console.log('Updating store configuration:', id, storeData);
      
      const payload = {
        data: storeData
       };
      
      const response = await strapiApi.put<StrapiSingleStoreConfigResponse>(`/api/store-configurations/${id}`, payload);
      
      console.log('Update store configuration response:', response.data);
      
      return normalizeStrapiStore(response.data.data);
    } catch (error: any) {
      console.error('Error updating store configuration:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Delete store configuration
  deleteStoreConfig: async (id: string): Promise<void> => {
    try {
      console.log('Deleting store configuration:', id);
      
      await strapiApi.delete(`/api/store-configurations/${id}`);
      
      console.log('Store configuration deleted successfully');
    } catch (error: any) {
      console.error('Error deleting store configuration:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Create store configuration with specific payload format
  createStoreConfigWithPayload: async (payload: any): Promise<any> => {
    try {
      console.log('Creating store configuration with payload:', payload);
      
      const response = await strapiApi.post('/api/store-configurations', {
        data: payload
      });
      
      console.log('Create store configuration response:', response.data);
      
      return response.data;
    } catch (error: any) {
      console.error('Error creating store configuration with payload:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Update store configuration with specific payload format using documentId
  updateStoreConfigWithPayload: async (documentId: string, payload: any): Promise<any> => {
    try {
      console.log('Updating store configuration with documentId:', documentId, 'payload:', payload);
      
      const response = await strapiApi.put(`/api/store-configurations/${documentId}`, {
        data: payload
      });
      
      console.log('Update store configuration response:', response.data);
      
      return response.data;
    } catch (error: any) {
      console.error('Error updating store configuration with payload:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
      throw error;
    }
  },

  // Get active banners for a specific store
  getBanners: async (storeHandle?: string): Promise<NormalizedBanner[]> => {
    try {
      console.log('Fetching banners from Strapi for store:', storeHandle);
      
      const params: any = {
        'populate': '*',
        'filters[active][$eq]': true,
        'filters[tenant_store][$eq]': storeHandle,
        'sort': 'position:asc'

      };
      
      // Add store filter if storeHandle is provided
      if (storeHandle) {
        // First, we need to get the store ID from the store handle
        try {
          const storeResult = await strapiApi.get('/api/store-configurations', {
            params: {
              'filters[store_handle][$eq]': storeHandle,
              'fields': ['id']
            }
          });
          
          if (storeResult.data.data && storeResult.data.data.length > 0) {
            const storeId = storeResult.data.data[0].id;
            console.log('Found store ID for handle', storeHandle, ':', storeId);
          } else {
            console.warn('No store found for handle:', storeHandle, '- fetching all banners');
          }
        } catch (storeError) {
          console.warn('Error fetching store ID for handle:', storeHandle, '- fetching all banners', storeError);
        }
      }
      
      const response = await strapiApi.get('/api/banners', { params });
      
      console.log('Banners response:', response.data);
      
      const apiResponse = response.data;
      
      if (!apiResponse.data || apiResponse.data.length === 0) {
        console.warn('No active banners found for store:', storeHandle);
        return [];
      }
      
      // Normalize the banners
      const normalizedBanners = apiResponse.data.map(normalizeStrapiBanner);
      
      console.log('Normalized banners for store', storeHandle, ':', normalizedBanners);
      
      return normalizedBanners;
    } catch (error: any) {
      console.error('Error fetching banners:', error);
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
      }
      throw error;
    }
  },
};

export default strapiStoreService;