// lib/api/medusa-client.ts
/**
 * Centralized Medusa API client that ensures all API calls use the correct base URL
 * from environment variables with appropriate prefixes for admin, store, and auth endpoints.
 */

import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { handleUnauthorizedWithSmartRedirect } from '@/lib/utils/authUtils';

export interface MedusaClientConfig {
  storeHandle?: string;
  timeout?: number;
}

/**
 * Centralized Medusa API client factory
 */
export class MedusaClient {
  private baseUrl: string;
  private adminClient: AxiosInstance;
  private storeClient: AxiosInstance;
  private authClient: AxiosInstance;
  private storeHandle?: string;

  constructor(config: MedusaClientConfig = {}) {
    this.baseUrl = process.env.NEXT_PUBLIC_MEDUSA_BASE_URL || 'http://localhost:9000';
    this.storeHandle = config.storeHandle;

    // Create admin client (base URL + /admin)
    this.adminClient = axios.create({
      baseURL: `${this.baseUrl}/admin`,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: config.timeout || 30000,
    });

    // Create store client (base URL + /store)  
    this.storeClient = axios.create({
      baseURL: `${this.baseUrl}/store`,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: config.timeout || 30000,
    });

    // Create auth client (base URL only, no prefix)
    this.authClient = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: config.timeout || 30000,
    });

    this.setupInterceptors();
  }

  /**
   * Setup request and response interceptors for all clients
   */
  private setupInterceptors() {
    // Admin client interceptors
    this.adminClient.interceptors.request.use((config) => {
      if (typeof window !== 'undefined') {
        const token = localStorage.getItem('ondc_auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        if (this.storeHandle) {
          config.headers['x-tenant-id'] = this.storeHandle;
        }
      }
      return config;
    });

    this.adminClient.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          console.log('🚨 Medusa Admin API returned 401 Unauthorized:', error.config?.url);
          handleUnauthorizedWithSmartRedirect(this.storeHandle);
        }
        return Promise.reject(error);
      }
    );

    // Store client interceptors
    this.storeClient.interceptors.request.use((config) => {
      if (typeof window !== 'undefined') {
        const token = localStorage.getItem('ondc_auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        if (this.storeHandle) {
          config.headers['x-tenant-id'] = this.storeHandle;
        }
      }
      return config;
    });

    this.storeClient.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          console.log('🚨 Medusa Store API returned 401 Unauthorized:', error.config?.url);
          handleUnauthorizedWithSmartRedirect(this.storeHandle);
        }
        return Promise.reject(error);
      }
    );

    // Auth client interceptors (no auth token needed for auth endpoints)
    this.authClient.interceptors.request.use((config) => {
      // Don't add Authorization header for auth endpoints
      if (this.storeHandle) {
        config.headers['x-tenant-id'] = this.storeHandle;
      }
      return config;
    });

    this.authClient.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          console.log('🚨 Medusa Auth API returned 401 Unauthorized:', error.config?.url);
          // Don't redirect on auth failures as these are expected during login
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Get admin client for admin API calls (/admin/*)
   */
  get admin(): AxiosInstance {
    return this.adminClient;
  }

  /**
   * Get store client for store API calls (/store/*)
   */
  get store(): AxiosInstance {
    return this.storeClient;
  }

  /**
   * Get auth client for auth API calls (/auth/*)
   */
  get auth(): AxiosInstance {
    return this.authClient;
  }

  /**
   * Set store handle for subsequent requests
   */
  setStoreHandle(storeHandle: string): void {
    this.storeHandle = storeHandle;
  }

  /**
   * Get current store handle
   */
  getStoreHandle(): string | undefined {
    return this.storeHandle;
  }

  /**
   * Get base URL
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }
}

// Default client instance
export const medusaClient = new MedusaClient();

/**
 * Create store-specific client
 */
export const createMedusaClient = (config: MedusaClientConfig): MedusaClient => {
  return new MedusaClient(config);
};

/**
 * Store-specific clients cache
 */
const storeClients = new Map<string, MedusaClient>();

/**
 * Get or create store-specific client
 */
export const getMedusaClient = (storeHandle: string): MedusaClient => {
  if (!storeClients.has(storeHandle)) {
    const client = createMedusaClient({ storeHandle });
    storeClients.set(storeHandle, client);
    console.log(`✨ Created Medusa client for store: ${storeHandle}`);
  }
  
  return storeClients.get(storeHandle)!;
};

/**
 * Clear store client
 */
export const clearMedusaClient = (storeHandle: string): void => {
  storeClients.delete(storeHandle);
  console.log(`🗑️ Cleared Medusa client for store: ${storeHandle}`);
};

export default medusaClient;