let token = "";

export const setMedusaToken = (newToken: string) => {
  token = newToken;
};

const ADMIN_BASE_URL = process.env.NEXT_PUBLIC_MEDUSA_API_URL as string;

export class MedusaAdminAPI {
  private baseURL: string;
  private headers: HeadersInit;

  constructor() {
    this.baseURL = ADMIN_BASE_URL;
    this.headers = {
      "Content-Type": "application/json",
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  private async request<T>(url: string, options: RequestInit = {}): Promise<T> {
    const res = await fetch(`${this.baseURL}${url}`, {
      ...options,
      headers: this.headers,
    });

    if (!res.ok) throw new Error(await res.text());
    return res.json();
  }

  async login(email: string, password: string) {
    return this.request<{ access_token: string }>(`/auth`, {
      method: "POST",
      body: JSON.stringify({ email, password }),
    });
  }

  async getAdminUser() {
    return this.request<{ user: any }>(`/users/me`);
  }

  async listProducts() {
    return this.request<{ products: any[] }>(`/products`);
  }
}

export const medusaAdminAPI = new MedusaAdminAPI();
