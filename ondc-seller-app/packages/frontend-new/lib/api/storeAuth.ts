'use client';

// Types
export interface StoreLoginCredentials {
  email: string;
  password: string;
}

export interface StoreAuthResponse {
  access_token: string;
}

export interface StoreCustomer {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  has_account?: boolean;
  created_at?: string;
  updated_at?: string;
  addresses?: StoreAddress[];
  metadata?: {
    [key: string]: any;
  };
}

export interface StoreCustomerResponse {
  customer: StoreCustomer;
}

export interface StoreAddress {
  id: string;
  first_name: string;
  last_name: string;
  phone?: string;
  company?: string;
  address_1: string;
  address_2?: string;
  city: string;
  country_code: string;
  province: string;
  postal_code: string;
  address_name?: string;
  metadata?: {
    [key: string]: any;
  };
}

export interface StoreAddressesResponse {
  addresses: StoreAddress[];
}

// API Configuration
import { medusaClient } from './medusa-client';

// Helper function to get base URL
function getBaseUrl(): string {
  return medusaClient.getBaseUrl();
}

const getHeaders = (storeHandle: string, token?: string): HeadersInit => {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'x-tenant-id': storeHandle || 'default',
    'x-publishable-api-key': process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || ''
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  return headers;
};

// API Error Handler
const handleApiError = async (response: Response) => {
  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
    
    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorData.error || errorMessage;
    } catch {
      // If response is not JSON, use status text
    }
    
    throw new Error(errorMessage);
  }
};

// Store Auth API Class
class StoreAuthAPI {
  private baseUrl: string;
  
  constructor() {
    this.baseUrl = medusaClient.getBaseUrl();
  }
  
  // Store customer login
  async login(credentials: StoreLoginCredentials, storeHandle: string): Promise<StoreAuthResponse> {
    const url = `${medusaClient.getBaseUrl()}/auth/customer/emailpass`;
    
    console.log('🔐 Store customer login:', { url, email: credentials.email });
    
    const response = await fetch(url, {
      method: 'POST',
      headers: getHeaders(storeHandle),
      body: JSON.stringify(credentials),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Store customer login successful:', { hasToken: !!data.access_token });
    return data;
  }
  
  // Get store customer details
  async getCustomerDetails(token: string, storeHandle: string): Promise<StoreCustomerResponse> {
    const url = `${medusaClient.getBaseUrl()}/store/customers/me`;
    
    console.log('👤 Fetching store customer details:', url);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: getHeaders(storeHandle, token),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Store customer details fetched:', { customerId: data.customer?.id });
    return data;
  }
  
  // Get store customer addresses
  async getCustomerAddresses(token: string, storeHandle: string): Promise<StoreAddressesResponse> {
    const url = `${medusaClient.getBaseUrl()}/store/customers/me/addresses`;
    
    console.log('📍 Fetching store customer addresses:', url);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: getHeaders(storeHandle, token),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Store customer addresses fetched:', { count: data.addresses?.length || 0 });
    return data;
  }
}

// Export singleton instance
export const storeAuthAPI = new StoreAuthAPI();