import { medusaClient } from './medusa-client';

export interface BulkUploadResponse {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}

export const bulkUploadApi = {
  /**
   * Upload bulk products file
   */
  uploadProducts: async (file: File): Promise<BulkUploadResponse> => {
    try {
      const backendUrl = medusaClient.getBaseUrl();
      
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${config.medusaBaseUrl}/api/products/bulk-upload`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Upload failed');
      }

      const result = await response.json();
      
      return {
        success: true,
        data: result,
        message: result?.message || 'Upload successful!',
      };
    } catch (error) {
      console.error('Error uploading bulk products:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Something went wrong while uploading!',
      };
    }
  },
};