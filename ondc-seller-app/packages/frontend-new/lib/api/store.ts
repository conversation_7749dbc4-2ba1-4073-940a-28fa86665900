import { medusaClient as medusaStoreClient, strapiClient } from "./client";
import { medusaClient } from "./medusa-client";
import { Store, Product, ProductCategory, StoreResponse, ProductsResponse, CategoriesResponse } from "@/types";
import { demoProducts } from "../demo-data";

// Note: fetchStoreByHandle has been removed - now using store-configurations endpoint via strapiStoreService
// Store data should be fetched using the useStoreConfig hook or strapiStoreService.getStoreConfigByHandle()

export const fetchStoreProducts = async (
  storeHandle: string,
  limit: number = 12,
  offset: number = 0
): Promise<Product[]> => {
  try {
    const response = await medusaStoreClient<ProductsResponse>(
      `/products`,
      {
        headers: {
          'x-tenant-id': storeHandle,
          'x-publishable-api-key': process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || ''
        }
      }
    );
    // If API returns products, use them
    console.log("producstData==========::::::::::::",response)
    if (response?.products && response?.products.length > 0) {
      return response?.products;
    }
    
    // If no products from API, use mock data as fallback
    console.warn("No products from API, using mock data for store:", storeHandle);
    return demoProducts;
  } catch (error) {
    console.error("Error fetching store products, using mock data:", error);
    // Return mock data when API fails
    return demoProducts;
  }
};

// Helper function to structure flat categories into hierarchical structure
const structureCategoriesHierarchy = (flatCategories: any[]): ProductCategory[] => {
  console.log('🏗️ Structuring categories hierarchy from flat data:', flatCategories);
  
  // Create maps for quick lookup
  const categoryMap = new Map();
  const parentCategories: ProductCategory[] = [];
  
  // First pass: Create all category objects
  flatCategories.forEach(cat => {
    const category: ProductCategory = {
      id: cat.id,
      name: cat.name,
      description: cat.description || '',
      handle: cat.handle || cat.name?.toLowerCase().replace(/\s+/g, '-') || '',
      is_active: cat.is_active !== false, // Default to true if not specified
      is_internal: cat.is_internal || false,
      parent_category_id: cat.parent_category_id || null,
      metadata: cat.metadata || {},
      category_children: []
    };
    
    categoryMap.set(cat.id, category);
  });
  
  // Second pass: Build hierarchy
  categoryMap.forEach(category => {
    if (category.parent_category_id) {
      // This is a subcategory
      const parentCategory = categoryMap.get(category.parent_category_id);
      if (parentCategory) {
        parentCategory.category_children = parentCategory.category_children || [];
        parentCategory.category_children.push(category);
      }
    } else {
      // This is a parent category
      parentCategories.push(category);
    }
  });
  
  console.log('🏗️ Structured categories:', parentCategories);
  console.log('🏗️ Parent categories count:', parentCategories.length);
  
  return parentCategories;
};

export const fetchStoreCategories = async (storeHandle: string): Promise<ProductCategory[]> => {
  try {
    console.log('='.repeat(50));
    console.log('🔍 STARTING CATEGORIES API CALL');
    console.log('🔍 Store Handle:', storeHandle);
    console.log('🔍 API endpoint: /product-categories');
    console.log('🔍 Full URL will be:', `${medusaClient.getBaseUrl()}/store/product-categories`);
    console.log('🔍 Headers:', {
      'x-tenant-id': storeHandle,
      'x-publishable-api-key': process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || ''
    });
    
    console.log('🚀 Making API request...');
    const response = await medusaStoreClient<CategoriesResponse>(
      `/product-categories`,
      {
        headers: {
          'x-tenant-id': storeHandle,
          'x-publishable-api-key': process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || ''
        }
      }
    );

    
    // Extract categories from product_categories field

    
    const productCategoriesData = response?.product_categories || response.data || [];
    
    // Get the categories data from the correct field
    const allCategories = Array.isArray(productCategoriesData) ? productCategoriesData : [];
    
    if (allCategories.length === 0) {
      console.log('❌ NO CATEGORIES FOUND!');
      console.log('🔍 Checking if data exists in other fields...');
      console.log('🔍 Available fields in response.data:', Object.keys(response.data || {}));
      if (response.data) {
        Object.keys(response.data).forEach(key => {
          console.log(`🔍 Field '${key}' (${typeof response.data[key]}):`, response.data[key]);
        });
      }
      console.log('❌ RETURNING EMPTY ARRAY');
      console.log('='.repeat(50));
      return [];
    }
    
    
    // Filter to get only parent categories (those with parent_category_id = null)
    // The API already provides category_children populated
    const parentCategories = allCategories.filter(cat => cat.parent_category_id === null);
    

    
    // Transform to match our ProductCategory interface
    const transformedCategories: ProductCategory[] = parentCategories.map(cat => ({
      id: cat.id,
      name: cat.name,
      description: cat.description || '',
      handle: cat.handle,
      is_active: cat.is_active,
      is_internal: cat.is_internal,
      parent_category_id: cat.parent_category_id,
      metadata: cat.metadata || {},
      category_children: cat.category_children || []
    }));
    
    
    return transformedCategories;
  } catch (error) {
    console.log('='.repeat(50));
    console.error('❌ CRITICAL ERROR in fetchStoreCategories!');
    console.error('❌ Error object:', error);
    console.error('❌ Error type:', typeof error);
    console.error('❌ Error message:', error instanceof Error ? error.message : 'Unknown error');
    console.error('❌ Error stack:', error instanceof Error ? error.stack : undefined);
    
    if (error instanceof Error) {
      console.error('❌ Error name:', error.name);
      console.error('❌ Error cause:', error.cause);
    }
    
    console.log('❌ RETURNING EMPTY ARRAY DUE TO ERROR');
    console.log('='.repeat(50));
    // Return empty array when API fails
    return [];
  }
};

export const fetchFeaturedProducts = async (
  storeHandle: string,
  limit: number = 8
): Promise<Product[]> => {
  try {
    const response = await medusaStoreClient<ProductsResponse>(
      `/products`,
      {
        headers: {
          'x-tenant-id': storeHandle,
          'x-publishable-api-key': process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || ''
        }
      }
    );
    
    // If API returns products, use them
    if (response.products && response.products.length > 0) {
      return response.products;
    }
    
    // If no featured products from API, use mock data as fallback
    console.warn("No featured products from API, using mock data for store:", storeHandle);
    return demoProducts.slice(0, limit);
  } catch (error) {
    console.error("Error fetching featured products, using mock data:", error);
    // Return mock data when API fails
    return demoProducts.slice(0, limit);
  }
};

export const fetchProductsByCategory = async (
  storeHandle: string,
  categoryId: string,
  limit: number = 12
): Promise<Product[]> => {
  try {
    console.log('🛍️ FETCHING PRODUCTS BY CATEGORY');
    console.log('🛍️ Store Handle:', storeHandle);
    console.log('🛍️ Category ID:', categoryId);
    console.log('🛍️ API endpoint: /products?category_id=' + categoryId);
    
    const response = await medusaStoreClient<ProductsResponse>(
      `/products?category_id=${categoryId}`,
      {
        headers: {
          'x-tenant-id': storeHandle,
          'x-publishable-api-key': process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || ''
        }
      }
    );
    
    console.log('🛍️ Products by category response:', response);
    
    // If API returns products, use them
    if (response?.products && response?.products.length > 0) {
      console.log('🛍️ Found', response.products.length, 'products for category', categoryId);
      return response.products;
    }
    
    // If no products from API, use mock data filtered by category as fallback
    console.warn("No products from API for category, using mock data:", categoryId);
    return demoProducts.filter(product => 
      product.categories?.some(cat => cat.id === categoryId)
    ).slice(0, limit);
  } catch (error) {
    console.error("Error fetching products by category, using mock data:", error);
    // Return mock data when API fails
    return demoProducts.filter(product => 
      product.categories?.some(cat => cat.id === categoryId)
    ).slice(0, limit);
  }
};

// New function to fetch products by subcategory
export const fetchProductsBySubcategory = async (
  storeHandle: string,
  subcategoryId: string,
  limit: number = 12
): Promise<Product[]> => {
  try {
    console.log('🛍️ FETCHING PRODUCTS BY SUBCATEGORY');
    console.log('🛍️ Store Handle:', storeHandle);
    console.log('🛍️ Subcategory ID:', subcategoryId);
    console.log('🛍️ API endpoint: /products?category_id=' + subcategoryId);
    
    const response = await medusaStoreClient<ProductsResponse>(
      `/products?category_id=${subcategoryId}`,
      {
        headers: {
          'x-tenant-id': storeHandle,
          'x-publishable-api-key': process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || ''
        }
      }
    );
    
    console.log('🛍️ Products by subcategory response:', response);
    
    // If API returns products, use them
    if (response?.products && response?.products.length > 0) {
      console.log('🛍️ Found', response.products.length, 'products for subcategory', subcategoryId);
      return response.products;
    }
    
    // If no products from API, use mock data filtered by subcategory as fallback
    console.warn("No products from API for subcategory, using mock data:", subcategoryId);
    return demoProducts.filter(product => 
      product.categories?.some(cat => cat.id === subcategoryId)
    ).slice(0, limit);
  } catch (error) {
    console.error("Error fetching products by subcategory, using mock data:", error);
    // Return mock data when API fails
    return demoProducts.filter(product => 
      product.categories?.some(cat => cat.id === subcategoryId)
    ).slice(0, limit);
  }
};

export const fetchStoreSingleProduct = async (
  storeHandle: string,
  productId: string
): Promise<Product[]> => {
  try {
    const response = await medusaStoreClient<ProductsResponse>(
      `/products/${productId}`,
      {
        headers: {
          'x-tenant-id': storeHandle,
          'x-publishable-api-key': process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || ''
        }
      }
    );
    // If API returns products, use them
    console.log("producstData==========::::::::::::",response)
    if (response?.product) {
      return response?.product;
    }
    

    console.warn("No products from API, using mock data for store:", storeHandle);
    
  } catch (error) {
    console.error("Error fetching store products, using mock data:", error);

  }
};