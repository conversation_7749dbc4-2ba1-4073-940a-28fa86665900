import { medusaClient } from './medusa-client';

export interface BulkImportResponse {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}

// Helper function to get tenant ID from storage
const getTenantId = (): string => {
  let tenantId = 'default-tenant';
  
  if (typeof window !== 'undefined') {
    try {
      // First, try to get from store-config-storage (preferred for onboarding)
      const storeConfigStorage = localStorage.getItem('store-config-storage');
      if (storeConfigStorage) {
        const storeConfigData = JSON.parse(storeConfigStorage);
        const storeHandle = storeConfigData.state?.storeData?.handle || 
                           storeConfigData.state?.storeConfig?.store_handle ||
                           storeConfigData.state?.currentStoreHandle;
        
        if (storeHandle) {
          console.log('Tenant ID from store-config-storage:', storeHandle);
          return storeHandle;
        }
      }
      
      // Fallback to auth-storage
      const authStorage = localStorage.getItem('auth-storage');
      if (authStorage) {
        const authData = JSON.parse(authStorage);
        const user = authData.state?.user;
        
        // Check multiple possible locations for store handle
        const storeHandle = 
          user?.user?.metadata?.store_handle || // From nested API response structure
          user?.user?.store_handle || // From nested API response structure
          user?.store_handle || 
          user?.metadata?.store_handle ||
          null;
        
        if (storeHandle) {
          console.log('Tenant ID from auth-storage:', storeHandle);
          return storeHandle;
        }
      }
    } catch (error) {
      console.error('Error getting tenant ID from storage:', error);
    }
  }
  
  console.log('Using default tenant ID:', tenantId);
  return tenantId;
};

// Helper function to get auth headers
const getAuthHeaders = () => {
  let token = '';
  const tenantId = getTenantId();
  
  if (typeof window !== 'undefined') {
    try {
      // Get token from auth store
      const authStorage = localStorage.getItem('auth-storage');
      if (authStorage) {
        const authData = JSON.parse(authStorage);
        token = authData.state?.token || '';
      }
    } catch (error) {
      console.error('Error getting auth token:', error);
    }
  }
  
  console.log('Auth headers - Token exists:', !!token);
  console.log('Auth headers - Tenant ID:', tenantId);
  
  return {
    'Authorization': `Bearer ${token}`,
    'X-Tenant-ID': tenantId,
  };
};

export const bulkImportApi = {
  /**
   * Download template file in XLSX format
   * GET {medusa_url}/admin/product-import
   */
  downloadTemplate: async (): Promise<Blob> => {
    try {
      console.log('=== DOWNLOADING TEMPLATE ===');
      
      const backendUrl = medusaClient.getBaseUrl();
      const headers = getAuthHeaders();
      
      console.log('Request headers:', {
        ...headers,
        'Authorization': headers.Authorization ? 'Bearer [REDACTED]' : 'No token'
      });
      
      const response = await fetch(`${backendUrl}/admin/product-import`, {
        method: 'GET',
        headers: {
          ...headers,
          'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        },
      });
      
      console.log('API response status:', response.status);
      console.log('API response ok:', response.ok);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('API error response:', errorText);
        
        let errorMessage = `API error: ${response.status} ${response.statusText}`;
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.message || errorData.error || errorMessage;
        } catch (e) {
          // Use default error message if parsing fails
        }
        
        throw new Error(errorMessage);
      }
      
      const blob = await response.blob();
      console.log('Template downloaded successfully, blob size:', blob.size);
      
      return blob;
      
    } catch (error) {
      console.error('Error downloading template:', error);
      throw error;
    }
  },

  /**
   * Upload Excel file for product import
   * POST {medusa_url}/admin/product-import
   */
  uploadProductImport: async (file: File): Promise<BulkImportResponse> => {
    try {
      console.log('=== UPLOADING PRODUCT IMPORT FILE ===');
      console.log('File name:', file.name);
      console.log('File size:', file.size);
      console.log('File type:', file.type);
      
      const backendUrl = medusaClient.getBaseUrl();
      const headers = getAuthHeaders();
      
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', file);
      
      console.log('Request headers:', {
        ...headers,
        'Authorization': headers.Authorization ? 'Bearer [REDACTED]' : 'No token'
      });
      
      const response = await fetch(`${backendUrl}/admin/product-import`, {
        method: 'POST',
        headers: {
          // Don't set Content-Type for FormData, let browser set it with boundary
          ...headers,
        },
        body: formData,
      });
      
      console.log('API response status:', response.status);
      console.log('API response ok:', response.ok);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('API error response:', errorText);
        
        let errorMessage = `API error: ${response.status} ${response.statusText}`;
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.message || errorData.error || errorMessage;
        } catch (e) {
          // Use default error message if parsing fails
        }
        
        return {
          success: false,
          error: errorMessage,
        };
      }
      
      const data = await response.json();
      console.log('Upload response data:', data);
      
      return {
        success: true,
        data: data,
        message: 'Products imported successfully!',
      };
      
    } catch (error) {
      console.error('Error uploading product import file:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to upload file. Please try again.',
      };
    }
  },
};