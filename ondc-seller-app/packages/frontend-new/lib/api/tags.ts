import { medusaClient } from './medusa-client';

export interface Tag {
  id: string;
  name: string;
  slug?: string;
  description?: string;
  color?: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt?: string;
  products_count?: number;
}

export interface TagsResponse {
  tags: Tag[];
  count: number;
  limit: number;
  offset: number;
}

export const tagsApi = {
  getTags: async (storeHandle: string, params?: {
    limit?: number;
    offset?: number;
    search?: string;
    status?: string;
  }): Promise<TagsResponse> => {
    console.log('=== TAGS API CALL ===');
    console.log('Store handle (x-tenant-id):', storeHandle);
    console.log('Request params:', params);
    
    try {
      const response = await medusaClient.admin.get('/product-tags', {
        headers: {
          'x-tenant-id': storeHandle,
        },
        params,
      });
      
      console.log('Tags API response status:', response.status);
      console.log('Tags API response headers:', response.headers);
      console.log('Tags API response data:', response.data);
      console.log('Tags API response structure:', {
        dataType: typeof response.data,
        dataKeys: response.data ? Object.keys(response.data) : 'No data',
        tagsArray: Array.isArray(response.data?.tags),
        tagsCount: response.data?.tags?.length || 0,
        sampleTag: response.data?.tags?.[0] || 'No tags'
      });
      
      return response.data;
    } catch (error: any) {
      console.error('=== TAGS API ERROR ===');
      console.error('Error details:', error);
      
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
        console.error('Error response headers:', error.response.headers);
      }
      
      throw error;
    }
  },

  getTag: async (storeHandle: string, tagId: string): Promise<Tag> => {
    console.log('=== GET TAG API CALL ===');
    console.log('Store handle (x-tenant-id):', storeHandle);
    console.log('Tag ID:', tagId);
    
    try {
      const response = await medusaClient.admin.get(`/product-tags/${tagId}`, {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Get tag API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching tag:', error);
      throw error;
    }
  },

  createTag: async (storeHandle: string, tagData: Partial<Tag>): Promise<Tag> => {
    console.log('=== CREATE TAG API CALL ===');
    console.log('Store handle (x-tenant-id):', storeHandle);
    console.log('Tag data:', tagData);
    
    try {
      const response = await medusaClient.admin.post('/product-tags', tagData, {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Create tag API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error creating tag:', error);
      throw error;
    }
  },

  updateTag: async (storeHandle: string, tagId: string, tagData: Partial<Tag>): Promise<Tag> => {
    console.log('=== UPDATE TAG API CALL ===');
    console.log('Store handle (x-tenant-id):', storeHandle);
    console.log('Tag ID:', tagId);
    console.log('Tag data:', tagData);
    
    try {
      const response = await medusaClient.admin.put(`/product-tags/${tagId}`, tagData, {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Update tag API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error updating tag:', error);
      throw error;
    }
  },

  deleteTag: async (storeHandle: string, tagId: string): Promise<void> => {
    console.log('=== DELETE TAG API CALL ===');
    console.log('Store handle (x-tenant-id):', storeHandle);
    console.log('Tag ID:', tagId);
    
    try {
      await medusaClient.admin.delete(`/product-tags/${tagId}`, {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Tag deleted successfully');
    } catch (error: any) {
      console.error('Error deleting tag:', error);
      throw error;
    }
  },
};

export default tagsApi;