'use client';

import { StoreLocalStorage } from '@/lib/utils/storeIsolation/storageUtils';

// Types
export interface Region {
  id: string;
  name: string;
  currency_code: string;
  tax_rate: number;
  countries: Country[];
}

export interface Country {
  id: string;
  name: string;
  iso_2: string;
  iso_3: string;
}

export interface CartItem {
  id: string;
  title: string;
  description?: string;
  thumbnail?: string;
  variant_id: string;
  quantity: number;
  unit_price: number;
  total: number;
  metadata?: {
    total_amount?: number;
    total_tax?: number;
    shipping_charges?: number;
    grand_total?: number;
  };
}

export interface Cart {
  id: string;
  region_id: string;
  region: Region;
  items: CartItem[];
  total: number;
  subtotal: number;
  tax_total: number;
  shipping_total: number;
  created_at: string;
  updated_at: string;
  completed_at?: string | null;
}

export interface CreateCartPayload {
  region_id: string;
}

export interface AddToCartPayload {
  variant_id: string;
  quantity: number;
  metadata: {
    total_amount: number;
    total_tax: number;
    shipping_charges: number;
    grand_total: number;
  };
}

export interface UpdateLineItemPayload {
  quantity: number;
}

export interface UpdateCartPayload {
  shipping_address?: {
    first_name: string;
    last_name: string;
    address_1: string;
    address_2?: string;
    city: string;
    province: string;
    postal_code: string;
    country_code: string;
    phone?: string;
  };
  billing_address?: {
    first_name: string;
    last_name: string;
    address_1: string;
    address_2?: string;
    city: string;
    province: string;
    postal_code: string;
    country_code: string;
    phone?: string;
  };
}

export interface CreatePaymentCollectionPayload {
  cart_id: string;
}

export interface CreatePaymentSessionPayload {
  provider_id: string;
}

export interface PaymentCollectionResponse {
  payment_collection: {
    id: string;
    status: string;
    amount: number;
    currency_code: string;
    created_at: string;
    updated_at: string;
  };
}

export interface PaymentSessionResponse {
  payment_session: {
    id: string;
    status: string;
    amount: number;
    currency_code: string;
    provider_id: string;
    data: any;
    created_at: string;
    updated_at: string;
  };
}

export interface ShippingOption {
  id: string;
  name: string;
  price_incl_tax: number;
  data: any;
  metadata: any;
}

export interface ShippingOptionsResponse {
  shipping_options: ShippingOption[];
}

export interface AddShippingMethodPayload {
  option_id: string;
}

export interface ShippingMethodResponse {
  cart: Cart;
}

// API Response Types
export interface RegionsResponse {
  regions: Region[];
}

export interface CartResponse {
  cart: Cart;
}

// Store-specific cart storage utilities
export class StoreCartStorage {
  private storeHandle: string;
  private cartIdKey: string;

  constructor(storeHandle: string) {
    this.storeHandle = storeHandle;
    this.cartIdKey = `${storeHandle}-cart-id`; // Simple naming: {storeHandle}-cart-id
  }

  getStoredCartId(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(this.cartIdKey);
  }

  setStoredCartId(cartId: string): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(this.cartIdKey, cartId);
    console.log(`🔒 Stored cart ID for store ${this.storeHandle}:`, cartId);
  }

  removeStoredCartId(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(this.cartIdKey);
    console.log(`🗑️ Removed cart ID for store ${this.storeHandle}`);
  }

  clearAllCartData(): void {
    if (typeof window === 'undefined') return;
    // Clear all cart-related data for this store
    const keysToRemove: string[] = [];
    const storePrefix = `${this.storeHandle}-cart`;
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(storePrefix)) {
        keysToRemove.push(key);
      }
    }
    
    keysToRemove.forEach(key => localStorage.removeItem(key));
    console.log(`🧹 Cleared all cart data for store ${this.storeHandle}`);
  }
}

// Legacy functions for backward compatibility (now use default store)
const defaultCartStorage = new StoreLocalStorage('default', 'cart');

export const getStoredCartId = (): string | null => {
  return defaultCartStorage.getItem('cart-id');
};

export const setStoredCartId = (cartId: string): void => {
  defaultCartStorage.setItem('cart-id', cartId);
};

export const removeStoredCartId = (): void => {
  defaultCartStorage.removeItem('cart-id');
};

// Utility Functions
export const calculateCartMetadata = (
  unitPrice: number, 
  quantity: number, 
  taxRate: number = 0.08,
  shippingRate: number = 0
) => {
  const totalAmount = unitPrice * quantity;
  const totalTax = totalAmount * taxRate;
  const shippingCharges = totalAmount > 5000 ? 0 : shippingRate;
  const grandTotal = totalAmount + totalTax + shippingCharges;
  
  return {
    total_amount: Math.round(totalAmount * 100) / 100,
    total_tax: Math.round(totalTax * 100) / 100,
    shipping_charges: Math.round(shippingCharges * 100) / 100,
    grand_total: Math.round(grandTotal * 100) / 100,
  };
};

// API Configuration
import { medusaClient } from './medusa-client';
import { getAuthHeaders } from '@/lib/utils/tokenUtils';

// Helper function to get base URL
function getBaseUrl(): string {
  return medusaClient.getBaseUrl();
}

const getHeaders = (storeHandle: string): HeadersInit => {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'x-tenant-id': storeHandle || 'default',
    'x-publishable-api-key': process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || ''
  };

  // Add authentication token if available
  if (typeof window !== 'undefined') {
    try {
      // Try to get token from store-specific auth storage first
      const storeAuthKey = `${storeHandle}-auth`;
      let authStorage = localStorage.getItem(storeAuthKey);
      
  
      
      if (authStorage) {
        const authData = JSON.parse(authStorage);
        const token = authData.state?.token;
        
        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
          console.log('🔐 Added auth token to cart API headers');
        }
      }
    } catch (error) {
      console.warn('Failed to get auth token for cart API:', error);
    }
  }

  return headers;
};

// API Error Handler
const handleApiError = async (response: Response) => {
  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
    
    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorData.error || errorMessage;
    } catch {
      // If response is not JSON, use status text
    }
    
    throw new Error(errorMessage);
  }
};

// Cart API Class
export class CartAPI {
  private baseUrl: string;
  
  constructor() {
    this.baseUrl = getBaseUrl();
  }
  
  // Get regions
  async getRegions(name?: string,storeHandle:string): Promise<RegionsResponse> {
    const url = new URL(`${medusaClient.getBaseUrl()}/store/regions`);
    if (name) {
      url.searchParams.append('name', name);
    }
    
    console.log('🌍 Fetching regions:', url.toString());
    
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: getHeaders(storeHandle),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Regions response:', data);
    return data;
  }
  
  // Create cart
  async createCart(payload: CreateCartPayload,storeHandle:string): Promise<CartResponse> {
    const url = `${medusaClient.getBaseUrl()}/store/carts`;
    
    console.log('🛒 Creating cart:', { url, payload });
    
    const response = await fetch(url, {
      method: 'POST',
      headers: getHeaders(storeHandle),
      body: JSON.stringify(payload),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Cart created:', data);
    return data;
  }
  
  // Get cart
  async getCart(cartId: string,storeHandle:string): Promise<CartResponse> {
    const url = `${medusaClient.getBaseUrl()}/store/carts/${cartId}`;
    
    console.log('📦 Fetching cart:', url);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: getHeaders(storeHandle),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Cart fetched:', data);
    
    // Check if cart has completed_at field with a value
    if (data.cart && data.cart.completed_at !== null && data.cart.completed_at !== undefined) {
      console.log('🚫 Cart is completed, clearing stored cart ID and rejecting response');
      
      // Note: Cart ID clearing is now handled by IsolatedCartAPI
      console.log('🚫 Cart is completed - should be cleared by IsolatedCartAPI');
      
      // Throw an error to indicate the cart is completed and should not be used
      throw new Error('Cart has been completed and is no longer available for modifications');
    }
    
    return data;
  }
  
  // Add line item to cart
  async addLineItem(cartId: string, payload: AddToCartPayload,storeHandle:string): Promise<CartResponse> {
    const url = `${medusaClient.getBaseUrl()}/store/carts/${cartId}/line-items`;
    
    console.log('➕ Adding line item:', { url, payload });
    
    const response = await fetch(url, {
      method: 'POST',
      headers: getHeaders(storeHandle),
      body: JSON.stringify(payload),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Line item added:', data);
    return data;
  }
  
  // Update line item
  async updateLineItem(
    cartId: string, 
    lineItemId: string, 
    payload: UpdateLineItemPayload,
    storeHandle:string
  ): Promise<CartResponse> {
    const url = `${medusaClient.getBaseUrl()}/store/carts/${cartId}/line-items/${lineItemId}`;
    
    console.log('📝 Updating line item:', { url, payload });
    
    const response = await fetch(url, {
      method: 'POST',
      headers: getHeaders(storeHandle),
      body: JSON.stringify(payload),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Line item updated:', data);
    return data;
  }
  
  // Remove line item
  async removeLineItem(cartId: string, lineItemId: string,storeHandle:string): Promise<CartResponse> {
    const url = `${medusaClient.getBaseUrl()}/store/carts/${cartId}/line-items/${lineItemId}`;
    
    console.log('🗑️ Removing line item:', url);
    
    const response = await fetch(url, {
      method: 'DELETE',
      headers: getHeaders(storeHandle),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Line item removed:', data);
    return data;
  }
  
  // Update cart with shipping and billing addresses
  async updateCart(
    cartId: string, 
    payload: UpdateCartPayload,
    storeHandle: string
  ): Promise<CartResponse> {
    const url = `${medusaClient.getBaseUrl()}/store/carts/${cartId}`;
    
    console.log('📝 Updating cart:', { url, payload });
    
    const response = await fetch(url, {
      method: 'POST',
      headers: getHeaders(storeHandle),
      body: JSON.stringify(payload),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Cart updated:', data);
    return data;
  }
  
  // Create payment collection
  async createPaymentCollection(
    payload: CreatePaymentCollectionPayload,
    storeHandle: string
  ): Promise<PaymentCollectionResponse> {
    const url = `${medusaClient.getBaseUrl()}/store/payment-collections`;
    
    console.log('💳 Creating payment collection:', { url, payload });
    
    const response = await fetch(url, {
      method: 'POST',
      headers: getHeaders(storeHandle),
      body: JSON.stringify(payload),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Payment collection created:', data);
    return data;
  }
  
  // Create payment session
  async createPaymentSession(
    paymentCollectionId: string,
    payload: CreatePaymentSessionPayload,
    storeHandle: string
  ): Promise<PaymentSessionResponse> {
    const url = `${medusaClient.getBaseUrl()}/store/payment-collections/${paymentCollectionId}/payment-sessions`;
    
    console.log('💳 Creating payment session:', { url, payload });
    
    const response = await fetch(url, {
      method: 'POST',
      headers: getHeaders(storeHandle),
      body: JSON.stringify(payload),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Payment session created:', data);
    return data;
  }
  
  // Get shipping options for cart
  async getShippingOptions(
    cartId: string,
    storeHandle: string
  ): Promise<ShippingOptionsResponse> {
    const url = `${medusaClient.getBaseUrl()}/store/shipping-options?cart_id=${cartId}`;
    
    console.log('🚚 Getting shipping options:', url);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: getHeaders(storeHandle),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Shipping options fetched:', data);
    return data;
  }
  
  // Add shipping method to cart
  async addShippingMethod(
    cartId: string,
    payload: AddShippingMethodPayload,
    storeHandle: string
  ): Promise<ShippingMethodResponse> {
    const url = `${medusaClient.getBaseUrl()}/store/carts/${cartId}/shipping-methods`;
    
    console.log('🚚 Adding shipping method:', { url, payload });
    
    const response = await fetch(url, {
      method: 'POST',
      headers: getHeaders(storeHandle),
      body: JSON.stringify(payload),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Shipping method added:', data);
    return data;
  }
  
  // Complete cart and create order
  async completeCart(
    cartId: string,
    storeHandle: string
  ): Promise<{ order: any }> {
    const url = `${medusaClient.getBaseUrl()}/store/carts/${cartId}/complete`;
    
    console.log('🏁 Completing cart:', url);
    console.log('🔐 Headers for cart completion:', getHeaders(storeHandle));
    
    const response = await fetch(url, {
      method: 'POST',
      headers: getHeaders(storeHandle),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Cart completed:', data);
    return data;
  }
}

// Store-isolated Cart API wrapper
export class IsolatedCartAPI {
  private cartAPI: CartAPI;
  private storage: StoreCartStorage;
  private storeHandle: string;

  constructor(storeHandle: string) {
    this.cartAPI = new CartAPI();
    this.storage = new StoreCartStorage(storeHandle);
    this.storeHandle = storeHandle;
  }

  // Storage methods
  getStoredCartId(): string | null {
    return this.storage.getStoredCartId();
  }

  setStoredCartId(cartId: string): void {
    this.storage.setStoredCartId(cartId);
  }

  removeStoredCartId(): void {
    this.storage.removeStoredCartId();
  }

  clearAllCartData(): void {
    this.storage.clearAllCartData();
  }

  // API methods with store handle automatically included
  async getRegions(name?: string): Promise<RegionsResponse> {
    return this.cartAPI.getRegions(name, this.storeHandle);
  }

  async createCart(payload: CreateCartPayload): Promise<CartResponse> {
    const response = await this.cartAPI.createCart(payload, this.storeHandle);
    
    // Automatically store the cart ID for this store
    if (response.cart?.id) {
      this.setStoredCartId(response.cart.id);
    }
    
    return response;
  }

  async getCart(cartId: string): Promise<CartResponse> {
    try {
      return await this.cartAPI.getCart(cartId, this.storeHandle);
    } catch (error) {
      // If cart is completed or doesn't exist, clear stored cart ID
      if (error instanceof Error && error.message.includes('completed')) {
        this.removeStoredCartId();
      }
      throw error;
    }
  }

  async addLineItem(cartId: string, payload: AddToCartPayload): Promise<CartResponse> {
    return this.cartAPI.addLineItem(cartId, payload, this.storeHandle);
  }

  async updateLineItem(
    cartId: string, 
    lineItemId: string, 
    payload: UpdateLineItemPayload
  ): Promise<CartResponse> {
    return this.cartAPI.updateLineItem(cartId, lineItemId, payload, this.storeHandle);
  }

  async removeLineItem(cartId: string, lineItemId: string): Promise<CartResponse> {
    return this.cartAPI.removeLineItem(cartId, lineItemId, this.storeHandle);
  }

  async updateCart(cartId: string, payload: UpdateCartPayload): Promise<CartResponse> {
    return this.cartAPI.updateCart(cartId, payload, this.storeHandle);
  }

  async createPaymentCollection(payload: CreatePaymentCollectionPayload): Promise<PaymentCollectionResponse> {
    return this.cartAPI.createPaymentCollection(payload, this.storeHandle);
  }

  async createPaymentSession(
    paymentCollectionId: string,
    payload: CreatePaymentSessionPayload
  ): Promise<PaymentSessionResponse> {
    return this.cartAPI.createPaymentSession(paymentCollectionId, payload, this.storeHandle);
  }

  async getShippingOptions(cartId: string): Promise<ShippingOptionsResponse> {
    return this.cartAPI.getShippingOptions(cartId, this.storeHandle);
  }

  async addShippingMethod(cartId: string, payload: AddShippingMethodPayload): Promise<ShippingMethodResponse> {
    return this.cartAPI.addShippingMethod(cartId, payload, this.storeHandle);
  }

  async completeCart(cartId: string): Promise<{ order: any }> {
    const response = await this.cartAPI.completeCart(cartId, this.storeHandle);
    
    // Clear cart data after completion
    this.removeStoredCartId();
    
    return response;
  }
}

// Store instances cache
const cartAPIInstances = new Map<string, IsolatedCartAPI>();

/**
 * Get or create store-specific cart API instance
 */
export const getStoreCartAPI = (storeHandle: string): IsolatedCartAPI => {
  if (!storeHandle) {
    throw new Error('Store handle is required for cart operations');
  }

  if (!cartAPIInstances.has(storeHandle)) {
    const cartAPI = new IsolatedCartAPI(storeHandle);
    cartAPIInstances.set(storeHandle, cartAPI);
    console.log(`✨ Created new cart API instance for store: ${storeHandle}`);
  }

  return cartAPIInstances.get(storeHandle)!;
};

/**
 * Clear cart API instance for specific store
 */
export const clearStoreCartAPI = (storeHandle: string): void => {
  const cartAPI = cartAPIInstances.get(storeHandle);
  if (cartAPI) {
    cartAPI.clearAllCartData();
    cartAPIInstances.delete(storeHandle);
    console.log(`🗑️ Cleared cart API for store: ${storeHandle}`);
  }
};

/**
 * Store-specific cart storage utilities (for direct use)
 */
export const getStoreCartStorage = (storeHandle: string): StoreCartStorage => {
  return new StoreCartStorage(storeHandle);
};

// Export singleton instance for backward compatibility
export const cartAPI = new CartAPI();
