import { SearchResult, SearchSuggestion, SearchFilters } from '@/stores/searchStore';
import { medusaClient } from './medusa-client';

export interface SearchResponse {
  hits: SearchResult[];
  query: string;
  processingTimeMs: number;
  limit: number;
  offset: number;
  estimatedTotalHits: number;
  facetDistribution?: Record<string, Record<string, number>>;
  tenant_id: string;
  filters_applied: {
    tenant_id: string;
    status?: string;
    category_id?: string;
    collection_id?: string;
  };
}

export interface SuggestionsResponse {
  suggestions: SearchSuggestion[];
  query: string;
  tenant_id: string;
}

export interface SearchParams {
  q?: string;
  limit?: number;
  offset?: number;
  category_id?: string;
  collection_id?: string;
  status?: string;
  sort?: string;
}

/**
 * Search API service for interacting with the Medusa backend search endpoints
 */
export class SearchAPI {
  private baseUrl: string;
  private publishableKey: string;

  constructor(storeHandle: string) {
    this.baseUrl = medusaClient.getBaseUrl();
    this.publishableKey = process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || '';
  }

  private getHeaders(tenantId: string): HeadersInit {
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'x-tenant-id': tenantId,
      'x-publishable-api-key': this.publishableKey,
    };
  }

  private buildSearchParams(params: SearchParams): URLSearchParams {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, value.toString());
      }
    });

    return searchParams;
  }

  /**
   * Search products using the backend search API
   */
  async searchProducts(
    tenantId: string,
    query: string,
    filters: SearchFilters = {},
    options: {
      limit?: number;
      offset?: number;
      sort?: string;
    } = {}
  ): Promise<SearchResponse> {
    try {
      const params: SearchParams = {
        q: query,
        limit: options.limit || 20,
        offset: options.offset || 0,
        sort: options.sort || 'created_at:desc',
        status: filters.status || 'published',
        category_id: filters.category_id,
        collection_id: filters.collection_id,
      };

      const searchParams = this.buildSearchParams(params);
      const url = `${medusaClient.getBaseUrl()}/store/search?${searchParams}`;

      console.log(`🔍 [SEARCH-API] Searching: "${query}" for tenant: ${tenantId}`);

      const response = await fetch(url, {
        method: 'GET',
        headers: this.getHeaders(tenantId),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Search failed: ${response.status} ${response.statusText}`);
      }

      const data: SearchResponse = await response.json();
      
      console.log(`✅ [SEARCH-API] Found ${data.hits.length} results in ${data.processingTimeMs}ms`);
      
      return data;
    } catch (error) {
      console.error('❌ [SEARCH-API] Search error:', error);
      throw error;
    }
  }

  /**
   * Get search suggestions/autocomplete
   */
  async getSuggestions(
    tenantId: string,
    query: string,
    limit: number = 5
  ): Promise<SuggestionsResponse> {
    try {
      if (!query || query.length < 2) {
        return {
          suggestions: [],
          query,
          tenant_id: tenantId,
        };
      }

      const url = `${medusaClient.getBaseUrl()}/store/search/suggestions`;

      console.log(`💡 [SEARCH-API] Getting suggestions for: "${query}"`);

      const response = await fetch(url, {
        method: 'POST',
        headers: this.getHeaders(tenantId),
        body: JSON.stringify({
          query,
          limit,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Suggestions failed: ${response.status} ${response.statusText}`);
      }

      const data: SuggestionsResponse = await response.json();
      
      console.log(`✅ [SEARCH-API] Got ${data.suggestions.length} suggestions`);
      
      return data;
    } catch (error) {
      console.error('❌ [SEARCH-API] Suggestions error:', error);
      throw error;
    }
  }

  /**
   * Direct MeiliSearch integration (fallback if backend search is not available)
   */
  async searchWithMeiliSearch(
    tenantId: string,
    query: string,
    filters: SearchFilters = {},
    options: {
      limit?: number;
      offset?: number;
      sort?: string;
    } = {}
  ): Promise<SearchResponse> {
    try {
      // This would use the MeiliSearch client directly
      // For now, we'll use the backend API
      return this.searchProducts(tenantId, query, filters, options);
    } catch (error) {
      console.error('❌ [SEARCH-API] MeiliSearch direct search error:', error);
      throw error;
    }
  }
}

/**
 * Create a search API instance for a specific store
 */
export const createSearchAPI = (storeHandle: string) => {
  return new SearchAPI(storeHandle);
};

/**
 * Default search API instance
 */
export const searchAPI = new SearchAPI('default');

/**
 * Search hook for React components
 */
export const useSearchAPI = (storeHandle: string) => {
  return createSearchAPI(storeHandle);
};
