'use client';

import { getAuthHeaders } from '@/lib/utils/tokenUtils';
import { medusaClient } from './medusa-client';

/**
 * Centralized API client with automatic token handling
 * Provides consistent API calling patterns across the application
 */

export interface ApiClientConfig {
  baseUrl?: string;
  storeHandle?: string;
  defaultHeaders?: Record<string, string>;
}

export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  status: number;
  ok: boolean;
}

export class ApiClient {
  private baseUrl: string;
  private storeHandle?: string;
  private defaultHeaders: Record<string, string>;

  constructor(config: ApiClientConfig = {}) {
    this.baseUrl = config.baseUrl || medusaClient.getBaseUrl();
    this.storeHandle = config.storeHandle;
    this.defaultHeaders = config.defaultHeaders || {};
  }

  /**
   * Set store handle for subsequent requests
   */
  setStoreHandle(storeHandle: string): void {
    this.storeHandle = storeHandle;
  }

  /**
   * Get current store handle
   */
  getStoreHandle(): string | undefined {
    return this.storeHandle;
  }

  /**
   * Check if client has authentication token
   */
  isAuthenticated(): boolean {
    const tokenInfo = getAuthToken(this.storeHandle);
    return tokenInfo.token !== null;
  }

  /**
   * Get current token info
   */
  getTokenInfo() {
    return getAuthToken(this.storeHandle);
  }

  /**
   * Make authenticated GET request
   */
  async get<T = any>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  /**
   * Make authenticated POST request
   */
  async post<T = any>(endpoint: string, data?: any, options: RequestInit = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Make authenticated PUT request
   */
  async put<T = any>(endpoint: string, data?: any, options: RequestInit = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Make authenticated PATCH request
   */
  async patch<T = any>(endpoint: string, data?: any, options: RequestInit = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Make authenticated DELETE request
   */
  async delete<T = any>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }

  /**
   * Make authenticated request with automatic token handling
   */
  async request<T = any>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    const url = endpoint.startsWith('http') ? endpoint : `${this.baseUrl}${endpoint}`;
    
    // Merge headers with auth headers
    const headers = getAuthHeaders(this.storeHandle, {
      ...this.defaultHeaders,
      ...(options.headers as Record<string, string> || {}),
    });

    const requestOptions: RequestInit = {
      ...options,
      headers,
    };

    console.log(`🌐 API Request: ${options.method || 'GET'} ${url}`);
    console.log(`🔐 Auth token: ${headers.Authorization ? 'present' : 'missing'}`);
    console.log(`🏪 Store handle: ${this.storeHandle || 'none'}`);

    try {
      const response = await fetch(url, requestOptions);
      
      let data: T | undefined;
      let error: string | undefined;

      // Try to parse response as JSON
      try {
        const responseText = await response.text();
        if (responseText) {
          data = JSON.parse(responseText);
        }
      } catch (parseError) {
        console.warn('Failed to parse response as JSON:', parseError);
        if (!response.ok) {
          error = `HTTP ${response.status}: ${response.statusText}`;
        }
      }

      // Handle error responses
      if (!response.ok && !error) {
        error = (data as any)?.message || (data as any)?.error || `HTTP ${response.status}: ${response.statusText}`;
      }

      const result: ApiResponse<T> = {
        data,
        error,
        status: response.status,
        ok: response.ok,
      };

      if (response.ok) {
        console.log(`✅ API Success: ${options.method || 'GET'} ${url}`);
      } else {
        console.error(`❌ API Error: ${options.method || 'GET'} ${url}`, error);
      }

      return result;

    } catch (networkError) {
      console.error(`🌐 Network Error: ${options.method || 'GET'} ${url}`, networkError);
      
      return {
        error: networkError instanceof Error ? networkError.message : 'Network error occurred',
        status: 0,
        ok: false,
      };
    }
  }
}

/**
 * Default API client instance
 */
export const apiClient = new ApiClient();

/**
 * Create store-specific API client
 */
export const createStoreApiClient = (storeHandle: string, config: Omit<ApiClientConfig, 'storeHandle'> = {}): ApiClient => {
  return new ApiClient({
    ...config,
    storeHandle,
  });
};

/**
 * Store-specific API clients cache
 */
const storeApiClients = new Map<string, ApiClient>();

/**
 * Get or create store-specific API client
 */
export const getStoreApiClient = (storeHandle: string): ApiClient => {
  if (!storeApiClients.has(storeHandle)) {
    const client = createStoreApiClient(storeHandle);
    storeApiClients.set(storeHandle, client);
    console.log(`✨ Created API client for store: ${storeHandle}`);
  }
  
  return storeApiClients.get(storeHandle)!;
};

/**
 * Clear store API client
 */
export const clearStoreApiClient = (storeHandle: string): void => {
  storeApiClients.delete(storeHandle);
  console.log(`🗑️ Cleared API client for store: ${storeHandle}`);
};

/**
 * Utility functions for common API patterns
 */
export const apiUtils = {
  /**
   * Handle API response with automatic error handling
   */
  handleResponse: async <T>(response: ApiResponse<T>): Promise<T> => {
    if (!response.ok) {
      throw new Error(response.error || `API error: ${response.status}`);
    }
    
    if (response.data === undefined) {
      throw new Error('No data received from API');
    }
    
    return response.data;
  },

  /**
   * Create paginated request
   */
  paginate: (limit: number = 20, offset: number = 0) => ({
    limit,
    offset,
  }),

  /**
   * Create query string from object
   */
  queryString: (params: Record<string, any>): string => {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });
    
    const queryString = searchParams.toString();
    return queryString ? `?${queryString}` : '';
  },
};

export default apiClient;