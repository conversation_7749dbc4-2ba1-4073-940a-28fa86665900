'use client';

// Types
export interface OrderItem {
  id: string;
  title: string;
  description?: string;
  thumbnail?: string;
  variant_id: string;
  quantity: number;
  unit_price: number;
  total: number;
  metadata?: any;
}

export interface OrderAddress {
  first_name: string;
  last_name: string;
  address_1: string;
  address_2?: string;
  city: string;
  province: string;
  postal_code: string;
  country_code: string;
  phone?: string;
}

export interface Order {
  id: string;
  display_id: number;
  status?: string;
  fulfillment_status?: string;
  payment_status?: string;
  total?: number;
  subtotal?: number;
  tax_total?: number;
  shipping_total?: number;
  currency_code?: string;
  items?: OrderItem[];
  shipping_address?: OrderAddress;
  billing_address?: OrderAddress;
  customer?: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
    phone?: string;
  };
  created_at: string;
  updated_at: string;
  metadata?: any;
}

// API Response Types
export interface OrderResponse {
  order: Order;
}

export interface OrdersResponse {
  orders: Order[];
  count: number;
  offset: number;
  limit: number;
}

// API Configuration
import { medusaClient } from '../medusa-client';

const getHeaders = (storeHandle: string, token?: string): HeadersInit => {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'x-tenant-id': storeHandle || 'default',
    'x-publishable-api-key': process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || ''
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  return headers;
};

// API Error Handler
const handleApiError = async (response: Response) => {
  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
    
    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorData.error || errorMessage;
    } catch {
      // If response is not JSON, use status text
    }
    
    throw new Error(errorMessage);
  }
};

// Orders API Class
class OrdersAPI {
  private baseUrl: string;
  
  constructor() {
    this.baseUrl = medusaClient.getBaseUrl();
  }
  
  // Get order by ID
  async getOrder(orderId: string, storeHandle: string, token?: string): Promise<OrderResponse> {
    const url = `${this.baseUrl}/store/orders/${orderId}`;
    
    console.log('📦 Fetching order:', url);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: getHeaders(storeHandle, token),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Order fetched:', data);
    return data;
  }
  
  // Get orders list (for customer)
  async getOrders(storeHandle: string, token?: string, params?: {
    limit?: number;
    offset?: number;
    customer_id?: string;
  }): Promise<OrdersResponse> {
    const url = new URL(`${this.baseUrl}/store/orders`);
    
    // Add query parameters
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, value.toString());
        }
      });
    }
    
    console.log('📋 Fetching orders:', url.toString());
    
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: getHeaders(storeHandle, token),
    });
    
    await handleApiError(response);
    const data = await response.json();
    
    console.log('✅ Orders fetched:', data);
    return data;
  }
}

// Export singleton instance
export const ordersAPI = new OrdersAPI();