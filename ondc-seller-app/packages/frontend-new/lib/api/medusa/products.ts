// Medusa Product interfaces (based on Medusa's actual API response structure)
export interface MedusaProduct {
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  handle: string;
  is_giftcard: boolean;
  status: 'draft' | 'proposed' | 'published' | 'rejected';
  images: MedusaImage[];
  thumbnail?: string;
  options: MedusaProductOption[];
  variants: MedusaProductVariant[];
  categories?: MedusaProductCategory[];
  collection?: MedusaCollection;
  tags?: MedusaProductTag[];
  type?: MedusaProductType;
  weight?: number;
  length?: number;
  height?: number;
  width?: number;
  hs_code?: string;
  origin_country?: string;
  mid_code?: string;
  material?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MedusaImage {
  id: string;
  url: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MedusaProductOption {
  id: string;
  title: string;
  values: MedusaProductOptionValue[];
  product_id: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MedusaProductOptionValue {
  id: string;
  value: string;
  option_id: string;
  variant_id: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MedusaProductVariant {
  id: string;
  title: string;
  product_id: string;
  sku?: string;
  barcode?: string;
  ean?: string;
  upc?: string;
  variant_rank?: number;
  inventory_quantity: number;
  allow_backorder: boolean;
  manage_inventory: boolean;
  weight?: number;
  length?: number;
  height?: number;
  width?: number;
  hs_code?: string;
  origin_country?: string;
  mid_code?: string;
  material?: string;
  metadata?: Record<string, any>;
  prices: MedusaPrice[];
  options: MedusaProductOptionValue[];
  created_at: string;
  updated_at: string;
}

export interface MedusaPrice {
  id: string;
  currency_code: string;
  amount: number;
  min_quantity?: number;
  max_quantity?: number;
  price_list_id?: string;
  variant_id: string;
  region_id?: string;
  created_at: string;
  updated_at: string;
}

export interface MedusaProductCategory {
  id: string;
  name: string;
  handle: string;
  description?: string;
  is_active: boolean;
  is_internal: boolean;
  rank?: number;
  parent_category_id?: string;
  parent_category?: MedusaProductCategory;
  category_children?: MedusaProductCategory[];
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MedusaCollection {
  id: string;
  title: string;
  handle: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MedusaProductTag {
  id: string;
  value: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MedusaProductType {
  id: string;
  value: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MedusaProductResponse {
  product: MedusaProduct;
}

export interface MedusaProductsResponse {
  products: MedusaProduct[];
  count: number;
  offset: number;
  limit: number;
}

// API Configuration interface
export interface MedusaApiConfig {
  baseUrl: string;
  publishableKey?: string;
  tenantId?: string;
}

// Transform Medusa product to our internal Product type
export function transformMedusaProduct(medusaProduct: MedusaProduct): any {
  // Get the primary price (first price from first variant)
  const primaryPrice = medusaProduct.variants?.[0]?.prices?.[0];
  const price = primaryPrice ? primaryPrice.amount : 0;
  
  // Extract images
  const images = medusaProduct.images?.map(img => img.url) || [];
  if (medusaProduct.thumbnail && !images.includes(medusaProduct.thumbnail)) {
    images.unshift(medusaProduct.thumbnail);
  }
  
  // Transform variants
  const variants = medusaProduct.variants?.map(variant => {
    const variantPrice = variant.prices?.[0];
    const options: Record<string, string> = {};
    
    // Build options object from variant options
    variant.options?.forEach(option => {
      const optionDef = medusaProduct.options?.find(opt => opt.id === option.option_id);
      if (optionDef) {
        options[optionDef.title.toLowerCase()] = option.value;
      }
    });
    
    return {
      id: variant.id,
      title: variant.title,
      price: variantPrice?.amount,
      stock: variant.inventory_quantity || 0,
      sku: variant.sku || '',
      options,
      images: images, // Use product images for variants
    };
  }) || [];
  
  // Extract category information
  const primaryCategory = medusaProduct.categories?.[0];
  const category = primaryCategory?.handle || 'uncategorized';
  const subcategory = primaryCategory?.parent_category?.handle || category;
  
  // Extract tags
  const tags = medusaProduct.tags?.map(tag => tag.value) || [];
  
  // Build features from metadata or options
  const features: string[] = [];
  if (medusaProduct.material) features.push(`Material: ${medusaProduct.material}`);
  if (medusaProduct.weight) features.push(`Weight: ${medusaProduct.weight}g`);
  if (medusaProduct.origin_country) features.push(`Made in ${medusaProduct.origin_country}`);
  
  // Build specifications
  const specifications: Record<string, string> = {};
  if (medusaProduct.material) specifications['Material'] = medusaProduct.material;
  if (medusaProduct.weight) specifications['Weight'] = `${medusaProduct.weight}g`;
  if (medusaProduct.origin_country) specifications['Origin'] = medusaProduct.origin_country;
  if (medusaProduct.hs_code) specifications['HS Code'] = medusaProduct.hs_code;
  
  // Add metadata to specifications
  if (medusaProduct.metadata) {
    Object.entries(medusaProduct.metadata).forEach(([key, value]) => {
      if (typeof value === 'string') {
        specifications[key] = value;
      }
    });
  }
  
  return {
    id: medusaProduct.id,
    title: medusaProduct.title,
    description: medusaProduct.description || '',
    price,
    images,
    category,
    subcategory,
    brand: medusaProduct.collection?.title || medusaProduct.type?.value || 'Unknown',
    rating: 4.5, // Default rating since Medusa doesn't have built-in reviews
    reviewCount: 0, // Default review count
    variants,
    tags,
    features,
    specifications,
    createdAt: medusaProduct.created_at,
    updatedAt: medusaProduct.updated_at,
    // Additional Medusa-specific fields
    handle: medusaProduct.handle,
    status: medusaProduct.status,
    thumbnail: medusaProduct.thumbnail,
  };
}

/**
 * Build headers for Medusa Store API requests
 */
function buildMedusaHeaders(config: MedusaApiConfig): HeadersInit {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  };

  // Add publishable key if provided
  if (config.publishableKey) {
    headers['x-publishable-api-key'] = config.publishableKey;
  }

  // Add tenant ID if provided
  if (config.tenantId) {
    headers['x-tenant-id'] = config.tenantId;
  }

  return headers;
}

// Medusa Store API service
export const medusaStoreProductsApi = {
  /**
   * Get a single product by ID from Medusa store API
   */
  getProduct: async (config: MedusaApiConfig, productId: string): Promise<any> => {
    console.log('=== MEDUSA STORE PRODUCT API CALL ===');
    console.log('Medusa Config:', config);
    console.log('Product ID:', productId);
    
    try {
      const url = `${config.baseUrl}/store/products/${productId}`;
      console.log('Full API URL:', url);
      
      const headers = buildMedusaHeaders(config);
      console.log('Request Headers:', headers);
      
      const response = await fetch(url, {
        method: 'GET',
        headers,
      });
      
      console.log('API Response Status:', response.status);
      console.log('response::::::::<>',response);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error Response:', errorText);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
      
      const data: MedusaProductResponse = await response.json();
      console.log('Raw Medusa Product Data:', data);
      
      if (!data.product) {
        throw new Error('Product not found in response');
      }
      
      // Transform Medusa product to our internal format
      const transformedProduct = transformMedusaProduct(data.product);
      console.log('Transformed Product:', transformedProduct);
      
      return transformedProduct;
    } catch (error: any) {
      console.error('=== MEDUSA STORE PRODUCT API ERROR ===');
      console.error('Error details:', error);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
      
      throw error;
    }
  },

  /**
   * Get multiple products from Medusa store API
   */
  getProducts: async (config: MedusaApiConfig, params?: {
    limit?: number;
    offset?: number;
    category_id?: string[];
    collection_id?: string[];
    tags?: string[];
    title?: string;
    description?: string;
    handle?: string;
    is_giftcard?: boolean;
    created_at?: any;
    updated_at?: any;
  }): Promise<{ products: any[]; count: number; offset: number; limit: number }> => {
    console.log('=== MEDUSA STORE PRODUCTS API CALL ===');
    console.log('Medusa Config:', config);
    console.log('Query Params:', params);
    
    try {
      const url = new URL(`${config.baseUrl}/store/products`);
      
      // Add query parameters
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              value.forEach(v => url.searchParams.append(key, v.toString()));
            } else {
              url.searchParams.append(key, value.toString());
            }
          }
        });
      }
      
      console.log('Full API URL:', url.toString());
      
      const headers = buildMedusaHeaders(config);
      console.log('Request Headers:', headers);
      
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers,
      });
      
      console.log('API Response Status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error Response:', errorText);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
      
      const data: MedusaProductsResponse = await response.json();
      console.log('Raw Medusa Products Data:', data);
      
      // Transform all products
      const transformedProducts = data.products.map(transformMedusaProduct);
      console.log('Transformed Products Count:', transformedProducts.length);
      
      return {
        products: transformedProducts,
        count: data.count,
        offset: data.offset,
        limit: data.limit,
      };
    } catch (error: any) {
      console.error('=== MEDUSA STORE PRODUCTS API ERROR ===');
      console.error('Error details:', error);
      
      throw error;
    }
  },
};

export default medusaStoreProductsApi;