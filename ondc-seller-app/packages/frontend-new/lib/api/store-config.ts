// lib/api/store-config.ts
import { strapiStoreService, type NormalizedStore } from './strapi-store';

export interface StoreConfig {
  id: string;
  name: string;
  handle: string;
  description?: string;
  logo?: string;
  medusa_base_url?: string;
  medusa_publishable_key?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    postal_code?: string;
    country?: string;
  };
  contact?: {
    email?: string;
    phone?: string;
    website?: string;
  };
  social_media?: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
  };
  business_hours?: {
    monday?: string;
    tuesday?: string;
    wednesday?: string;
    thursday?: string;
    friday?: string;
    saturday?: string;
    sunday?: string;
  };
  theme?: {
    primary_color?: string;
    secondary_color?: string;
    accent_color?: string;
    background_color?: string;
    text_color?: string;
  };
  store_color_palette?: any;
  settings?: {
    currency?: string;
    timezone?: string;
    language?: string;
    tax_inclusive?: boolean;
    shipping_enabled?: boolean;
  };
  created_at: string;
  updated_at: string;
}

export interface MedusaApiConfig {
  baseUrl: string;
  publishableKey?: string;
  tenantId?: string;
}

/**
 * Store Configuration API Service
 * Handles fetching store configuration and building Medusa API config
 */
export const storeConfigApi = {
  /**
   * Get store configuration by handle
   */
  getStoreConfig: async (storeHandle: string): Promise<StoreConfig | null> => {
    try {
      console.log('=== STORE CONFIG API CALL ===');
      console.log('Store Handle:', storeHandle);
      
      const result = await strapiStoreService.getStoreConfigByHandle(storeHandle);
      
      if (!result) {
        console.log('No store configuration found for handle:', storeHandle);
        return null;
      }
      
      console.log('Store config fetched successfully:', result.normalizedData);
      
      // Extract additional fields from raw response if needed
      const rawData = result.rawResponse.data?.[0];
      const medusaConfig = {
        medusa_base_url: rawData?.medusa_base_url || process.env.NEXT_PUBLIC_MEDUSA_BASE_URL,
        medusa_publishable_key: rawData?.medusa_publishable_key || process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY,
      };
      
      // Combine normalized data with Medusa config
      const storeConfig: StoreConfig = {
        ...result.normalizedData,
        ...medusaConfig,
        store_color_palette: rawData?.store_color_palette,
      };
      
      console.log('Final store config:', storeConfig);
      return storeConfig;
    } catch (error: any) {
      console.error('=== STORE CONFIG API ERROR ===');
      console.error('Error details:', error);
      throw error;
    }
  },

  /**
   * Build Medusa API configuration from store config
   */
  buildMedusaConfig: (storeConfig: StoreConfig | null, storeHandle: string): MedusaApiConfig => {
    console.log('=== BUILDING MEDUSA CONFIG ===');
    console.log('Store Config:', storeConfig);
    console.log('Store Handle:', storeHandle);
    
    const baseUrl = storeConfig?.medusa_base_url || 
                   process.env.NEXT_PUBLIC_MEDUSA_BASE_URL || 
                   'http://localhost:9000';

    const publishableKey = storeConfig?.medusa_publishable_key || 
                          process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY;

    const tenantId = storeHandle; // Use store handle as tenant ID

    const config: MedusaApiConfig = {
      baseUrl,
      publishableKey,
      tenantId,
    };
    
    console.log('Built Medusa config:', config);
    return config;
  },

  /**
   * Get store configuration and build Medusa config in one call
   */
  getStoreWithMedusaConfig: async (storeHandle: string): Promise<{
    storeConfig: StoreConfig | null;
    medusaConfig: MedusaApiConfig;
  }> => {
    try {
      console.log('=== GET STORE WITH MEDUSA CONFIG ===');
      console.log('Store Handle:', storeHandle);
      
      const storeConfig = await storeConfigApi.getStoreConfig(storeHandle);
      const medusaConfig = storeConfigApi.buildMedusaConfig(storeConfig, storeHandle);
      
      return {
        storeConfig,
        medusaConfig,
      };
    } catch (error: any) {
      console.error('=== GET STORE WITH MEDUSA CONFIG ERROR ===');
      console.error('Error details:', error);
      throw error;
    }
  },
};

export default storeConfigApi;