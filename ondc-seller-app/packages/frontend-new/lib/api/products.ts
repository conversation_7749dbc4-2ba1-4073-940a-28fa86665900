import { medusaClient } from './medusa-client';
import { getAuthHeaders } from '@/lib/utils/tokenUtils';

export interface Product {
  id: string;
  title: string;
  handle: string;
  description: string;
  status: 'draft' | 'published' | 'archived';
  thumbnail?: string;
  images?: string[];
  categories?: Array<{ id: string; name?: string }>;
  variants?: Array<{
    id: string;
    title: string;
    sku: string;
    prices: Array<{ currency_code: string; amount: number }>;
    metadata?: {
      sale_price?: number;
      original_price?: number;
      product_quantity?: number;
      product_inventory_status?: string;
    };
  }>;
  metadata?: {
    additional_data?: {
      product_prices?: Array<{
        sale_price?: number;
        original_price?: number;
      }>;
      product_quantity?: number;
      product_inventory_status?: string;
      product_overview?: string;
      product_features?: string;
      product_specifications?: string;
    };
  };
  options?: Array<{
    title: string;
    values: string[];
  }>;
  created_at?: string;
  updated_at?: string;
}

export interface ProductsResponse {
  products: Product[];
  count?: number;
  limit?: number;
  offset?: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Helper function to get tenant ID from storage
const getTenantId = (): string => {
  let tenantId = 'default-tenant';
  
  if (typeof window !== 'undefined') {
    try {
      // First, try to get from store-config-storage (preferred for onboarding)
      const storeConfigStorage = localStorage.getItem('store-config-storage');
      if (storeConfigStorage) {
        const storeConfigData = JSON.parse(storeConfigStorage);
        const storeHandle = storeConfigData.state?.storeData?.handle || 
                           storeConfigData.state?.storeConfig?.store_handle ||
                           storeConfigData.state?.currentStoreHandle;
        
        if (storeHandle) {
          console.log('Tenant ID from store-config-storage:', storeHandle);
          return storeHandle;
        }
      }
      
      // Fallback to auth-storage
      const authStorage = localStorage.getItem('auth-storage');
      if (authStorage) {
        const authData = JSON.parse(authStorage);
        const user = authData.state?.user;
        
        // Check multiple possible locations for store handle
        const storeHandle = 
          user?.user?.metadata?.store_handle || // From nested API response structure
          user?.user?.store_handle || // From nested API response structure
          user?.store_handle || 
          user?.metadata?.store_handle ||
          null;
        
        if (storeHandle) {
          console.log('Tenant ID from auth-storage:', storeHandle);
          return storeHandle;
        }
      }
    } catch (error) {
      console.error('Error getting tenant ID from storage:', error);
    }
  }
  
  console.log('Using default tenant ID:', tenantId);
  return tenantId;
};

// Helper function to get auth headers with auto token
const getProductsAuthHeaders = (storeHandle?: string) => {
  const tenantId = storeHandle || getTenantId();
  
  // Use the centralized auth headers utility (it already sets x-tenant-id)
  const headers = getAuthHeaders(tenantId);
  
  console.log('Products API - Auth headers - Token exists:', !!headers.Authorization);
  console.log('Products API - Auth headers - Tenant ID:', tenantId);
  
  return headers;
};

export const productsApi = {
  getProducts: async (storeHandle?: string): Promise<ApiResponse<Product[]>> => {
    try {
      console.log('=== FETCHING PRODUCTS ===');
      
  const backendUrl = medusaClient.getBaseUrl();
      const headers = getProductsAuthHeaders(storeHandle);
      
      console.log('Request headers:', {
        ...headers,
        'Authorization': headers.Authorization ? 'Bearer [REDACTED]' : 'No token'
      });
      
      const response = await medusaClient.admin.get('/products', {
        headers,
      });
      
      const data = response.data;
      console.log('=== RAW API RESPONSE ===');
      console.log('Response data:', data);
      console.log('Data type:', typeof data);
      console.log('Is array:', Array.isArray(data));
      
      let products = [];
      
      // Handle different possible response structures
      if (Array.isArray(data)) {
        products = data;
      } else if (data.products && Array.isArray(data.products)) {
        products = data.products;
      } else if (data.data && Array.isArray(data.data)) {
        products = data.data;
      } else {
        console.log('Unexpected response structure, treating as empty array:', data);
        products = [];
      }
      
      console.log('Extracted products:', products);
      console.log('Products count:', products.length);
      
      return {
        success: true,
        data: products,
      };
      
    } catch (error) {
      console.error('Error fetching products:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch products.',
      };
    }
  },

  getProduct: async (productId: string, storeHandle?: string): Promise<ApiResponse<Product>> => {
    try {
      console.log('=== FETCHING SINGLE PRODUCT ===');
      console.log('Product ID:', productId);
      
      const headers = getProductsAuthHeaders(storeHandle);
      
      const response = await medusaClient.admin.get(`/products/${productId}`, {
        headers,
      });
      
      const data = response.data;
      console.log('Product API response data:', data);
      
      return {
        success: true,
        data: data,
      };
      
    } catch (error) {
      console.error('Error fetching product:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch product.',
      };
    }
  },

  createProduct: async (productData: Partial<Product>, storeHandle?: string): Promise<ApiResponse<Product>> => {
    try {
      console.log('=== CREATING PRODUCT ===');
      console.log('Product data:', JSON.stringify(productData, null, 2));
      
      const headers = getProductsAuthHeaders(storeHandle);
      
      console.log('Request headers:', {
        ...headers,
        'Authorization': headers.Authorization ? 'Bearer [REDACTED]' : 'No token'
      });
      
      const response = await medusaClient.admin.post('/products', productData, {
        headers,
      });
      
      console.log('API response status:', response.status);
      
      const data = response.data;
      console.log('API response data:', data);
      
      return {
        success: true,
        data: data,
        message: 'Product created successfully!',
      };
      
    } catch (error) {
      console.error('Error creating product:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create product. Please try again.',
      };
    }
  },

  updateProduct: async (productId: string, productData: Partial<Product>, storeHandle?: string): Promise<ApiResponse<Product>> => {
    try {
      console.log('=== UPDATING PRODUCT ===');
      console.log('Product ID:', productId);
      console.log('Product data:', JSON.stringify(productData, null, 2));
      
      const headers = getProductsAuthHeaders(storeHandle);
      
      const response = await medusaClient.admin.put(`/products/${productId}`, productData, {
        headers,
      });
      
      const data = response.data;
      console.log('Update product API response data:', data);
      
      return {
        success: true,
        data: data,
        message: 'Product updated successfully!',
      };
      
    } catch (error) {
      console.error('Error updating product:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update product.',
      };
    }
  },

  deleteProduct: async (productId: string, storeHandle?: string): Promise<ApiResponse<void>> => {
    try {
      console.log('=== DELETING PRODUCT ===');
      console.log('Product ID:', productId);
      
      const headers = getProductsAuthHeaders(storeHandle);
      
      const response = await medusaClient.admin.delete(`/products/${productId}`, {
        headers,
      });
      
      console.log('Product deleted successfully');
      
      return {
        success: true,
        message: 'Product deleted successfully!',
      };
      
    } catch (error) {
      console.error('Error deleting product:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete product.',
      };
    }
  },
};

export default productsApi;