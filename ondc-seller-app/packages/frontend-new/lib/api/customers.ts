import { medusaClient } from './medusa-client';

export interface Customer {
  id: string;
  first_name?: string;
  last_name?: string;
  email: string;
  phone?: string;
  status: 'active' | 'inactive' | 'blocked';
  total_orders?: number;
  total_spent?: number;
  last_order_date?: string;
  createdAt: string;
  updatedAt?: string;
  addresses?: any[];
}

export interface CustomersResponse {
  customers: Customer[];
  count: number;
  limit: number;
  offset: number;
}

export const customersApi = {
  getCustomers: async (storeHandle: string, params?: {
    limit?: number;
    offset?: number;
    search?: string;
    status?: string;
  }): Promise<CustomersResponse> => {
    console.log('=== CUSTOMERS API CALL ===');
    console.log('Store handle (x-tenant-id):', storeHandle);
    console.log('Request params:', params);
    
    try {
      const response = await medusaClient.admin.get('/customers', {
        headers: {
          'x-tenant-id': storeHandle,
        },
        params,
      });
      
      console.log('Customers API response status:', response.status);
      console.log('Customers API response headers:', response.headers);
      console.log('Customers API response data:', response.data);
      console.log('Customers API response structure:', {
        dataType: typeof response.data,
        dataKeys: response.data ? Object.keys(response.data) : 'No data',
        customersArray: Array.isArray(response.data?.customers),
        customersCount: response.data?.customers?.length || 0,
        sampleCustomer: response.data?.customers?.[0] || 'No customers'
      });
      
      return response.data;
    } catch (error: any) {
      console.error('=== CUSTOMERS API ERROR ===');
      console.error('Error details:', error);
      
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
        console.error('Error response headers:', error.response.headers);
      }
      
      throw error;
    }
  },

  getCustomer: async (storeHandle: string, customerId: string): Promise<Customer> => {
    console.log('=== GET CUSTOMER API CALL ===');
    console.log('Store handle (x-tenant-id):', storeHandle);
    console.log('Customer ID:', customerId);
    
    try {
      const response = await medusaClient.admin.get(`/customers/${customerId}`, {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Get customer API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching customer:', error);
      throw error;
    }
  },

  updateCustomer: async (storeHandle: string, customerId: string, customerData: Partial<Customer>): Promise<Customer> => {
    console.log('=== UPDATE CUSTOMER API CALL ===');
    console.log('Store handle (x-tenant-id):', storeHandle);
    console.log('Customer ID:', customerId);
    console.log('Customer data:', customerData);
    
    try {
      const response = await medusaClient.admin.put(`/customers/${customerId}`, customerData, {
        headers: {
          'x-tenant-id': storeHandle,
        },
      });
      
      console.log('Update customer API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error updating customer:', error);
      throw error;
    }
  },
};

export default customersApi;