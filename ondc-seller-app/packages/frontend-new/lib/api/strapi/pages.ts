import { strapiFetcher } from "./index";

// CMS Page interfaces
export interface CMSPageData {
  id: number;
  documentId?: string;
  title?: string;
  slug?: string;
  content?: string | null;
  status?: 'published' | 'draft' | 'scheduled';
  isActive?: boolean;
  tenant_store?: string;
  createdAt?: string;
  updatedAt?: string;
  publishedAt?: string | null;
  // Additional fields that might come from Strapi
  [key: string]: any;
}

export interface StrapiResponse<T> {
  data: T;
  meta: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

// Get authentication token from environment
const getStrapiToken = (): string => {
  const token = process.env.NEXT_PUBLIC_STRAPI_ACCESS_TOKEN;
  if (!token) {
    console.warn('⚠️ NEXT_PUBLIC_STRAPI_ACCESS_TOKEN not found in environment variables');
  }
  return token || '';
};

// Get all CMS pages for a store
export const getCMSPagesByStoreHandle = (storeHandle: string): Promise<StrapiResponse<CMSPageData[]>> => {
  const endpoint = `/api/cms-pages?filters[pageStoreName][$eq]=${storeHandle}&populate=*&sort=updatedAt:desc`;
  const token = getStrapiToken();
  
  console.log('🔄 Fetching CMS pages from Strapi:', {
    endpoint,
    hasToken: !!token,
    storeHandle
  });
  
  return strapiFetcher(endpoint, 'GET', undefined, token);
};

// Get single CMS page by documentId
export const getCMSPageByDocumentId = (documentId: string): Promise<StrapiResponse<CMSPageData>> => {
  const endpoint = `/pages/${documentId}?populate=*`;
  const token = getStrapiToken();
  
  console.log('🎯 Fetching single CMS page from Strapi by documentId:', {
    endpoint,
    documentId,
    hasToken: !!token
  });
  
  return strapiFetcher(endpoint, 'GET', undefined, token);
};

// Get single CMS page by ID (for edit functionality)
export const getCMSPageById = (pageId: string): Promise<StrapiResponse<CMSPageData>> => {
  const endpoint = `/api/cms-pages/${pageId}?populate=*`;
  const token = getStrapiToken();
  
  console.log('📝 Fetching CMS page for editing:', {
    endpoint,
    pageId,
    hasToken: !!token
  });
  
  return strapiFetcher(endpoint, 'GET', undefined, token);
};

// Legacy functions for backward compatibility
export const getStaticPages = (storeId: number) =>
  strapiFetcher(`/static-pages?filters[store][id][$eq]=${storeId}&populate=*`);

export const getPageBySlug = (slug: string) =>
  strapiFetcher(`/static-pages?filters[slug][$eq]=${slug}&populate=*`);

// Create new CMS page
export const createCMSPage = (data: any, userToken?: string): Promise<StrapiResponse<CMSPageData>> => {
  const token = userToken || getStrapiToken();
  console.log('🆕 Creating new CMS page:', { hasToken: !!token });
  return strapiFetcher("/pages", "POST", { data }, token);
};

// Create new CMS page (for form)
export const createCMSPageById = (data: any, userToken?: string): Promise<StrapiResponse<CMSPageData>> => {
  const token = userToken || getStrapiToken();
  
  console.log('🆕 Creating new CMS page:', {
    hasToken: !!token,
    payload: { data }
  });
  
  return strapiFetcher("/api/cms-pages", "POST", { data }, token);
};

// Update CMS page by documentId
export const updateCMSPageByDocumentId = (documentId: string, data: any, userToken?: string): Promise<StrapiResponse<CMSPageData>> => {
  const token = userToken || getStrapiToken();
  console.log('🔄 Updating CMS page by documentId:', { documentId, hasToken: !!token });
  return strapiFetcher(`/pages/${documentId}`, "PUT", { data }, token);
};

// Update CMS page by ID (for edit form)
export const updateCMSPageById = (pageId: string, data: any, userToken?: string): Promise<StrapiResponse<CMSPageData>> => {
  const token = userToken || getStrapiToken();
  
  console.log('💾 Updating CMS page:', {
    pageId,
    hasToken: !!token,
    payload: { data }
  });
  
  return strapiFetcher(`/api/cms-pages/${pageId}`, "PUT", { data }, token);
};

// Delete CMS page by documentId
export const deleteCMSPageByDocumentId = (documentId: string, userToken?: string): Promise<void> => {
  const token = userToken || getStrapiToken();
  console.log('🗑️ Deleting CMS page by documentId:', { documentId, hasToken: !!token });
  return strapiFetcher(`/api/cms-pages/${documentId}`, "DELETE", undefined, token);
};

// Delete CMS page by ID (for listing page)
export const deleteCMSPageById = (pageId: string, userToken?: string): Promise<void> => {
  const token = userToken || getStrapiToken();
  
  console.log('🗑️ Deleting CMS page:', {
    pageId,
    hasToken: !!token
  });
  
  return strapiFetcher(`/cms-pages/${pageId}`, "DELETE", undefined, token);
};

// Get published CMS pages for store view (public API)
export const getPublishedCMSPagesByStoreHandle = (storeHandle: string): Promise<StrapiResponse<CMSPageData[]>> => {
  // For public pages, we only fetch published pages (where publishedAt is not null)
  const endpoint = `/cms-pages?filters[tenant_store][$eq]=${storeHandle}&filters[publishedAt][$notNull]=true&populate=*&sort=updatedAt:desc`;
  const token = getStrapiToken();
  
  console.log('🌍 Fetching published CMS pages for store view:', {
    endpoint,
    storeHandle,
    hasToken: !!token
  });
  
  return strapiFetcher(endpoint, 'GET', undefined, token);
};

// Get CMS page by store handle and page name (for static pages like About Us, Contact Us, etc.)
export const getCMSPageByStoreAndPageName = (storeHandle: string, pageName: string): Promise<StrapiResponse<CMSPageData[]>> => {
  const endpoint = `/api/cms-pages?sort=createdAt:asc&filters[pageStoreName][$eq]=${storeHandle}&filters[pageName][$eq]=${pageName}&populate=*`;
  const token = getStrapiToken();
  
  console.log('📄 Fetching CMS page by store and page name:', {
    endpoint,
    storeHandle,
    pageName,
    hasToken: !!token
  });
  
  return strapiFetcher(endpoint, 'GET', undefined, token);
};

// Get single published CMS page by slug for store view (public API)
export const getPublishedCMSPageBySlug = (storeHandle: string, pageSlug: string): Promise<StrapiResponse<CMSPageData>> => {
  const endpoint = `/cms-pages?filters[tenant_store][$eq]=${storeHandle}&filters[pageSlug][$eq]=${pageSlug}&filters[publishedAt][$notNull]=true&populate=*`;
  const token = getStrapiToken();
  
  console.log('📄 Fetching published CMS page by slug for store view:', {
    endpoint,
    storeHandle,
    pageSlug,
    hasToken: !!token
  });
  
  return strapiFetcher(endpoint, 'GET', undefined, token);
};

// Legacy functions for backward compatibility
export const createPage = (data: any, token: string) =>
  strapiFetcher("/pages", "POST", { data }, token);

export const updatePage = (id: number, data: any, token: string) =>
  strapiFetcher(`/pages/${id}`, "PUT", { data }, token);

export const deletePage = (id: number, token: string) =>
  strapiFetcher(`/pages/${id}`, "DELETE", undefined, token);
