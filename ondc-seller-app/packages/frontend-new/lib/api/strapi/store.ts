// lib/api/strapi/store.ts
import { strapiFetcher } from "./index";

export const getStoreDetails = (storeHandle: string) =>
  strapiFetcher(
    `/store-configurations?filters[store_handle][$eq]=${storeHandle}&populate=*`,
  );

export const updateStoreDetails = (id: number, data: any, token: string) =>
  strapiFetcher(`/store-configurations/${id}`, "PUT", { data }, token);

export const createStore = (data: any, token: string) =>
  strapiFetcher("/store-configurations", "POST", { data }, token);
