import { strapiFetcher } from "./index";

// Banner interfaces
export interface BannerImage {
  id: number;
  documentId: string;
  name: string;
  alternativeText?: string;
  caption?: string;
  width: number;
  height: number;
  formats?: {
    thumbnail?: {
      ext: string;
      url: string;
      hash: string;
      mime: string;
      name: string;
      path?: string;
      size: number;
      width: number;
      height: number;
      sizeInBytes: number;
    };
  };
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl?: string;
  provider: string;
  provider_metadata?: any;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
}

export interface BannerData {
  id: number;
  documentId: string;
  title: string;
  subtitle?: string;
  description: string;
  buttonText?: string;
  buttonLink?: string;
  backgroundColor?: string;
  link?: string;
  start_date?: string;
  end_date?: string;
  active: boolean;
  position: number;
  tenant_store: string;
  image_url?: string;
  image?: BannerImage;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
}

export interface StrapiResponse<T> {
  data: T;
  meta: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

// Get banners by store handle with proper filtering and sorting
export const getBannersByStoreHandle = (storeHandle: string): Promise<StrapiResponse<BannerData[]>> => {
  const endpoint = `/api/banners?populate=*&filters[tenant_store][$eq]=${storeHandle}&sort=position:asc`;
  console.log('🎯 Fetching banners from Strapi:', endpoint);
  return strapiFetcher(endpoint);
};

// Get single banner by documentId with population
export const getBannerByDocumentId = (documentId: string): Promise<StrapiResponse<BannerData>> => {
  const endpoint = `/api/banners/${documentId}?populate=*`;
  console.log('🎯 Fetching single banner from Strapi by documentId:', endpoint);
  return strapiFetcher(endpoint);
};

// Legacy method - Get single banner by ID (kept for backward compatibility)
export const getBannerById = (bannerId: string): Promise<StrapiResponse<BannerData>> => {
  const endpoint = `/banners/${bannerId}?populate=*`;
  console.log('🎯 Fetching single banner from Strapi by ID:', endpoint);
  return strapiFetcher(endpoint);
};

// Legacy method - kept for backward compatibility
export const getBanners = (storeId: number) =>
  strapiFetcher(`/banners?filters[store][id][$eq]=${storeId}&populate=*`);

export const createBanner = (data: any) =>
  strapiFetcher("/api/banners", "POST", { data });

// Update banner using documentId
export const updateBannerByDocumentId = (documentId: string, data: any) => {
  console.log('🎯 Updating banner by documentId:', documentId, data);
  return strapiFetcher(`/api/banners/${documentId}`, "PUT", { data });
};

// Legacy update method using ID
export const updateBanner = (id: number, data: any, token: string) =>
  strapiFetcher(`/banners/${id}`, "PUT", { data }, token);

// Delete banner using documentId
export const deleteBannerByDocumentId = (documentId: string, token: string) => {
  console.log('🎯 Deleting banner by documentId:', documentId);
  return strapiFetcher(`/banners/${documentId}`, "DELETE", undefined, token);
};

// Legacy delete method using ID
export const deleteBanner = (id: number, token: string) =>
  strapiFetcher(`/banners/${id}`, "DELETE", undefined, token);
