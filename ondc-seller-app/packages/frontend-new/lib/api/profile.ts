import { medusaClient } from './medusa-client';
import { getAuthHeaders } from '@/lib/utils/tokenUtils';

// Store user interface (different from admin User)
export interface StoreUser {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  has_account?: boolean;
  created_at?: string;
  updated_at?: string;
  addresses?: Address[];
  metadata?: {
    [key: string]: any;
  };
}

// Address interface
export interface Address {
  id: string;
  first_name: string;
  last_name: string;
  phone?: string;
  company?: string;
  address_1: string;
  address_2?: string;
  city: string;
  country_code: string;
  province: string;
  postal_code: string;
  address_name?: string;
  metadata?: {
    [key: string]: any;
  };
}

export interface ProfileUpdatePayload {
  phone?: string;
  metadata?: {
    date_of_birth?: string;
    gender?: string;
    [key: string]: any;
  };
}

export interface AddressPayload {
  first_name: string;
  last_name: string;
  phone?: string;
  company?: string;
  address_1: string;
  address_2?: string;
  city: string;
  country_code: string;
  province: string;
  postal_code: string;
  address_name?: string;
  metadata?: {
    [key: string]: any;
  };
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Helper function to get tenant ID from storage
const getTenantId = (): string => {
  let tenantId = 'default-tenant';
  
  if (typeof window !== 'undefined') {
    try {
      // First, try to get from store-config-storage (preferred for onboarding)
      const storeConfigStorage = localStorage.getItem('store-config-storage');
      if (storeConfigStorage) {
        const storeConfigData = JSON.parse(storeConfigStorage);
        const storeHandle = storeConfigData.state?.storeData?.handle || 
                           storeConfigData.state?.storeConfig?.store_handle ||
                           storeConfigData.state?.currentStoreHandle;
        
        if (storeHandle) {
          console.log('Tenant ID from store-config-storage:', storeHandle);
          return storeHandle;
        }
      }
      
      // Fallback to auth-storage
      const authStorage = localStorage.getItem('auth-storage');
      if (authStorage) {
        const authData = JSON.parse(authStorage);
        const user = authData.state?.user;
        
        // Check multiple possible locations for store handle
        const storeHandle = 
          user?.user?.metadata?.store_handle || // From nested API response structure
          user?.user?.store_handle || // From nested API response structure
          user?.store_handle || 
          user?.metadata?.store_handle ||
          null;
        
        if (storeHandle) {
          console.log('Tenant ID from auth-storage:', storeHandle);
          return storeHandle;
        }
      }
    } catch (error) {
      console.error('Error getting tenant ID from storage:', error);
    }
  }
  
  console.log('Using default tenant ID:', tenantId);
  return tenantId;
};

/**
 * Get authentication headers for profile API calls
 */
const getProfileAuthHeaders = (storeHandle?: string) => {
  const tenantId = storeHandle || getTenantId();
  
  // Use the centralized auth headers utility
  const headers = getAuthHeaders(tenantId);
  
  console.log('Profile API - Auth headers - Token exists:', !!headers.Authorization);
  console.log('Profile API - Auth headers - Tenant ID:', tenantId);
  
  return {
    ...headers,
    'x-publishable-api-key': process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || '',
  };
};

export const profileApi = {
  /**
   * Update user profile
   */
  updateProfile: async (
    payload: ProfileUpdatePayload,
    storeHandle: string
  ): Promise<ApiResponse<StoreUser>> => {
    try {
      const headers = getProfileAuthHeaders(storeHandle);
      
      const response = await fetch(`${medusaClient.getBaseUrl()}/store/customers/me`, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update profile');
      }
      
      const data = await response.json();
      
      return {
        success: true,
        data: data.customer || data,
        message: 'Profile updated successfully!',
      };
    } catch (error: any) {
      console.error('Error updating profile:', error);
      return {
        success: false,
        error: error.message || 'Failed to update profile. Please try again.',
      };
    }
  },

  /**
   * Get user profile
   */
  getProfile: async (storeHandle: string): Promise<ApiResponse<StoreUser>> => {
    try {
      const headers = getProfileAuthHeaders(storeHandle);
      
      const response = await fetch(`${medusaClient.getBaseUrl()}/store/customers/me`, {
        method: 'GET',
        headers,
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch profile');
      }
      
      const data = await response.json();
      
      return {
        success: true,
        data: data.customer || data,
      };
    } catch (error: any) {
      console.error('Error fetching profile:', error);
      return {
        success: false,
        error: error.message || 'Failed to fetch profile.',
      };
    }
  },

  /**
   * Add new address
   */
  addAddress: async (
    addressData: AddressPayload,
    storeHandle: string
  ): Promise<ApiResponse<Address>> => {
    try {
      const headers = getProfileAuthHeaders(storeHandle);
      
      const response = await fetch(`${medusaClient.getBaseUrl()}/store/customers/me/addresses`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          ...addressData,
          metadata: addressData.metadata || {},
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to add address');
      }
      
      const data = await response.json();
      
      return {
        success: true,
        data: data.address || data,
        message: 'Address added successfully!',
      };
    } catch (error: any) {
      console.error('Error adding address:', error);
      return {
        success: false,
        error: error.message || 'Failed to add address. Please try again.',
      };
    }
  },

  /**
   * Update existing address
   */
  updateAddress: async (
    addressId: string,
    addressData: AddressPayload,
    storeHandle: string
  ): Promise<ApiResponse<Address>> => {
    try {
      const headers = getProfileAuthHeaders(storeHandle);
      
      const response = await fetch(`${medusaClient.getBaseUrl()}/store/customers/me/addresses/${addressId}`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          ...addressData,
          metadata: addressData.metadata || {},
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update address');
      }
      
      const data = await response.json();
      
      return {
        success: true,
        data: data.address || data,
        message: 'Address updated successfully!',
      };
    } catch (error: any) {
      console.error('Error updating address:', error);
      return {
        success: false,
        error: error.message || 'Failed to update address. Please try again.',
      };
    }
  },

  /**
   * Delete address
   */
  deleteAddress: async (
    addressId: string,
    storeHandle: string
  ): Promise<ApiResponse<void>> => {
    try {
      const headers = getProfileAuthHeaders(storeHandle);
      
      const response = await fetch(`${medusaClient.getBaseUrl()}/store/customers/me/addresses/${addressId}`, {
        method: 'DELETE',
        headers,
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete address');
      }
      
      return {
        success: true,
        message: 'Address deleted successfully!',
      };
    } catch (error: any) {
      console.error('Error deleting address:', error);
      return {
        success: false,
        error: error.message || 'Failed to delete address. Please try again.',
      };
    }
  },
};