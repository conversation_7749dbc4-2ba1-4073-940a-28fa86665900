# Store Customer Registration Update

## Overview
Updated the `storeCustomerRegister` function in `/lib/api/services/medusa-store-auth.ts` to make a second API call to complete customer registration with profile data.

## Changes Made

### 1. **Enhanced Registration Flow**
The registration process now consists of two steps:

1. **Step 1**: Initial registration via `/auth/customer/emailpass/register`
   - Creates the customer account with authentication
   - Returns a token for subsequent API calls

2. **Step 2**: Complete registration via `/store/customers` 
   - Creates the customer profile with additional metadata
   - Uses the token from Step 1 for authentication

### 2. **Key Improvements**

#### **Proper Token Usage**
- ✅ **Added Authorization Header**: The second API call now includes `Authorization: Bearer ${token}`
- ✅ **Token Validation**: Only proceeds with Step 2 if token is received from Step 1

#### **Enhanced Payload Structure**
- ✅ **Password Removal**: Uses destructuring to remove password from the payload
- ✅ **Metadata Addition**: Adds comprehensive metadata including:
  - `user_type: "customer"`
  - `registration_completed: true`
  - `registration_date: ISO timestamp`

#### **Improved Error Handling**
- ✅ **Non-blocking Profile Creation**: If profile creation fails, registration is still considered successful
- ✅ **Detailed Logging**: Added comprehensive logging for debugging
- ✅ **Graceful Degradation**: User can complete profile later if second API call fails

#### **Enhanced Response**
- ✅ **Customer Data Inclusion**: Adds customer profile data to the response if profile creation succeeds
- ✅ **Backward Compatibility**: Maintains existing response structure

### 3. **API Call Details**

#### **First API Call**: `/auth/customer/emailpass/register`
```typescript
// Payload includes all registration data
{
  email: string,
  password: string,
  first_name: string,
  last_name: string,
  phone?: string
}

// Response
{
  token: string,
  customer?: StoreCustomer
}
```

#### **Second API Call**: `/store/customers`
```typescript
// Headers
{
  'Authorization': 'Bearer ${token}'
}

// Payload (password removed, metadata added)
{
  email: string,
  first_name: string,
  last_name: string,
  phone?: string,
  metadata: {
    user_type: "customer",
    registration_completed: true,
    registration_date: "2024-01-01T00:00:00.000Z"
  }
}

// Response
{
  customer: StoreCustomer
}
```

### 4. **Logging Enhancements**

The function now provides detailed logging at each step:

```typescript
// Step 1 logging
console.log('📝 Store customer registration:', { 
  endpoint: '/auth/customer/emailpass/register',
  email: registrationData.email,
  storeHandle 
});

// Step 2 logging
console.log('📝 Completing customer registration with profile data:', {
  endpoint: '/store/customers',
  storeHandle
});

// Payload logging (without sensitive data)
console.log('📝 Customer profile payload:', {
  email: customerProfilePayload.email,
  first_name: customerProfilePayload.first_name,
  last_name: customerProfilePayload.last_name,
  phone: customerProfilePayload.phone,
  metadata: customerProfilePayload.metadata
});
```

### 5. **Error Handling Strategy**

- **Step 1 Failure**: Throws error immediately (registration failed)
- **Step 2 Failure**: Logs warning but doesn't throw (registration succeeded, profile can be completed later)

### 6. **Benefits**

1. **Complete Registration**: Ensures customer profile is properly created with metadata
2. **Robust Error Handling**: Graceful degradation if profile creation fails
3. **Better Tracking**: Metadata allows tracking of registration completion status
4. **Debugging Support**: Comprehensive logging for troubleshooting
5. **Backward Compatibility**: Existing code continues to work unchanged

### 7. **Usage Example**

```typescript
try {
  const result = await medusaStoreAuthService.storeCustomerRegister({
    email: '<EMAIL>',
    password: 'securepassword',
    first_name: 'John',
    last_name: 'Doe',
    phone: '+1234567890'
  }, 'store-handle');
  
  console.log('Registration successful:', {
    token: result.token,
    customerId: result.customer?.id,
    hasProfile: !!result.customer
  });
} catch (error) {
  console.error('Registration failed:', error.message);
}
```

### 8. **Testing Recommendations**

1. **Successful Registration**: Test complete flow with valid data
2. **Profile Creation Failure**: Test when second API call fails
3. **Token Validation**: Test with invalid/missing tokens
4. **Network Issues**: Test with network interruptions between calls
5. **Metadata Verification**: Verify metadata is properly stored

The updated registration flow provides a more robust and complete customer registration experience while maintaining backward compatibility and graceful error handling.