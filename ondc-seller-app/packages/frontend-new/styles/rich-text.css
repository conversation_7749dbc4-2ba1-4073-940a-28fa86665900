/* Rich Text Content Styling */
.rich-text-content {
  font-family: inherit;
  line-height: 1.6;
  color: var(--theme-text, #111827);
}

.rich-text-content h1 {
  font-size: 2rem;
  font-weight: bold;
  margin: 1.5rem 0 1rem 0;
  line-height: 1.2;
  color: var(--theme-text, #111827);
}

.rich-text-content h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 1.25rem 0 0.75rem 0;
  line-height: 1.3;
  color: var(--theme-text, #111827);
}

.rich-text-content h3 {
  font-size: 1.25rem;
  font-weight: bold;
  margin: 1rem 0 0.5rem 0;
  line-height: 1.4;
  color: var(--theme-text, #111827);
}

.rich-text-content h4 {
  font-size: 1.125rem;
  font-weight: bold;
  margin: 0.875rem 0 0.5rem 0;
  line-height: 1.4;
  color: var(--theme-text, #111827);
}

.rich-text-content h5 {
  font-size: 1rem;
  font-weight: bold;
  margin: 0.75rem 0 0.5rem 0;
  line-height: 1.4;
  color: var(--theme-text, #111827);
}

.rich-text-content h6 {
  font-size: 0.875rem;
  font-weight: bold;
  margin: 0.75rem 0 0.5rem 0;
  line-height: 1.4;
  color: var(--theme-text, #111827);
}

.rich-text-content p {
  margin: 0.75rem 0;
  line-height: 1.6;
}

.rich-text-content ul,
.rich-text-content ol {
  margin: 0.75rem 0;
  padding-left: 1.5rem;
}

.rich-text-content li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

.rich-text-content ul li {
  list-style-type: disc;
}

.rich-text-content ol li {
  list-style-type: decimal;
}

.rich-text-content blockquote {
  border-left: 4px solid var(--theme-border, #e5e7eb);
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: var(--theme-text-secondary, #6b7280);
  background-color: var(--theme-background-secondary, #f9fafb);
  padding: 1rem;
  border-radius: 0.375rem;
}

.rich-text-content pre {
  background-color: var(--theme-background-secondary, #f5f5f5);
  padding: 1rem;
  border-radius: 0.375rem;
  margin: 1rem 0;
  font-family: 'Courier New', Courier, monospace;
  font-size: 0.875rem;
  overflow-x: auto;
  border: 1px solid var(--theme-border, #e5e7eb);
}

.rich-text-content code {
  background-color: var(--theme-background-secondary, #f5f5f5);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', Courier, monospace;
  font-size: 0.875rem;
  border: 1px solid var(--theme-border, #e5e7eb);
}

.rich-text-content pre code {
  background-color: transparent;
  padding: 0;
  border: none;
  border-radius: 0;
}

.rich-text-content strong,
.rich-text-content b {
  font-weight: bold;
}

.rich-text-content em,
.rich-text-content i {
  font-style: italic;
}

.rich-text-content u {
  text-decoration: underline;
}

.rich-text-content a {
  color: var(--theme-primary, #3b82f6);
  text-decoration: underline;
  transition: color 0.2s ease;
}

.rich-text-content a:hover {
  color: var(--theme-primary-dark, #2563eb);
}

.rich-text-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
  margin: 0.5rem 0;
}

.rich-text-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
  border: 1px solid var(--theme-border, #e5e7eb);
  border-radius: 0.375rem;
  overflow: hidden;
}

.rich-text-content th,
.rich-text-content td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--theme-border, #e5e7eb);
}

.rich-text-content th {
  background-color: var(--theme-background-secondary, #f9fafb);
  font-weight: bold;
}

.rich-text-content tr:last-child td {
  border-bottom: none;
}

.rich-text-content hr {
  border: none;
  border-top: 1px solid var(--theme-border, #e5e7eb);
  margin: 1.5rem 0;
}

/* Plain text content styling */
.plain-text-content {
  font-family: inherit;
  line-height: 1.6;
  color: var(--theme-text, #111827);
  white-space: pre-wrap;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .rich-text-content h1 {
    font-size: 1.75rem;
  }
  
  .rich-text-content h2 {
    font-size: 1.375rem;
  }
  
  .rich-text-content h3 {
    font-size: 1.125rem;
  }
  
  .rich-text-content ul,
  .rich-text-content ol {
    padding-left: 1.25rem;
  }
  
  .rich-text-content blockquote {
    padding: 0.75rem;
    margin: 0.75rem 0;
  }
  
  .rich-text-content pre {
    padding: 0.75rem;
    font-size: 0.8125rem;
  }
  
  .rich-text-content th,
  .rich-text-content td {
    padding: 0.5rem;
    font-size: 0.875rem;
  }
}