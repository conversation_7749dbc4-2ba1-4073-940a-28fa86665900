/* Quill Editor Styles */
/* Note: Quill CSS is imported dynamically in the component */

/* Custom Quill styling */
.ql-toolbar {
  border-top: 1px solid #e0e0e0 !important;
  border-left: 1px solid #e0e0e0 !important;
  border-right: 1px solid #e0e0e0 !important;
  border-bottom: none !important;
  border-top-left-radius: 4px !important;
  border-top-right-radius: 4px !important;
  background: #fafafa;
}

.ql-container {
  border-bottom: 1px solid #e0e0e0 !important;
  border-left: 1px solid #e0e0e0 !important;
  border-right: 1px solid #e0e0e0 !important;
  border-top: none !important;
  border-bottom-left-radius: 4px !important;
  border-bottom-right-radius: 4px !important;
  font-family: inherit !important;
}

.ql-editor {
  font-size: 14px !important;
  line-height: 1.5 !important;
  padding: 12px 15px !important;
}

.ql-editor.ql-blank::before {
  color: #9e9e9e !important;
  font-style: normal !important;
}

/* Error state */
.ql-toolbar.error {
  border-color: #f44336 !important;
}

.ql-container.error {
  border-color: #f44336 !important;
}

/* Focus state */
.ql-container.ql-focused {
  border-color: #2196f3 !important;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2) !important;
}

.ql-toolbar.ql-focused {
  border-color: #2196f3 !important;
}

/* Toolbar button styling */
.ql-toolbar .ql-picker-label {
  border: none !important;
}

.ql-toolbar .ql-picker-options {
  background: white !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.ql-toolbar button:hover {
  color: #2196f3 !important;
}

.ql-toolbar button.ql-active {
  color: #2196f3 !important;
}

/* Snow theme customizations */
.ql-snow .ql-tooltip {
  background: white !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.ql-snow .ql-tooltip input[type=text] {
  border: 1px solid #e0e0e0 !important;
  border-radius: 4px !important;
  padding: 8px 12px !important;
}

.ql-snow .ql-tooltip a.ql-action::after {
  content: 'Save' !important;
}

.ql-snow .ql-tooltip a.ql-remove::before {
  content: 'Remove' !important;
}