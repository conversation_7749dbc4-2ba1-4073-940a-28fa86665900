/**
 * Store Theme CSS Variables
 * Auto-generated theme colors applied globally
 */

:root {
  /* Default theme colors (fallback) */
  --store-primary: #1976d2;
  --store-secondary: #dc004e;
  --store-accent: #ff5722;
  --store-background: #ffffff;
  --store-text: #1a1a1a;
  --store-surface: #fafafa;
  --store-on-surface: #424242;
  --store-primary-light: #42a5f5;
  --store-primary-dark: #1565c0;
  --store-secondary-light: #f5325b;
  --store-secondary-dark: #9a0036;

  /* MUI integration variables */
  --mui-palette-primary-main: var(--store-primary);
  --mui-palette-secondary-main: var(--store-secondary);
  --mui-palette-background-default: var(--store-background);
  --mui-palette-background-paper: var(--store-surface);
  --mui-palette-text-primary: var(--store-text);
  --mui-palette-text-secondary: var(--store-on-surface);

  /* Legacy theme variables (backward compatibility) */
  --theme-primary: var(--store-primary);
  --theme-secondary: var(--store-secondary);
  --theme-accent: var(--store-accent);
  --theme-background: var(--store-background);
  --theme-text: var(--store-text);
  --theme-text-secondary: var(--store-on-surface);
  --theme-surface: var(--store-surface);
  
  /* Text color variables */
  --text-primary: var(--store-text);
  --text-secondary: var(--store-on-surface);
  --text-muted: var(--store-on-surface);

  /* Component-specific variables */
  --btn-primary: var(--store-primary);
  --btn-primary-hover: var(--store-primary-dark);
  --btn-text: #ffffff;

  /* Header variables */
  --header-bg: var(--store-background);
  --header-text: var(--store-text);
  --header-border: var(--store-surface);

  /* Navigation variables */
  --nav-bg: var(--store-surface);
  --nav-text: var(--store-text);
  --nav-text-secondary: var(--store-on-surface);
  --nav-active: var(--store-primary);
  --nav-hover: var(--store-primary-light);
  
  /* Breadcrumb variables */
  --breadcrumb-text: var(--store-on-surface);
  --breadcrumb-active: var(--store-text);
  --breadcrumb-separator: var(--store-on-surface);

  /* Card variables */
  --card-bg: var(--store-background);
  --card-border: var(--store-surface);
  --card-shadow: rgba(0, 0, 0, 0.1);

  /* Form variables */
  --input-border: var(--store-on-surface);
  --input-focus: var(--store-primary);
  --input-bg: var(--store-background);

  /* Status colors */
  --success: #4caf50;
  --warning: #ff9800;
  --error: #f44336;
  --info: #2196f3;
}

/* Global theme application */
body {
  background-color: var(--store-background);
  color: var(--store-text);
  font-family: 'Inter', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Store-specific component styling */
.store-header {
  background-color: var(--header-bg);
  color: var(--header-text);
  border-bottom: 1px solid var(--header-border);
}

.store-nav {
  background-color: var(--nav-bg);
  color: var(--nav-text);
}

.store-nav-item {
  color: var(--nav-text);
  transition: color 0.2s ease, background-color 0.2s ease;
}

.store-nav-item:hover {
  color: var(--nav-hover);
  background-color: rgba(var(--store-primary-rgb), 0.1);
}

.store-nav-item.active {
  color: var(--nav-active);
  background-color: rgba(var(--store-primary-rgb), 0.2);
}

.store-card {
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  box-shadow: 0 1px 3px var(--card-shadow);
  border-radius: 12px;
  transition: box-shadow 0.2s ease, transform 0.2s ease;
}

.store-card:hover {
  box-shadow: 0 4px 12px var(--card-shadow);
  transform: translateY(-1px);
}

.store-button {
  background-color: var(--btn-primary);
  color: var(--btn-text);
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
}

.store-button:hover {
  background-color: var(--btn-primary-hover);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.store-button.secondary {
  background-color: var(--store-secondary);
}

.store-button.secondary:hover {
  background-color: var(--store-secondary-dark);
}

.store-input {
  background-color: var(--input-bg);
  border: 1px solid var(--input-border);
  border-radius: 8px;
  padding: 8px 12px;
  color: var(--store-text);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.store-input:focus {
  outline: none;
  border-color: var(--input-focus);
  box-shadow: 0 0 0 2px rgba(var(--store-primary-rgb), 0.2);
}

/* Theme-aware scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--store-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--store-on-surface);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--store-primary);
}

/* Loading states with theme colors */
.store-loading {
  background: linear-gradient(
    90deg,
    var(--store-surface) 25%,
    var(--store-primary-light) 50%,
    var(--store-surface) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Breadcrumb styling */
.MuiBreadcrumbs-root {
  color: var(--breadcrumb-text) !important;
}

.MuiBreadcrumbs-li {
  color: var(--breadcrumb-text) !important;
}

.MuiBreadcrumbs-li:last-child {
  color: var(--breadcrumb-active) !important;
  font-weight: 500 !important;
}

.MuiBreadcrumbs-separator {
  color: var(--breadcrumb-separator) !important;
}

/* Typography color overrides */
.MuiTypography-colorTextPrimary {
  color: var(--text-primary) !important;
}

.MuiTypography-colorTextSecondary {
  color: var(--text-secondary) !important;
}

.MuiTypography-h1,
.MuiTypography-h2,
.MuiTypography-h3,
.MuiTypography-h4,
.MuiTypography-h5,
.MuiTypography-h6 {
  color: var(--text-primary) !important;
}

.MuiTypography-subtitle1,
.MuiTypography-subtitle2,
.MuiTypography-body2,
.MuiTypography-caption {
  color: var(--text-secondary) !important;
}

.MuiTypography-body1 {
  color: var(--text-primary) !important;
}

/* Link styling */
.MuiLink-root {
  color: var(--store-primary) !important;
  text-decoration: none !important;
}

.MuiLink-root:hover {
  color: var(--store-primary-dark) !important;
  text-decoration: underline !important;
}

/* Theme transition animations */
* {
  transition: 
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
}

/* Responsive theme adjustments */
@media (max-width: 768px) {
  .store-card {
    border-radius: 8px;
  }
  
  .store-button {
    padding: 10px 16px;
    font-size: 14px;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  :root {
    --store-background: #121212;
    --store-surface: #1e1e1e;
    --store-text: #ffffff;
    --store-on-surface: #b0b0b0;
    --card-shadow: rgba(255, 255, 255, 0.1);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .store-button {
    border: 2px solid var(--store-text);
  }
  
  .store-card {
    border: 2px solid var(--store-on-surface);
  }
  
  .store-input {
    border: 2px solid var(--store-text);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}

/* Print styles */
@media print {
  .store-card {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .store-button {
    background: transparent !important;
    color: #000 !important;
    border: 1px solid #000;
  }
}