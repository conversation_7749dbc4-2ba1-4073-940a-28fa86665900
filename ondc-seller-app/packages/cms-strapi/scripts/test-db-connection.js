#!/usr/bin/env node

/**
 * Test Database Connection Script for Strapi CMS
 * This script tests the PostgreSQL connection using the same configuration as Strapi
 */

const { Client } = require('pg');
require('dotenv').config();

async function testDatabaseConnection() {
  console.log('🔍 Testing Strapi CMS PostgreSQL Database Connection...\n');

  const config = {
    host: process.env.DATABASE_HOST || 'localhost',
    port: parseInt(process.env.DATABASE_PORT) || 5432,
    database: process.env.DATABASE_NAME || 'strapi_cms',
    user: process.env.DATABASE_USERNAME || 'strapi',
    password: process.env.DATABASE_PASSWORD || 'strapi_password',
    ssl: process.env.DATABASE_SSL === 'true' ? { rejectUnauthorized: false } : false,
  };

  console.log('📋 Connection Configuration:');
  console.log(`   Host: ${config.host}`);
  console.log(`   Port: ${config.port}`);
  console.log(`   Database: ${config.database}`);
  console.log(`   User: ${config.user}`);
  console.log(`   SSL: ${config.ssl ? 'enabled' : 'disabled'}\n`);

  const client = new Client(config);

  try {
    // Test connection
    console.log('🔌 Connecting to database...');
    await client.connect();
    console.log('✅ Database connection successful!\n');

    // Test basic queries
    console.log('📊 Running database tests...');
    
    // Check database version
    const versionResult = await client.query('SELECT version()');
    console.log(`   PostgreSQL Version: ${versionResult.rows[0].version.split(' ')[1]}`);

    // Check if Strapi tables exist
    const tablesResult = await client.query(`
      SELECT COUNT(*) as table_count 
      FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_name LIKE 'strapi_%'
    `);
    console.log(`   Strapi Tables: ${tablesResult.rows[0].table_count}`);

    // Check if admin users exist
    const adminResult = await client.query(`
      SELECT COUNT(*) as admin_count 
      FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_name = 'admin_users'
    `);
    
    if (adminResult.rows[0].admin_count > 0) {
      const userCountResult = await client.query('SELECT COUNT(*) as user_count FROM admin_users');
      console.log(`   Admin Users: ${userCountResult.rows[0].user_count}`);
    }

    // Check content types
    const contentTypesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name NOT LIKE 'strapi_%' 
      AND table_name NOT LIKE 'admin_%'
      AND table_name NOT LIKE 'up_%'
      AND table_name NOT LIKE 'i18n_%'
      AND table_name NOT LIKE 'files%'
      AND table_name NOT LIKE 'upload_%'
      AND table_name NOT LIKE 'components_%'
      ORDER BY table_name
    `);

    if (contentTypesResult.rows.length > 0) {
      console.log(`   Content Types: ${contentTypesResult.rows.length}`);
      contentTypesResult.rows.forEach(row => {
        console.log(`     - ${row.table_name}`);
      });
    }

    console.log('\n🎉 All database tests passed successfully!');
    console.log('✅ Strapi CMS is ready to use PostgreSQL database');

  } catch (error) {
    console.error('❌ Database connection failed:');
    console.error(`   Error: ${error.message}`);
    console.error('\n🔧 Troubleshooting steps:');
    console.error('   1. Ensure PostgreSQL server is running');
    console.error('   2. Verify database credentials in .env file');
    console.error('   3. Check if database "strapi_cms" exists');
    console.error('   4. Verify user permissions');
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run the test
testDatabaseConnection().catch(console.error);
