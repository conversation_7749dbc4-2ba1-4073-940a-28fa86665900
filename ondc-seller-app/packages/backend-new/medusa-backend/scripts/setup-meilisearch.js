const { MeiliSearch } = require('meilisearch');
require('dotenv').config();

async function setupMeiliSearch() {
  try {
    console.log('🔧 Setting up MeiliSearch index configuration...');

    // Initialize MeiliSearch client
    const client = new MeiliSearch({
      host: process.env.MEILISEARCH_HOST || 'https://apigw.cubeone.in/api/search',
      apiKey: process.env.MEILISEARCH_MASTER_API_KEY || 'ohYQooC62EJ7VXAX96CAifTXL',
    });

    const indexName = process.env.MEILISEARCH_INDEX_PREFIX || 'medusa-products';
    console.log(`📋 Configuring index: ${indexName}`);

    // Get or create the index
    const index = client.index(indexName);

    // Check if index exists, if not create it
    try {
      await index.getStats();
      console.log('✅ Index already exists');
    } catch (error) {
      if (error.code === 'index_not_found') {
        console.log('🆕 Creating new index...');
        await client.createIndex(indexName, { primaryKey: 'id' });
        console.log('✅ Index created successfully');
      } else {
        throw error;
      }
    }

    // Configure searchable attributes
    console.log('🔍 Configuring searchable attributes...');
    await index.updateSearchableAttributes([
      'title',
      'description',
      'handle',
      'tags',
      'collection.title',
      'categories.name',
      'variants.title',
      'variants.sku',
    ]);

    // Configure filterable attributes
    console.log('🔧 Configuring filterable attributes...');
    await index.updateFilterableAttributes([
      'id',
      'handle',
      'status',
      'tenant_id',
      'collection_id',
      'category_id',
      'tags',
      'created_at',
      'updated_at',
      'variants.prices.amount',
    ]);

    // Configure sortable attributes
    console.log('📊 Configuring sortable attributes...');
    await index.updateSortableAttributes([
      'title',
      'created_at',
      'updated_at',
      'variants.prices.amount',
    ]);

    // Configure displayed attributes
    console.log('👁️ Configuring displayed attributes...');
    await index.updateDisplayedAttributes([
      'id',
      'title',
      'description',
      'handle',
      'status',
      'thumbnail',
      'tags',
      'collection_id',
      'category_id',
      'tenant_id',
      'created_at',
      'updated_at',
    ]);

    // Configure ranking rules
    console.log('🏆 Configuring ranking rules...');
    await index.updateRankingRules([
      'words',
      'typo',
      'proximity',
      'attribute',
      'sort',
      'exactness',
    ]);

    // Configure stop words (optional)
    console.log('🛑 Configuring stop words...');
    await index.updateStopWords([
      'the',
      'a',
      'an',
      'and',
      'or',
      'but',
      'in',
      'on',
      'at',
      'to',
      'for',
      'of',
      'with',
      'by',
    ]);

    // Configure synonyms (optional)
    console.log('🔄 Configuring synonyms...');
    await index.updateSynonyms({
      phone: ['mobile', 'smartphone', 'cell'],
      laptop: ['notebook', 'computer'],
      tv: ['television', 'monitor'],
      shirt: ['top', 'blouse'],
      pants: ['trousers', 'jeans'],
    });

    // Wait for all tasks to complete
    console.log('⏳ Waiting for configuration to complete...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Get final index stats
    const stats = await index.getStats();
    console.log('📊 Index statistics:', {
      numberOfDocuments: stats.numberOfDocuments,
      isIndexing: stats.isIndexing,
      fieldDistribution: stats.fieldDistribution,
    });

    console.log('✅ MeiliSearch index configuration completed successfully!');
    console.log(`🔗 Index URL: ${process.env.MEILISEARCH_HOST}/indexes/${indexName}`);
  } catch (error) {
    console.error('❌ Error setting up MeiliSearch:', error);
    process.exit(1);
  }
}

// Add sample data function
async function addSampleData() {
  try {
    console.log('📝 Adding sample data for testing...');

    const client = new MeiliSearch({
      host: process.env.MEILISEARCH_HOST || 'https://apigw.cubeone.in/api/search',
      apiKey: process.env.MEILISEARCH_MASTER_API_KEY || 'ohYQooC62EJ7VXAX96CAifTXL',
    });

    const indexName = process.env.MEILISEARCH_INDEX_PREFIX || 'medusa-products';
    const index = client.index(indexName);

    const sampleProducts = [
      {
        id: 'prod_kirana_001',
        title: 'Basmati Rice Premium 5kg',
        description: 'Premium quality basmati rice, long grain, aromatic and perfect for biryanis',
        handle: 'basmati-rice-premium-5kg',
        status: 'published',
        tenant_id: 'my-kirana-store',
        collection_id: 'col_grains',
        category_id: 'cat_rice',
        tags: ['rice', 'basmati', 'premium', 'grains'],
        thumbnail: '/images/basmati-rice-5kg.jpg',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        variants: {
          title: '5kg Pack',
          sku: 'RICE-BASMATI-5KG',
          prices: { amount: 45000 },
        },
      },
      {
        id: 'prod_kirana_002',
        title: 'Tata Salt 1kg',
        description: 'Pure and natural iodized salt for daily cooking needs',
        handle: 'tata-salt-1kg',
        status: 'published',
        tenant_id: 'my-kirana-store',
        collection_id: 'col_spices',
        category_id: 'cat_salt',
        tags: ['salt', 'tata', 'iodized', 'cooking'],
        thumbnail: '/images/tata-salt-1kg.jpg',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        variants: {
          title: '1kg Pack',
          sku: 'SALT-TATA-1KG',
          prices: { amount: 2500 },
        },
      },
      {
        id: 'prod_kirana_003',
        title: 'Amul Fresh Milk 1L',
        description: 'Fresh full cream milk from Amul, rich in calcium and protein',
        handle: 'amul-fresh-milk-1l',
        status: 'published',
        tenant_id: 'my-kirana-store',
        collection_id: 'col_dairy',
        category_id: 'cat_milk',
        tags: ['milk', 'amul', 'fresh', 'dairy'],
        thumbnail: '/images/amul-milk-1l.jpg',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        variants: {
          title: '1L Tetra Pack',
          sku: 'MILK-AMUL-1L',
          prices: { amount: 6000 },
        },
      },
      {
        id: 'prod_kirana_004',
        title: 'Maggi Noodles 2-Minute',
        description: 'Instant noodles with masala flavor, ready in 2 minutes',
        handle: 'maggi-noodles-2-minute',
        status: 'published',
        tenant_id: 'my-kirana-store',
        collection_id: 'col_instant_food',
        category_id: 'cat_noodles',
        tags: ['noodles', 'maggi', 'instant', 'masala'],
        thumbnail: '/images/maggi-noodles.jpg',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        variants: {
          title: '70g Pack',
          sku: 'NOODLES-MAGGI-70G',
          prices: { amount: 1400 },
        },
      },
      {
        id: 'prod_kirana_005',
        title: 'Britannia Good Day Cookies',
        description: 'Delicious butter cookies with cashew and almond pieces',
        handle: 'britannia-good-day-cookies',
        status: 'published',
        tenant_id: 'my-kirana-store',
        collection_id: 'col_snacks',
        category_id: 'cat_biscuits',
        tags: ['cookies', 'britannia', 'good day', 'snacks'],
        thumbnail: '/images/good-day-cookies.jpg',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        variants: {
          title: '200g Pack',
          sku: 'COOKIES-BRITANNIA-200G',
          prices: { amount: 4500 },
        },
      },
    ];

    // Add documents to the index
    const task = await index.addDocuments(sampleProducts);
    console.log('📤 Sample data upload task:', task.taskUid);

    // Wait for indexing to complete
    console.log('⏳ Waiting for indexing to complete...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    const stats = await index.getStats();
    console.log('✅ Sample data added successfully!');
    console.log(`📊 Total documents: ${stats.numberOfDocuments}`);
  } catch (error) {
    console.error('❌ Error adding sample data:', error);
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);

  if (args.includes('--setup-only')) {
    await setupMeiliSearch();
  } else if (args.includes('--data-only')) {
    await addSampleData();
  } else {
    await setupMeiliSearch();
    await addSampleData();
  }
}

main().catch(console.error);
