/**
 * Quick Verification Test
 * 
 * Tests the critical endpoints that were fixed to verify they're working
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:9000';
const TENANT_1 = 'my-kirana-store';
const TENANT_2 = 'kisan-connect';
const PUBLISHABLE_API_KEY = 'pk_3d67561dece2d466dc798c18c1f80523f84f3b2f01316e0bf915e51f3a59b98b';

const makeRequest = async (endpoint, tenantId, options = {}) => {
  const startTime = Date.now();
  try {
    const config = {
      method: options.method || 'GET',
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'x-tenant-id': tenantId,
        'x-publishable-api-key': PUBLISHABLE_API_KEY,
        'Content-Type': 'application/json',
        ...options.headers
      },
      timeout: 10000,
      ...options
    };

    if (options.data) {
      config.data = options.data;
    }

    if (options.params) {
      config.params = options.params;
    }

    const response = await axios(config);
    const responseTime = Date.now() - startTime;
    
    return {
      success: true,
      status: response.status,
      data: response.data,
      responseTime
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    return {
      success: false,
      status: error.response?.status || 0,
      error: error.response?.data || error.message,
      responseTime
    };
  }
};

const testCollectionsAPI = async () => {
  console.log('\n📚 Testing Fixed Collections API...');
  
  // Test both tenants
  for (const tenant of [TENANT_1, TENANT_2]) {
    const result = await makeRequest('/store/collections', tenant);
    
    if (result.success && result.status === 200) {
      console.log(`✅ Collections API (${tenant}): PASSED (${result.responseTime}ms)`);
      console.log(`   Found ${result.data.collections?.length || 0} collections`);
    } else {
      console.log(`❌ Collections API (${tenant}): FAILED`);
      console.log(`   Status: ${result.status}`);
      console.log(`   Error: ${JSON.stringify(result.error)}`);
    }
  }
};

const testSearchAPI = async () => {
  console.log('\n🔍 Testing Search API...');
  
  for (const tenant of [TENANT_1, TENANT_2]) {
    // Test search with query
    const searchResult = await makeRequest('/store/search', tenant, {
      params: { q: 'product', limit: 5 }
    });
    
    if (searchResult.success && searchResult.status === 200) {
      const hasMetadata = searchResult.data.hits?.some(hit => hit.metadata !== undefined);
      const hasVariants = searchResult.data.hits?.some(hit => hit.variants !== undefined);
      
      console.log(`✅ Search API (${tenant}): PASSED (${searchResult.responseTime}ms)`);
      console.log(`   Found ${searchResult.data.hits?.length || 0} results`);
      console.log(`   Metadata included: ${hasMetadata ? 'Yes' : 'No'}`);
      console.log(`   Variants included: ${hasVariants ? 'Yes' : 'No'}`);
    } else {
      console.log(`❌ Search API (${tenant}): FAILED`);
      console.log(`   Status: ${searchResult.status}`);
    }
    
    // Test search suggestions
    const suggestionsResult = await makeRequest('/store/search/suggestions', tenant, {
      method: 'POST',
      data: { query: 'prod', limit: 3 }
    });
    
    if (suggestionsResult.success && suggestionsResult.status === 200) {
      console.log(`✅ Search Suggestions (${tenant}): PASSED (${suggestionsResult.responseTime}ms)`);
      console.log(`   Found ${suggestionsResult.data.hits?.length || 0} suggestions`);
    } else {
      console.log(`❌ Search Suggestions (${tenant}): FAILED`);
      console.log(`   Status: ${suggestionsResult.status}`);
    }
  }
};

const testTenantIsolation = async () => {
  console.log('\n🔒 Testing Tenant Isolation...');
  
  // Get collections for both tenants
  const tenant1Collections = await makeRequest('/store/collections', TENANT_1);
  const tenant2Collections = await makeRequest('/store/collections', TENANT_2);
  
  if (tenant1Collections.success && tenant2Collections.success) {
    const tenant1Ids = new Set((tenant1Collections.data.collections || []).map(c => c.id));
    const tenant2Ids = new Set((tenant2Collections.data.collections || []).map(c => c.id));
    
    const overlap = [...tenant1Ids].filter(id => tenant2Ids.has(id));
    
    if (overlap.length === 0) {
      console.log('✅ Perfect tenant isolation - no collection overlap detected');
      console.log(`   ${TENANT_1}: ${tenant1Ids.size} collections`);
      console.log(`   ${TENANT_2}: ${tenant2Ids.size} collections`);
    } else {
      console.log(`❌ Tenant isolation issue - ${overlap.length} collections found in both tenants`);
    }
  } else {
    console.log('❌ Could not test tenant isolation due to API failures');
  }
};

const runQuickVerification = async () => {
  console.log('🚀 ===== QUICK VERIFICATION TEST =====');
  console.log(`🌐 Base URL: ${BASE_URL}`);
  console.log(`🏢 Testing Tenants: ${TENANT_1}, ${TENANT_2}`);
  
  try {
    await testCollectionsAPI();
    await testSearchAPI();
    await testTenantIsolation();
    
    console.log('\n🎉 ===== QUICK VERIFICATION COMPLETED =====');
    
  } catch (error) {
    console.error('💥 Fatal error during verification:', error);
  }
};

// Run verification
runQuickVerification();
