/**
 * Comprehensive Medusa API Test Suite
 *
 * Tests all available store and admin endpoints with multi-tenant isolation validation
 */

const axios = require("axios");
const fs = require("fs");

// Configuration
const BASE_URL = "http://localhost:9000";
const TENANT_1 = "my-kirana-store";
const TENANT_2 = "kisan-connect";

// Authentication tokens
const PUBLISHABLE_API_KEY =
  "pk_3d67561dece2d466dc798c18c1f80523f84f3b2f01316e0bf915e51f3a59b98b";
const CUSTOMER_TOKEN =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY3Rvcl9pZCI6ImN1c18wMUszOFA5TlhTS1ZEOERSUEswTlA1VDE2VCIsImFjdG9yX3R5cGUiOiJjdXN0b21lciIsImF1dGhfaWRlbnRpdHlfaWQiOiJhdXRoaWRfMDFLMzhQOU5TRDFCR0RUM1k0SDMyS1JLNFQiLCJhcHBfbWV0YWRhdGEiOnsiY3VzdG9tZXJfaWQiOiJjdXNfMDFLMzhQOU5YU0tWRDhEUlBLME5QNVQxNlQifSwiaWF0IjoxNzU3MDU5NTM3LCJleHAiOjE3NTcxNDU5Mzd9.05EzwxHk1LzPnWcKK5GkMSePNeO8YxG166i3TJIz7Z0";
const ADMIN_TOKEN =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY3Rvcl9pZCI6InVzZXJfMDFKWjRWVkdFSlg2S1RRM1JaTUI3OFk1TTEiLCJhY3Rvcl90eXBlIjoidXNlciIsImF1dGhfaWRlbnRpdHlfaWQiOiJhdXRoaWRfMDFKWjRWVkdKSlRLVzNSWUdNTUUwRVg0NDMiLCJhcHBfbWV0YWRhdGEiOnsidXNlcl9pZCI6InVzZXJfMDFKWjRWVkdFSlg2S1RRM1JaTUI3OFk1TTEifSwiaWF0IjoxNzU3MDU0NzI2LCJleHAiOjE3NTcxNDExMjZ9.ov7IsbAUsfkd_Xo_0DIl4Vyohs88ZzLfum_sXHW3aGk";

// Test results storage
const testResults = {
  store: {},
  admin: {},
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    errors: [],
  },
};

// Utility functions
const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

const makeStoreRequest = async (endpoint, tenantId, options = {}) => {
  const startTime = Date.now();
  try {
    const config = {
      method: options.method || "GET",
      url: `${BASE_URL}${endpoint}`,
      headers: {
        "x-tenant-id": tenantId,
        "x-publishable-api-key": PUBLISHABLE_API_KEY,
        "Content-Type": "application/json",
        ...options.headers,
      },
      timeout: 10000,
      ...options,
    };

    if (options.data) {
      config.data = options.data;
    }

    if (options.params) {
      config.params = options.params;
    }

    const response = await axios(config);
    const responseTime = Date.now() - startTime;

    return {
      success: true,
      status: response.status,
      data: response.data,
      responseTime,
      headers: response.headers,
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    return {
      success: false,
      status: error.response?.status || 0,
      error: error.response?.data || error.message,
      responseTime,
    };
  }
};

const makeAdminRequest = async (endpoint, tenantId, options = {}) => {
  const startTime = Date.now();
  try {
    const config = {
      method: options.method || "GET",
      url: `${BASE_URL}${endpoint}`,
      headers: {
        "x-tenant-id": tenantId,
        Authorization: `Bearer ${ADMIN_TOKEN}`,
        "Content-Type": "application/json",
        ...options.headers,
      },
      timeout: 10000,
      ...options,
    };

    if (options.data) {
      config.data = options.data;
    }

    if (options.params) {
      config.params = options.params;
    }

    const response = await axios(config);
    const responseTime = Date.now() - startTime;

    return {
      success: true,
      status: response.status,
      data: response.data,
      responseTime,
      headers: response.headers,
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    return {
      success: false,
      status: error.response?.status || 0,
      error: error.response?.data || error.message,
      responseTime,
    };
  }
};

const validateResponse = (
  result,
  expectedStatus = 200,
  validationRules = {}
) => {
  const validation = {
    statusCode: result.status === expectedStatus,
    hasData: !!result.data,
    responseTime: result.responseTime < 5000, // 5 second timeout
    tenantIsolation: true,
    dataIntegrity: true,
    errors: [],
  };

  // Validate status code
  if (!validation.statusCode) {
    validation.errors.push(
      `Expected status ${expectedStatus}, got ${result.status}`
    );
  }

  // Validate response structure
  if (validationRules.requiredFields && result.data) {
    for (const field of validationRules.requiredFields) {
      if (!(field in result.data)) {
        validation.dataIntegrity = false;
        validation.errors.push(`Missing required field: ${field}`);
      }
    }
  }

  // Validate tenant isolation
  if (validationRules.checkTenantId && result.data) {
    const data = Array.isArray(result.data) ? result.data : [result.data];
    for (const item of data) {
      if (
        item.tenant_id &&
        item.tenant_id !== validationRules.expectedTenantId
      ) {
        validation.tenantIsolation = false;
        validation.errors.push(
          `Tenant isolation violation: expected ${validationRules.expectedTenantId}, got ${item.tenant_id}`
        );
      }
    }
  }

  // Validate metadata and variants inclusion
  if (validationRules.checkMetadata && result.data) {
    const items = Array.isArray(result.data.products || result.data)
      ? result.data.products || result.data
      : [result.data];
    for (const item of items) {
      if (item.metadata === undefined) {
        validation.dataIntegrity = false;
        validation.errors.push("Missing metadata field in product data");
      }
      if (validationRules.checkVariants && item.variants === undefined) {
        validation.dataIntegrity = false;
        validation.errors.push("Missing variants field in product data");
      }
    }
  }

  validation.overall =
    validation.statusCode &&
    validation.hasData &&
    validation.responseTime &&
    validation.tenantIsolation &&
    validation.dataIntegrity;

  return validation;
};

const logTestResult = (category, endpoint, tenantId, result, validation) => {
  const testKey = `${endpoint}_${tenantId}`;

  if (!testResults[category]) {
    testResults[category] = {};
  }

  testResults[category][testKey] = {
    endpoint,
    tenantId,
    status: result.status,
    success: result.success,
    responseTime: result.responseTime,
    validation,
    timestamp: new Date().toISOString(),
  };

  testResults.summary.total++;
  if (validation.overall && result.success) {
    testResults.summary.passed++;
    console.log(
      `✅ ${category.toUpperCase()} ${endpoint} (${tenantId}): PASSED (${
        result.responseTime
      }ms)`
    );
  } else {
    testResults.summary.failed++;
    console.log(
      `❌ ${category.toUpperCase()} ${endpoint} (${tenantId}): FAILED`
    );
    if (validation.errors.length > 0) {
      console.log(`   Errors: ${validation.errors.join(", ")}`);
    }
    if (!result.success) {
      console.log(
        `   HTTP Error: ${result.status} - ${JSON.stringify(result.error)}`
      );
    }
    testResults.summary.errors.push({
      category,
      endpoint,
      tenantId,
      errors: validation.errors,
      httpError: result.success ? null : result.error,
    });
  }
};

// Store API Test Functions
const testStoreProducts = async (tenantId) => {
  console.log(`\n🛍️  Testing Store Products API for tenant: ${tenantId}`);

  // Test 1: Get all products
  const allProducts = await makeStoreRequest("/store/products", tenantId);
  const validation1 = validateResponse(allProducts, 200, {
    requiredFields: ["products"],
    checkTenantId: true,
    expectedTenantId: tenantId,
    checkMetadata: true,
    checkVariants: true,
  });
  logTestResult("store", "/store/products", tenantId, allProducts, validation1);

  // Test 2: Get products with pagination
  const paginatedProducts = await makeStoreRequest(
    "/store/products",
    tenantId,
    {
      params: { limit: 5, offset: 0 },
    }
  );
  const validation2 = validateResponse(paginatedProducts, 200, {
    requiredFields: ["products"],
    checkTenantId: true,
    expectedTenantId: tenantId,
  });
  logTestResult(
    "store",
    "/store/products?limit=5",
    tenantId,
    paginatedProducts,
    validation2
  );

  // Test 3: Get single product (if products exist)
  if (
    allProducts.success &&
    allProducts.data.products &&
    allProducts.data.products.length > 0
  ) {
    const productId = allProducts.data.products[0].id;
    const singleProduct = await makeStoreRequest(
      `/store/products/${productId}`,
      tenantId
    );
    const validation3 = validateResponse(singleProduct, 200, {
      requiredFields: ["product"],
      checkTenantId: true,
      expectedTenantId: tenantId,
      checkMetadata: true,
      checkVariants: true,
    });
    logTestResult(
      "store",
      `/store/products/${productId}`,
      tenantId,
      singleProduct,
      validation3
    );
  }
};

const testStoreCollections = async (tenantId) => {
  console.log(`\n📚 Testing Store Collections API for tenant: ${tenantId}`);

  // Test 1: Get all collections
  const allCollections = await makeStoreRequest("/store/collections", tenantId);
  const validation1 = validateResponse(allCollections, 200, {
    requiredFields: ["collections"],
    checkTenantId: true,
    expectedTenantId: tenantId,
  });
  logTestResult(
    "store",
    "/store/collections",
    tenantId,
    allCollections,
    validation1
  );

  // Test 2: Get single collection (if collections exist)
  if (
    allCollections.success &&
    allCollections.data.collections &&
    allCollections.data.collections.length > 0
  ) {
    const collectionId = allCollections.data.collections[0].id;
    const singleCollection = await makeStoreRequest(
      `/store/collections/${collectionId}`,
      tenantId
    );
    const validation2 = validateResponse(singleCollection, 200, {
      requiredFields: ["collection"],
    });
    logTestResult(
      "store",
      `/store/collections/${collectionId}`,
      tenantId,
      singleCollection,
      validation2
    );
  }
};

const testStoreCategories = async (tenantId) => {
  console.log(`\n🏷️  Testing Store Categories API for tenant: ${tenantId}`);

  // Test 1: Get all categories
  const allCategories = await makeStoreRequest(
    "/store/product-categories",
    tenantId
  );
  const validation1 = validateResponse(allCategories, 200, {
    requiredFields: ["product_categories"],
  });
  logTestResult(
    "store",
    "/store/product-categories",
    tenantId,
    allCategories,
    validation1
  );

  // Test 2: Get single category (if categories exist)
  if (
    allCategories.success &&
    allCategories.data.product_categories &&
    allCategories.data.product_categories.length > 0
  ) {
    const categoryId = allCategories.data.product_categories[0].id;
    const singleCategory = await makeStoreRequest(
      `/store/product-categories/${categoryId}`,
      tenantId
    );
    const validation2 = validateResponse(singleCategory, 200, {
      requiredFields: ["product_category"],
    });
    logTestResult(
      "store",
      `/store/product-categories/${categoryId}`,
      tenantId,
      singleCategory,
      validation2
    );
  }
};

const testStoreSearch = async (tenantId) => {
  console.log(`\n🔍 Testing Store Search API for tenant: ${tenantId}`);

  // Test 1: Search with query
  const searchResults = await makeStoreRequest("/store/search", tenantId, {
    params: { q: "product", limit: 10 },
  });
  const validation1 = validateResponse(searchResults, 200, {
    requiredFields: ["hits"],
    checkTenantId: true,
    expectedTenantId: tenantId,
    checkMetadata: true,
    checkVariants: true,
  });
  logTestResult(
    "store",
    "/store/search?q=product",
    tenantId,
    searchResults,
    validation1
  );

  // Test 2: Search without query (should return products)
  const noQuerySearch = await makeStoreRequest("/store/search", tenantId);
  const validation2 = validateResponse(noQuerySearch, 200, {
    requiredFields: ["hits"],
  });
  logTestResult("store", "/store/search", tenantId, noQuerySearch, validation2);

  // Test 3: Search suggestions
  const suggestions = await makeStoreRequest(
    "/store/search/suggestions",
    tenantId,
    {
      method: "POST",
      data: { query: "prod", limit: 5 },
    }
  );
  const validation3 = validateResponse(suggestions, 200, {
    requiredFields: ["hits"],
  });
  logTestResult(
    "store",
    "/store/search/suggestions",
    tenantId,
    suggestions,
    validation3
  );
};

const testStoreCart = async (tenantId) => {
  console.log(`\n🛒 Testing Store Cart API for tenant: ${tenantId}`);

  // Test 1: Create cart
  const createCart = await makeStoreRequest("/store/carts", tenantId, {
    method: "POST",
    data: {
      region_id: "reg_01K33E8B4MEHSRZBWT2RM11N6A", // Default region
    },
  });
  const validation1 = validateResponse(createCart, 200, {
    requiredFields: ["cart"],
  });
  logTestResult(
    "store",
    "POST /store/carts",
    tenantId,
    createCart,
    validation1
  );

  // Test 2: Get cart (if cart was created)
  if (createCart.success && createCart.data.cart) {
    const cartId = createCart.data.cart.id;
    const getCart = await makeStoreRequest(`/store/carts/${cartId}`, tenantId);
    const validation2 = validateResponse(getCart, 200, {
      requiredFields: ["cart"],
    });
    logTestResult(
      "store",
      `/store/carts/${cartId}`,
      tenantId,
      getCart,
      validation2
    );
  }
};

const testStoreCustomer = async (tenantId) => {
  console.log(`\n👤 Testing Store Customer API for tenant: ${tenantId}`);

  // Test 1: Get customer profile (authenticated)
  const customerProfile = await makeStoreRequest(
    "/store/customers/me",
    tenantId,
    {
      headers: {
        Authorization: `Bearer ${CUSTOMER_TOKEN}`,
      },
    }
  );
  const validation1 = validateResponse(customerProfile, 200, {
    requiredFields: ["customer"],
  });
  logTestResult(
    "store",
    "/store/customers/me",
    tenantId,
    customerProfile,
    validation1
  );

  // Test 2: Get customer orders (authenticated)
  const customerOrders = await makeStoreRequest(
    "/store/customers/me/orders",
    tenantId,
    {
      headers: {
        Authorization: `Bearer ${CUSTOMER_TOKEN}`,
      },
    }
  );
  const validation2 = validateResponse(customerOrders, 200, {
    requiredFields: ["orders"],
  });
  logTestResult(
    "store",
    "/store/customers/me/orders",
    tenantId,
    customerOrders,
    validation2
  );
};

const testStoreRegions = async (tenantId) => {
  console.log(`\n🌍 Testing Store Regions API for tenant: ${tenantId}`);

  // Test 1: Get all regions
  const allRegions = await makeStoreRequest("/store/regions", tenantId);
  const validation1 = validateResponse(allRegions, 200, {
    requiredFields: ["regions"],
  });
  logTestResult("store", "/store/regions", tenantId, allRegions, validation1);

  // Test 2: Get single region (if regions exist)
  if (
    allRegions.success &&
    allRegions.data.regions &&
    allRegions.data.regions.length > 0
  ) {
    const regionId = allRegions.data.regions[0].id;
    const singleRegion = await makeStoreRequest(
      `/store/regions/${regionId}`,
      tenantId
    );
    const validation2 = validateResponse(singleRegion, 200, {
      requiredFields: ["region"],
    });
    logTestResult(
      "store",
      `/store/regions/${regionId}`,
      tenantId,
      singleRegion,
      validation2
    );
  }
};

// Admin API Test Functions
const testAdminProducts = async (tenantId) => {
  console.log(`\n🔧 Testing Admin Products API for tenant: ${tenantId}`);

  // Test 1: Get all products
  const allProducts = await makeAdminRequest("/admin/products", tenantId);
  const validation1 = validateResponse(allProducts, 200, {
    requiredFields: ["products"],
    checkTenantId: true,
    expectedTenantId: tenantId,
    checkMetadata: true,
    checkVariants: true,
  });
  logTestResult("admin", "/admin/products", tenantId, allProducts, validation1);

  // Test 2: Get products with pagination
  const paginatedProducts = await makeAdminRequest(
    "/admin/products",
    tenantId,
    {
      params: { limit: 5, offset: 0 },
    }
  );
  const validation2 = validateResponse(paginatedProducts, 200, {
    requiredFields: ["products"],
    checkTenantId: true,
    expectedTenantId: tenantId,
  });
  logTestResult(
    "admin",
    "/admin/products?limit=5",
    tenantId,
    paginatedProducts,
    validation2
  );

  // Test 3: Get single product (if products exist)
  if (
    allProducts.success &&
    allProducts.data.products &&
    allProducts.data.products.length > 0
  ) {
    const productId = allProducts.data.products[0].id;
    const singleProduct = await makeAdminRequest(
      `/admin/products/${productId}`,
      tenantId
    );
    const validation3 = validateResponse(singleProduct, 200, {
      requiredFields: ["product"],
      checkTenantId: true,
      expectedTenantId: tenantId,
      checkMetadata: true,
      checkVariants: true,
    });
    logTestResult(
      "admin",
      `/admin/products/${productId}`,
      tenantId,
      singleProduct,
      validation3
    );
  }
};

const testAdminCustomers = async (tenantId) => {
  console.log(`\n👥 Testing Admin Customers API for tenant: ${tenantId}`);

  // Test 1: Get all customers
  const allCustomers = await makeAdminRequest("/admin/customers", tenantId);
  const validation1 = validateResponse(allCustomers, 200, {
    requiredFields: ["customers"],
    checkTenantId: true,
    expectedTenantId: tenantId,
  });
  logTestResult(
    "admin",
    "/admin/customers",
    tenantId,
    allCustomers,
    validation1
  );

  // Test 2: Get customers with pagination
  const paginatedCustomers = await makeAdminRequest(
    "/admin/customers",
    tenantId,
    {
      params: { limit: 10, offset: 0 },
    }
  );
  const validation2 = validateResponse(paginatedCustomers, 200, {
    requiredFields: ["customers"],
  });
  logTestResult(
    "admin",
    "/admin/customers?limit=10",
    tenantId,
    paginatedCustomers,
    validation2
  );

  // Test 3: Get single customer (if customers exist)
  if (
    allCustomers.success &&
    allCustomers.data.customers &&
    allCustomers.data.customers.length > 0
  ) {
    const customerId = allCustomers.data.customers[0].id;
    const singleCustomer = await makeAdminRequest(
      `/admin/customers/${customerId}`,
      tenantId
    );
    const validation3 = validateResponse(singleCustomer, 200, {
      requiredFields: ["customer"],
    });
    logTestResult(
      "admin",
      `/admin/customers/${customerId}`,
      tenantId,
      singleCustomer,
      validation3
    );
  }
};

const testAdminOrders = async (tenantId) => {
  console.log(`\n📋 Testing Admin Orders API for tenant: ${tenantId}`);

  // Test 1: Get all orders
  const allOrders = await makeAdminRequest("/admin/orders", tenantId);
  const validation1 = validateResponse(allOrders, 200, {
    requiredFields: ["orders"],
    checkTenantId: true,
    expectedTenantId: tenantId,
  });
  logTestResult("admin", "/admin/orders", tenantId, allOrders, validation1);

  // Test 2: Get orders with pagination
  const paginatedOrders = await makeAdminRequest("/admin/orders", tenantId, {
    params: { limit: 10, offset: 0 },
  });
  const validation2 = validateResponse(paginatedOrders, 200, {
    requiredFields: ["orders"],
  });
  logTestResult(
    "admin",
    "/admin/orders?limit=10",
    tenantId,
    paginatedOrders,
    validation2
  );

  // Test 3: Get single order (if orders exist)
  if (
    allOrders.success &&
    allOrders.data.orders &&
    allOrders.data.orders.length > 0
  ) {
    const orderId = allOrders.data.orders[0].id;
    const singleOrder = await makeAdminRequest(
      `/admin/orders/${orderId}`,
      tenantId
    );
    const validation3 = validateResponse(singleOrder, 200, {
      requiredFields: ["order"],
    });
    logTestResult(
      "admin",
      `/admin/orders/${orderId}`,
      tenantId,
      singleOrder,
      validation3
    );
  }
};

const testAdminCollections = async (tenantId) => {
  console.log(`\n📚 Testing Admin Collections API for tenant: ${tenantId}`);

  // Test 1: Get all collections
  const allCollections = await makeAdminRequest("/admin/collections", tenantId);
  const validation1 = validateResponse(allCollections, 200, {
    requiredFields: ["collections"],
    checkTenantId: true,
    expectedTenantId: tenantId,
  });
  logTestResult(
    "admin",
    "/admin/collections",
    tenantId,
    allCollections,
    validation1
  );

  // Test 2: Get single collection (if collections exist)
  if (
    allCollections.success &&
    allCollections.data.collections &&
    allCollections.data.collections.length > 0
  ) {
    const collectionId = allCollections.data.collections[0].id;
    const singleCollection = await makeAdminRequest(
      `/admin/collections/${collectionId}`,
      tenantId
    );
    const validation2 = validateResponse(singleCollection, 200, {
      requiredFields: ["collection"],
    });
    logTestResult(
      "admin",
      `/admin/collections/${collectionId}`,
      tenantId,
      singleCollection,
      validation2
    );
  }
};

const testAdminInventory = async (tenantId) => {
  console.log(`\n📦 Testing Admin Inventory API for tenant: ${tenantId}`);

  // Test 1: Get inventory items
  const inventoryItems = await makeAdminRequest(
    "/admin/inventory-items",
    tenantId
  );
  const validation1 = validateResponse(inventoryItems, 200, {
    requiredFields: ["inventory_items"],
  });
  logTestResult(
    "admin",
    "/admin/inventory-items",
    tenantId,
    inventoryItems,
    validation1
  );

  // Test 2: Get stock locations
  const stockLocations = await makeAdminRequest(
    "/admin/stock-locations",
    tenantId
  );
  const validation2 = validateResponse(stockLocations, 200, {
    requiredFields: ["stock_locations"],
  });
  logTestResult(
    "admin",
    "/admin/stock-locations",
    tenantId,
    stockLocations,
    validation2
  );
};

// Test execution functions
const runStoreTests = async (tenantId) => {
  console.log(
    `\n🏪 ===== RUNNING STORE API TESTS FOR TENANT: ${tenantId} =====`
  );

  await testStoreProducts(tenantId);
  await delay(1000);

  await testStoreCollections(tenantId);
  await delay(1000);

  await testStoreCategories(tenantId);
  await delay(1000);

  await testStoreSearch(tenantId);
  await delay(1000);

  await testStoreCart(tenantId);
  await delay(1000);

  await testStoreCustomer(tenantId);
  await delay(1000);

  await testStoreRegions(tenantId);
  await delay(1000);
};

const runAdminTests = async (tenantId) => {
  console.log(
    `\n🔧 ===== RUNNING ADMIN API TESTS FOR TENANT: ${tenantId} =====`
  );

  await testAdminProducts(tenantId);
  await delay(1000);

  await testAdminCustomers(tenantId);
  await delay(1000);

  await testAdminOrders(tenantId);
  await delay(1000);

  await testAdminCollections(tenantId);
  await delay(1000);

  await testAdminInventory(tenantId);
  await delay(1000);
};

const generateTestReport = () => {
  console.log("\n📊 ===== COMPREHENSIVE TEST REPORT =====");

  const report = {
    summary: testResults.summary,
    timestamp: new Date().toISOString(),
    configuration: {
      baseUrl: BASE_URL,
      tenants: [TENANT_1, TENANT_2],
      publishableApiKey: PUBLISHABLE_API_KEY.substring(0, 20) + "...",
      adminToken: ADMIN_TOKEN.substring(0, 20) + "...",
      customerToken: CUSTOMER_TOKEN.substring(0, 20) + "...",
    },
    results: {
      store: testResults.store,
      admin: testResults.admin,
    },
  };

  // Console summary
  console.log(`\n📈 SUMMARY:`);
  console.log(`   Total Tests: ${testResults.summary.total}`);
  console.log(
    `   Passed: ${testResults.summary.passed} (${(
      (testResults.summary.passed / testResults.summary.total) *
      100
    ).toFixed(1)}%)`
  );
  console.log(
    `   Failed: ${testResults.summary.failed} (${(
      (testResults.summary.failed / testResults.summary.total) *
      100
    ).toFixed(1)}%)`
  );

  // Tenant isolation analysis
  console.log(`\n🔒 TENANT ISOLATION ANALYSIS:`);
  const tenant1Tests =
    Object.values(testResults.store).filter((t) => t.tenantId === TENANT_1)
      .length +
    Object.values(testResults.admin).filter((t) => t.tenantId === TENANT_1)
      .length;
  const tenant2Tests =
    Object.values(testResults.store).filter((t) => t.tenantId === TENANT_2)
      .length +
    Object.values(testResults.admin).filter((t) => t.tenantId === TENANT_2)
      .length;

  console.log(`   ${TENANT_1}: ${tenant1Tests} tests`);
  console.log(`   ${TENANT_2}: ${tenant2Tests} tests`);

  // Performance analysis
  console.log(`\n⚡ PERFORMANCE ANALYSIS:`);
  const allTests = [
    ...Object.values(testResults.store),
    ...Object.values(testResults.admin),
  ];
  const responseTimes = allTests
    .map((t) => t.responseTime)
    .filter((t) => t > 0);

  if (responseTimes.length > 0) {
    const avgResponseTime =
      responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    const maxResponseTime = Math.max(...responseTimes);
    const minResponseTime = Math.min(...responseTimes);

    console.log(`   Average Response Time: ${avgResponseTime.toFixed(0)}ms`);
    console.log(`   Max Response Time: ${maxResponseTime}ms`);
    console.log(`   Min Response Time: ${minResponseTime}ms`);
  }

  // Error analysis
  if (testResults.summary.errors.length > 0) {
    console.log(`\n❌ ERROR ANALYSIS:`);
    testResults.summary.errors.forEach((error, index) => {
      console.log(
        `   ${index + 1}. ${error.category.toUpperCase()} ${error.endpoint} (${
          error.tenantId
        }):`
      );
      if (error.errors.length > 0) {
        error.errors.forEach((err) => console.log(`      - ${err}`));
      }
      if (error.httpError) {
        console.log(`      - HTTP Error: ${JSON.stringify(error.httpError)}`);
      }
    });
  }

  // Save detailed report to file
  const reportFileName = `api-test-report-${new Date()
    .toISOString()
    .replace(/[:.]/g, "-")}.json`;
  fs.writeFileSync(reportFileName, JSON.stringify(report, null, 2));
  console.log(`\n💾 Detailed report saved to: ${reportFileName}`);

  return report;
};

// Main test execution
const runAllTests = async () => {
  console.log("🚀 ===== STARTING COMPREHENSIVE MEDUSA API TESTS =====");
  console.log(`🌐 Base URL: ${BASE_URL}`);
  console.log(`🏢 Testing Tenants: ${TENANT_1}, ${TENANT_2}`);
  console.log(
    `🔑 Using Publishable Key: ${PUBLISHABLE_API_KEY.substring(0, 20)}...`
  );
  console.log(`🔐 Using Admin Token: ${ADMIN_TOKEN.substring(0, 20)}...`);
  console.log(`👤 Using Customer Token: ${CUSTOMER_TOKEN.substring(0, 20)}...`);

  try {
    // Test Store APIs for both tenants
    await runStoreTests(TENANT_1);
    await runStoreTests(TENANT_2);

    // Test Admin APIs for both tenants
    await runAdminTests(TENANT_1);
    await runAdminTests(TENANT_2);

    // Generate comprehensive report
    const report = generateTestReport();

    console.log("\n🎉 ===== ALL TESTS COMPLETED =====");

    // Exit with appropriate code
    if (testResults.summary.failed === 0) {
      console.log("✅ All tests passed successfully!");
      process.exit(0);
    } else {
      console.log(
        `❌ ${testResults.summary.failed} tests failed. Check the report for details.`
      );
      process.exit(1);
    }
  } catch (error) {
    console.error("💥 Fatal error during test execution:", error);
    process.exit(1);
  }
};

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = {
  runAllTests,
  runStoreTests,
  runAdminTests,
  generateTestReport,
  testResults,
};
