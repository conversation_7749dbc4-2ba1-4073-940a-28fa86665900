
# Product Data Integrity Fix Required

## Issue
Single product API responses missing metadata and variants:
- /admin/products/{id}
- /store/search?q=product

## Root Cause
Product retrieval not including complete relations and metadata.

## Fix Required
1. Ensure product queries include relations: ['variants', 'variants.prices', 'tags', 'collection', 'categories']
2. Verify metadata is properly included in responses
3. Check variant data structure completeness

## Expected Response Structure
```json
{
  "product": {
    "id": "prod_xxx",
    "title": "Product Title",
    "metadata": {},
    "variants": [
      {
        "id": "variant_xxx",
        "title": "Variant Title",
        "metadata": {},
        "prices": []
      }
    ]
  }
}
```
