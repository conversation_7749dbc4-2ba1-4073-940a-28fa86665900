/**
 * MeiliSearch Enhancement API Test
 *
 * Tests the enhanced MeiliSearch integration with:
 * - Multi-tenant filtering
 * - Metadata inclusion
 * - Product variants data
 * - Cross-tenant isolation
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:9000';
const TENANT_1 = 'my-kirana-store';
const TENANT_2 = 'kisan-connect';
const PUBLISHABLE_API_KEY = 'pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0';

// Test utilities
const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

const makeRequest = async (endpoint, tenantId, params = {}) => {
  try {
    const response = await axios.get(`${BASE_URL}${endpoint}`, {
      headers: {
        'x-tenant-id': tenantId,
        'x-publishable-api-key': PUBLISHABLE_API_KEY,
        'Content-Type': 'application/json',
      },
      params,
    });
    return response.data;
  } catch (error) {
    console.error(
      `❌ Request failed for ${endpoint} (tenant: ${tenantId}):`,
      error.response?.data || error.message
    );
    throw error;
  }
};

const makeSuggestionRequest = async (query, tenantId, limit = 5) => {
  try {
    const response = await axios.post(
      `${BASE_URL}/store/search/suggestions`,
      {
        query,
        limit,
      },
      {
        headers: {
          'x-tenant-id': tenantId,
          'x-publishable-api-key': PUBLISHABLE_API_KEY,
          'Content-Type': 'application/json',
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error(
      `❌ Suggestion request failed (tenant: ${tenantId}):`,
      error.response?.data || error.message
    );
    throw error;
  }
};

// Test functions
async function testBasicSearch() {
  console.log('\n🔍 Testing Basic Search Functionality...');

  try {
    // Test search with query for tenant 1
    const searchResults1 = await makeRequest('/store/search', TENANT_1, { q: 'product' });
    console.log(`✅ Tenant 1 search results: ${searchResults1.hits?.length || 0} products found`);

    // Test search with query for tenant 2
    const searchResults2 = await makeRequest('/store/search', TENANT_2, { q: 'product' });
    console.log(`✅ Tenant 2 search results: ${searchResults2.hits?.length || 0} products found`);

    return { searchResults1, searchResults2 };
  } catch (error) {
    console.error('❌ Basic search test failed:', error.message);
    throw error;
  }
}

async function testMetadataInclusion(searchResults) {
  console.log('\n📋 Testing Metadata Inclusion...');

  try {
    const { searchResults1, searchResults2 } = searchResults;

    // Check if metadata is included in tenant 1 results
    if (searchResults1.hits && searchResults1.hits.length > 0) {
      const firstProduct = searchResults1.hits[0];
      console.log(`✅ Tenant 1 - Product metadata present: ${firstProduct.metadata !== undefined}`);
      console.log(`📋 Tenant 1 - Sample metadata:`, JSON.stringify(firstProduct.metadata, null, 2));
    }

    // Check if metadata is included in tenant 2 results
    if (searchResults2.hits && searchResults2.hits.length > 0) {
      const firstProduct = searchResults2.hits[0];
      console.log(`✅ Tenant 2 - Product metadata present: ${firstProduct.metadata !== undefined}`);
      console.log(`📋 Tenant 2 - Sample metadata:`, JSON.stringify(firstProduct.metadata, null, 2));
    }

    return true;
  } catch (error) {
    console.error('❌ Metadata inclusion test failed:', error.message);
    throw error;
  }
}

async function testVariantsInclusion(searchResults) {
  console.log('\n🔄 Testing Product Variants Inclusion...');

  try {
    const { searchResults1, searchResults2 } = searchResults;

    // Check if variants are included in tenant 1 results
    if (searchResults1.hits && searchResults1.hits.length > 0) {
      const firstProduct = searchResults1.hits[0];
      const hasVariants = firstProduct.variants && Array.isArray(firstProduct.variants);
      console.log(`✅ Tenant 1 - Product variants present: ${hasVariants}`);
      console.log(`🔄 Tenant 1 - Variants count: ${firstProduct.variants?.length || 0}`);

      if (firstProduct.variants && firstProduct.variants.length > 0) {
        const firstVariant = firstProduct.variants[0];
        console.log(`📋 Tenant 1 - Sample variant structure:`, {
          id: firstVariant.id,
          title: firstVariant.title,
          sku: firstVariant.sku,
          metadata: firstVariant.metadata,
          prices: firstVariant.prices?.length || 0,
        });
      }
    }

    // Check if variants are included in tenant 2 results
    if (searchResults2.hits && searchResults2.hits.length > 0) {
      const firstProduct = searchResults2.hits[0];
      const hasVariants = firstProduct.variants && Array.isArray(firstProduct.variants);
      console.log(`✅ Tenant 2 - Product variants present: ${hasVariants}`);
      console.log(`🔄 Tenant 2 - Variants count: ${firstProduct.variants?.length || 0}`);

      if (firstProduct.variants && firstProduct.variants.length > 0) {
        const firstVariant = firstProduct.variants[0];
        console.log(`📋 Tenant 2 - Sample variant structure:`, {
          id: firstVariant.id,
          title: firstVariant.title,
          sku: firstVariant.sku,
          metadata: firstVariant.metadata,
          prices: firstVariant.prices?.length || 0,
        });
      }
    }

    return true;
  } catch (error) {
    console.error('❌ Variants inclusion test failed:', error.message);
    throw error;
  }
}

async function testTenantIsolation() {
  console.log('\n🔒 Testing Multi-Tenant Isolation...');

  try {
    // Get all products for tenant 1
    const tenant1Products = await makeRequest('/store/search', TENANT_1, { limit: 50 });

    // Get all products for tenant 2
    const tenant2Products = await makeRequest('/store/search', TENANT_2, { limit: 50 });

    console.log(`📊 Tenant 1 (${TENANT_1}): ${tenant1Products.hits?.length || 0} products`);
    console.log(`📊 Tenant 2 (${TENANT_2}): ${tenant2Products.hits?.length || 0} products`);

    // Check for cross-tenant contamination
    const tenant1Ids = new Set((tenant1Products.hits || []).map(p => p.id));
    const tenant2Ids = new Set((tenant2Products.hits || []).map(p => p.id));

    const overlap = [...tenant1Ids].filter(id => tenant2Ids.has(id));

    if (overlap.length === 0) {
      console.log('✅ Perfect tenant isolation - no product overlap detected');
    } else {
      console.warn(
        `⚠️ Tenant isolation issue - ${overlap.length} products found in both tenants:`,
        overlap
      );
    }

    // Verify tenant_id in responses
    const tenant1HasCorrectTenantId = (tenant1Products.hits || []).every(
      p => p.tenant_id === TENANT_1
    );
    const tenant2HasCorrectTenantId = (tenant2Products.hits || []).every(
      p => p.tenant_id === TENANT_2
    );

    console.log(`✅ Tenant 1 products have correct tenant_id: ${tenant1HasCorrectTenantId}`);
    console.log(`✅ Tenant 2 products have correct tenant_id: ${tenant2HasCorrectTenantId}`);

    return {
      tenant1Count: tenant1Products.hits?.length || 0,
      tenant2Count: tenant2Products.hits?.length || 0,
      overlap: overlap.length,
      tenant1HasCorrectTenantId,
      tenant2HasCorrectTenantId,
    };
  } catch (error) {
    console.error('❌ Tenant isolation test failed:', error.message);
    throw error;
  }
}

async function testSearchSuggestions() {
  console.log('\n💡 Testing Search Suggestions...');

  try {
    // Test suggestions for tenant 1
    const suggestions1 = await makeSuggestionRequest('prod', TENANT_1, 5);
    console.log(`✅ Tenant 1 suggestions: ${suggestions1.hits?.length || 0} results`);

    // Test suggestions for tenant 2
    const suggestions2 = await makeSuggestionRequest('prod', TENANT_2, 5);
    console.log(`✅ Tenant 2 suggestions: ${suggestions2.hits?.length || 0} results`);

    // Check if suggestions include metadata and variants
    if (suggestions1.hits && suggestions1.hits.length > 0) {
      const firstSuggestion = suggestions1.hits[0];
      console.log(
        `📋 Tenant 1 suggestion includes metadata: ${firstSuggestion.metadata !== undefined}`
      );
      console.log(
        `🔄 Tenant 1 suggestion includes variants: ${Array.isArray(firstSuggestion.variants)}`
      );
    }

    if (suggestions2.hits && suggestions2.hits.length > 0) {
      const firstSuggestion = suggestions2.hits[0];
      console.log(
        `📋 Tenant 2 suggestion includes metadata: ${firstSuggestion.metadata !== undefined}`
      );
      console.log(
        `🔄 Tenant 2 suggestion includes variants: ${Array.isArray(firstSuggestion.variants)}`
      );
    }

    return { suggestions1, suggestions2 };
  } catch (error) {
    console.error('❌ Search suggestions test failed:', error.message);
    throw error;
  }
}

async function testAdvancedFiltering() {
  console.log('\n🎯 Testing Advanced Filtering...');

  try {
    // Test category filtering
    const categoryResults1 = await makeRequest('/store/search', TENANT_1, {
      q: 'product',
      category_id: 'some-category-id',
      limit: 10,
    });
    console.log(`✅ Tenant 1 category filtering: ${categoryResults1.hits?.length || 0} results`);

    // Test collection filtering
    const collectionResults1 = await makeRequest('/store/search', TENANT_1, {
      q: 'product',
      collection_id: 'some-collection-id',
      limit: 10,
    });
    console.log(
      `✅ Tenant 1 collection filtering: ${collectionResults1.hits?.length || 0} results`
    );

    // Test sorting
    const sortedResults1 = await makeRequest('/store/search', TENANT_1, {
      q: 'product',
      sort: 'created_at:desc',
      limit: 10,
    });
    console.log(`✅ Tenant 1 sorted results: ${sortedResults1.hits?.length || 0} results`);

    return true;
  } catch (error) {
    console.error('❌ Advanced filtering test failed:', error.message);
    throw error;
  }
}

// Main test execution
async function runAllTests() {
  console.log('🚀 Starting MeiliSearch Enhancement API Tests...');
  console.log(`🏢 Testing with tenants: ${TENANT_1} and ${TENANT_2}`);
  console.log(`🌐 Base URL: ${BASE_URL}`);

  try {
    // Run all tests
    const searchResults = await testBasicSearch();
    await delay(1000);

    await testMetadataInclusion(searchResults);
    await delay(1000);

    await testVariantsInclusion(searchResults);
    await delay(1000);

    const isolationResults = await testTenantIsolation();
    await delay(1000);

    await testSearchSuggestions();
    await delay(1000);

    await testAdvancedFiltering();

    // Summary
    console.log('\n📊 Test Summary:');
    console.log('✅ Basic search functionality: PASSED');
    console.log('✅ Metadata inclusion: PASSED');
    console.log('✅ Product variants inclusion: PASSED');
    console.log(`✅ Tenant isolation: ${isolationResults.overlap === 0 ? 'PASSED' : 'FAILED'}`);
    console.log('✅ Search suggestions: PASSED');
    console.log('✅ Advanced filtering: PASSED');

    console.log('\n🎉 All MeiliSearch enhancement tests completed successfully!');
  } catch (error) {
    console.error('\n❌ Test suite failed:', error.message);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = {
  runAllTests,
  testBasicSearch,
  testMetadataInclusion,
  testVariantsInclusion,
  testTenantIsolation,
  testSearchSuggestions,
  testAdvancedFiltering,
};
