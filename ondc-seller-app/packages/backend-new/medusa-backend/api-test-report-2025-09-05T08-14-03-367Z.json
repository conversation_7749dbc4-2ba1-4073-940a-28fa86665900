{"summary": {"total": 51, "passed": 37, "failed": 14, "errors": [{"category": "store", "endpoint": "/store/collections", "tenantId": "my-kirana-store", "errors": ["Expected status 200, got 500"], "httpError": {"error": "Failed to fetch collections", "message": "handle is not defined", "tenant_id": "my-kirana-store", "_debug": {"error_type": "store_collections_error", "timestamp": "2025-09-05T08:13:37.054Z", "stack": "ReferenceError: handle is not defined\n    at GET (/home/<USER>/Documents/augment-projects/git-projects/ondc-seller-app/packages/backend-new/medusa-backend/src/api/store/collections/route.ts:47:5)\n    at GET (/home/<USER>/Documents/augment-projects/git-projects/ondc-seller-app/packages/backend-new/medusa-backend/node_modules/@medusajs/framework/src/http/utils/wrap-handler.ts:27:20)\n    at Layer.handle [as handle_request] (/home/<USER>/Documents/augment-projects/git-projects/ondc-seller-app/packages/backend-new/medusa-backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Documents/augment-projects/git-projects/ondc-seller-app/packages/backend-new/medusa-backend/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/home/<USER>/Documents/augment-projects/git-projects/ondc-seller-app/packages/backend-new/medusa-backend/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Documents/augment-projects/git-projects/ondc-seller-app/packages/backend-new/medusa-backend/node_modules/express/lib/router/layer.js:95:5)\n    at /home/<USER>/Documents/augment-projects/git-projects/ondc-seller-app/packages/backend-new/medusa-backend/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/home/<USER>/Documents/augment-projects/git-projects/ondc-seller-app/packages/backend-new/medusa-backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Documents/augment-projects/git-projects/ondc-seller-app/packages/backend-new/medusa-backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/home/<USER>/Documents/augment-projects/git-projects/ondc-seller-app/packages/backend-new/medusa-backend/node_modules/express/lib/router/route.js:141:14)"}}}, {"category": "store", "endpoint": "/store/search?q=product", "tenantId": "my-kirana-store", "errors": ["Missing metadata field in product data", "Missing variants field in product data"], "httpError": null}, {"category": "store", "endpoint": "/store/search/suggestions", "tenantId": "my-kirana-store", "errors": ["Missing required field: hits"], "httpError": null}, {"category": "store", "endpoint": "POST /store/carts", "tenantId": "my-kirana-store", "errors": ["Expected status 200, got 404"], "httpError": {"type": "not_found", "message": "No regions found"}}, {"category": "store", "endpoint": "/store/customers/me", "tenantId": "my-kirana-store", "errors": ["Expected status 200, got 400"], "httpError": {"type": "not_allowed", "message": "Publishable API key required in the request header: x-publishable-api-key. You can manage your keys in settings in the dashboard."}}, {"category": "store", "endpoint": "/store/customers/me/orders", "tenantId": "my-kirana-store", "errors": ["Expected status 200, got 400"], "httpError": {"type": "not_allowed", "message": "Publishable API key required in the request header: x-publishable-api-key. You can manage your keys in settings in the dashboard."}}, {"category": "store", "endpoint": "/store/collections", "tenantId": "kisan-connect", "errors": ["Expected status 200, got 500"], "httpError": {"error": "Failed to fetch collections", "message": "handle is not defined", "tenant_id": "kisan-connect", "_debug": {"error_type": "store_collections_error", "timestamp": "2025-09-05T08:13:44.651Z", "stack": "ReferenceError: handle is not defined\n    at GET (/home/<USER>/Documents/augment-projects/git-projects/ondc-seller-app/packages/backend-new/medusa-backend/src/api/store/collections/route.ts:47:5)\n    at GET (/home/<USER>/Documents/augment-projects/git-projects/ondc-seller-app/packages/backend-new/medusa-backend/node_modules/@medusajs/framework/src/http/utils/wrap-handler.ts:27:20)\n    at Layer.handle [as handle_request] (/home/<USER>/Documents/augment-projects/git-projects/ondc-seller-app/packages/backend-new/medusa-backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Documents/augment-projects/git-projects/ondc-seller-app/packages/backend-new/medusa-backend/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/home/<USER>/Documents/augment-projects/git-projects/ondc-seller-app/packages/backend-new/medusa-backend/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Documents/augment-projects/git-projects/ondc-seller-app/packages/backend-new/medusa-backend/node_modules/express/lib/router/layer.js:95:5)\n    at /home/<USER>/Documents/augment-projects/git-projects/ondc-seller-app/packages/backend-new/medusa-backend/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/home/<USER>/Documents/augment-projects/git-projects/ondc-seller-app/packages/backend-new/medusa-backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/home/<USER>/Documents/augment-projects/git-projects/ondc-seller-app/packages/backend-new/medusa-backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/home/<USER>/Documents/augment-projects/git-projects/ondc-seller-app/packages/backend-new/medusa-backend/node_modules/express/lib/router/route.js:141:14)"}}}, {"category": "store", "endpoint": "/store/search?q=product", "tenantId": "kisan-connect", "errors": ["Missing metadata field in product data", "Missing variants field in product data"], "httpError": null}, {"category": "store", "endpoint": "/store/search/suggestions", "tenantId": "kisan-connect", "errors": ["Missing required field: hits"], "httpError": null}, {"category": "store", "endpoint": "POST /store/carts", "tenantId": "kisan-connect", "errors": ["Expected status 200, got 404"], "httpError": {"type": "not_found", "message": "No regions found"}}, {"category": "store", "endpoint": "/store/customers/me", "tenantId": "kisan-connect", "errors": ["Expected status 200, got 400"], "httpError": {"type": "not_allowed", "message": "Publishable API key required in the request header: x-publishable-api-key. You can manage your keys in settings in the dashboard."}}, {"category": "store", "endpoint": "/store/customers/me/orders", "tenantId": "kisan-connect", "errors": ["Expected status 200, got 400"], "httpError": {"type": "not_allowed", "message": "Publishable API key required in the request header: x-publishable-api-key. You can manage your keys in settings in the dashboard."}}, {"category": "admin", "endpoint": "/admin/products/prod_1756799418168_yy4kb2dpo", "tenantId": "my-kirana-store", "errors": ["Missing metadata field in product data", "Missing variants field in product data"], "httpError": null}, {"category": "admin", "endpoint": "/admin/products/prod_1754978391532_jr02ba43p", "tenantId": "kisan-connect", "errors": ["Missing metadata field in product data", "Missing variants field in product data"], "httpError": null}]}, "timestamp": "2025-09-05T08:14:03.363Z", "configuration": {"baseUrl": "http://localhost:9000", "tenants": ["my-kirana-store", "kisan-connect"], "publishableApiKey": "pk_3d67561dece2d466d...", "adminToken": "eyJhbGciOiJIUzI1NiIs...", "customerToken": "eyJhbGciOiJIUzI1NiIs..."}, "results": {"store": {"/store/products_my-kirana-store": {"endpoint": "/store/products", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 96, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:35.995Z"}, "/store/products?limit=5_my-kirana-store": {"endpoint": "/store/products?limit=5", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 28, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:36.024Z"}, "/store/collections_my-kirana-store": {"endpoint": "/store/collections", "tenantId": "my-kirana-store", "status": 500, "success": false, "responseTime": 31, "validation": {"statusCode": false, "hasData": false, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": ["Expected status 200, got 500"], "overall": false}, "timestamp": "2025-09-05T08:13:37.057Z"}, "/store/product-categories_my-kirana-store": {"endpoint": "/store/product-categories", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 42, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:38.101Z"}, "/store/product-categories/pcat_1755171690805_g2mydkd_my-kirana-store": {"endpoint": "/store/product-categories/pcat_1755171690805_g2mydkd", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 40, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:38.141Z"}, "/store/search?q=product_my-kirana-store": {"endpoint": "/store/search?q=product", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 118, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": false, "errors": ["Missing metadata field in product data", "Missing variants field in product data"], "overall": false}, "timestamp": "2025-09-05T08:13:39.259Z"}, "/store/search_my-kirana-store": {"endpoint": "/store/search", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 36, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:39.295Z"}, "/store/search/suggestions_my-kirana-store": {"endpoint": "/store/search/suggestions", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 34, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": false, "errors": ["Missing required field: hits"], "overall": false}, "timestamp": "2025-09-05T08:13:39.329Z"}, "POST /store/carts_my-kirana-store": {"endpoint": "POST /store/carts", "tenantId": "my-kirana-store", "status": 404, "success": false, "responseTime": 207, "validation": {"statusCode": false, "hasData": false, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": ["Expected status 200, got 404"], "overall": false}, "timestamp": "2025-09-05T08:13:40.538Z"}, "/store/customers/me_my-kirana-store": {"endpoint": "/store/customers/me", "tenantId": "my-kirana-store", "status": 400, "success": false, "responseTime": 6, "validation": {"statusCode": false, "hasData": false, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": ["Expected status 200, got 400"], "overall": false}, "timestamp": "2025-09-05T08:13:41.545Z"}, "/store/customers/me/orders_my-kirana-store": {"endpoint": "/store/customers/me/orders", "tenantId": "my-kirana-store", "status": 400, "success": false, "responseTime": 4, "validation": {"statusCode": false, "hasData": false, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": ["Expected status 200, got 400"], "overall": false}, "timestamp": "2025-09-05T08:13:41.549Z"}, "/store/regions_my-kirana-store": {"endpoint": "/store/regions", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 38, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:42.588Z"}, "/store/regions/reg_01JZ7RPY072WGWKTJ6Q2YE46V7_my-kirana-store": {"endpoint": "/store/regions/reg_01JZ7RPY072WGWKTJ6Q2YE46V7", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 15, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:42.603Z"}, "/store/products_kisan-connect": {"endpoint": "/store/products", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 14, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:43.617Z"}, "/store/products?limit=5_kisan-connect": {"endpoint": "/store/products?limit=5", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 17, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:43.634Z"}, "/store/collections_kisan-connect": {"endpoint": "/store/collections", "tenantId": "kisan-connect", "status": 500, "success": false, "responseTime": 17, "validation": {"statusCode": false, "hasData": false, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": ["Expected status 200, got 500"], "overall": false}, "timestamp": "2025-09-05T08:13:44.652Z"}, "/store/product-categories_kisan-connect": {"endpoint": "/store/product-categories", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 61, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:45.715Z"}, "/store/product-categories/pcat_1754662121907_h6ub3dv_kisan-connect": {"endpoint": "/store/product-categories/pcat_1754662121907_h6ub3dv", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 37, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:45.752Z"}, "/store/search?q=product_kisan-connect": {"endpoint": "/store/search?q=product", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 66, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": false, "errors": ["Missing metadata field in product data", "Missing variants field in product data"], "overall": false}, "timestamp": "2025-09-05T08:13:46.818Z"}, "/store/search_kisan-connect": {"endpoint": "/store/search", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 22, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:46.841Z"}, "/store/search/suggestions_kisan-connect": {"endpoint": "/store/search/suggestions", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 46, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": false, "errors": ["Missing required field: hits"], "overall": false}, "timestamp": "2025-09-05T08:13:46.887Z"}, "POST /store/carts_kisan-connect": {"endpoint": "POST /store/carts", "tenantId": "kisan-connect", "status": 404, "success": false, "responseTime": 57, "validation": {"statusCode": false, "hasData": false, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": ["Expected status 200, got 404"], "overall": false}, "timestamp": "2025-09-05T08:13:47.945Z"}, "/store/customers/me_kisan-connect": {"endpoint": "/store/customers/me", "tenantId": "kisan-connect", "status": 400, "success": false, "responseTime": 5, "validation": {"statusCode": false, "hasData": false, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": ["Expected status 200, got 400"], "overall": false}, "timestamp": "2025-09-05T08:13:48.952Z"}, "/store/customers/me/orders_kisan-connect": {"endpoint": "/store/customers/me/orders", "tenantId": "kisan-connect", "status": 400, "success": false, "responseTime": 5, "validation": {"statusCode": false, "hasData": false, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": ["Expected status 200, got 400"], "overall": false}, "timestamp": "2025-09-05T08:13:48.957Z"}, "/store/regions_kisan-connect": {"endpoint": "/store/regions", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 41, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:49.999Z"}, "/store/regions/reg_01JZ7RPY072WGWKTJ6Q2YE46V7_kisan-connect": {"endpoint": "/store/regions/reg_01JZ7RPY072WGWKTJ6Q2YE46V7", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 21, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:50.021Z"}}, "admin": {"/admin/products_my-kirana-store": {"endpoint": "/admin/products", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 69, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:51.091Z"}, "/admin/products?limit=5_my-kirana-store": {"endpoint": "/admin/products?limit=5", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 39, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:51.130Z"}, "/admin/products/prod_1756799418168_yy4kb2dpo_my-kirana-store": {"endpoint": "/admin/products/prod_1756799418168_yy4kb2dpo", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 235, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": false, "errors": ["Missing metadata field in product data", "Missing variants field in product data"], "overall": false}, "timestamp": "2025-09-05T08:13:51.366Z"}, "/admin/customers_my-kirana-store": {"endpoint": "/admin/customers", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 17, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:52.385Z"}, "/admin/customers?limit=10_my-kirana-store": {"endpoint": "/admin/customers?limit=10", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 24, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:52.409Z"}, "/admin/customers/cus_01JZ4YTK58CYV26PZ299MK5QMD_my-kirana-store": {"endpoint": "/admin/customers/cus_01JZ4YTK58CYV26PZ299MK5QMD", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 29, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:52.438Z"}, "/admin/orders_my-kirana-store": {"endpoint": "/admin/orders", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 835, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:54.274Z"}, "/admin/orders?limit=10_my-kirana-store": {"endpoint": "/admin/orders?limit=10", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 76, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:54.350Z"}, "/admin/orders/order_01K3QW93Z3RKNPD4PNP1YZADHJ_my-kirana-store": {"endpoint": "/admin/orders/order_01K3QW93Z3RKNPD4PNP1YZADHJ", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 54, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:54.404Z"}, "/admin/collections_my-kirana-store": {"endpoint": "/admin/collections", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 77, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:55.482Z"}, "/admin/collections/pcol_1754565221865_ku8dnn9_my-kirana-store": {"endpoint": "/admin/collections/pcol_1754565221865_ku8dnn9", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 31, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:55.513Z"}, "/admin/inventory-items_my-kirana-store": {"endpoint": "/admin/inventory-items", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 219, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:56.733Z"}, "/admin/stock-locations_my-kirana-store": {"endpoint": "/admin/stock-locations", "tenantId": "my-kirana-store", "status": 200, "success": true, "responseTime": 85, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:56.818Z"}, "/admin/products_kisan-connect": {"endpoint": "/admin/products", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 31, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:57.850Z"}, "/admin/products?limit=5_kisan-connect": {"endpoint": "/admin/products?limit=5", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 25, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:57.875Z"}, "/admin/products/prod_1754978391532_jr02ba43p_kisan-connect": {"endpoint": "/admin/products/prod_1754978391532_jr02ba43p", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 79, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": false, "errors": ["Missing metadata field in product data", "Missing variants field in product data"], "overall": false}, "timestamp": "2025-09-05T08:13:57.954Z"}, "/admin/customers_kisan-connect": {"endpoint": "/admin/customers", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 12, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:58.967Z"}, "/admin/customers?limit=10_kisan-connect": {"endpoint": "/admin/customers?limit=10", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 12, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:13:58.979Z"}, "/admin/orders_kisan-connect": {"endpoint": "/admin/orders", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 108, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:14:00.088Z"}, "/admin/orders?limit=10_kisan-connect": {"endpoint": "/admin/orders?limit=10", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 95, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:14:00.183Z"}, "/admin/orders/order_01K3GRPDVFE885QBM5HTW1Q4VY_kisan-connect": {"endpoint": "/admin/orders/order_01K3GRPDVFE885QBM5HTW1Q4VY", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 48, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:14:00.232Z"}, "/admin/collections_kisan-connect": {"endpoint": "/admin/collections", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 40, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:14:01.272Z"}, "/admin/collections/pcol_1754997526350_8jfeer1_kisan-connect": {"endpoint": "/admin/collections/pcol_1754997526350_8jfeer1", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 44, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:14:01.316Z"}, "/admin/inventory-items_kisan-connect": {"endpoint": "/admin/inventory-items", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 31, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:14:02.348Z"}, "/admin/stock-locations_kisan-connect": {"endpoint": "/admin/stock-locations", "tenantId": "kisan-connect", "status": 200, "success": true, "responseTime": 14, "validation": {"statusCode": true, "hasData": true, "responseTime": true, "tenantIsolation": true, "dataIntegrity": true, "errors": [], "overall": true}, "timestamp": "2025-09-05T08:14:02.362Z"}}}}