# Enhanced Query Parameters for Medusa v2 Store API

This document describes the comprehensive query parameter support implemented for all Medusa v2 store API endpoints.

## Overview

All store API endpoints now support advanced query parameters for:
- **Filtering** with operators
- **Sorting** with multiple fields
- **Pagination** (offset-based and page-based)
- **Field Selection** for optimized responses
- **Relation Expansion** for including related data
- **Search** across multiple fields
- **Date Range Filtering**

## Supported Endpoints

- `/store/product-categories`
- `/store/products`
- `/store/collections`
- `/store/tags`

## Query Parameter Types

### 1. Pagination

#### Offset-based Pagination
```
GET /store/products?limit=20&offset=40
```

#### Page-based Pagination
```
GET /store/products?page=3&limit=20
```

**Parameters:**
- `limit`: Number of items to return (max: 100, default varies by endpoint)
- `offset`: Number of items to skip
- `page`: Page number (1-based, alternative to offset)

### 2. Sorting

Sort by single or multiple fields with direction:

```
GET /store/products?sort=title:asc
GET /store/products?sort=created_at:desc,title:asc
```

**Format:** `sort=field:direction[,field:direction]`
- `direction`: `asc` (ascending) or `desc` (descending)
- Multiple sorts are applied in order

**Available Sort Fields by Endpoint:**

#### Product Categories
- `id`, `name`, `handle`, `created_at`, `updated_at`, `rank`, `is_active`

#### Products
- `id`, `title`, `handle`, `status`, `created_at`, `updated_at`, `weight`, `length`, `height`, `width`

#### Collections
- `id`, `title`, `handle`, `created_at`, `updated_at`

#### Tags
- `id`, `value`, `created_at`, `updated_at`

### 3. Filtering

#### Basic Filtering
```
GET /store/products?status=published
GET /store/product-categories?is_active=true
```

#### Operator-based Filtering
```
GET /store/products?created_at__gte=2024-01-01
GET /store/products?weight__lt=1000
GET /store/products?id__in=prod_1,prod_2,prod_3
GET /store/product-categories?name__like=grocery
```

**Available Operators:**
- `__gt`: Greater than
- `__gte`: Greater than or equal
- `__lt`: Less than
- `__lte`: Less than or equal
- `__in`: In list (comma-separated values)
- `__not`: Not equal
- `__like`: Case-insensitive partial match
- `__startswith`: Starts with
- `__endswith`: Ends with

### 4. Field Selection

Select specific fields to optimize response size:

```
GET /store/products?fields=id,title,handle,status
GET /store/product-categories?fields=id,name,description
```

### 5. Relation Expansion

Include related data in the response:

```
GET /store/products?expand=variants,images,tags
GET /store/product-categories?expand=parent_category,category_children
```

**Available Relations by Endpoint:**

#### Product Categories
- `parent_category`, `category_children`, `products`

#### Products
- `variants`, `options`, `images`, `tags`, `type`, `collection`, `categories`, `profiles`

#### Collections
- `products`

#### Tags
- `products`

### 6. Search

Search across multiple fields:

```
GET /store/products?q=organic rice
GET /store/product-categories?search=grocery
```

**Search Fields by Endpoint:**

#### Product Categories
- `name`, `description`, `handle`

#### Products
- `title`, `subtitle`, `description`, `handle`

#### Collections
- `title`, `handle`

#### Tags
- `value`

### 7. Date Filtering

Filter by date ranges:

```
GET /store/products?created_after=2024-01-01
GET /store/products?created_before=2024-12-31
GET /store/products?updated_after=2024-06-01&updated_before=2024-06-30
```

**Available Date Filters:**
- `created_after`: Items created after this date
- `created_before`: Items created before this date
- `updated_after`: Items updated after this date
- `updated_before`: Items updated before this date

## Response Format

All endpoints return enhanced responses with metadata:

```json
{
  "products": [...],
  "count": 10,
  "offset": 0,
  "limit": 20,
  "total": 150,
  "_meta": {
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total_pages": 8,
      "has_more": true
    },
    "filters_applied": {
      "status": "published",
      "category_id": "cat_123"
    },
    "sorting": [
      {"field": "title", "direction": "ASC"}
    ],
    "search": "organic rice",
    "tenant_id": "my-store"
  }
}
```

## Example Queries

### Complex Product Search
```
GET /store/products?q=organic&status=published&sort=created_at:desc&fields=id,title,thumbnail&expand=variants&limit=10
```

### Category Hierarchy with Filtering
```
GET /store/product-categories?is_active=true&sort=rank:asc,name:asc&expand=category_children&fields=id,name,handle
```

### Date Range Product Listing
```
GET /store/products?created_after=2024-01-01&created_before=2024-03-31&sort=created_at:desc&limit=50
```

### Advanced Filtering with Operators
```
GET /store/products?weight__gte=100&weight__lte=1000&status=published&category_id__in=cat_1,cat_2
```

## Error Handling

Invalid query parameters return 400 Bad Request with details:

```json
{
  "error": "Invalid query parameters",
  "details": [
    "Invalid sort field: invalid_field. Allowed: id,name,handle,created_at,updated_at",
    "Invalid expand relation: invalid_relation. Allowed: parent_category,category_children"
  ],
  "tenant_id": "my-store"
}
```

## Performance Considerations

1. **Limit Usage**: Use appropriate limits to avoid large response payloads
2. **Field Selection**: Use `fields` parameter to reduce response size
3. **Indexing**: Ensure database indexes exist for frequently filtered/sorted fields
4. **Caching**: Consider caching for frequently accessed data

## Migration from Legacy API

The enhanced query parameters are backward compatible. Existing queries will continue to work, but you can now use the new features:

### Before
```
GET /store/products?limit=20&offset=0&search=rice
```

### After (Enhanced)
```
GET /store/products?limit=20&page=1&q=rice&sort=title:asc&fields=id,title,thumbnail
```

## Implementation Details

- **Query Parser**: `enhanced-query-parser.ts` handles all parameter parsing and validation
- **Configuration**: `store-api-configs.ts` defines allowed fields and limits per endpoint
- **Validation**: Comprehensive validation with detailed error messages
- **Multi-tenant**: All queries respect tenant isolation
- **SQL Generation**: Optimized SQL generation with proper parameterization
