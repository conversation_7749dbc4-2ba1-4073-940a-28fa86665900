# Medusa Service Refactoring Implementation Plan

## Overview

This plan systematically replaces direct database queries with proper Medusa v2 services to resolve connection timeout issues and improve architecture compliance.

## Phase 1: Critical Admin Endpoints (Priority 1)

### 1.1 Admin Products Endpoint
**File**: `src/api/admin/products/route.ts`
**Current Issue**: Uses `centralizedDb.query()` with raw SQL
**Target**: Replace with `productModuleService`

#### Implementation Steps:
```typescript
// BEFORE (Current - Problematic)
const productsResult = await centralizedDb.query(`
  SELECT id, title, handle, description, status, created_at, updated_at, tenant_id, metadata, thumbnail, collection_id
  FROM product
  WHERE tenant_id = $1 AND deleted_at IS NULL
  ORDER BY created_at DESC
  LIMIT $2 OFFSET $3
`, [tenantId, limit, offset]);

// AFTER (Target - Using Medusa Services)
const productService = req.scope.resolve('product');
const products = await productService.listProducts({
  tenant_id: tenantId,
  deleted_at: null
}, {
  skip: parseInt(offset as string),
  take: parseInt(limit as string),
  order: { created_at: 'DESC' },
  select: ['id', 'title', 'handle', 'description', 'status', 'created_at', 'updated_at', 'tenant_id', 'metadata', 'thumbnail', 'collection_id']
});
```

#### Testing Requirements:
- Verify identical response structure
- Test pagination (limit/offset)
- Confirm tenant filtering works
- Validate sorting by created_at DESC

### 1.2 Admin Customers Endpoint
**File**: `src/api/admin/customers/route.ts`
**Current Issue**: Uses `centralizedDb.getCustomers()` with raw SQL
**Target**: Replace with `customerModuleService`

#### Implementation Steps:
```typescript
// BEFORE (Current - Problematic)
const customersResult = await centralizedDb.getCustomers(tenantId, {
  limit: parseInt(limit as string),
  offset: parseInt(offset as string),
});

// AFTER (Target - Using Medusa Services)
const customerService = req.scope.resolve('customer');
const customers = await customerService.listCustomers({
  tenant_id: tenantId
}, {
  skip: parseInt(offset as string),
  take: parseInt(limit as string)
});
```

### 1.3 Admin Orders Endpoint
**File**: `src/api/admin/orders/route.ts`
**Current Issue**: Creates new PostgreSQL client with complex raw SQL
**Target**: Replace with `orderModuleService`

#### Implementation Steps:
```typescript
// BEFORE (Current - Problematic)
const { Client } = require('pg');
const client = new Client({ connectionString: process.env.DATABASE_URL });
await client.connect();
const result = await client.query(`
  SELECT o.id, o.status, o.currency_code, o.email, o.display_id,
         o.created_at, o.updated_at, o.tenant_id, o.metadata,
         c.email as customer_email, c.first_name as customer_first_name
  FROM "order" o
  LEFT JOIN customer c ON o.customer_id = c.id
  WHERE o.tenant_id = $1 AND o.deleted_at IS NULL
  ORDER BY o.created_at DESC
  LIMIT $2 OFFSET $3
`, [tenantId, limit, offset]);

// AFTER (Target - Using Medusa Services)
const orderService = req.scope.resolve('order');
const orders = await orderService.listOrders({
  tenant_id: tenantId,
  deleted_at: null
}, {
  skip: parseInt(offset as string),
  take: parseInt(limit as string),
  order: { created_at: 'DESC' },
  relations: ['customer']
});
```

## Phase 2: Critical Store Endpoints (Priority 1)

### 2.1 Store Orders Endpoint
**File**: `src/api/store/orders/route.ts`
**Current Issue**: Creates new PostgreSQL client for customer orders
**Target**: Replace with `orderModuleService`

#### Implementation Steps:
```typescript
// BEFORE (Current - Problematic)
const { Client } = require('pg');
const client = new Client({ connectionString: process.env.DATABASE_URL });
const result = await client.query(`
  SELECT o.id, o.status, o.currency_code, o.email, o.display_id,
         os.totals as order_totals
  FROM "order" o
  LEFT JOIN order_summary os ON o.id = os.order_id
  WHERE o.customer_id = $1 AND o.tenant_id = $2 AND o.deleted_at IS NULL
  ORDER BY o.created_at DESC
  LIMIT $3 OFFSET $4
`, [customerId, tenantId, limit, offset]);

// AFTER (Target - Using Medusa Services)
const orderService = req.scope.resolve('order');
const orders = await orderService.listOrders({
  customer_id: customerId,
  tenant_id: tenantId,
  deleted_at: null
}, {
  skip: parseInt(offset as string),
  take: parseInt(limit as string),
  order: { created_at: 'DESC' },
  relations: ['order_summary']
});
```

### 2.2 Store Order Detail Endpoint
**File**: `src/api/store/orders/[id]/route.ts`
**Current Issue**: Creates new PostgreSQL client for single order
**Target**: Replace with `orderModuleService.retrieveOrder()`

## Phase 3: Service Cleanup (Priority 2)

### 3.1 Remove DirectDatabaseService
**File**: `src/services/direct-database-service.ts`
**Action**: Complete removal - replace all usages with Medusa services

#### Affected Endpoints:
- `/admin/direct-tenant-test` - Replace with proper service calls
- Any other endpoints using `DirectDatabaseService.fromRequest()`

### 3.2 Remove CentralizedDatabaseService
**File**: `src/services/centralized-database.ts`
**Action**: Complete removal - replace all usages with Medusa services

#### Affected Endpoints:
- `/admin/products` - Already covered in Phase 1.1
- `/admin/customers` - Already covered in Phase 1.2

### 3.3 Refactor TenantAwareProductService
**File**: `src/services/tenant-aware-product.ts`
**Action**: Remove direct PostgreSQL connections, use Medusa services only

## Implementation Guidelines

### Service Resolution Pattern
```typescript
// ✅ CORRECT - Use these service names
const productService = req.scope.resolve('product');
const customerService = req.scope.resolve('customer');
const orderService = req.scope.resolve('order');
const cartService = req.scope.resolve('cart');

// ❌ INCORRECT - Don't use these patterns
const productService = req.scope.resolve('productService');
const productService = req.scope.resolve('productModuleService');
```

### Multi-Tenant Filtering
```typescript
// Apply tenant filtering in service calls
const filters = {
  tenant_id: tenantId,
  deleted_at: null,
  ...otherFilters
};

const config = {
  skip: parseInt(offset as string),
  take: parseInt(limit as string),
  order: { created_at: 'DESC' },
  relations: ['variants', 'images']
};

const products = await productService.listProducts(filters, config);
```

### Error Handling Pattern
```typescript
try {
  const service = req.scope.resolve('product');
  const result = await service.listProducts(filters, config);
  
  res.status(200).json({
    products: result,
    count: result.length,
    offset: parseInt(offset as string),
    limit: parseInt(limit as string)
  });
} catch (error) {
  console.error(`❌ [ENDPOINT] Error:`, error);
  res.status(500).json({
    error: 'Internal server error',
    message: error instanceof Error ? error.message : 'Unknown error'
  });
}
```

## Testing Strategy

### Pre-Implementation Testing
1. **Document Current Responses**: Capture exact response formats from existing endpoints
2. **Create Test Cases**: Comprehensive test scenarios for each endpoint
3. **Performance Baseline**: Measure current response times and connection usage

### Post-Implementation Testing
1. **Response Validation**: Ensure identical response structure
2. **Multi-Tenant Isolation**: Verify tenant data separation
3. **Performance Testing**: Confirm no regression in response times
4. **Connection Monitoring**: Verify connection pool usage reduction

### Test Commands
```bash
# Run database migrations
npx medusa migration run

# Test specific endpoint
curl -H "x-tenant-id: tenant-electronics-001" \
     -H "Authorization: Bearer <token>" \
     http://localhost:9000/admin/products

# Monitor connection usage
# Check PostgreSQL connection count before/after changes
```

## Success Criteria

### Phase 1 Success Metrics
- [ ] All admin endpoints use Medusa services
- [ ] No direct database connections in admin routes
- [ ] Response formats remain identical
- [ ] Multi-tenant isolation maintained
- [ ] Performance equal or better

### Phase 2 Success Metrics
- [ ] All store endpoints use Medusa services
- [ ] Customer-facing functionality preserved
- [ ] Authentication and authorization working
- [ ] Order history and details accurate

### Phase 3 Success Metrics
- [ ] All custom database services removed
- [ ] Connection pool usage reduced by 80%+
- [ ] Memory leak issues resolved
- [ ] Architecture compliance achieved

## Risk Mitigation

### Rollback Strategy
- Keep original files as `.backup` during implementation
- Test each endpoint individually before proceeding
- Maintain comprehensive test coverage

### Monitoring
- Monitor connection pool usage during implementation
- Track response times and error rates
- Set up alerts for connection timeout issues

---

**Next Step**: Begin Phase 1.1 - Admin Products Endpoint Refactoring
