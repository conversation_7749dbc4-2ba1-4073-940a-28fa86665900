import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { centralizedDb } from '../../../../services/centralized-database';

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  console.log(`🚀 [ADMIN-USER-GET] === TENANT-AWARE USER GET ENDPOINT CALLED ===`);

  try {
    // Extract tenant ID from header and user ID from params
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';
    const userId = req.params.id;

    console.log(`🔍 [ADMIN-USER-GET] Getting user ${userId} for tenant: ${tenantId}`);

    let user: any = null;

    try {
      // Get user by ID (users are global, not tenant-specific)
      const query = `
        SELECT
          u.id, u.email, u.first_name, u.last_name,
          u.avatar_url, u.created_at, u.updated_at, u.deleted_at,
          u.metadata
        FROM "user" u
        WHERE u.id = $1 AND u.deleted_at IS NULL
      `;

      const result = await centralizedDb.query(query, [userId], { tenantId });

      if (result.rows && result.rows.length > 0) {
        user = result.rows[0];

        // Parse metadata
        if (typeof user.metadata === 'string') {
          try {
            user.metadata = JSON.parse(user.metadata);
          } catch (e) {
            console.log(`⚠️ Could not parse metadata for user ${user.id}`);
          }
        }
      }

      console.log(`👤 [ADMIN-USER-GET] Retrieved user: ${user ? 'found' : 'not found'}`);
    } catch (dbError) {
      console.error('❌ [ADMIN-USER-GET] Database error:', dbError);
      return res.status(500).json({
        error: 'Database error',
        message: dbError.message,
        tenant_id: tenantId,
      });
    }

    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: `User with id ${userId} not found`,
        tenant_id: tenantId,
      });
    }

    // Add tenant headers for debugging
    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Tenant-Filtered', 'true');
    res.setHeader('X-Service-Method', 'centralized-database');

    res.json({ user });
  } catch (error: any) {
    console.error('❌ [ADMIN-USER-GET] Error getting user:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    res.status(500).json({
      error: 'Failed to get user',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'database_error',
        timestamp: new Date().toISOString(),
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      },
    });
  }
}

export async function PATCH(req: MedusaRequest, res: MedusaResponse) {
  console.log(`🚀 [ADMIN-USER-PATCH] === TENANT-AWARE USER UPDATE ENDPOINT CALLED ===`);
  console.log(`🚀 [ADMIN-USER-PATCH] Body:`, JSON.stringify(req.body, null, 2));

  try {
    // Extract tenant ID from header and user ID from params
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';
    const userId = req.params.id;

    console.log(`🔍 [ADMIN-USER-PATCH] Updating user ${userId} for tenant: ${tenantId}`);

    const { email, first_name, last_name, avatar_url, metadata } = req.body;

    let user: any = null;

    try {
      // First, check if user exists (users are global, not tenant-specific)
      const checkQuery = `
        SELECT id, metadata FROM "user"
        WHERE id = $1 AND deleted_at IS NULL
      `;

      const checkResult = await centralizedDb.query(checkQuery, [userId], { tenantId });

      if (!checkResult.rows || checkResult.rows.length === 0) {
        return res.status(404).json({
          error: 'User not found',
          message: `User with id ${userId} not found`,
          tenant_id: tenantId,
        });
      }

      // Build update query dynamically
      let updateFields: string[] = [];
      let updateParams: any[] = [];
      let paramIndex = 1;

      if (email !== undefined) {
        updateFields.push(`email = $${paramIndex++}`);
        updateParams.push(email);
      }
      if (first_name !== undefined) {
        updateFields.push(`first_name = $${paramIndex++}`);
        updateParams.push(first_name);
      }
      if (last_name !== undefined) {
        updateFields.push(`last_name = $${paramIndex++}`);
        updateParams.push(last_name);
      }
      if (avatar_url !== undefined) {
        updateFields.push(`avatar_url = $${paramIndex++}`);
        updateParams.push(avatar_url);
      }
      if (metadata !== undefined) {
        // Merge with existing metadata
        let existingMetadata = {};
        if (checkResult.rows[0].metadata) {
          try {
            existingMetadata =
              typeof checkResult.rows[0].metadata === 'string'
                ? JSON.parse(checkResult.rows[0].metadata)
                : checkResult.rows[0].metadata;
          } catch (e) {
            console.log(`⚠️ Could not parse existing metadata for user ${userId}`);
          }
        }

        const mergedMetadata = { ...existingMetadata, ...metadata };
        updateFields.push(`metadata = $${paramIndex++}`);
        updateParams.push(JSON.stringify(mergedMetadata));
      }

      // Always update updated_at
      updateFields.push(`updated_at = NOW()`);

      if (updateFields.length === 1) {
        // Only updated_at
        return res.status(400).json({
          error: 'No fields to update',
          message: 'At least one field must be provided for update',
          tenant_id: tenantId,
        });
      }

      // Add WHERE conditions (users are global, not tenant-specific)
      updateParams.push(userId);
      const whereClause = `WHERE id = $${paramIndex++} AND deleted_at IS NULL`;

      const updateQuery = `
        UPDATE "user"
        SET ${updateFields.join(', ')}
        ${whereClause}
        RETURNING id, email, first_name, last_name, avatar_url, created_at, updated_at, metadata
      `;

      console.log(`🔄 [ADMIN-USER-PATCH] Update query:`, updateQuery);
      console.log(`🔄 [ADMIN-USER-PATCH] Update params:`, updateParams);

      const result = await centralizedDb.query(updateQuery, updateParams, { tenantId });

      if (result.rows && result.rows.length > 0) {
        user = result.rows[0];

        // Parse metadata
        if (typeof user.metadata === 'string') {
          try {
            user.metadata = JSON.parse(user.metadata);
          } catch (e) {
            console.log(`⚠️ Could not parse metadata for updated user ${user.id}`);
          }
        }
      }

      console.log(`👤 [ADMIN-USER-PATCH] Updated user successfully`);
    } catch (dbError) {
      console.error('❌ [ADMIN-USER-PATCH] Database error:', dbError);
      return res.status(500).json({
        error: 'Database error',
        message: dbError.message,
        tenant_id: tenantId,
      });
    }

    // Add tenant headers for debugging
    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Tenant-Filtered', 'true');
    res.setHeader('X-Service-Method', 'centralized-database');

    res.json({
      user,
      message: 'User updated successfully',
      tenant_id: tenantId,
    });
  } catch (error: any) {
    console.error('❌ [ADMIN-USER-PATCH] Error updating user:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    res.status(500).json({
      error: 'Failed to update user',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'database_error',
        timestamp: new Date().toISOString(),
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      },
    });
  }
}
