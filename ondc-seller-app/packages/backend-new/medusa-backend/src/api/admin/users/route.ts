import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { centralizedDb } from '../../../services/centralized-database';

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  console.log(`🚀 [ADMIN-USERS] === TENANT-AWARE USERS ENDPOINT CALLED ===`);
  console.log(`🚀 [ADMIN-USERS] Headers:`, JSON.stringify(req.headers, null, 2));
  console.log(`🚀 [ADMIN-USERS] Query:`, JSON.stringify(req.query, null, 2));

  try {
    // Extract tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    console.log(`🔍 [ADMIN-USERS] Processing users request for tenant: ${tenantId}`);

    // Get query parameters
    const { limit = 50, offset = 0, email, first_name, last_name, expand, fields } = req.query;

    console.log(`🔗 [ADMIN-USERS] Using centralized database for tenant: ${tenantId}`);

    let users: any[] = [];
    let count = 0;

    try {
      // Build WHERE clause for filters (users are global, not tenant-specific)
      let whereClause = 'WHERE u.deleted_at IS NULL';
      let queryParams: any[] = [];
      let paramIndex = 1;

      if (email) {
        whereClause += ` AND u.email ILIKE $${paramIndex++}`;
        queryParams.push(`%${email}%`);
      }
      if (first_name) {
        whereClause += ` AND u.first_name ILIKE $${paramIndex++}`;
        queryParams.push(`%${first_name}%`);
      }
      if (last_name) {
        whereClause += ` AND u.last_name ILIKE $${paramIndex++}`;
        queryParams.push(`%${last_name}%`);
      }

      // Get total count for this tenant
      const countResult = await centralizedDb.query(
        `SELECT COUNT(*) as total FROM "user" u ${whereClause}`,
        queryParams,
        { tenantId }
      );
      count = parseInt(countResult.rows[0]?.total || 0);

      // Get users
      const query = `
        SELECT
          u.id, u.email, u.first_name, u.last_name,
          u.avatar_url, u.created_at, u.updated_at, u.deleted_at,
          u.metadata
        FROM "user" u
        ${whereClause}
        ORDER BY u.created_at DESC
        LIMIT $${paramIndex++} OFFSET $${paramIndex++}
      `;

      queryParams.push(limit as string, offset as string);

      const result = await centralizedDb.query(query, queryParams, { tenantId });
      users = result.rows || [];

      // Parse metadata for each user
      users.forEach(user => {
        if (typeof user.metadata === 'string') {
          try {
            user.metadata = JSON.parse(user.metadata);
          } catch (e) {
            console.log(`⚠️ Could not parse metadata for user ${user.id}`);
          }
        }
      });

      console.log(
        `👥 [ADMIN-USERS] Retrieved ${users.length}/${count} users using centralized database`
      );
    } catch (dbError) {
      console.error('❌ [ADMIN-USERS] Database error:', dbError);
      users = [];
      count = 0;
    }

    // Return response in Medusa format
    const response = {
      users,
      count: count,
      offset: parseInt(offset as string),
      limit: parseInt(limit as string),
      total: count,
      // Add tenant info for debugging
      _tenant: {
        id: tenantId,
        filtered: true,
        method: 'centralized_database',
        total_in_db: count,
      },
    };

    // Add tenant headers for debugging
    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Tenant-Filtered', 'true');
    res.setHeader('X-Users-Count', (users?.length || 0).toString());
    res.setHeader('X-Users-Total', (count || 0).toString());
    res.setHeader('X-Service-Method', 'centralized-database');

    console.log(`📤 [ADMIN-USERS] Returning response:`, {
      users_count: users.length,
      total_count: count,
      tenant_id: tenantId,
    });

    res.json(response);
  } catch (error: any) {
    console.error('❌ [ADMIN-USERS] Error getting users:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    res.status(500).json({
      error: 'Failed to get users',
      message: error.message,
      tenant_id: tenantId,
      service_method: 'centralized_database',
      _debug: {
        error_type: 'database_error',
        timestamp: new Date().toISOString(),
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      },
    });
  }
}
