import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { centralizedDb } from '../../../../services/centralized-database';

/**
 * Admin Single Order Endpoint - Admin-facing API with tenant filtering
 *
 * This endpoint provides tenant-filtered individual order details for admin users.
 * It ensures proper multi-tenant isolation and admin authentication.
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const orderId = req.params.id;

  console.log(`🚀 [ADMIN-ORDER-DETAIL] Getting order ${orderId} for admin user`);
  console.log(`🚀 [ADMIN-ORDER-DETAIL] Headers:`, {
    authorization: req.headers.authorization ? 'Bearer [REDACTED]' : 'None',
    'x-tenant-id': req.headers['x-tenant-id'],
  });

  try {
    // Extract tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';
    console.log(`🚀 [ADMIN-ORDER-DETAIL] Processing order request for tenant: ${tenantId}`);

    // Validate order ID
    if (!orderId) {
      console.log(`❌ [ADMIN-ORDER-DETAIL] Order ID is required`);
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Order ID is required',
        tenant_id: tenantId,
      });
    }

    // Get query parameters for field selection and expansion
    const { fields, expand } = req.query;
    console.log(`🚀 [ADMIN-ORDER-DETAIL] Query params:`, { fields, expand });

    // Get Medusa order module service
    const orderModuleService = req.scope.resolve('order');

    let order: any = null;

    try {
      console.log(`🔗 [ADMIN-ORDER-DETAIL] Using centralized database`);

      // Get order with complete information using centralized database
      const query = `
        SELECT
          o.id, o.status, o.created_at, o.updated_at, o.currency_code,
          o.customer_id, o.email, o.display_id, o.version,
          o.billing_address_id, o.shipping_address_id,
          o.region_id, o.sales_channel_id, o.metadata, o.tenant_id,
          o.is_draft_order, o.no_notification, o.canceled_at,

          -- Financial data from order_summary
          os.totals,

          -- Customer details
          c.first_name as customer_first_name,
          c.last_name as customer_last_name,
          c.email as customer_email,
          c.phone as customer_phone,

          -- Billing address details
          ba.id as billing_address_id,
          ba.first_name as billing_first_name,
          ba.last_name as billing_last_name,
          ba.address_1 as billing_address_1,
          ba.address_2 as billing_address_2,
          ba.city as billing_city,
          ba.postal_code as billing_postal_code,
          ba.province as billing_province,
          ba.country_code as billing_country_code,
          ba.phone as billing_phone,
          ba.company as billing_company,

          -- Shipping address details
          sa.id as shipping_address_id,
          sa.first_name as shipping_first_name,
          sa.last_name as shipping_last_name,
          sa.address_1 as shipping_address_1,
          sa.address_2 as shipping_address_2,
          sa.city as shipping_city,
          sa.postal_code as shipping_postal_code,
          sa.province as shipping_province,
          sa.country_code as shipping_country_code,
          sa.phone as shipping_phone,
          sa.company as shipping_company

        FROM "order" o
        LEFT JOIN customer c ON o.customer_id = c.id
        LEFT JOIN order_summary os ON o.id = os.order_id
        LEFT JOIN order_address ba ON o.billing_address_id = ba.id
        LEFT JOIN order_address sa ON o.shipping_address_id = sa.id
        WHERE o.id = $1 AND o.tenant_id = $2 AND o.deleted_at IS NULL
      `;

      const result = await centralizedDb.query(query, [orderId, tenantId], { tenantId });

      if (!result.rows || result.rows.length === 0) {
        console.log(`❌ [ADMIN-ORDER-DETAIL] Order ${orderId} not found for tenant ${tenantId}`);
        return res.status(404).json({
          error: 'Not Found',
          message: 'Order not found or access denied',
          order_id: orderId,
          tenant_id: tenantId,
          service_method: 'centralized_database',
        });
      }

      const orderRow = result.rows[0];
      console.log(`✅ [ADMIN-ORDER-DETAIL] Order ${orderId} found for tenant ${tenantId}`);

      // Get order items for this order
      const itemsQuery = `
        SELECT
          oli.id as line_item_id,
          oli.title,
          oli.subtitle,
          oli.thumbnail,
          oli.unit_price,
          oli.metadata as line_item_metadata,
          oli.variant_id,
          oli.product_id,
          oli.created_at as line_item_created_at,
          oli.updated_at as line_item_updated_at,
          oli.product_title,
          oli.product_description,
          oli.variant_title,
          oli.variant_sku,
          oli.variant_barcode,

          -- Order item quantities and totals
          oi.quantity,
          oi.fulfilled_quantity,
          oi.shipped_quantity,
          oi.return_received_quantity as returned_quantity,
          oi.delivered_quantity,

          -- Product information
          p.id as product_id,
          p.title as product_title,
          p.description as product_description,
          p.thumbnail as product_thumbnail,
          p.status as product_status,
          p.handle as product_handle,

          -- Variant information
          pv.id as variant_id,
          pv.title as variant_title,
          pv.sku as variant_sku,
          pv.barcode as variant_barcode,
          pv.manage_inventory as variant_manage_inventory,
          pv.allow_backorder as variant_allow_backorder

        FROM order_line_item oli
        LEFT JOIN order_item oi ON oli.id = oi.item_id AND oi.deleted_at IS NULL
        LEFT JOIN product p ON oli.product_id = p.id AND p.deleted_at IS NULL
        LEFT JOIN product_variant pv ON oli.variant_id = pv.id AND pv.deleted_at IS NULL
        WHERE oi.order_id = $1
        AND oli.deleted_at IS NULL
        ORDER BY oli.created_at ASC
      `;

      const itemsResult = await centralizedDb.query(itemsQuery, [orderId], { tenantId });
      const orderItems = itemsResult.rows || [];

      // Process the order data to ensure all required fields are present
      // Parse totals from order_summary
      let totals = {};
      if (orderRow.totals) {
        try {
          totals =
            typeof orderRow.totals === 'string' ? JSON.parse(orderRow.totals) : orderRow.totals;
        } catch (e) {
          console.log(`⚠️ Could not parse totals for order ${orderRow.id}`);
          totals = {};
        }
      }

      // Transform order items
      const items = orderItems.map(item => ({
        id: item.line_item_id,
        title: item.title,
        subtitle: item.subtitle,
        thumbnail: item.thumbnail,
        quantity: item.quantity,
        fulfilled_quantity: item.fulfilled_quantity,
        shipped_quantity: item.shipped_quantity,
        returned_quantity: item.returned_quantity,
        delivered_quantity: item.delivered_quantity,
        unit_price: item.unit_price,
        total: (item.unit_price || 0) * (item.quantity || 0),
        metadata: item.line_item_metadata,
        variant_id: item.variant_id,
        product_id: item.product_id,
        created_at: item.line_item_created_at,
        updated_at: item.line_item_updated_at,
        product: item.product_id
          ? {
              id: item.product_id,
              title: item.product_title,
              description: item.product_description,
              thumbnail: item.product_thumbnail,
              status: item.product_status,
              handle: item.product_handle,
            }
          : null,
        variant: item.variant_id
          ? {
              id: item.variant_id,
              title: item.variant_title,
              sku: item.variant_sku,
              barcode: item.variant_barcode,
              manage_inventory: item.variant_manage_inventory,
              allow_backorder: item.variant_allow_backorder,
            }
          : null,
      }));

      order = {
        id: orderRow.id,
        status: orderRow.status,
        created_at: orderRow.created_at,
        updated_at: orderRow.updated_at,
        currency_code: orderRow.currency_code,
        customer_id: orderRow.customer_id,
        email: orderRow.email,
        display_id: orderRow.display_id,
        version: orderRow.version,
        billing_address_id: orderRow.billing_address_id,
        shipping_address_id: orderRow.shipping_address_id,
        region_id: orderRow.region_id,
        sales_channel_id: orderRow.sales_channel_id,
        is_draft_order: orderRow.is_draft_order,
        no_notification: orderRow.no_notification,
        canceled_at: orderRow.canceled_at,
        metadata:
          typeof orderRow.metadata === 'string'
            ? JSON.parse(orderRow.metadata || '{}')
            : orderRow.metadata,
        tenant_id: orderRow.tenant_id,

        // Financial data from totals
        total: totals.current_order_total || 0,
        subtotal: totals.current_order_total || 0,
        tax_total: 0,
        shipping_total: 0,
        discount_total: 0,
        gift_card_total: 0,
        paid_total: totals.paid_total || 0,
        refunded_total: totals.refunded_total || 0,
        pending_difference: totals.pending_difference || 0,

        // Status fields
        payment_status: 'pending',
        fulfillment_status: 'not_fulfilled',

        // Customer object
        customer: orderRow.customer_id
          ? {
              id: orderRow.customer_id,
              first_name: orderRow.customer_first_name,
              last_name: orderRow.customer_last_name,
              email: orderRow.customer_email,
              phone: orderRow.customer_phone,
            }
          : null,

        // Complete billing address object
        billing_address: orderRow.billing_address_id
          ? {
              id: orderRow.billing_address_id,
              first_name: orderRow.billing_first_name,
              last_name: orderRow.billing_last_name,
              address_1: orderRow.billing_address_1,
              address_2: orderRow.billing_address_2,
              city: orderRow.billing_city,
              postal_code: orderRow.billing_postal_code,
              province: orderRow.billing_province,
              country_code: orderRow.billing_country_code,
              phone: orderRow.billing_phone,
              company: orderRow.billing_company,
            }
          : null,

        // Complete shipping address object
        shipping_address: orderRow.shipping_address_id
          ? {
              id: orderRow.shipping_address_id,
              first_name: orderRow.shipping_first_name,
              last_name: orderRow.shipping_last_name,
              address_1: orderRow.shipping_address_1,
              address_2: orderRow.shipping_address_2,
              city: orderRow.shipping_city,
              postal_code: orderRow.shipping_postal_code,
              province: orderRow.shipping_province,
              country_code: orderRow.shipping_country_code,
              phone: orderRow.shipping_phone,
              company: orderRow.shipping_company,
            }
          : null,

        // Order items with complete product and variant details
        items: items,

        // Summary counts
        item_count: items.length,
        total_quantity: items.reduce((sum, item) => sum + (item.quantity || 0), 0),
        fulfilled_quantity: items.reduce((sum, item) => sum + (item.fulfilled_quantity || 0), 0),
        shipped_quantity: items.reduce((sum, item) => sum + (item.shipped_quantity || 0), 0),
        returned_quantity: items.reduce((sum, item) => sum + (item.returned_quantity || 0), 0),

        // Raw totals for debugging
        _totals: totals,
      };

      console.log(
        `✅ [ADMIN-ORDER-DETAIL] Retrieved order details for ${orderId} with complete data`
      );
      console.log(`📦 [ADMIN-ORDER-DETAIL] Found ${items.length} items for order ${orderId}`);
    } catch (serviceError) {
      console.error('❌ [ADMIN-ORDER-DETAIL] Service error:', serviceError);
      throw serviceError;
    }

    // Return response in Medusa v2 format
    const response = {
      order,
      _tenant: {
        id: tenantId,
        filtered: true,
        method: 'centralized_database',
      },
    };

    // Add tenant headers for debugging
    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Tenant-Filtered', 'true');
    res.setHeader('X-Service-Method', 'medusa-order-service');

    console.log(`✅ [ADMIN-ORDER-DETAIL] Returning order ${orderId} for tenant ${tenantId}`);
    return res.status(200).json(response);
  } catch (error) {
    console.error('❌ [ADMIN-ORDER-DETAIL] Error fetching order:', error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch order details',
    });
  }
}
