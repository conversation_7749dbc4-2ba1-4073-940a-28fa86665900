import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { authenticate } from '@medusajs/framework';
import { centralizedDb } from '../../../services/centralized-database';

/**
 * Store Orders Endpoint - Customer-facing API with tenant filtering and authentication
 *
 * This endpoint provides tenant-filtered orders for authenticated customers.
 * It ensures proper multi-tenant isolation and customer authentication.
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  console.log(`📦 [STORE-ORDERS] === TENANT-AWARE STORE ORDERS ENDPOINT CALLED ===`);
  console.log(`📦 [STORE-ORDERS] Headers:`, {
    authorization: req.headers.authorization ? 'Bearer [REDACTED]' : 'None',
    'x-tenant-id': req.headers['x-tenant-id'],
    'x-publishable-api-key': req.headers['x-publishable-api-key'],
  });

  try {
    // Extract tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';
    console.log(`📦 [STORE-ORDERS] Processing orders request for tenant: ${tenantId}`);

    // Check if user is authenticated
    if (!req.auth_context || !req.auth_context.actor_id) {
      console.log(`❌ [STORE-ORDERS] No authentication context found`);
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required to access orders',
      });
    }

    const customerId = req.auth_context.actor_id;
    console.log(`📦 [STORE-ORDERS] Authenticated customer ID: ${customerId}`);

    // Get query parameters
    const { limit = 50, offset = 0, fields, order = '-created_at', ...filters } = req.query;

    console.log(`📦 [STORE-ORDERS] Query params:`, { limit, offset, order, filters });

    console.log(`🔗 [STORE-ORDERS] Using centralized database for tenant: ${tenantId}`);

    // Build filters for centralized database query
    const orderFilters: any = {
      customer_id: customerId, // Filter by authenticated customer
      tenant_id: tenantId, // Filter by tenant
    };

    // Add any additional filters from query params
    Object.keys(filters).forEach(key => {
      if (filters[key]) {
        orderFilters[key] = filters[key];
      }
    });

    // Define what data to expand/include
    const expandFields = [
      'items',
      'items.product',
      'items.variant',
      'customer',
      'billing_address',
      'shipping_address',
      'summary',
      'payments',
      'fulfillments',
    ];

    console.log(`📦 [STORE-ORDERS] Filters:`, orderFilters);
    console.log(`📦 [STORE-ORDERS] Expanding fields:`, expandFields);

    let orders: any[] = [];
    let count = 0;

    try {
      // Build WHERE clause for filters
      let whereClause = 'WHERE o.customer_id = $1 AND o.tenant_id = $2 AND o.deleted_at IS NULL';
      let queryParams = [customerId, tenantId];
      let paramIndex = 3;

      // Add any additional filters from query params
      Object.keys(filters).forEach(key => {
        if (filters[key]) {
          whereClause += ` AND o.${key} = $${paramIndex++}`;
          queryParams.push(filters[key]);
        }
      });

      // Get total count for this customer and tenant
      const countResult = await centralizedDb.query(
        `SELECT COUNT(*) as total FROM "order" o ${whereClause}`,
        queryParams,
        { tenantId }
      );
      count = parseInt(countResult.rows[0]?.total || 0);

      // Get orders with complete information including financial data, addresses, and items
      const query = `
        SELECT
          o.id, o.status, o.created_at, o.updated_at, o.currency_code,
          o.customer_id, o.email, o.display_id, o.version,
          o.billing_address_id, o.shipping_address_id,
          o.region_id, o.sales_channel_id, o.metadata, o.tenant_id,
          o.is_draft_order, o.no_notification, o.canceled_at,

          -- Financial data from order_summary
          os.totals,

          -- Customer details
          c.first_name as customer_first_name,
          c.last_name as customer_last_name,
          c.email as customer_email,
          c.phone as customer_phone,

          -- Billing address details
          ba.id as billing_address_id,
          ba.first_name as billing_first_name,
          ba.last_name as billing_last_name,
          ba.address_1 as billing_address_1,
          ba.address_2 as billing_address_2,
          ba.city as billing_city,
          ba.postal_code as billing_postal_code,
          ba.province as billing_province,
          ba.country_code as billing_country_code,
          ba.phone as billing_phone,
          ba.company as billing_company,

          -- Shipping address details
          sa.id as shipping_address_id,
          sa.first_name as shipping_first_name,
          sa.last_name as shipping_last_name,
          sa.address_1 as shipping_address_1,
          sa.address_2 as shipping_address_2,
          sa.city as shipping_city,
          sa.postal_code as shipping_postal_code,
          sa.province as shipping_province,
          sa.country_code as shipping_country_code,
          sa.phone as shipping_phone,
          sa.company as shipping_company

        FROM "order" o
        LEFT JOIN customer c ON o.customer_id = c.id
        LEFT JOIN order_summary os ON o.id = os.order_id
        LEFT JOIN order_address ba ON o.billing_address_id = ba.id
        LEFT JOIN order_address sa ON o.shipping_address_id = sa.id
        ${whereClause}
        ORDER BY o.created_at DESC
        LIMIT $${paramIndex++} OFFSET $${paramIndex++}
      `;

      queryParams.push(limit as string, offset as string);

      const result = await centralizedDb.query(query, queryParams, { tenantId });
      const rawOrders = result.rows || [];

      // Get order items for all orders in a single query
      let orderItems = [];
      if (rawOrders.length > 0) {
        const orderIds = rawOrders.map(order => order.id);
        const itemsQuery = `
          SELECT
            oli.id as line_item_id,
            oli.title,
            oli.subtitle,
            oli.thumbnail,
            oli.unit_price,
            oli.metadata as line_item_metadata,
            oli.variant_id,
            oli.product_id,
            oli.created_at as line_item_created_at,
            oli.updated_at as line_item_updated_at,
            oli.product_title,
            oli.product_description,
            oli.variant_title,
            oli.variant_sku,
            oli.variant_barcode,

            -- Order item quantities and totals
            oi.order_id,
            oi.quantity,
            oi.fulfilled_quantity,
            oi.shipped_quantity,
            oi.return_received_quantity as returned_quantity,
            oi.delivered_quantity,

            -- Product information
            p.id as product_id,
            p.title as product_title,
            p.description as product_description,
            p.thumbnail as product_thumbnail,
            p.status as product_status,
            p.handle as product_handle,

            -- Variant information
            pv.id as variant_id,
            pv.title as variant_title,
            pv.sku as variant_sku,
            pv.barcode as variant_barcode,
            pv.manage_inventory as variant_manage_inventory,
            pv.allow_backorder as variant_allow_backorder

          FROM order_line_item oli
          LEFT JOIN order_item oi ON oli.id = oi.item_id AND oi.deleted_at IS NULL
          LEFT JOIN product p ON oli.product_id = p.id AND p.deleted_at IS NULL
          LEFT JOIN product_variant pv ON oli.variant_id = pv.id AND pv.deleted_at IS NULL
          WHERE oi.order_id = ANY($1)
          AND oli.deleted_at IS NULL
          ORDER BY oli.created_at ASC
        `;

        const itemsResult = await centralizedDb.query(itemsQuery, [orderIds], { tenantId });
        orderItems = itemsResult.rows || [];
      }

      // Transform the raw data into proper order objects
      orders = rawOrders.map(row => {
        // Parse totals from order_summary
        let totals = {};
        if (row.totals) {
          try {
            totals = typeof row.totals === 'string' ? JSON.parse(row.totals) : row.totals;
          } catch (e) {
            console.log(`⚠️ Could not parse totals for order ${row.id}`);
            totals = {};
          }
        }

        // Get items for this order
        const items = orderItems
          .filter(item => item.order_id === row.id)
          .map(item => ({
            id: item.line_item_id,
            title: item.title,
            subtitle: item.subtitle,
            thumbnail: item.thumbnail,
            quantity: item.quantity,
            fulfilled_quantity: item.fulfilled_quantity,
            shipped_quantity: item.shipped_quantity,
            returned_quantity: item.returned_quantity,
            delivered_quantity: item.delivered_quantity,
            unit_price: item.unit_price,
            total: (item.unit_price || 0) * (item.quantity || 0),
            metadata: item.line_item_metadata,
            variant_id: item.variant_id,
            product_id: item.product_id,
            created_at: item.line_item_created_at,
            updated_at: item.line_item_updated_at,
            product: item.product_id
              ? {
                  id: item.product_id,
                  title: item.product_title,
                  description: item.product_description,
                  thumbnail: item.product_thumbnail,
                  status: item.product_status,
                  handle: item.product_handle,
                }
              : null,
            variant: item.variant_id
              ? {
                  id: item.variant_id,
                  title: item.variant_title,
                  sku: item.variant_sku,
                  barcode: item.variant_barcode,
                  manage_inventory: item.variant_manage_inventory,
                  allow_backorder: item.variant_allow_backorder,
                }
              : null,
          }));

        return {
          id: row.id,
          status: row.status,
          created_at: row.created_at,
          updated_at: row.updated_at,
          currency_code: row.currency_code,
          customer_id: row.customer_id,
          email: row.email,
          display_id: row.display_id,
          version: row.version,
          billing_address_id: row.billing_address_id,
          shipping_address_id: row.shipping_address_id,
          region_id: row.region_id,
          sales_channel_id: row.sales_channel_id,
          is_draft_order: row.is_draft_order,
          no_notification: row.no_notification,
          canceled_at: row.canceled_at,
          metadata:
            typeof row.metadata === 'string' ? JSON.parse(row.metadata || '{}') : row.metadata,
          tenant_id: row.tenant_id,

          // Financial data from totals
          total: totals.current_order_total || 0,
          subtotal: totals.current_order_total || 0,
          tax_total: 0,
          shipping_total: 0,
          discount_total: 0,
          gift_card_total: 0,
          paid_total: totals.paid_total || 0,
          refunded_total: totals.refunded_total || 0,
          pending_difference: totals.pending_difference || 0,

          // Status fields
          payment_status: 'pending',
          fulfillment_status: 'not_fulfilled',

          // Customer object
          customer: row.customer_id
            ? {
                id: row.customer_id,
                first_name: row.customer_first_name,
                last_name: row.customer_last_name,
                email: row.customer_email,
                phone: row.customer_phone,
              }
            : null,

          // Complete billing address object
          billing_address: row.billing_address_id
            ? {
                id: row.billing_address_id,
                first_name: row.billing_first_name,
                last_name: row.billing_last_name,
                address_1: row.billing_address_1,
                address_2: row.billing_address_2,
                city: row.billing_city,
                postal_code: row.billing_postal_code,
                province: row.billing_province,
                country_code: row.billing_country_code,
                phone: row.billing_phone,
                company: row.billing_company,
              }
            : null,

          // Complete shipping address object
          shipping_address: row.shipping_address_id
            ? {
                id: row.shipping_address_id,
                first_name: row.shipping_first_name,
                last_name: row.shipping_last_name,
                address_1: row.shipping_address_1,
                address_2: row.shipping_address_2,
                city: row.shipping_city,
                postal_code: row.shipping_postal_code,
                province: row.shipping_province,
                country_code: row.shipping_country_code,
                phone: row.shipping_phone,
                company: row.shipping_company,
              }
            : null,

          // Order items with complete product and variant details
          items: items,

          // Summary counts
          item_count: items.length,
          total_quantity: items.reduce((sum, item) => sum + (item.quantity || 0), 0),
          fulfilled_quantity: items.reduce((sum, item) => sum + (item.fulfilled_quantity || 0), 0),
          shipped_quantity: items.reduce((sum, item) => sum + (item.shipped_quantity || 0), 0),
          returned_quantity: items.reduce((sum, item) => sum + (item.returned_quantity || 0), 0),

          // Raw totals for debugging
          _totals: totals,
        };
      });

      console.log(
        `📊 [STORE-ORDERS] Total orders for customer ${customerId} in tenant ${tenantId}: ${count}`
      );
      console.log(
        `✅ [STORE-ORDERS] Found ${orders.length} orders for customer using Medusa order service`
      );

      if (count === 0) {
        console.log(`📦 [STORE-ORDERS] No orders found for customer`);
        return res.status(200).json({
          orders: [],
          count: 0,
          offset: parseInt(offset as string),
          limit: parseInt(limit as string),
          _tenant: {
            id: tenantId,
            filtered: true,
            method: 'centralized_database',
          },
        });
      }

      // Orders already include items and relations from the service
      console.log(`📦 [STORE-ORDERS] Orders with relations loaded via Medusa order service`);
    } catch (serviceError) {
      console.error('❌ [STORE-ORDERS] Medusa service error:', serviceError);
      orders = [];
      count = 0;
    }

    // Return response in Medusa v2 format
    const response = {
      orders,
      count,
      offset: parseInt(offset as string),
      limit: parseInt(limit as string),
      _tenant: {
        id: tenantId,
        filtered: true,
        method: 'medusa_order_service',
        customer_id: customerId,
      },
    };

    console.log(`✅ [STORE-ORDERS] Returning ${orders.length} orders for customer ${customerId}`);
    return res.status(200).json(response);
  } catch (error: any) {
    console.error('❌ [STORE-ORDERS] Error fetching orders:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch orders',
      tenant_id: tenantId,
      service_method: 'tenant_service_factory',
      _debug: {
        error_type: 'tenant_service_error',
        timestamp: new Date().toISOString(),
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      },
    });
  }
}
