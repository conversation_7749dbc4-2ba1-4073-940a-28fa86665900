import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import request from 'supertest';
import { DataSource } from 'typeorm';
import { initializeApp } from '../../../../app';
import { clearDatabase, seedTestData } from '../../../../test-utils/test-helpers';

describe('Search API', () => {
  let app: any;
  let dataSource: DataSource;
  let testProducts: any[] = [];

  beforeAll(async () => {
    const { app: testApp, dataSource: testDataSource } = await initializeApp();
    app = testApp;
    dataSource = testDataSource;
  });

  afterAll(async () => {
    if (dataSource) {
      await dataSource.destroy();
    }
  });

  beforeEach(async () => {
    await clearDatabase(dataSource);
    testProducts = await seedTestData(dataSource);
  });

  describe('GET /store/search', () => {
    it('should search products successfully with valid query', async () => {
      const response = await request(app)
        .get('/store/search')
        .query({
          q: 'test',
          limit: 10,
          offset: 0,
        })
        .set('x-tenant-id', 'tenant-electronics-001')
        .set('x-publishable-api-key', process.env.MEDUSA_PUBLISHABLE_KEY || 'pk_test');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('hits');
      expect(response.body).toHaveProperty('query', 'test');
      expect(response.body).toHaveProperty('tenant_id', 'tenant-electronics-001');
      expect(response.body).toHaveProperty('estimatedTotalHits');
      expect(response.body).toHaveProperty('processingTimeMs');
      expect(Array.isArray(response.body.hits)).toBe(true);
    });

    it('should filter products by tenant', async () => {
      // Test with tenant-electronics-001
      const electronicsResponse = await request(app)
        .get('/store/search')
        .query({ q: 'product', limit: 20 })
        .set('x-tenant-id', 'tenant-electronics-001')
        .set('x-publishable-api-key', process.env.MEDUSA_PUBLISHABLE_KEY || 'pk_test');

      // Test with tenant-fashion-002
      const fashionResponse = await request(app)
        .get('/store/search')
        .query({ q: 'product', limit: 20 })
        .set('x-tenant-id', 'tenant-fashion-002')
        .set('x-publishable-api-key', process.env.MEDUSA_PUBLISHABLE_KEY || 'pk_test');

      expect(electronicsResponse.status).toBe(200);
      expect(fashionResponse.status).toBe(200);

      // Results should be different for different tenants
      expect(electronicsResponse.body.tenant_id).toBe('tenant-electronics-001');
      expect(fashionResponse.body.tenant_id).toBe('tenant-fashion-002');
    });

    it('should support pagination', async () => {
      const page1Response = await request(app)
        .get('/store/search')
        .query({
          q: 'product',
          limit: 5,
          offset: 0,
        })
        .set('x-tenant-id', 'tenant-electronics-001')
        .set('x-publishable-api-key', process.env.MEDUSA_PUBLISHABLE_KEY || 'pk_test');

      const page2Response = await request(app)
        .get('/store/search')
        .query({
          q: 'product',
          limit: 5,
          offset: 5,
        })
        .set('x-tenant-id', 'tenant-electronics-001')
        .set('x-publishable-api-key', process.env.MEDUSA_PUBLISHABLE_KEY || 'pk_test');

      expect(page1Response.status).toBe(200);
      expect(page2Response.status).toBe(200);
      expect(page1Response.body.limit).toBe(5);
      expect(page1Response.body.offset).toBe(0);
      expect(page2Response.body.limit).toBe(5);
      expect(page2Response.body.offset).toBe(5);
    });

    it('should support sorting', async () => {
      const newestFirstResponse = await request(app)
        .get('/store/search')
        .query({
          q: 'product',
          sort: 'created_at:desc',
        })
        .set('x-tenant-id', 'tenant-electronics-001')
        .set('x-publishable-api-key', process.env.MEDUSA_PUBLISHABLE_KEY || 'pk_test');

      const oldestFirstResponse = await request(app)
        .get('/store/search')
        .query({
          q: 'product',
          sort: 'created_at:asc',
        })
        .set('x-tenant-id', 'tenant-electronics-001')
        .set('x-publishable-api-key', process.env.MEDUSA_PUBLISHABLE_KEY || 'pk_test');

      expect(newestFirstResponse.status).toBe(200);
      expect(oldestFirstResponse.status).toBe(200);
    });

    it('should filter by category', async () => {
      const response = await request(app)
        .get('/store/search')
        .query({
          q: 'product',
          category_id: 'test-category-id',
        })
        .set('x-tenant-id', 'tenant-electronics-001')
        .set('x-publishable-api-key', process.env.MEDUSA_PUBLISHABLE_KEY || 'pk_test');

      expect(response.status).toBe(200);
      expect(response.body.filters_applied).toHaveProperty('category_id', 'test-category-id');
    });

    it('should filter by collection', async () => {
      const response = await request(app)
        .get('/store/search')
        .query({
          q: 'product',
          collection_id: 'test-collection-id',
        })
        .set('x-tenant-id', 'tenant-electronics-001')
        .set('x-publishable-api-key', process.env.MEDUSA_PUBLISHABLE_KEY || 'pk_test');

      expect(response.status).toBe(200);
      expect(response.body.filters_applied).toHaveProperty('collection_id', 'test-collection-id');
    });

    it('should return 400 for missing tenant header', async () => {
      const response = await request(app)
        .get('/store/search')
        .query({ q: 'test' })
        .set('x-publishable-api-key', process.env.MEDUSA_PUBLISHABLE_KEY || 'pk_test');

      expect(response.status).toBe(400);
    });

    it('should return 400 for missing publishable key', async () => {
      const response = await request(app)
        .get('/store/search')
        .query({ q: 'test' })
        .set('x-tenant-id', 'tenant-electronics-001');

      expect(response.status).toBe(400);
    });
  });

  describe('POST /store/search/suggestions', () => {
    it('should return search suggestions', async () => {
      const response = await request(app)
        .post('/store/search/suggestions')
        .send({
          query: 'test',
          limit: 5,
        })
        .set('x-tenant-id', 'tenant-electronics-001')
        .set('x-publishable-api-key', process.env.MEDUSA_PUBLISHABLE_KEY || 'pk_test');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('suggestions');
      expect(response.body).toHaveProperty('query', 'test');
      expect(response.body).toHaveProperty('tenant_id', 'tenant-electronics-001');
      expect(Array.isArray(response.body.suggestions)).toBe(true);
    });

    it('should return 400 for short query', async () => {
      const response = await request(app)
        .post('/store/search/suggestions')
        .send({
          query: 'a',
          limit: 5,
        })
        .set('x-tenant-id', 'tenant-electronics-001')
        .set('x-publishable-api-key', process.env.MEDUSA_PUBLISHABLE_KEY || 'pk_test');

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Query too short');
    });

    it('should limit suggestions correctly', async () => {
      const response = await request(app)
        .post('/store/search/suggestions')
        .send({
          query: 'product',
          limit: 3,
        })
        .set('x-tenant-id', 'tenant-electronics-001')
        .set('x-publishable-api-key', process.env.MEDUSA_PUBLISHABLE_KEY || 'pk_test');

      expect(response.status).toBe(200);
      expect(response.body.suggestions.length).toBeLessThanOrEqual(3);
    });

    it('should filter suggestions by tenant', async () => {
      const electronicsResponse = await request(app)
        .post('/store/search/suggestions')
        .send({
          query: 'product',
          limit: 5,
        })
        .set('x-tenant-id', 'tenant-electronics-001')
        .set('x-publishable-api-key', process.env.MEDUSA_PUBLISHABLE_KEY || 'pk_test');

      const fashionResponse = await request(app)
        .post('/store/search/suggestions')
        .send({
          query: 'product',
          limit: 5,
        })
        .set('x-tenant-id', 'tenant-fashion-002')
        .set('x-publishable-api-key', process.env.MEDUSA_PUBLISHABLE_KEY || 'pk_test');

      expect(electronicsResponse.status).toBe(200);
      expect(fashionResponse.status).toBe(200);
      expect(electronicsResponse.body.tenant_id).toBe('tenant-electronics-001');
      expect(fashionResponse.body.tenant_id).toBe('tenant-fashion-002');
    });
  });
});
