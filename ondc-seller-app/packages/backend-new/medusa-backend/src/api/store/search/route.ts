import type { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { MeiliSearch } from 'meilisearch';
import { TenantAwareProductService } from '../../../services/tenant-aware-product';

/**
 * GET /store/search
 *
 * Search products using MeiliSearch with multi-tenant support
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = (req as any).tenant_id || 'default';
    console.log(`🔍 [SEARCH] Search request for tenant: ${tenantId}`);

    // Get query parameters
    const {
      q = '',
      limit = 20,
      offset = 0,
      category_id,
      collection_id,
      status = 'published',
      sort = 'created_at:desc',
    } = req.query;

    // If no search query, use Medusa service to get products directly
    const queryString = typeof q === 'string' ? q : '';
    if (!queryString || queryString.trim() === '') {
      console.log(`📦 [SEARCH] No search query, using Medusa service for tenant: ${tenantId}`);
      return await getProductsFromMedusaService(req, res, {
        category_id,
        collection_id,
        status,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        sort: sort as string,
      });
    }

    // Initialize MeiliSearch client (use master key for search operations)
    const client = new MeiliSearch({
      host: process.env.MEILISEARCH_HOST_URL || 'https://apigw.cubeone.in/api/search',
      apiKey: process.env.MEILISEARCH_MASTER_API_KEY || 'ohYQooC62EJ7VXAX96CAifTXL',
    });

    // Get the index
    const indexName = process.env.MEILISEARCH_INDEX_PREFIX || 'medusa-products';
    const index = client.index(indexName);

    // Build search filters for multi-tenant isolation
    const filters: string[] = [];

    // Always filter by tenant
    filters.push(`tenant_id = "${tenantId}"`);

    // Add status filter
    if (status) {
      filters.push(`status = "${status}"`);
    }

    // Add category filter
    if (category_id) {
      filters.push(`category_id = "${category_id}"`);
    }

    // Add collection filter
    if (collection_id) {
      filters.push(`collection_id = "${collection_id}"`);
    }

    // Build search options
    const searchOptions: any = {
      limit: parseInt(limit as string),
      offset: parseInt(offset as string),
      filter: filters.join(' AND '),
      attributesToRetrieve: [
        'id',
        'title',
        'description',
        'handle',
        'status',
        'thumbnail',
        'tags',
        'collection_id',
        'category_id',
        'created_at',
        'updated_at',
        'variants',
        'metadata',
      ],
      attributesToHighlight: ['title', 'description'],
    };

    // Add sorting
    if (sort && typeof sort === 'string') {
      const [field, direction] = sort.split(':');
      searchOptions.sort = [`${field}:${direction || 'asc'}`];
    }

    console.log(`🔍 [SEARCH] Query: "${q}", Filters: ${filters.join(' AND ')}`);

    // Perform search
    const searchResults = await index.search(q as string, searchOptions);

    // Check if results have proper structure (metadata, variants, images, and categories)
    const needsResync =
      searchResults.hits.length === 0 ||
      (searchResults.hits.length > 0 &&
        (!searchResults.hits[0].metadata ||
          !searchResults.hits[0].variants ||
          !searchResults.hits[0].images ||
          !searchResults.hits[0].categories));

    // If no results found in MeiliSearch or results lack proper structure, sync products
    if (needsResync) {
      const reason =
        searchResults.hits.length === 0
          ? 'no results found'
          : `incomplete data structure (missing: ${[
              !searchResults.hits[0].metadata && 'metadata',
              !searchResults.hits[0].variants && 'variants',
              !searchResults.hits[0].images && 'images',
              !searchResults.hits[0].categories && 'categories',
            ]
              .filter(Boolean)
              .join(', ')})`;
      console.log(`🔄 [SEARCH] Syncing products for tenant ${tenantId} (reason: ${reason})`);

      try {
        await syncProductsToMeiliSearch(req, index, tenantId);

        // Retry search after sync
        const retryResults = await index.search(q as string, searchOptions);

        if (retryResults.hits.length > 0) {
          console.log(
            `✅ [SEARCH] Found ${retryResults.hits.length} results after sync for tenant: ${tenantId}`
          );

          const syncedResponse = {
            hits: retryResults.hits,
            query: q,
            processingTimeMs: retryResults.processingTimeMs,
            limit: retryResults.limit,
            offset: retryResults.offset,
            estimatedTotalHits: retryResults.estimatedTotalHits,
            facetDistribution: retryResults.facetDistribution,
            tenant_id: tenantId,
            synced: true,
            filters_applied: {
              tenant_id: tenantId,
              status,
              category_id,
              collection_id,
            },
          };

          res.status(200).json(syncedResponse);
          return;
        }
      } catch (syncError) {
        console.warn(`⚠️ [SEARCH] Failed to sync products: ${syncError}`);
      }
    }

    // Format response
    const response = {
      hits: searchResults.hits,
      query: q,
      processingTimeMs: searchResults.processingTimeMs,
      limit: searchResults.limit,
      offset: searchResults.offset,
      estimatedTotalHits: searchResults.estimatedTotalHits,
      facetDistribution: searchResults.facetDistribution,
      tenant_id: tenantId,
      filters_applied: {
        tenant_id: tenantId,
        status,
        category_id,
        collection_id,
      },
    };

    console.log(`✅ [SEARCH] Found ${searchResults.hits.length} results for tenant: ${tenantId}`);

    res.status(200).json(response);
  } catch (error) {
    console.error('❌ [SEARCH] Error:', error);

    // Handle specific MeiliSearch errors
    if ((error as any).name === 'MeiliSearchApiError') {
      res.status(400).json({
        error: 'Search service error',
        message: (error as any).message,
        code: (error as any).code,
      });
      return;
    }

    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to perform search',
    });
  }
}

/**
 * Get products directly from Medusa service (for non-search queries)
 */
async function getProductsFromMedusaService(req: MedusaRequest, res: MedusaResponse, filters: any) {
  try {
    const tenantId = (req as any).tenant_id || 'default';
    console.log(`📦 [SEARCH-MEDUSA] Getting products from Medusa service for tenant: ${tenantId}`);

    // Create tenant-aware product service
    const productService = TenantAwareProductService.fromRequest(req);

    // Build filters for Medusa service
    const medusaFilters: any = {};

    if (filters.category_id) {
      medusaFilters.category_id = filters.category_id;
    }

    if (filters.collection_id) {
      medusaFilters.collection_id = filters.collection_id;
    }

    if (filters.status) {
      medusaFilters.status = filters.status;
    }

    // Build config for pagination and sorting with relations
    const config: any = {
      take: filters.limit || 20,
      skip: filters.offset || 0,
      relations: ['variants', 'variants.prices', 'tags', 'collection', 'categories'],
    };

    // Add sorting if specified
    if (filters.sort && typeof filters.sort === 'string') {
      const [field, direction] = filters.sort.split(':');
      config.order = { [field]: direction?.toUpperCase() === 'DESC' ? 'DESC' : 'ASC' };
    }

    // Get products using tenant-aware service
    const products = await productService.listProducts(medusaFilters, config);

    // Transform products for search API response format with enhanced data
    const transformedProducts = Array.isArray(products) ? products : products?.products || [];

    const searchResults = transformedProducts.map((product: any) => ({
      id: product.id,
      title: product.title,
      description: product.description,
      handle: product.handle,
      status: product.status,
      thumbnail: product.thumbnail,
      collection_id: product.collection_id,
      category_id: product.category_id,
      tenant_id: product.tenant_id,
      created_at: product.created_at,
      updated_at: product.updated_at,
      // Enhanced variants data with complete structure
      variants: (product.variants || []).map((variant: any) => ({
        id: variant.id,
        title: variant.title,
        sku: variant.sku,
        barcode: variant.barcode,
        ean: variant.ean,
        upc: variant.upc,
        inventory_quantity: variant.inventory_quantity,
        allow_backorder: variant.allow_backorder,
        manage_inventory: variant.manage_inventory,
        weight: variant.weight,
        length: variant.length,
        height: variant.height,
        width: variant.width,
        origin_country: variant.origin_country,
        mid_code: variant.mid_code,
        material: variant.material,
        metadata: variant.metadata || {},
        prices: variant.prices || [],
        options: variant.options || [],
        created_at: variant.created_at,
        updated_at: variant.updated_at,
      })),
      tags: (product.tags || []).map((tag: any) => ({
        id: tag.id,
        value: tag.value,
        metadata: tag.metadata || {},
      })),
      // Enhanced metadata inclusion
      metadata: product.metadata || {},
      // Additional searchable fields
      weight: product.weight,
      length: product.length,
      height: product.height,
      width: product.width,
      origin_country: product.origin_country,
      mid_code: product.mid_code,
      material: product.material,
      // Collection and category data for better filtering
      collection: product.collection
        ? {
            id: product.collection.id,
            title: product.collection.title,
            handle: product.collection.handle,
            metadata: product.collection.metadata || {},
          }
        : null,
      categories: (product.categories || []).map((category: any) => ({
        id: category.id,
        name: category.name,
        handle: category.handle,
        metadata: category.metadata || {},
      })),
    }));

    console.log(
      `✅ [SEARCH-MEDUSA] Found ${searchResults.length} products for tenant: ${tenantId}`
    );

    res.status(200).json({
      hits: searchResults,
      query: '',
      limit: filters.limit || 20,
      offset: filters.offset || 0,
      estimatedTotalHits: searchResults.length,
      processingTimeMs: 0,
      tenant_id: tenantId,
      source: 'medusa-service',
    });
  } catch (error) {
    console.error('❌ [SEARCH-MEDUSA] Error getting products from Medusa service:', error);

    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get products from Medusa service',
    });
  }
}

/**
 * Sync products from Medusa service to MeiliSearch
 */
async function syncProductsToMeiliSearch(req: MedusaRequest, index: any, tenantId: string) {
  try {
    console.log(`🔄 [SEARCH-SYNC] Starting product sync for tenant: ${tenantId}`);

    // Create tenant-aware product service
    const productService = TenantAwareProductService.fromRequest(req);

    // Get all published products for the tenant with relations
    const products = await productService.listProducts(
      { status: 'published' },
      {
        take: 100, // Limit to avoid overwhelming the system
        relations: ['variants', 'variants.prices', 'tags', 'collection', 'categories', 'images'],
      }
    );

    const productList = Array.isArray(products) ? products : products?.products || [];

    if (productList.length === 0) {
      console.log(`⚠️ [SEARCH-SYNC] No products found to sync for tenant: ${tenantId}`);
      return;
    }

    // Debug: Check what data we're getting
    console.log(`🔍 [SEARCH-SYNC] Sample product data:`, {
      id: productList[0]?.id,
      title: productList[0]?.title,
      hasMetadata: !!productList[0]?.metadata,
      hasVariants: !!productList[0]?.variants,
      variantsLength: productList[0]?.variants?.length || 0,
      hasImages: !!productList[0]?.images,
      imagesLength: productList[0]?.images?.length || 0,
      hasCategories: !!productList[0]?.categories,
      categoriesLength: productList[0]?.categories?.length || 0,
    });

    // Transform products for MeiliSearch indexing with enhanced data
    const indexData = productList.map((product: any) => {
      const transformedProduct = {
        id: product.id,
        title: product.title,
        description: product.description,
        handle: product.handle,
        status: product.status,
        tenant_id: product.tenant_id || tenantId,
        collection_id: product.collection_id,
        category_id: product.category_id,
        thumbnail: product.thumbnail,
        created_at: product.created_at,
        updated_at: product.updated_at,
        // Enhanced variants data with complete structure
        variants: (product.variants || []).map((variant: any) => ({
          id: variant.id,
          title: variant.title,
          sku: variant.sku,
          barcode: variant.barcode,
          ean: variant.ean,
          upc: variant.upc,
          inventory_quantity: variant.inventory_quantity,
          allow_backorder: variant.allow_backorder,
          manage_inventory: variant.manage_inventory,
          weight: variant.weight,
          length: variant.length,
          height: variant.height,
          width: variant.width,
          origin_country: variant.origin_country,
          mid_code: variant.mid_code,
          material: variant.material,
          metadata: variant.metadata || {},
          prices: variant.prices || [],
          options: variant.options || [],
          created_at: variant.created_at,
          updated_at: variant.updated_at,
        })),
        tags: (product.tags || []).map((tag: any) => ({
          id: tag.id,
          value: tag.value,
          metadata: tag.metadata || {},
        })),
        // Enhanced metadata inclusion
        metadata: product.metadata || {},
        // Additional searchable fields
        weight: product.weight,
        length: product.length,
        height: product.height,
        width: product.width,
        origin_country: product.origin_country,
        mid_code: product.mid_code,
        material: product.material,
        // Collection and category data for better filtering
        collection: product.collection
          ? {
              id: product.collection.id,
              title: product.collection.title,
              handle: product.collection.handle,
              metadata: product.collection.metadata || {},
            }
          : null,
        categories: (product.categories || []).map((category: any) => ({
          id: category.id,
          name: category.name,
          handle: category.handle,
          metadata: category.metadata || {},
        })),
        images: (product.images || []).map((image: any) => ({
          id: image.id,
          url: image.url,
          metadata: image.metadata || {},
          created_at: image.created_at,
          updated_at: image.updated_at,
        })),
      };

      return transformedProduct;
    });

    // Configure MeiliSearch index settings to allow all fields
    try {
      await index.updateSettings({
        searchableAttributes: ['*'],
        displayedAttributes: [
          '*',
          'images',
          'categories',
          'variants',
          'tags',
          'collection',
          'metadata',
        ],
        filterableAttributes: ['tenant_id', 'status', 'collection_id', 'category_id'],
        sortableAttributes: ['created_at', 'updated_at', 'title'],
      });
      console.log(`⚙️ [SEARCH-SYNC] Updated MeiliSearch index settings for tenant: ${tenantId}`);
    } catch (settingsError) {
      console.log(`⚠️ [SEARCH-SYNC] Failed to update index settings:`, settingsError.message);
    }

    // Clear existing data for the tenant using document IDs
    const existingDocs = await index.search('', {
      filter: `tenant_id = "${tenantId}"`,
      limit: 1000,
      attributesToRetrieve: ['id'],
    });

    if (existingDocs.hits.length > 0) {
      const docIds = existingDocs.hits.map((doc: any) => doc.id);
      await index.deleteDocuments(docIds);
      console.log(
        `🗑️ [SEARCH-SYNC] Deleted ${docIds.length} existing documents for tenant: ${tenantId}`
      );
    }

    // Debug: Log the first document being indexed
    if (indexData.length > 0) {
      console.log(`🔍 [SEARCH-SYNC] Sample indexed document:`, {
        id: indexData[0].id,
        title: indexData[0].title,
        hasImages: !!indexData[0].images,
        imagesLength: indexData[0].images?.length || 0,
        hasCategories: !!indexData[0].categories,
        categoriesLength: indexData[0].categories?.length || 0,
        firstImage: indexData[0].images?.[0],
        firstCategory: indexData[0].categories?.[0],
      });
    }

    // Add new documents
    const task = await index.addDocuments(indexData);

    console.log(
      `✅ [SEARCH-SYNC] Synced ${indexData.length} products for tenant: ${tenantId}, task: ${task.taskUid}`
    );

    // Wait for the task to complete with proper error handling
    try {
      const client = index.client;
      await client.waitForTask(task.taskUid, { timeOutMs: 10000 });
      console.log(`✅ [SEARCH-SYNC] Task ${task.taskUid} completed successfully`);
    } catch (taskError) {
      console.log(`⚠️ [SEARCH-SYNC] Task ${task.taskUid} failed or timed out:`, taskError.message);
    }
  } catch (error) {
    console.error(`❌ [SEARCH-SYNC] Failed to sync products for tenant ${tenantId}:`, error);
    throw error;
  }
}
