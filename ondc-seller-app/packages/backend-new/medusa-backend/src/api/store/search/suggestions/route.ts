import type { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { MeiliSearch } from 'meilisearch';

/**
 * POST /store/search/suggestions
 *
 * Get search suggestions/autocomplete
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const tenantId = (req as any).tenant_id || 'default';
    const { query, limit = 5 } = req.body;

    console.log(
      `💡 [SEARCH-SUGGESTIONS] Getting suggestions for: "${query}" (tenant: ${tenantId})`
    );

    if (!query || query.length < 2) {
      return res.status(400).json({
        error: 'Query too short',
        message: 'Query must be at least 2 characters long',
      });
    }

    // Initialize MeiliSearch client (use master key for search operations)
    const client = new MeiliSearch({
      host: process.env.MEILISEARCH_HOST_URL || 'https://apigw.cubeone.in/api/search',
      apiKey: process.env.MEILISEARCH_MASTER_API_KEY || 'ohYQooC62EJ7VXAX96CAifTXL',
    });

    const indexName = process.env.MEILISEARCH_INDEX_PREFIX || 'medusa-products';
    const index = client.index(indexName);

    // Search for suggestions with enhanced attributes
    const searchResults = await index.search(query, {
      limit: parseInt(limit),
      filter: `tenant_id = "${tenantId}" AND status = "published"`,
      attributesToRetrieve: [
        'id',
        'title',
        'handle',
        'thumbnail',
        'metadata',
        'variants',
        'tags',
        'collection_id',
        'category_id',
        'description',
        'status',
        'created_at',
        'updated_at',
        'collection',
        'categories',
      ],
      attributesToHighlight: ['title', 'description'],
    });

    const suggestions = searchResults.hits.map(hit => ({
      id: hit.id,
      title: hit.title,
      handle: hit.handle,
      thumbnail: hit.thumbnail,
      highlighted: hit._formatted?.title || hit.title,
    }));

    console.log(
      `✅ [SEARCH-SUGGESTIONS] Found ${suggestions.length} suggestions for tenant: ${tenantId}`
    );

    res.status(200).json({
      suggestions,
      query,
      tenant_id: tenantId,
    });
  } catch (error) {
    console.error('❌ [SEARCH-SUGGESTIONS] Error:', error);

    // Handle specific MeiliSearch errors
    if ((error as any).name === 'MeiliSearchApiError') {
      res.status(400).json({
        error: 'Search service error',
        message: (error as any).message,
        code: (error as any).code,
      });
      return;
    }

    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get search suggestions',
    });
  }
}
