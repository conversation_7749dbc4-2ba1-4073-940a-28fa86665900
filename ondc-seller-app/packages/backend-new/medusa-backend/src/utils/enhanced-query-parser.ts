/**
 * Enhanced Query Parameter Parser for Medusa v2 Store API
 * 
 * Provides comprehensive query parameter parsing with support for:
 * - Advanced filtering with operators
 * - Sorting with multiple fields
 * - Pagination with validation
 * - Field selection and relation expansion
 * - Search functionality
 * - Date range filtering
 */

import { MedusaRequest } from '@medusajs/framework/http';

export interface ParsedQueryParams {
  // Pagination
  pagination: {
    limit: number;
    offset: number;
    page?: number;
  };
  
  // Sorting
  sorting: {
    field: string;
    direction: 'ASC' | 'DESC';
  }[];
  
  // Filtering
  filters: Record<string, any>;
  
  // Field selection
  fields?: string[];
  
  // Relation expansion
  expand?: string[];
  
  // Search
  search?: {
    query: string;
    fields: string[];
  };
  
  // Date filters
  dateFilters: {
    created_after?: Date;
    created_before?: Date;
    updated_after?: Date;
    updated_before?: Date;
  };
}

export interface QueryParserConfig {
  // Allowed fields for sorting
  sortableFields: string[];
  
  // Allowed fields for filtering
  filterableFields: string[];
  
  // Allowed fields for selection
  selectableFields: string[];
  
  // Allowed relations for expansion
  expandableRelations: string[];
  
  // Fields that support search
  searchableFields: string[];
  
  // Default sorting
  defaultSort: { field: string; direction: 'ASC' | 'DESC' };
  
  // Pagination limits
  maxLimit: number;
  defaultLimit: number;
}

/**
 * Parse query parameters with comprehensive validation and transformation
 */
export function parseQueryParameters(
  req: MedusaRequest,
  config: QueryParserConfig
): ParsedQueryParams {
  const query = req.query || {};
  
  return {
    pagination: parsePagination(query, config),
    sorting: parseSorting(query, config),
    filters: parseFilters(query, config),
    fields: parseFields(query, config),
    expand: parseExpand(query, config),
    search: parseSearch(query, config),
    dateFilters: parseDateFilters(query),
  };
}

/**
 * Parse pagination parameters
 */
function parsePagination(query: any, config: QueryParserConfig) {
  const limit = Math.min(
    Math.max(parseInt(query.limit) || config.defaultLimit, 1),
    config.maxLimit
  );
  
  const offset = Math.max(parseInt(query.offset) || 0, 0);
  const page = query.page ? Math.max(parseInt(query.page), 1) : undefined;
  
  return {
    limit,
    offset: page ? (page - 1) * limit : offset,
    page,
  };
}

/**
 * Parse sorting parameters
 * Supports: sort=name:asc,created_at:desc
 */
function parseSorting(query: any, config: QueryParserConfig) {
  const sortParam = query.sort || query.order;
  const sorting: { field: string; direction: 'ASC' | 'DESC' }[] = [];
  
  if (sortParam) {
    const sortFields = Array.isArray(sortParam) ? sortParam : [sortParam];
    
    for (const sortField of sortFields) {
      const [field, direction = 'asc'] = sortField.split(':');
      
      if (config.sortableFields.includes(field)) {
        sorting.push({
          field,
          direction: direction.toLowerCase() === 'desc' ? 'DESC' : 'ASC',
        });
      }
    }
  }
  
  // Add default sorting if none provided
  if (sorting.length === 0) {
    sorting.push(config.defaultSort);
  }
  
  return sorting;
}

/**
 * Parse filter parameters with operators
 * Supports: field=value, field__gt=value, field__lt=value, field__in=value1,value2
 */
function parseFilters(query: any, config: QueryParserConfig) {
  const filters: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(query)) {
    // Skip non-filter parameters
    if (['limit', 'offset', 'page', 'sort', 'order', 'fields', 'expand', 'q', 'search'].includes(key)) {
      continue;
    }
    
    // Parse field with operator
    const [field, operator] = key.split('__');
    
    if (!config.filterableFields.includes(field)) {
      continue;
    }
    
    // Apply operator-based filtering
    switch (operator) {
      case 'gt':
        filters[field] = { $gt: parseFilterValue(value) };
        break;
      case 'gte':
        filters[field] = { $gte: parseFilterValue(value) };
        break;
      case 'lt':
        filters[field] = { $lt: parseFilterValue(value) };
        break;
      case 'lte':
        filters[field] = { $lte: parseFilterValue(value) };
        break;
      case 'in':
        const values = Array.isArray(value) ? value : value.toString().split(',');
        filters[field] = { $in: values.map(parseFilterValue) };
        break;
      case 'not':
        filters[field] = { $ne: parseFilterValue(value) };
        break;
      case 'like':
        filters[field] = { $ilike: `%${value}%` };
        break;
      case 'startswith':
        filters[field] = { $ilike: `${value}%` };
        break;
      case 'endswith':
        filters[field] = { $ilike: `%${value}` };
        break;
      default:
        // Direct equality
        filters[field] = parseFilterValue(value);
        break;
    }
  }
  
  return filters;
}

/**
 * Parse field selection parameters
 * Supports: fields=id,name,handle
 */
function parseFields(query: any, config: QueryParserConfig) {
  if (!query.fields) return undefined;
  
  const fields = Array.isArray(query.fields) 
    ? query.fields 
    : query.fields.split(',');
  
  return fields.filter((field: string) => 
    config.selectableFields.includes(field.trim())
  );
}

/**
 * Parse relation expansion parameters
 * Supports: expand=category,variants,images
 */
function parseExpand(query: any, config: QueryParserConfig) {
  if (!query.expand) return undefined;
  
  const expand = Array.isArray(query.expand) 
    ? query.expand 
    : query.expand.split(',');
  
  return expand.filter((relation: string) => 
    config.expandableRelations.includes(relation.trim())
  );
}

/**
 * Parse search parameters
 * Supports: q=search term, search=search term
 */
function parseSearch(query: any, config: QueryParserConfig) {
  const searchQuery = query.q || query.search;
  
  if (!searchQuery || config.searchableFields.length === 0) {
    return undefined;
  }
  
  return {
    query: searchQuery.toString(),
    fields: config.searchableFields,
  };
}

/**
 * Parse date filter parameters
 */
function parseDateFilters(query: any) {
  const dateFilters: any = {};
  
  if (query.created_after) {
    dateFilters.created_after = new Date(query.created_after);
  }
  if (query.created_before) {
    dateFilters.created_before = new Date(query.created_before);
  }
  if (query.updated_after) {
    dateFilters.updated_after = new Date(query.updated_after);
  }
  if (query.updated_before) {
    dateFilters.updated_before = new Date(query.updated_before);
  }
  
  return dateFilters;
}

/**
 * Parse and convert filter values to appropriate types
 */
function parseFilterValue(value: any): any {
  if (typeof value !== 'string') return value;
  
  // Boolean conversion
  if (value === 'true') return true;
  if (value === 'false') return false;
  
  // Number conversion
  if (/^\d+$/.test(value)) return parseInt(value);
  if (/^\d+\.\d+$/.test(value)) return parseFloat(value);
  
  // Date conversion (ISO format)
  if (/^\d{4}-\d{2}-\d{2}/.test(value)) {
    const date = new Date(value);
    if (!isNaN(date.getTime())) return date;
  }
  
  return value;
}

/**
 * Build SQL WHERE clause from parsed filters
 */
export function buildSQLFilters(
  filters: Record<string, any>,
  tenantId: string,
  startParamIndex: number = 1
): { whereClause: string; values: any[]; nextParamIndex: number } {
  const conditions = [`tenant_id = $${startParamIndex}`];
  const values = [tenantId];
  let paramIndex = startParamIndex + 1;
  
  for (const [field, filter] of Object.entries(filters)) {
    if (typeof filter === 'object' && filter !== null) {
      // Handle operator-based filters
      for (const [operator, value] of Object.entries(filter)) {
        switch (operator) {
          case '$gt':
            conditions.push(`${field} > $${paramIndex}`);
            values.push(value);
            paramIndex++;
            break;
          case '$gte':
            conditions.push(`${field} >= $${paramIndex}`);
            values.push(value);
            paramIndex++;
            break;
          case '$lt':
            conditions.push(`${field} < $${paramIndex}`);
            values.push(value);
            paramIndex++;
            break;
          case '$lte':
            conditions.push(`${field} <= $${paramIndex}`);
            values.push(value);
            paramIndex++;
            break;
          case '$in':
            const placeholders = (value as any[]).map(() => `$${paramIndex++}`).join(',');
            conditions.push(`${field} IN (${placeholders})`);
            values.push(...(value as any[]));
            break;
          case '$ne':
            conditions.push(`${field} != $${paramIndex}`);
            values.push(value);
            paramIndex++;
            break;
          case '$ilike':
            conditions.push(`${field} ILIKE $${paramIndex}`);
            values.push(value);
            paramIndex++;
            break;
        }
      }
    } else {
      // Direct equality
      conditions.push(`${field} = $${paramIndex}`);
      values.push(filter);
      paramIndex++;
    }
  }
  
  return {
    whereClause: conditions.join(' AND '),
    values,
    nextParamIndex: paramIndex,
  };
}

/**
 * Build SQL ORDER BY clause from parsed sorting
 */
export function buildSQLSorting(sorting: { field: string; direction: 'ASC' | 'DESC' }[]): string {
  if (sorting.length === 0) return '';
  
  const orderClauses = sorting.map(sort => `${sort.field} ${sort.direction}`);
  return `ORDER BY ${orderClauses.join(', ')}`;
}
