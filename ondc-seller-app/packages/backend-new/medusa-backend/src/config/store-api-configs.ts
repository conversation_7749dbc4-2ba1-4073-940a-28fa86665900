/**
 * Store API Endpoint Configurations
 * 
 * Defines query parameter configurations for each store API endpoint
 * including allowed fields for filtering, sorting, selection, and expansion.
 */

import { QueryParserConfig } from '../utils/enhanced-query-parser';

/**
 * Product Categories API Configuration
 */
export const PRODUCT_CATEGORIES_CONFIG: QueryParserConfig = {
  sortableFields: [
    'id',
    'name',
    'handle',
    'created_at',
    'updated_at',
    'rank',
    'is_active',
  ],
  
  filterableFields: [
    'id',
    'name',
    'handle',
    'parent_category_id',
    'is_active',
    'is_internal',
    'created_at',
    'updated_at',
    'rank',
  ],
  
  selectableFields: [
    'id',
    'name',
    'description',
    'handle',
    'is_active',
    'is_internal',
    'parent_category_id',
    'rank',
    'created_at',
    'updated_at',
    'metadata',
  ],
  
  expandableRelations: [
    'parent_category',
    'category_children',
    'products',
  ],
  
  searchableFields: [
    'name',
    'description',
    'handle',
  ],
  
  defaultSort: { field: 'rank', direction: 'ASC' },
  maxLimit: 100,
  defaultLimit: 50,
};

/**
 * Products API Configuration
 */
export const PRODUCTS_CONFIG: QueryParserConfig = {
  sortableFields: [
    'id',
    'title',
    'handle',
    'status',
    'created_at',
    'updated_at',
    'weight',
    'length',
    'height',
    'width',
  ],
  
  filterableFields: [
    'id',
    'title',
    'handle',
    'status',
    'category_id',
    'subcategory_id',
    'parent_category_id',
    'collection_id',
    'type_id',
    'tags',
    'is_giftcard',
    'weight',
    'length',
    'height',
    'width',
    'created_at',
    'updated_at',
  ],
  
  selectableFields: [
    'id',
    'title',
    'subtitle',
    'description',
    'handle',
    'status',
    'thumbnail',
    'weight',
    'length',
    'height',
    'width',
    'hs_code',
    'origin_country',
    'mid_code',
    'material',
    'created_at',
    'updated_at',
    'metadata',
  ],
  
  expandableRelations: [
    'variants',
    'options',
    'images',
    'tags',
    'type',
    'collection',
    'categories',
    'profiles',
  ],
  
  searchableFields: [
    'title',
    'subtitle',
    'description',
    'handle',
  ],
  
  defaultSort: { field: 'created_at', direction: 'DESC' },
  maxLimit: 100,
  defaultLimit: 20,
};

/**
 * Collections API Configuration
 */
export const COLLECTIONS_CONFIG: QueryParserConfig = {
  sortableFields: [
    'id',
    'title',
    'handle',
    'created_at',
    'updated_at',
  ],
  
  filterableFields: [
    'id',
    'title',
    'handle',
    'created_at',
    'updated_at',
  ],
  
  selectableFields: [
    'id',
    'title',
    'handle',
    'created_at',
    'updated_at',
    'metadata',
  ],
  
  expandableRelations: [
    'products',
  ],
  
  searchableFields: [
    'title',
    'handle',
  ],
  
  defaultSort: { field: 'title', direction: 'ASC' },
  maxLimit: 100,
  defaultLimit: 50,
};

/**
 * Tags API Configuration
 */
export const TAGS_CONFIG: QueryParserConfig = {
  sortableFields: [
    'id',
    'value',
    'created_at',
    'updated_at',
  ],
  
  filterableFields: [
    'id',
    'value',
    'created_at',
    'updated_at',
  ],
  
  selectableFields: [
    'id',
    'value',
    'created_at',
    'updated_at',
    'metadata',
  ],
  
  expandableRelations: [
    'products',
  ],
  
  searchableFields: [
    'value',
  ],
  
  defaultSort: { field: 'value', direction: 'ASC' },
  maxLimit: 100,
  defaultLimit: 50,
};

/**
 * Get configuration for a specific endpoint
 */
export function getEndpointConfig(endpoint: string): QueryParserConfig {
  switch (endpoint) {
    case 'product-categories':
      return PRODUCT_CATEGORIES_CONFIG;
    case 'products':
      return PRODUCTS_CONFIG;
    case 'collections':
      return COLLECTIONS_CONFIG;
    case 'tags':
      return TAGS_CONFIG;
    default:
      throw new Error(`Unknown endpoint configuration: ${endpoint}`);
  }
}

/**
 * Validate query parameters against endpoint configuration
 */
export function validateQueryParams(
  query: Record<string, any>,
  config: QueryParserConfig
): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Validate sort fields
  if (query.sort) {
    const sortFields = Array.isArray(query.sort) ? query.sort : [query.sort];
    for (const sortField of sortFields) {
      const [field] = sortField.split(':');
      if (!config.sortableFields.includes(field)) {
        errors.push(`Invalid sort field: ${field}. Allowed: ${config.sortableFields.join(', ')}`);
      }
    }
  }
  
  // Validate expand relations
  if (query.expand) {
    const expandFields = Array.isArray(query.expand) ? query.expand : query.expand.split(',');
    for (const relation of expandFields) {
      if (!config.expandableRelations.includes(relation.trim())) {
        errors.push(`Invalid expand relation: ${relation}. Allowed: ${config.expandableRelations.join(', ')}`);
      }
    }
  }
  
  // Validate field selection
  if (query.fields) {
    const fields = Array.isArray(query.fields) ? query.fields : query.fields.split(',');
    for (const field of fields) {
      if (!config.selectableFields.includes(field.trim())) {
        errors.push(`Invalid field selection: ${field}. Allowed: ${config.selectableFields.join(', ')}`);
      }
    }
  }
  
  // Validate pagination limits
  if (query.limit) {
    const limit = parseInt(query.limit);
    if (isNaN(limit) || limit < 1 || limit > config.maxLimit) {
      errors.push(`Invalid limit: ${query.limit}. Must be between 1 and ${config.maxLimit}`);
    }
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
}
