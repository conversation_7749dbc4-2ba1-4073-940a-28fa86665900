import { SubscriberArgs, SubscriberConfig } from "@medusajs/medusa";
import { MeiliSearchIndexer } from "../services/meilisearch-indexer";

export default async function meiliSearchSyncSubscriber({
  eventBusService,
  container,
}: SubscriberArgs) {
  console.log('🔄 [MEILISEARCH-SYNC] Initializing MeiliSearch sync subscriber');

  // Get the database connection from the container
  const dataSource = container.resolve("manager").connection;
  const indexer = new MeiliSearchIndexer(dataSource);

  // Initialize the index on startup
  try {
    await indexer.initializeIndex();
    console.log('✅ [MEILISEARCH-SYNC] MeiliSearch index initialized');
  } catch (error) {
    console.error('❌ [MEILISEARCH-SYNC] Failed to initialize index:', error);
  }

  // Subscribe to product creation events
  eventBusService.subscribe("product.created", async (data: any) => {
    try {
      console.log(`📦 [MEILISEARCH-SYNC] Product created: ${data.id}`);
      await indexer.indexProduct(data.id);
      console.log(`✅ [MEILISEARCH-SYNC] Product indexed: ${data.id}`);
    } catch (error) {
      console.error(`❌ [MEILISEARCH-SYNC] Failed to index created product ${data.id}:`, error);
    }
  });

  // Subscribe to product update events
  eventBusService.subscribe("product.updated", async (data: any) => {
    try {
      console.log(`📦 [MEILISEARCH-SYNC] Product updated: ${data.id}`);
      await indexer.indexProduct(data.id);
      console.log(`✅ [MEILISEARCH-SYNC] Product re-indexed: ${data.id}`);
    } catch (error) {
      console.error(`❌ [MEILISEARCH-SYNC] Failed to re-index updated product ${data.id}:`, error);
    }
  });

  // Subscribe to product deletion events
  eventBusService.subscribe("product.deleted", async (data: any) => {
    try {
      console.log(`🗑️ [MEILISEARCH-SYNC] Product deleted: ${data.id}`);
      await indexer.removeProduct(data.id);
      console.log(`✅ [MEILISEARCH-SYNC] Product removed from index: ${data.id}`);
    } catch (error) {
      console.error(`❌ [MEILISEARCH-SYNC] Failed to remove deleted product ${data.id}:`, error);
    }
  });

  // Subscribe to product variant events
  eventBusService.subscribe("product-variant.created", async (data: any) => {
    try {
      console.log(`📦 [MEILISEARCH-SYNC] Product variant created for product: ${data.product_id}`);
      await indexer.indexProduct(data.product_id);
      console.log(`✅ [MEILISEARCH-SYNC] Product re-indexed after variant creation: ${data.product_id}`);
    } catch (error) {
      console.error(`❌ [MEILISEARCH-SYNC] Failed to re-index product after variant creation ${data.product_id}:`, error);
    }
  });

  eventBusService.subscribe("product-variant.updated", async (data: any) => {
    try {
      console.log(`📦 [MEILISEARCH-SYNC] Product variant updated for product: ${data.product_id}`);
      await indexer.indexProduct(data.product_id);
      console.log(`✅ [MEILISEARCH-SYNC] Product re-indexed after variant update: ${data.product_id}`);
    } catch (error) {
      console.error(`❌ [MEILISEARCH-SYNC] Failed to re-index product after variant update ${data.product_id}:`, error);
    }
  });

  eventBusService.subscribe("product-variant.deleted", async (data: any) => {
    try {
      console.log(`🗑️ [MEILISEARCH-SYNC] Product variant deleted for product: ${data.product_id}`);
      await indexer.indexProduct(data.product_id);
      console.log(`✅ [MEILISEARCH-SYNC] Product re-indexed after variant deletion: ${data.product_id}`);
    } catch (error) {
      console.error(`❌ [MEILISEARCH-SYNC] Failed to re-index product after variant deletion ${data.product_id}:`, error);
    }
  });

  console.log('✅ [MEILISEARCH-SYNC] MeiliSearch sync subscriber initialized successfully');
}

export const config: SubscriberConfig = {
  event: [
    "product.created",
    "product.updated", 
    "product.deleted",
    "product-variant.created",
    "product-variant.updated",
    "product-variant.deleted"
  ],
};
