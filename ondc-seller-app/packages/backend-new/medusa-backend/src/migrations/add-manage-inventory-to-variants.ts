import { Migration } from '@medusajs/medusa'

/**
 * Migration to add manage_inventory column to product_variant table
 * This ensures the column exists for the auto-configuration functionality
 */
export const migration: Migration = {
  name: 'AddManageInventoryToVariants1703000000',
  
  async up(queryRunner) {
    // Check if the column already exists
    const hasColumn = await queryRunner.hasColumn('product_variant', 'manage_inventory')
    
    if (!hasColumn) {
      console.log('Adding manage_inventory column to product_variant table...')
      
      await queryRunner.query(`
        ALTER TABLE product_variant 
        ADD COLUMN manage_inventory BOOLEAN DEFAULT true
      `)
      
      console.log('✅ manage_inventory column added to product_variant table')
    } else {
      console.log('manage_inventory column already exists in product_variant table')
    }
  },

  async down(queryRunner) {
    const hasColumn = await queryRunner.hasColumn('product_variant', 'manage_inventory')
    
    if (hasColumn) {
      console.log('Removing manage_inventory column from product_variant table...')
      
      await queryRunner.query(`
        ALTER TABLE product_variant 
        DROP COLUMN manage_inventory
      `)
      
      console.log('✅ manage_inventory column removed from product_variant table')
    }
  }
}
