import { MeiliSearch } from 'meilisearch';
import { DataSource } from 'typeorm';

export interface ProductIndexData {
  id: string;
  title: string;
  description?: string;
  handle: string;
  status: string;
  tenant_id: string;
  collection_id?: string;
  category_id?: string;
  tags?: string[];
  thumbnail?: string;
  created_at: string;
  updated_at: string;
  variants?: {
    id: string;
    title: string;
    sku?: string;
    prices?: {
      amount: number;
      currency_code: string;
    }[];
  }[];
}

export class MeiliSearchIndexer {
  private client: MeiliSearch;
  private indexName: string;
  private dataSource: DataSource;

  constructor(dataSource: DataSource) {
    this.client = new MeiliSearch({
      host: process.env.MEILISEARCH_HOST_URL || 'https://apigw.cubeone.in/api/search',
      apiKey: process.env.MEILISEARCH_API_KEY || '264f59ea726bd9c1e24f3abd03918c124bcf5539f7c3589d0c49d6a0fc36a02d',
    });
    this.indexName = process.env.MEILISEARCH_INDEX_PREFIX || 'medusa-products';
    this.dataSource = dataSource;
  }

  /**
   * Initialize the MeiliSearch index with proper settings
   */
  async initializeIndex(): Promise<void> {
    try {
      console.log(`🔧 [MEILISEARCH-INDEXER] Initializing index: ${this.indexName}`);

      const index = this.client.index(this.indexName);

      // Check if index exists, if not create it
      try {
        await index.getStats();
        console.log('✅ [MEILISEARCH-INDEXER] Index already exists');
      } catch (error) {
        if (error.code === 'index_not_found') {
          console.log('🆕 [MEILISEARCH-INDEXER] Creating new index...');
          await this.client.createIndex(this.indexName, { primaryKey: 'id' });
          console.log('✅ [MEILISEARCH-INDEXER] Index created successfully');
        } else {
          throw error;
        }
      }

      // Configure index settings
      await this.configureIndexSettings();
      
      console.log('✅ [MEILISEARCH-INDEXER] Index initialization completed');
    } catch (error) {
      console.error('❌ [MEILISEARCH-INDEXER] Failed to initialize index:', error);
      throw error;
    }
  }

  /**
   * Configure MeiliSearch index settings
   */
  private async configureIndexSettings(): Promise<void> {
    const index = this.client.index(this.indexName);

    // Configure searchable attributes
    await index.updateSearchableAttributes([
      'title',
      'description',
      'handle',
      'tags',
      'variants.title',
      'variants.sku'
    ]);

    // Configure filterable attributes
    await index.updateFilterableAttributes([
      'id',
      'handle',
      'status',
      'tenant_id',
      'collection_id',
      'category_id',
      'tags',
      'created_at',
      'updated_at'
    ]);

    // Configure sortable attributes
    await index.updateSortableAttributes([
      'title',
      'created_at',
      'updated_at'
    ]);

    // Configure displayed attributes
    await index.updateDisplayedAttributes([
      'id',
      'title',
      'description',
      'handle',
      'status',
      'thumbnail',
      'tags',
      'collection_id',
      'category_id',
      'tenant_id',
      'created_at',
      'updated_at',
      'variants'
    ]);

    console.log('✅ [MEILISEARCH-INDEXER] Index settings configured');
  }

  /**
   * Index all products for a specific tenant
   */
  async indexAllProducts(tenantId: string): Promise<void> {
    try {
      console.log(`📦 [MEILISEARCH-INDEXER] Starting full index for tenant: ${tenantId}`);

      const products = await this.fetchProductsFromDatabase(tenantId);
      
      if (products.length === 0) {
        console.log(`⚠️ [MEILISEARCH-INDEXER] No products found for tenant: ${tenantId}`);
        return;
      }

      const indexData = products.map(product => this.transformProductForIndex(product));
      
      const index = this.client.index(this.indexName);
      const task = await index.addDocuments(indexData);
      
      console.log(`📤 [MEILISEARCH-INDEXER] Indexing task created: ${task.taskUid}`);
      console.log(`✅ [MEILISEARCH-INDEXER] Indexed ${products.length} products for tenant: ${tenantId}`);
      
    } catch (error) {
      console.error(`❌ [MEILISEARCH-INDEXER] Failed to index products for tenant ${tenantId}:`, error);
      throw error;
    }
  }

  /**
   * Index a single product
   */
  async indexProduct(productId: string): Promise<void> {
    try {
      console.log(`📦 [MEILISEARCH-INDEXER] Indexing single product: ${productId}`);

      const product = await this.fetchSingleProductFromDatabase(productId);
      
      if (!product) {
        console.log(`⚠️ [MEILISEARCH-INDEXER] Product not found: ${productId}`);
        return;
      }

      const indexData = this.transformProductForIndex(product);
      
      const index = this.client.index(this.indexName);
      await index.addDocuments([indexData]);
      
      console.log(`✅ [MEILISEARCH-INDEXER] Product indexed: ${productId}`);
      
    } catch (error) {
      console.error(`❌ [MEILISEARCH-INDEXER] Failed to index product ${productId}:`, error);
      throw error;
    }
  }

  /**
   * Remove a product from the index
   */
  async removeProduct(productId: string): Promise<void> {
    try {
      console.log(`🗑️ [MEILISEARCH-INDEXER] Removing product from index: ${productId}`);

      const index = this.client.index(this.indexName);
      await index.deleteDocument(productId);
      
      console.log(`✅ [MEILISEARCH-INDEXER] Product removed from index: ${productId}`);
      
    } catch (error) {
      console.error(`❌ [MEILISEARCH-INDEXER] Failed to remove product ${productId}:`, error);
      throw error;
    }
  }

  /**
   * Clear all documents for a specific tenant
   */
  async clearTenantData(tenantId: string): Promise<void> {
    try {
      console.log(`🧹 [MEILISEARCH-INDEXER] Clearing data for tenant: ${tenantId}`);

      const index = this.client.index(this.indexName);
      await index.deleteDocuments({
        filter: `tenant_id = "${tenantId}"`
      });
      
      console.log(`✅ [MEILISEARCH-INDEXER] Cleared data for tenant: ${tenantId}`);
      
    } catch (error) {
      console.error(`❌ [MEILISEARCH-INDEXER] Failed to clear data for tenant ${tenantId}:`, error);
      throw error;
    }
  }

  /**
   * Fetch products from the database for a specific tenant
   */
  private async fetchProductsFromDatabase(tenantId: string): Promise<any[]> {
    const query = `
      SELECT 
        p.id,
        p.title,
        p.description,
        p.handle,
        p.status,
        p.tenant_id,
        p.collection_id,
        p.thumbnail,
        p.created_at,
        p.updated_at,
        COALESCE(
          JSON_AGG(
            DISTINCT JSONB_BUILD_OBJECT(
              'id', pv.id,
              'title', pv.title,
              'sku', pv.sku
            )
          ) FILTER (WHERE pv.id IS NOT NULL), 
          '[]'
        ) as variants,
        COALESCE(
          ARRAY_AGG(DISTINCT pt.value) FILTER (WHERE pt.value IS NOT NULL), 
          '{}'
        ) as tags
      FROM product p
      LEFT JOIN product_variant pv ON p.id = pv.product_id AND pv.deleted_at IS NULL
      LEFT JOIN product_tag pt ON p.id = pt.product_id AND pt.deleted_at IS NULL
      WHERE p.tenant_id = $1 
        AND p.deleted_at IS NULL 
        AND p.status = 'published'
      GROUP BY p.id, p.title, p.description, p.handle, p.status, p.tenant_id, p.collection_id, p.thumbnail, p.created_at, p.updated_at
      ORDER BY p.created_at DESC
    `;

    const result = await this.dataSource.query(query, [tenantId]);
    return result;
  }

  /**
   * Fetch a single product from the database
   */
  private async fetchSingleProductFromDatabase(productId: string): Promise<any | null> {
    const query = `
      SELECT 
        p.id,
        p.title,
        p.description,
        p.handle,
        p.status,
        p.tenant_id,
        p.collection_id,
        p.thumbnail,
        p.created_at,
        p.updated_at,
        COALESCE(
          JSON_AGG(
            DISTINCT JSONB_BUILD_OBJECT(
              'id', pv.id,
              'title', pv.title,
              'sku', pv.sku
            )
          ) FILTER (WHERE pv.id IS NOT NULL), 
          '[]'
        ) as variants,
        COALESCE(
          ARRAY_AGG(DISTINCT pt.value) FILTER (WHERE pt.value IS NOT NULL), 
          '{}'
        ) as tags
      FROM product p
      LEFT JOIN product_variant pv ON p.id = pv.product_id AND pv.deleted_at IS NULL
      LEFT JOIN product_tag pt ON p.id = pt.product_id AND pt.deleted_at IS NULL
      WHERE p.id = $1 AND p.deleted_at IS NULL
      GROUP BY p.id, p.title, p.description, p.handle, p.status, p.tenant_id, p.collection_id, p.thumbnail, p.created_at, p.updated_at
    `;

    const result = await this.dataSource.query(query, [productId]);
    return result.length > 0 ? result[0] : null;
  }

  /**
   * Transform product data for MeiliSearch indexing
   */
  private transformProductForIndex(product: any): ProductIndexData {
    return {
      id: product.id,
      title: product.title,
      description: product.description,
      handle: product.handle,
      status: product.status,
      tenant_id: product.tenant_id,
      collection_id: product.collection_id,
      category_id: product.category_id,
      tags: Array.isArray(product.tags) ? product.tags : [],
      thumbnail: product.thumbnail,
      created_at: product.created_at,
      updated_at: product.updated_at,
      variants: Array.isArray(product.variants) ? product.variants : []
    };
  }

  /**
   * Get index statistics
   */
  async getIndexStats(): Promise<any> {
    try {
      const index = this.client.index(this.indexName);
      const stats = await index.getStats();
      return stats;
    } catch (error) {
      console.error('❌ [MEILISEARCH-INDEXER] Failed to get index stats:', error);
      throw error;
    }
  }
}
