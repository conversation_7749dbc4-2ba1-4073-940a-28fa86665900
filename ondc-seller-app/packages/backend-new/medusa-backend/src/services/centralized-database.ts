/**
 * Centralized Database Service
 *
 * This service provides a unified interface for all database operations
 * across the application. It ensures proper connection management,
 * tenant isolation, and resource cleanup.
 *
 * USAGE: All API endpoints should use this service instead of creating
 * their own database connections.
 */

import { PoolClient } from 'pg';
import { dbPool } from '../utils/database-pool';
import { dbMonitor } from './database-monitor';

export interface QueryOptions {
  tenantId?: string;
  timeout?: number;
  retries?: number;
}

export interface QueryResult<T = any> {
  rows: T[];
  rowCount: number;
  duration: number;
}

export class CentralizedDatabaseService {
  private static instance: CentralizedDatabaseService;

  private constructor() {}

  public static getInstance(): CentralizedDatabaseService {
    if (!CentralizedDatabaseService.instance) {
      CentralizedDatabaseService.instance = new CentralizedDatabaseService();
    }
    return CentralizedDatabaseService.instance;
  }

  /**
   * Execute a query with tenant context and proper error handling
   */
  public async query<T = any>(
    sql: string,
    params: any[] = [],
    options: QueryOptions = {}
  ): Promise<QueryResult<T>> {
    const { tenantId, timeout = 30000, retries = 2 } = options;

    let attempt = 0;
    let lastError: Error | null = null;

    while (attempt <= retries) {
      try {
        const startTime = Date.now();

        // Get connection from the centralized pool
        const client = await dbPool.getConnection();

        try {
          // Set tenant context if provided
          if (tenantId) {
            await this.setTenantContext(client, tenantId);
          }

          // Execute the query with timeout
          const result = await Promise.race([
            client.query(sql, params),
            this.createTimeoutPromise(timeout),
          ]);

          const duration = Date.now() - startTime;

          console.log(`✅ [CENTRALIZED-DB] Query executed successfully`, {
            tenantId,
            duration: `${duration}ms`,
            rows: result.rowCount,
            attempt: attempt + 1,
          });

          // Record successful request for monitoring
          dbMonitor.recordRequest(duration, false);

          return {
            rows: result.rows as T[],
            rowCount: result.rowCount || 0,
            duration,
          };
        } finally {
          // Always release the connection
          client.release();
        }
      } catch (error) {
        attempt++;
        lastError = error instanceof Error ? error : new Error(String(error));

        const errorDuration = Date.now() - startTime;

        console.error(`❌ [CENTRALIZED-DB] Query failed (attempt ${attempt}/${retries + 1}):`, {
          error: lastError.message,
          tenantId,
          sql: sql.substring(0, 100),
        });

        // Record failed request for monitoring
        dbMonitor.recordRequest(errorDuration, true);

        // If this was the last attempt, throw the error
        if (attempt > retries) {
          break;
        }

        // Wait before retrying (exponential backoff)
        await this.delay(Math.pow(2, attempt) * 1000);
      }
    }

    throw lastError || new Error('Query failed after all retries');
  }

  /**
   * Execute a transaction with proper error handling and cleanup
   */
  public async transaction<T>(
    callback: (client: PoolClient) => Promise<T>,
    options: QueryOptions = {}
  ): Promise<T> {
    const { tenantId, timeout = 60000 } = options;
    const client = await dbPool.getConnection();

    try {
      // Set tenant context if provided
      if (tenantId) {
        await this.setTenantContext(client, tenantId);
      }

      // Start transaction
      await client.query('BEGIN');
      console.log(`🔄 [CENTRALIZED-DB] Transaction started for tenant: ${tenantId || 'default'}`);

      // Execute callback with timeout
      const result = await Promise.race([callback(client), this.createTimeoutPromise<T>(timeout)]);

      // Commit transaction
      await client.query('COMMIT');
      console.log(`✅ [CENTRALIZED-DB] Transaction committed for tenant: ${tenantId || 'default'}`);

      return result;
    } catch (error) {
      // Rollback transaction
      try {
        await client.query('ROLLBACK');
        console.log(
          `🔄 [CENTRALIZED-DB] Transaction rolled back for tenant: ${tenantId || 'default'}`
        );
      } catch (rollbackError) {
        console.error('❌ [CENTRALIZED-DB] Error during rollback:', rollbackError);
      }

      console.error(
        `❌ [CENTRALIZED-DB] Transaction failed for tenant: ${tenantId || 'default'}:`,
        error
      );
      throw error;
    } finally {
      // Always release the connection
      client.release();
    }
  }

  /**
   * Get products with tenant filtering
   */
  public async getProducts(
    tenantId: string,
    filters: any = {},
    pagination: { limit?: number; offset?: number } = {}
  ): Promise<QueryResult> {
    const { limit = 50, offset = 0 } = pagination;

    let whereClause = 'WHERE tenant_id = $1';
    let params: any[] = [tenantId];
    let paramIndex = 2;

    // Add additional filters
    if (filters.category_id) {
      whereClause += ` AND id IN (
        SELECT product_id FROM product_category 
        WHERE category_id = $${paramIndex}
      )`;
      params.push(filters.category_id);
      paramIndex++;
    }

    if (filters.status) {
      whereClause += ` AND status = $${paramIndex}`;
      params.push(filters.status);
      paramIndex++;
    }

    const sql = `
      SELECT 
        p.id, p.title, p.subtitle, p.description, p.handle,
        p.status, p.created_at, p.updated_at, p.tenant_id,
        p.metadata, p.weight, p.length, p.height, p.width,
        p.origin_country, p.material, p.mid_code, p.hs_code
      FROM product p
      ${whereClause}
      ORDER BY p.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    params.push(limit, offset);

    return this.query(sql, params, { tenantId });
  }

  /**
   * Get customers with tenant filtering
   */
  public async getCustomers(
    tenantId: string,
    pagination: { limit?: number; offset?: number } = {}
  ): Promise<QueryResult> {
    const { limit = 50, offset = 0 } = pagination;

    const sql = `
      SELECT 
        id, email, first_name, last_name, phone,
        created_at, updated_at, tenant_id, metadata
      FROM customer 
      WHERE tenant_id = $1 
      ORDER BY created_at DESC 
      LIMIT $2 OFFSET $3
    `;

    return this.query(sql, [tenantId, limit, offset], { tenantId });
  }

  /**
   * Get count of records for a table with tenant filtering
   */
  public async getCount(
    table: string,
    tenantId: string,
    additionalWhere: string = ''
  ): Promise<number> {
    const whereClause = additionalWhere
      ? `WHERE tenant_id = $1 AND ${additionalWhere}`
      : 'WHERE tenant_id = $1';

    const sql = `SELECT COUNT(*) as total FROM ${table} ${whereClause}`;
    const result = await this.query(sql, [tenantId], { tenantId });

    return parseInt(result.rows[0]?.total || '0');
  }

  /**
   * Set tenant context for RLS policies
   */
  private async setTenantContext(client: PoolClient, tenantId: string): Promise<void> {
    try {
      await client.query('SELECT set_config($1, $2, true)', ['app.current_tenant_id', tenantId]);
      console.log(`🔐 [CENTRALIZED-DB] Tenant context set: ${tenantId}`);
    } catch (error) {
      console.warn(`⚠️ [CENTRALIZED-DB] Failed to set tenant context: ${error}`);
      // Don't throw here as RLS might not be enabled
    }
  }

  /**
   * Create a timeout promise for query timeouts
   */
  private createTimeoutPromise<T>(timeoutMs: number): Promise<T> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Query timeout after ${timeoutMs}ms`));
      }, timeoutMs);
    });
  }

  /**
   * Delay utility for retries
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Health check for the database service
   */
  public async healthCheck(): Promise<boolean> {
    try {
      const result = await this.query('SELECT 1 as health_check');
      return result.rows.length > 0 && result.rows[0].health_check === 1;
    } catch (error) {
      console.error('❌ [CENTRALIZED-DB] Health check failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const centralizedDb = CentralizedDatabaseService.getInstance();
