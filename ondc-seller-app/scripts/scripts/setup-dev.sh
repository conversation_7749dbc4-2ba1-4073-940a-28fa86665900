#!/bin/bash

# Exit on error
set -e

# Print commands
set -x

# Create directories if they don't exist
mkdir -p packages/frontend-new
mkdir -p packages/backend-new/medusa-backend
mkdir -p packages/cms-strapi

# Install dependencies
npm install

# Set up Strapi
cd packages/cms-strapi
npm install
# npm run db:generate

# Set up Backend
cd ../backend-new/medusa-backend
npm install
npx medusa db:migrate

# Set up Frontend
cd ../frontend-new
npm install

# Return to root
cd ../..

# Make scripts executable
chmod +x packages/backend/scripts/generate-api-docs.js
chmod +x scripts/setup-dev.sh

echo "Development environment setup complete!"
