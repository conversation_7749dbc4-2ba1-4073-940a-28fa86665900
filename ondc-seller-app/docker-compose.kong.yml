version: '3.8'

networks:
  kong-net:
    driver: bridge

services:
  kong-database:
    image: postgres:13
    container_name: kong-database
    restart: unless-stopped
    networks:
      - kong-net
    # ports:
    #   - '5434:5432' # Use 5434 to avoid conflict
    environment:
      POSTGRES_DB: kong
      POSTGRES_USER: kong
      POSTGRES_PASSWORD: kong

  kong-migrations:
    image: kong:3.4
    depends_on:
      - kong-database
    networks:
      - kong-net
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: kong-database
      KONG_PG_PORT: 5432
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: kong
    entrypoint: ['/bin/sh', '-c']
    command: >
      "kong migrations bootstrap && kong migrations up && kong migrations finish"
    restart: on-failure

  kong:
    image: kong:3.4
    container_name: kong
    depends_on:
      - kong-database
      - kong-migrations
    networks:
      - kong-net
    ports:
      - '8000:8000' # Kong Proxy (Public)
      - '8443:8443' # Kong Proxy HTTPS (Optional)
      - '8001:8001' # Kong Admin API
      - '8444:8444' # Kong Admin HTTPS (Optional)
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: kong-database
      KONG_PG_PORT: 5432
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: kong
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
    restart: unless-stopped

  # Optional GUI for Kong
  konga:
    image: pantsel/konga:latest
    container_name: konga
    networks:
      - kong-net
    ports:
      - '1338:1337'
    environment:
      NODE_ENV: production
    restart: unless-stopped
