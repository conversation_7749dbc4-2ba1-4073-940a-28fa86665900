--
-- PostgreSQL database dump
--

-- Dumped from database version 15.13
-- Dumped by pg_dump version 17.5 (Ubuntu 17.5-1.pgdg24.04+1)

-- Started on 2025-09-03 17:53:55 IST

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 1236 (class 1247 OID 75651)
-- Name: claim_reason_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.claim_reason_enum AS ENUM (
    'missing_item',
    'wrong_item',
    'production_failure',
    'other'
);


--
-- TOC entry 1230 (class 1247 OID 75630)
-- Name: order_claim_type_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.order_claim_type_enum AS ENUM (
    'refund',
    'replace'
);


--
-- TOC entry 1176 (class 1247 OID 75296)
-- Name: order_status_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.order_status_enum AS ENUM (
    'pending',
    'completed',
    'draft',
    'archived',
    'canceled',
    'requires_action'
);


--
-- TOC entry 1245 (class 1247 OID 75701)
-- Name: return_status_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.return_status_enum AS ENUM (
    'open',
    'requested',
    'received',
    'partially_received',
    'canceled'
);


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- TOC entry 280 (class 1259 OID 75248)
-- Name: account_holder; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.account_holder (
    id text NOT NULL,
    provider_id text NOT NULL,
    external_id text NOT NULL,
    email text,
    data jsonb DEFAULT '{}'::jsonb NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 264 (class 1259 OID 74971)
-- Name: api_key; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.api_key (
    id text NOT NULL,
    token text NOT NULL,
    salt text NOT NULL,
    redacted text NOT NULL,
    title text NOT NULL,
    type text NOT NULL,
    last_used_at timestamp with time zone,
    created_by text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    revoked_by text,
    revoked_at timestamp with time zone,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    CONSTRAINT api_key_type_check CHECK ((type = ANY (ARRAY['publishable'::text, 'secret'::text])))
);


--
-- TOC entry 246 (class 1259 OID 74506)
-- Name: application_method_buy_rules; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.application_method_buy_rules (
    application_method_id text NOT NULL,
    promotion_rule_id text NOT NULL
);


--
-- TOC entry 245 (class 1259 OID 74499)
-- Name: application_method_target_rules; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.application_method_target_rules (
    application_method_id text NOT NULL,
    promotion_rule_id text NOT NULL
);


--
-- TOC entry 309 (class 1259 OID 75752)
-- Name: auth_identity; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.auth_identity (
    id text NOT NULL,
    app_metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 278 (class 1259 OID 75164)
-- Name: capture; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.capture (
    id text NOT NULL,
    amount numeric NOT NULL,
    raw_amount jsonb NOT NULL,
    payment_id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    created_by text,
    metadata jsonb
);


--
-- TOC entry 253 (class 1259 OID 74724)
-- Name: cart; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cart (
    id text NOT NULL,
    region_id text,
    customer_id text,
    sales_channel_id text,
    email text,
    currency_code text NOT NULL,
    shipping_address_id text,
    billing_address_id text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    completed_at timestamp with time zone
);


--
-- TOC entry 254 (class 1259 OID 74739)
-- Name: cart_address; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cart_address (
    id text NOT NULL,
    customer_id text,
    company text,
    first_name text,
    last_name text,
    address_1 text,
    address_2 text,
    city text,
    country_code text,
    province text,
    postal_code text,
    phone text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 255 (class 1259 OID 74748)
-- Name: cart_line_item; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cart_line_item (
    id text NOT NULL,
    cart_id text NOT NULL,
    title text NOT NULL,
    subtitle text,
    thumbnail text,
    quantity integer NOT NULL,
    variant_id text,
    product_id text,
    product_title text,
    product_description text,
    product_subtitle text,
    product_type text,
    product_collection text,
    product_handle text,
    variant_sku text,
    variant_barcode text,
    variant_title text,
    variant_option_values jsonb,
    requires_shipping boolean DEFAULT true NOT NULL,
    is_discountable boolean DEFAULT true NOT NULL,
    is_tax_inclusive boolean DEFAULT false NOT NULL,
    compare_at_unit_price numeric,
    raw_compare_at_unit_price jsonb,
    unit_price numeric NOT NULL,
    raw_unit_price jsonb NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    product_type_id text,
    is_custom_price boolean DEFAULT false NOT NULL,
    is_giftcard boolean DEFAULT false NOT NULL,
    CONSTRAINT cart_line_item_unit_price_check CHECK ((unit_price >= (0)::numeric))
);


--
-- TOC entry 256 (class 1259 OID 74774)
-- Name: cart_line_item_adjustment; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cart_line_item_adjustment (
    id text NOT NULL,
    description text,
    promotion_id text,
    code text,
    amount numeric NOT NULL,
    raw_amount jsonb NOT NULL,
    provider_id text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    item_id text,
    is_tax_inclusive boolean DEFAULT false NOT NULL,
    CONSTRAINT cart_line_item_adjustment_check CHECK ((amount >= (0)::numeric))
);


--
-- TOC entry 257 (class 1259 OID 74786)
-- Name: cart_line_item_tax_line; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cart_line_item_tax_line (
    id text NOT NULL,
    description text,
    tax_rate_id text,
    code text NOT NULL,
    rate real NOT NULL,
    provider_id text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    item_id text
);


--
-- TOC entry 347 (class 1259 OID 76282)
-- Name: cart_payment_collection; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cart_payment_collection (
    cart_id character varying(255) NOT NULL,
    payment_collection_id character varying(255) NOT NULL,
    id character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 330 (class 1259 OID 76113)
-- Name: cart_promotion; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cart_promotion (
    cart_id character varying(255) NOT NULL,
    promotion_id character varying(255) NOT NULL,
    id character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 258 (class 1259 OID 74797)
-- Name: cart_shipping_method; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cart_shipping_method (
    id text NOT NULL,
    cart_id text NOT NULL,
    name text NOT NULL,
    description jsonb,
    amount numeric NOT NULL,
    raw_amount jsonb NOT NULL,
    is_tax_inclusive boolean DEFAULT false NOT NULL,
    shipping_option_id text,
    data jsonb,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    CONSTRAINT cart_shipping_method_check CHECK ((amount >= (0)::numeric))
);


--
-- TOC entry 259 (class 1259 OID 74810)
-- Name: cart_shipping_method_adjustment; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cart_shipping_method_adjustment (
    id text NOT NULL,
    description text,
    promotion_id text,
    code text,
    amount numeric NOT NULL,
    raw_amount jsonb NOT NULL,
    provider_id text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    shipping_method_id text
);


--
-- TOC entry 260 (class 1259 OID 74821)
-- Name: cart_shipping_method_tax_line; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cart_shipping_method_tax_line (
    id text NOT NULL,
    description text,
    tax_rate_id text,
    code text NOT NULL,
    rate real NOT NULL,
    provider_id text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    shipping_method_id text
);


--
-- TOC entry 261 (class 1259 OID 74924)
-- Name: credit_line; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.credit_line (
    id text NOT NULL,
    cart_id text NOT NULL,
    reference text,
    reference_id text,
    amount numeric NOT NULL,
    raw_amount jsonb NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 271 (class 1259 OID 75089)
-- Name: currency; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.currency (
    code text NOT NULL,
    symbol text NOT NULL,
    symbol_native text NOT NULL,
    decimal_digits integer DEFAULT 0 NOT NULL,
    rounding numeric DEFAULT 0 NOT NULL,
    raw_rounding jsonb NOT NULL,
    name text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 248 (class 1259 OID 74636)
-- Name: customer; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.customer (
    id text NOT NULL,
    company_name text,
    first_name text,
    last_name text,
    email text,
    phone text,
    has_account boolean DEFAULT false NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    created_by text
);


--
-- TOC entry 346 (class 1259 OID 76275)
-- Name: customer_account_holder; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.customer_account_holder (
    customer_id character varying(255) NOT NULL,
    account_holder_id character varying(255) NOT NULL,
    id character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 249 (class 1259 OID 74646)
-- Name: customer_address; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.customer_address (
    id text NOT NULL,
    customer_id text NOT NULL,
    address_name text,
    is_default_shipping boolean DEFAULT false NOT NULL,
    is_default_billing boolean DEFAULT false NOT NULL,
    company text,
    first_name text,
    last_name text,
    address_1 text,
    address_2 text,
    city text,
    country_code text,
    province text,
    postal_code text,
    phone text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 250 (class 1259 OID 74660)
-- Name: customer_group; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.customer_group (
    id text NOT NULL,
    name text NOT NULL,
    metadata jsonb,
    created_by text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 251 (class 1259 OID 74670)
-- Name: customer_group_customer; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.customer_group_customer (
    id text NOT NULL,
    customer_id text NOT NULL,
    customer_group_id text NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    created_by text,
    deleted_at timestamp with time zone
);


--
-- TOC entry 322 (class 1259 OID 75918)
-- Name: fulfillment; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.fulfillment (
    id text NOT NULL,
    location_id text NOT NULL,
    packed_at timestamp with time zone,
    shipped_at timestamp with time zone,
    delivered_at timestamp with time zone,
    canceled_at timestamp with time zone,
    data jsonb,
    provider_id text,
    shipping_option_id text,
    metadata jsonb,
    delivery_address_id text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    marked_shipped_by text,
    created_by text,
    requires_shipping boolean DEFAULT true NOT NULL
);


--
-- TOC entry 313 (class 1259 OID 75808)
-- Name: fulfillment_address; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.fulfillment_address (
    id text NOT NULL,
    company text,
    first_name text,
    last_name text,
    address_1 text,
    address_2 text,
    city text,
    country_code text,
    province text,
    postal_code text,
    phone text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 324 (class 1259 OID 75944)
-- Name: fulfillment_item; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.fulfillment_item (
    id text NOT NULL,
    title text NOT NULL,
    sku text NOT NULL,
    barcode text NOT NULL,
    quantity numeric NOT NULL,
    raw_quantity jsonb NOT NULL,
    line_item_id text,
    inventory_item_id text,
    fulfillment_id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 323 (class 1259 OID 75933)
-- Name: fulfillment_label; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.fulfillment_label (
    id text NOT NULL,
    tracking_number text NOT NULL,
    tracking_url text NOT NULL,
    label_url text NOT NULL,
    fulfillment_id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 314 (class 1259 OID 75818)
-- Name: fulfillment_provider; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.fulfillment_provider (
    id text NOT NULL,
    is_enabled boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 315 (class 1259 OID 75827)
-- Name: fulfillment_set; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.fulfillment_set (
    id text NOT NULL,
    name text NOT NULL,
    type text NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 317 (class 1259 OID 75851)
-- Name: geo_zone; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.geo_zone (
    id text NOT NULL,
    type text DEFAULT 'country'::text NOT NULL,
    country_code text NOT NULL,
    province_code text,
    city text,
    service_zone_id text NOT NULL,
    postal_expression jsonb,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    CONSTRAINT geo_zone_type_check CHECK ((type = ANY (ARRAY['country'::text, 'province'::text, 'city'::text, 'zip'::text])))
);


--
-- TOC entry 225 (class 1259 OID 73933)
-- Name: image; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.image (
    id text NOT NULL,
    url text NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    rank integer DEFAULT 0 NOT NULL,
    product_id text NOT NULL
);


--
-- TOC entry 218 (class 1259 OID 73774)
-- Name: inventory_item; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.inventory_item (
    id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    sku text,
    origin_country text,
    hs_code text,
    mid_code text,
    material text,
    weight integer,
    length integer,
    height integer,
    width integer,
    requires_shipping boolean DEFAULT true NOT NULL,
    description text,
    title text,
    thumbnail text,
    metadata jsonb
);


--
-- TOC entry 219 (class 1259 OID 73786)
-- Name: inventory_level; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.inventory_level (
    id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    inventory_item_id text NOT NULL,
    location_id text NOT NULL,
    stocked_quantity numeric DEFAULT 0 NOT NULL,
    reserved_quantity numeric DEFAULT 0 NOT NULL,
    incoming_quantity numeric DEFAULT 0 NOT NULL,
    metadata jsonb,
    raw_stocked_quantity jsonb,
    raw_reserved_quantity jsonb,
    raw_incoming_quantity jsonb
);


--
-- TOC entry 311 (class 1259 OID 75781)
-- Name: invite; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.invite (
    id text NOT NULL,
    email text NOT NULL,
    accepted boolean DEFAULT false NOT NULL,
    token text NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 329 (class 1259 OID 76101)
-- Name: link_module_migrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.link_module_migrations (
    id integer NOT NULL,
    table_name character varying(255) NOT NULL,
    link_descriptor jsonb DEFAULT '{}'::jsonb NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- TOC entry 328 (class 1259 OID 76100)
-- Name: link_module_migrations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.link_module_migrations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- TOC entry 5141 (class 0 OID 0)
-- Dependencies: 328
-- Name: link_module_migrations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.link_module_migrations_id_seq OWNED BY public.link_module_migrations.id;


--
-- TOC entry 334 (class 1259 OID 76121)
-- Name: location_fulfillment_provider; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.location_fulfillment_provider (
    stock_location_id character varying(255) NOT NULL,
    fulfillment_provider_id character varying(255) NOT NULL,
    id character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 331 (class 1259 OID 76114)
-- Name: location_fulfillment_set; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.location_fulfillment_set (
    stock_location_id character varying(255) NOT NULL,
    fulfillment_set_id character varying(255) NOT NULL,
    id character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 215 (class 1259 OID 73733)
-- Name: mikro_orm_migrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.mikro_orm_migrations (
    id integer NOT NULL,
    name character varying(255),
    executed_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- TOC entry 214 (class 1259 OID 73732)
-- Name: mikro_orm_migrations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.mikro_orm_migrations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- TOC entry 5142 (class 0 OID 0)
-- Dependencies: 214
-- Name: mikro_orm_migrations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.mikro_orm_migrations_id_seq OWNED BY public.mikro_orm_migrations.id;


--
-- TOC entry 326 (class 1259 OID 76053)
-- Name: notification; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.notification (
    id text NOT NULL,
    "to" text NOT NULL,
    channel text NOT NULL,
    template text NOT NULL,
    data jsonb,
    trigger_type text,
    resource_id text,
    resource_type text,
    receiver_id text,
    original_notification_id text,
    idempotency_key text,
    external_id text,
    provider_id text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    status text DEFAULT 'pending'::text NOT NULL,
    CONSTRAINT notification_status_check CHECK ((status = ANY (ARRAY['pending'::text, 'success'::text, 'failure'::text])))
);


--
-- TOC entry 325 (class 1259 OID 76045)
-- Name: notification_provider; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.notification_provider (
    id text NOT NULL,
    handle text NOT NULL,
    name text NOT NULL,
    is_enabled boolean DEFAULT true NOT NULL,
    channels text[] DEFAULT '{}'::text[] NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 283 (class 1259 OID 75283)
-- Name: order; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public."order" (
    id text NOT NULL,
    region_id text,
    display_id integer,
    customer_id text,
    version integer DEFAULT 1 NOT NULL,
    sales_channel_id text,
    status public.order_status_enum DEFAULT 'pending'::public.order_status_enum NOT NULL,
    is_draft_order boolean DEFAULT false NOT NULL,
    email text,
    currency_code text NOT NULL,
    shipping_address_id text,
    billing_address_id text,
    no_notification boolean,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    canceled_at timestamp with time zone
);


--
-- TOC entry 281 (class 1259 OID 75272)
-- Name: order_address; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_address (
    id text NOT NULL,
    customer_id text,
    company text,
    first_name text,
    last_name text,
    address_1 text,
    address_2 text,
    city text,
    country_code text,
    province text,
    postal_code text,
    phone text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 333 (class 1259 OID 76117)
-- Name: order_cart; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_cart (
    order_id character varying(255) NOT NULL,
    cart_id character varying(255) NOT NULL,
    id character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 285 (class 1259 OID 75335)
-- Name: order_change; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_change (
    id text NOT NULL,
    order_id text NOT NULL,
    version integer NOT NULL,
    description text,
    status text DEFAULT 'pending'::text NOT NULL,
    internal_note text,
    created_by text,
    requested_by text,
    requested_at timestamp with time zone,
    confirmed_by text,
    confirmed_at timestamp with time zone,
    declined_by text,
    declined_reason text,
    metadata jsonb,
    declined_at timestamp with time zone,
    canceled_by text,
    canceled_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    change_type text,
    deleted_at timestamp with time zone,
    return_id text,
    claim_id text,
    exchange_id text,
    CONSTRAINT order_change_status_check CHECK ((status = ANY (ARRAY['confirmed'::text, 'declined'::text, 'requested'::text, 'pending'::text, 'canceled'::text])))
);


--
-- TOC entry 287 (class 1259 OID 75350)
-- Name: order_change_action; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_change_action (
    id text NOT NULL,
    order_id text,
    version integer,
    ordering bigint NOT NULL,
    order_change_id text,
    reference text,
    reference_id text,
    action text NOT NULL,
    details jsonb,
    amount numeric,
    raw_amount jsonb,
    internal_note text,
    applied boolean DEFAULT false NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    return_id text,
    claim_id text,
    exchange_id text
);


--
-- TOC entry 286 (class 1259 OID 75349)
-- Name: order_change_action_ordering_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.order_change_action_ordering_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- TOC entry 5143 (class 0 OID 0)
-- Dependencies: 286
-- Name: order_change_action_ordering_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.order_change_action_ordering_seq OWNED BY public.order_change_action.ordering;


--
-- TOC entry 305 (class 1259 OID 75636)
-- Name: order_claim; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_claim (
    id text NOT NULL,
    order_id text NOT NULL,
    return_id text,
    order_version integer NOT NULL,
    display_id integer NOT NULL,
    type public.order_claim_type_enum NOT NULL,
    no_notification boolean,
    refund_amount numeric,
    raw_refund_amount jsonb,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    canceled_at timestamp with time zone,
    created_by text
);


--
-- TOC entry 304 (class 1259 OID 75635)
-- Name: order_claim_display_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.order_claim_display_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- TOC entry 5144 (class 0 OID 0)
-- Dependencies: 304
-- Name: order_claim_display_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.order_claim_display_id_seq OWNED BY public.order_claim.display_id;


--
-- TOC entry 306 (class 1259 OID 75659)
-- Name: order_claim_item; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_claim_item (
    id text NOT NULL,
    claim_id text NOT NULL,
    item_id text NOT NULL,
    is_additional_item boolean DEFAULT false NOT NULL,
    reason public.claim_reason_enum,
    quantity numeric NOT NULL,
    raw_quantity jsonb NOT NULL,
    note text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 307 (class 1259 OID 75672)
-- Name: order_claim_item_image; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_claim_item_image (
    id text NOT NULL,
    claim_item_id text NOT NULL,
    url text NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 308 (class 1259 OID 75730)
-- Name: order_credit_line; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_credit_line (
    id text NOT NULL,
    order_id text NOT NULL,
    reference text,
    reference_id text,
    amount numeric NOT NULL,
    raw_amount jsonb NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 282 (class 1259 OID 75282)
-- Name: order_display_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.order_display_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- TOC entry 5145 (class 0 OID 0)
-- Dependencies: 282
-- Name: order_display_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.order_display_id_seq OWNED BY public."order".display_id;


--
-- TOC entry 302 (class 1259 OID 75602)
-- Name: order_exchange; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_exchange (
    id text NOT NULL,
    order_id text NOT NULL,
    return_id text,
    order_version integer NOT NULL,
    display_id integer NOT NULL,
    no_notification boolean,
    allow_backorder boolean DEFAULT false NOT NULL,
    difference_due numeric,
    raw_difference_due jsonb,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    canceled_at timestamp with time zone,
    created_by text
);


--
-- TOC entry 301 (class 1259 OID 75601)
-- Name: order_exchange_display_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.order_exchange_display_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- TOC entry 5146 (class 0 OID 0)
-- Dependencies: 301
-- Name: order_exchange_display_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.order_exchange_display_id_seq OWNED BY public.order_exchange.display_id;


--
-- TOC entry 303 (class 1259 OID 75617)
-- Name: order_exchange_item; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_exchange_item (
    id text NOT NULL,
    exchange_id text NOT NULL,
    item_id text NOT NULL,
    quantity numeric NOT NULL,
    raw_quantity jsonb NOT NULL,
    note text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 332 (class 1259 OID 76116)
-- Name: order_fulfillment; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_fulfillment (
    order_id character varying(255) NOT NULL,
    fulfillment_id character varying(255) NOT NULL,
    id character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 288 (class 1259 OID 75364)
-- Name: order_item; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_item (
    id text NOT NULL,
    order_id text NOT NULL,
    version integer NOT NULL,
    item_id text NOT NULL,
    quantity numeric NOT NULL,
    raw_quantity jsonb NOT NULL,
    fulfilled_quantity numeric NOT NULL,
    raw_fulfilled_quantity jsonb NOT NULL,
    shipped_quantity numeric NOT NULL,
    raw_shipped_quantity jsonb NOT NULL,
    return_requested_quantity numeric NOT NULL,
    raw_return_requested_quantity jsonb NOT NULL,
    return_received_quantity numeric NOT NULL,
    raw_return_received_quantity jsonb NOT NULL,
    return_dismissed_quantity numeric NOT NULL,
    raw_return_dismissed_quantity jsonb NOT NULL,
    written_off_quantity numeric NOT NULL,
    raw_written_off_quantity jsonb NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    delivered_quantity numeric DEFAULT 0 NOT NULL,
    raw_delivered_quantity jsonb NOT NULL,
    unit_price numeric,
    raw_unit_price jsonb,
    compare_at_unit_price numeric,
    raw_compare_at_unit_price jsonb
);


--
-- TOC entry 290 (class 1259 OID 75388)
-- Name: order_line_item; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_line_item (
    id text NOT NULL,
    totals_id text,
    title text NOT NULL,
    subtitle text,
    thumbnail text,
    variant_id text,
    product_id text,
    product_title text,
    product_description text,
    product_subtitle text,
    product_type text,
    product_collection text,
    product_handle text,
    variant_sku text,
    variant_barcode text,
    variant_title text,
    variant_option_values jsonb,
    requires_shipping boolean DEFAULT true NOT NULL,
    is_discountable boolean DEFAULT true NOT NULL,
    is_tax_inclusive boolean DEFAULT false NOT NULL,
    compare_at_unit_price numeric,
    raw_compare_at_unit_price jsonb,
    unit_price numeric NOT NULL,
    raw_unit_price jsonb NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    is_custom_price boolean DEFAULT false NOT NULL,
    product_type_id text,
    is_giftcard boolean DEFAULT false NOT NULL
);


--
-- TOC entry 292 (class 1259 OID 75412)
-- Name: order_line_item_adjustment; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_line_item_adjustment (
    id text NOT NULL,
    description text,
    promotion_id text,
    code text,
    amount numeric NOT NULL,
    raw_amount jsonb NOT NULL,
    provider_id text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    item_id text NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 291 (class 1259 OID 75402)
-- Name: order_line_item_tax_line; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_line_item_tax_line (
    id text NOT NULL,
    description text,
    tax_rate_id text,
    code text NOT NULL,
    rate numeric NOT NULL,
    raw_rate jsonb NOT NULL,
    provider_id text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    item_id text NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 335 (class 1259 OID 76132)
-- Name: order_payment_collection; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_payment_collection (
    order_id character varying(255) NOT NULL,
    payment_collection_id character varying(255) NOT NULL,
    id character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 336 (class 1259 OID 76151)
-- Name: order_promotion; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_promotion (
    order_id character varying(255) NOT NULL,
    promotion_id character varying(255) NOT NULL,
    id character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 289 (class 1259 OID 75376)
-- Name: order_shipping; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_shipping (
    id text NOT NULL,
    order_id text NOT NULL,
    version integer NOT NULL,
    shipping_method_id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    return_id text,
    claim_id text,
    exchange_id text
);


--
-- TOC entry 293 (class 1259 OID 75422)
-- Name: order_shipping_method; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_shipping_method (
    id text NOT NULL,
    name text NOT NULL,
    description jsonb,
    amount numeric NOT NULL,
    raw_amount jsonb NOT NULL,
    is_tax_inclusive boolean DEFAULT false NOT NULL,
    shipping_option_id text,
    data jsonb,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    is_custom_amount boolean DEFAULT false NOT NULL
);


--
-- TOC entry 294 (class 1259 OID 75433)
-- Name: order_shipping_method_adjustment; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_shipping_method_adjustment (
    id text NOT NULL,
    description text,
    promotion_id text,
    code text,
    amount numeric NOT NULL,
    raw_amount jsonb NOT NULL,
    provider_id text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    shipping_method_id text NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 295 (class 1259 OID 75443)
-- Name: order_shipping_method_tax_line; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_shipping_method_tax_line (
    id text NOT NULL,
    description text,
    tax_rate_id text,
    code text NOT NULL,
    rate numeric NOT NULL,
    raw_rate jsonb NOT NULL,
    provider_id text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    shipping_method_id text NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 284 (class 1259 OID 75324)
-- Name: order_summary; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_summary (
    id text NOT NULL,
    order_id text NOT NULL,
    version integer DEFAULT 1 NOT NULL,
    totals jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 296 (class 1259 OID 75453)
-- Name: order_transaction; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_transaction (
    id text NOT NULL,
    order_id text NOT NULL,
    version integer DEFAULT 1 NOT NULL,
    amount numeric NOT NULL,
    raw_amount jsonb NOT NULL,
    currency_code text NOT NULL,
    reference text,
    reference_id text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    return_id text,
    claim_id text,
    exchange_id text
);


--
-- TOC entry 276 (class 1259 OID 75146)
-- Name: payment; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.payment (
    id text NOT NULL,
    amount numeric NOT NULL,
    raw_amount jsonb NOT NULL,
    currency_code text NOT NULL,
    provider_id text NOT NULL,
    data jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    captured_at timestamp with time zone,
    canceled_at timestamp with time zone,
    payment_collection_id text NOT NULL,
    payment_session_id text NOT NULL,
    metadata jsonb
);


--
-- TOC entry 272 (class 1259 OID 75100)
-- Name: payment_collection; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.payment_collection (
    id text NOT NULL,
    currency_code text NOT NULL,
    amount numeric NOT NULL,
    raw_amount jsonb NOT NULL,
    authorized_amount numeric,
    raw_authorized_amount jsonb,
    captured_amount numeric,
    raw_captured_amount jsonb,
    refunded_amount numeric,
    raw_refunded_amount jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    completed_at timestamp with time zone,
    status text DEFAULT 'not_paid'::text NOT NULL,
    metadata jsonb,
    CONSTRAINT payment_collection_status_check CHECK ((status = ANY (ARRAY['not_paid'::text, 'awaiting'::text, 'authorized'::text, 'partially_authorized'::text, 'canceled'::text, 'failed'::text, 'partially_captured'::text, 'completed'::text])))
);


--
-- TOC entry 274 (class 1259 OID 75128)
-- Name: payment_collection_payment_providers; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.payment_collection_payment_providers (
    payment_collection_id text NOT NULL,
    payment_provider_id text NOT NULL
);


--
-- TOC entry 273 (class 1259 OID 75120)
-- Name: payment_provider; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.payment_provider (
    id text NOT NULL,
    is_enabled boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 275 (class 1259 OID 75135)
-- Name: payment_session; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.payment_session (
    id text NOT NULL,
    currency_code text NOT NULL,
    amount numeric NOT NULL,
    raw_amount jsonb NOT NULL,
    provider_id text NOT NULL,
    data jsonb DEFAULT '{}'::jsonb NOT NULL,
    context jsonb,
    status text DEFAULT 'pending'::text NOT NULL,
    authorized_at timestamp with time zone,
    payment_collection_id text NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    CONSTRAINT payment_session_status_check CHECK ((status = ANY (ARRAY['authorized'::text, 'captured'::text, 'pending'::text, 'requires_more'::text, 'error'::text, 'canceled'::text])))
);


--
-- TOC entry 234 (class 1259 OID 74199)
-- Name: price; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.price (
    id text NOT NULL,
    title text,
    price_set_id text NOT NULL,
    currency_code text NOT NULL,
    raw_amount jsonb NOT NULL,
    rules_count integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    price_list_id text,
    amount numeric NOT NULL,
    min_quantity integer,
    max_quantity integer
);


--
-- TOC entry 236 (class 1259 OID 74275)
-- Name: price_list; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.price_list (
    id text NOT NULL,
    status text DEFAULT 'draft'::text NOT NULL,
    starts_at timestamp with time zone,
    ends_at timestamp with time zone,
    rules_count integer DEFAULT 0,
    title text NOT NULL,
    description text NOT NULL,
    type text DEFAULT 'sale'::text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    CONSTRAINT price_list_status_check CHECK ((status = ANY (ARRAY['active'::text, 'draft'::text]))),
    CONSTRAINT price_list_type_check CHECK ((type = ANY (ARRAY['sale'::text, 'override'::text])))
);


--
-- TOC entry 237 (class 1259 OID 74285)
-- Name: price_list_rule; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.price_list_rule (
    id text NOT NULL,
    price_list_id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    value jsonb,
    attribute text DEFAULT ''::text NOT NULL
);


--
-- TOC entry 238 (class 1259 OID 74380)
-- Name: price_preference; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.price_preference (
    id text NOT NULL,
    attribute text NOT NULL,
    value text,
    is_tax_inclusive boolean DEFAULT false NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 235 (class 1259 OID 74230)
-- Name: price_rule; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.price_rule (
    id text NOT NULL,
    value text NOT NULL,
    priority integer DEFAULT 0 NOT NULL,
    price_id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    attribute text DEFAULT ''::text NOT NULL,
    operator text DEFAULT 'eq'::text NOT NULL,
    CONSTRAINT price_rule_operator_check CHECK ((operator = ANY (ARRAY['gte'::text, 'lte'::text, 'gt'::text, 'lt'::text, 'eq'::text])))
);


--
-- TOC entry 233 (class 1259 OID 74190)
-- Name: price_set; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.price_set (
    id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 221 (class 1259 OID 73877)
-- Name: product; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product (
    id text NOT NULL,
    title text NOT NULL,
    handle text NOT NULL,
    subtitle text,
    description text,
    is_giftcard boolean DEFAULT false NOT NULL,
    status text DEFAULT 'draft'::text NOT NULL,
    thumbnail text,
    weight text,
    length text,
    height text,
    width text,
    origin_country text,
    hs_code text,
    mid_code text,
    material text,
    collection_id text,
    type_id text,
    discountable boolean DEFAULT true NOT NULL,
    external_id text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    metadata jsonb,
    CONSTRAINT product_status_check CHECK ((status = ANY (ARRAY['draft'::text, 'proposed'::text, 'published'::text, 'rejected'::text])))
);


--
-- TOC entry 229 (class 1259 OID 73977)
-- Name: product_category; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_category (
    id text NOT NULL,
    name text NOT NULL,
    description text DEFAULT ''::text NOT NULL,
    handle text NOT NULL,
    mpath text NOT NULL,
    is_active boolean DEFAULT false NOT NULL,
    is_internal boolean DEFAULT false NOT NULL,
    rank integer DEFAULT 0 NOT NULL,
    parent_category_id text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    metadata jsonb
);


--
-- TOC entry 231 (class 1259 OID 74007)
-- Name: product_category_product; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_category_product (
    product_id text NOT NULL,
    product_category_id text NOT NULL
);


--
-- TOC entry 228 (class 1259 OID 73966)
-- Name: product_collection; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_collection (
    id text NOT NULL,
    title text NOT NULL,
    handle text NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 223 (class 1259 OID 73911)
-- Name: product_option; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_option (
    id text NOT NULL,
    title text NOT NULL,
    product_id text NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 224 (class 1259 OID 73922)
-- Name: product_option_value; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_option_value (
    id text NOT NULL,
    value text NOT NULL,
    option_id text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 338 (class 1259 OID 76161)
-- Name: product_sales_channel; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_sales_channel (
    product_id character varying(255) NOT NULL,
    sales_channel_id character varying(255) NOT NULL,
    id character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 345 (class 1259 OID 76267)
-- Name: product_shipping_profile; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_shipping_profile (
    product_id character varying(255) NOT NULL,
    shipping_profile_id character varying(255) NOT NULL,
    id character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 226 (class 1259 OID 73944)
-- Name: product_tag; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_tag (
    id text NOT NULL,
    value text NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 230 (class 1259 OID 73993)
-- Name: product_tags; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_tags (
    product_id text NOT NULL,
    product_tag_id text NOT NULL
);


--
-- TOC entry 227 (class 1259 OID 73955)
-- Name: product_type; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_type (
    id text NOT NULL,
    value text NOT NULL,
    metadata json,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 222 (class 1259 OID 73893)
-- Name: product_variant; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_variant (
    id text NOT NULL,
    title text NOT NULL,
    sku text,
    barcode text,
    ean text,
    upc text,
    allow_backorder boolean DEFAULT false NOT NULL,
    manage_inventory boolean DEFAULT true NOT NULL,
    hs_code text,
    origin_country text,
    mid_code text,
    material text,
    weight integer,
    length integer,
    height integer,
    width integer,
    metadata jsonb,
    variant_rank integer DEFAULT 0,
    product_id text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 337 (class 1259 OID 76152)
-- Name: product_variant_inventory_item; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_variant_inventory_item (
    variant_id character varying(255) NOT NULL,
    inventory_item_id character varying(255) NOT NULL,
    id character varying(255) NOT NULL,
    required_quantity integer DEFAULT 1 NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 232 (class 1259 OID 74014)
-- Name: product_variant_option; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_variant_option (
    variant_id text NOT NULL,
    option_value_id text NOT NULL
);


--
-- TOC entry 340 (class 1259 OID 76244)
-- Name: product_variant_price_set; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_variant_price_set (
    variant_id character varying(255) NOT NULL,
    price_set_id character varying(255) NOT NULL,
    id character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 241 (class 1259 OID 74448)
-- Name: promotion; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.promotion (
    id text NOT NULL,
    code text NOT NULL,
    campaign_id text,
    is_automatic boolean DEFAULT false NOT NULL,
    type text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    status text DEFAULT 'draft'::text NOT NULL,
    is_tax_inclusive boolean DEFAULT false NOT NULL,
    CONSTRAINT promotion_status_check CHECK ((status = ANY (ARRAY['draft'::text, 'active'::text, 'inactive'::text]))),
    CONSTRAINT promotion_type_check CHECK ((type = ANY (ARRAY['standard'::text, 'buyget'::text])))
);


--
-- TOC entry 242 (class 1259 OID 74463)
-- Name: promotion_application_method; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.promotion_application_method (
    id text NOT NULL,
    value numeric,
    raw_value jsonb,
    max_quantity integer,
    apply_to_quantity integer,
    buy_rules_min_quantity integer,
    type text NOT NULL,
    target_type text NOT NULL,
    allocation text,
    promotion_id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    currency_code text,
    CONSTRAINT promotion_application_method_allocation_check CHECK ((allocation = ANY (ARRAY['each'::text, 'across'::text]))),
    CONSTRAINT promotion_application_method_target_type_check CHECK ((target_type = ANY (ARRAY['order'::text, 'shipping_methods'::text, 'items'::text]))),
    CONSTRAINT promotion_application_method_type_check CHECK ((type = ANY (ARRAY['fixed'::text, 'percentage'::text])))
);


--
-- TOC entry 239 (class 1259 OID 74423)
-- Name: promotion_campaign; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.promotion_campaign (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    campaign_identifier text NOT NULL,
    starts_at timestamp with time zone,
    ends_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 240 (class 1259 OID 74434)
-- Name: promotion_campaign_budget; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.promotion_campaign_budget (
    id text NOT NULL,
    type text NOT NULL,
    campaign_id text NOT NULL,
    "limit" numeric,
    raw_limit jsonb,
    used numeric DEFAULT 0 NOT NULL,
    raw_used jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    currency_code text,
    CONSTRAINT promotion_campaign_budget_type_check CHECK ((type = ANY (ARRAY['spend'::text, 'usage'::text])))
);


--
-- TOC entry 244 (class 1259 OID 74492)
-- Name: promotion_promotion_rule; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.promotion_promotion_rule (
    promotion_id text NOT NULL,
    promotion_rule_id text NOT NULL
);


--
-- TOC entry 243 (class 1259 OID 74480)
-- Name: promotion_rule; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.promotion_rule (
    id text NOT NULL,
    description text,
    attribute text NOT NULL,
    operator text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    CONSTRAINT promotion_rule_operator_check CHECK ((operator = ANY (ARRAY['gte'::text, 'lte'::text, 'gt'::text, 'lt'::text, 'eq'::text, 'ne'::text, 'in'::text])))
);


--
-- TOC entry 247 (class 1259 OID 74513)
-- Name: promotion_rule_value; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.promotion_rule_value (
    id text NOT NULL,
    promotion_rule_id text NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 310 (class 1259 OID 75761)
-- Name: provider_identity; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.provider_identity (
    id text NOT NULL,
    entity_id text NOT NULL,
    provider text NOT NULL,
    auth_identity_id text NOT NULL,
    user_metadata jsonb,
    provider_metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 341 (class 1259 OID 76251)
-- Name: publishable_api_key_sales_channel; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.publishable_api_key_sales_channel (
    publishable_key_id character varying(255) NOT NULL,
    sales_channel_id character varying(255) NOT NULL,
    id character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 277 (class 1259 OID 75155)
-- Name: refund; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.refund (
    id text NOT NULL,
    amount numeric NOT NULL,
    raw_amount jsonb NOT NULL,
    payment_id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    created_by text,
    metadata jsonb,
    refund_reason_id text,
    note text
);


--
-- TOC entry 279 (class 1259 OID 75214)
-- Name: refund_reason; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.refund_reason (
    id text NOT NULL,
    label text NOT NULL,
    description text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 262 (class 1259 OID 74943)
-- Name: region; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.region (
    id text NOT NULL,
    name text NOT NULL,
    currency_code text NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    automatic_taxes boolean DEFAULT true NOT NULL
);


--
-- TOC entry 263 (class 1259 OID 74954)
-- Name: region_country; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.region_country (
    iso_2 text NOT NULL,
    iso_3 text NOT NULL,
    num_code text NOT NULL,
    name text NOT NULL,
    display_name text NOT NULL,
    region_id text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 343 (class 1259 OID 76259)
-- Name: region_payment_provider; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.region_payment_provider (
    region_id character varying(255) NOT NULL,
    payment_provider_id character varying(255) NOT NULL,
    id character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 220 (class 1259 OID 73801)
-- Name: reservation_item; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.reservation_item (
    id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    line_item_id text,
    location_id text NOT NULL,
    quantity numeric NOT NULL,
    external_id text,
    description text,
    created_by text,
    metadata jsonb,
    inventory_item_id text NOT NULL,
    allow_backorder boolean DEFAULT false,
    raw_quantity jsonb
);


--
-- TOC entry 299 (class 1259 OID 75572)
-- Name: return; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.return (
    id text NOT NULL,
    order_id text NOT NULL,
    claim_id text,
    exchange_id text,
    order_version integer NOT NULL,
    display_id integer NOT NULL,
    status public.return_status_enum DEFAULT 'open'::public.return_status_enum NOT NULL,
    no_notification boolean,
    refund_amount numeric,
    raw_refund_amount jsonb,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    received_at timestamp with time zone,
    canceled_at timestamp with time zone,
    location_id text,
    requested_at timestamp with time zone,
    created_by text
);


--
-- TOC entry 298 (class 1259 OID 75571)
-- Name: return_display_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.return_display_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- TOC entry 5147 (class 0 OID 0)
-- Dependencies: 298
-- Name: return_display_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.return_display_id_seq OWNED BY public.return.display_id;


--
-- TOC entry 339 (class 1259 OID 76162)
-- Name: return_fulfillment; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.return_fulfillment (
    return_id character varying(255) NOT NULL,
    fulfillment_id character varying(255) NOT NULL,
    id character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 300 (class 1259 OID 75587)
-- Name: return_item; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.return_item (
    id text NOT NULL,
    return_id text NOT NULL,
    reason_id text,
    item_id text NOT NULL,
    quantity numeric NOT NULL,
    raw_quantity jsonb NOT NULL,
    received_quantity numeric DEFAULT 0 NOT NULL,
    raw_received_quantity jsonb NOT NULL,
    note text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    damaged_quantity numeric DEFAULT 0 NOT NULL,
    raw_damaged_quantity jsonb NOT NULL
);


--
-- TOC entry 297 (class 1259 OID 75466)
-- Name: return_reason; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.return_reason (
    id character varying NOT NULL,
    value character varying NOT NULL,
    label character varying NOT NULL,
    description character varying,
    metadata jsonb,
    parent_return_reason_id character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 252 (class 1259 OID 74713)
-- Name: sales_channel; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.sales_channel (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    is_disabled boolean DEFAULT false NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 342 (class 1259 OID 76258)
-- Name: sales_channel_stock_location; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.sales_channel_stock_location (
    sales_channel_id character varying(255) NOT NULL,
    stock_location_id character varying(255) NOT NULL,
    id character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 349 (class 1259 OID 76349)
-- Name: script_migrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.script_migrations (
    id integer NOT NULL,
    script_name character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    finished_at timestamp with time zone
);


--
-- TOC entry 348 (class 1259 OID 76348)
-- Name: script_migrations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.script_migrations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- TOC entry 5148 (class 0 OID 0)
-- Dependencies: 348
-- Name: script_migrations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.script_migrations_id_seq OWNED BY public.script_migrations.id;


--
-- TOC entry 316 (class 1259 OID 75839)
-- Name: service_zone; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.service_zone (
    id text NOT NULL,
    name text NOT NULL,
    metadata jsonb,
    fulfillment_set_id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 320 (class 1259 OID 75888)
-- Name: shipping_option; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.shipping_option (
    id text NOT NULL,
    name text NOT NULL,
    price_type text DEFAULT 'flat'::text NOT NULL,
    service_zone_id text NOT NULL,
    shipping_profile_id text,
    provider_id text,
    data jsonb,
    metadata jsonb,
    shipping_option_type_id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    CONSTRAINT shipping_option_price_type_check CHECK ((price_type = ANY (ARRAY['calculated'::text, 'flat'::text])))
);


--
-- TOC entry 344 (class 1259 OID 76264)
-- Name: shipping_option_price_set; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.shipping_option_price_set (
    shipping_option_id character varying(255) NOT NULL,
    price_set_id character varying(255) NOT NULL,
    id character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 321 (class 1259 OID 75906)
-- Name: shipping_option_rule; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.shipping_option_rule (
    id text NOT NULL,
    attribute text NOT NULL,
    operator text NOT NULL,
    value jsonb,
    shipping_option_id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    CONSTRAINT shipping_option_rule_operator_check CHECK ((operator = ANY (ARRAY['in'::text, 'eq'::text, 'ne'::text, 'gt'::text, 'gte'::text, 'lt'::text, 'lte'::text, 'nin'::text])))
);


--
-- TOC entry 318 (class 1259 OID 75867)
-- Name: shipping_option_type; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.shipping_option_type (
    id text NOT NULL,
    label text NOT NULL,
    description text,
    code text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 319 (class 1259 OID 75877)
-- Name: shipping_profile; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.shipping_profile (
    id text NOT NULL,
    name text NOT NULL,
    type text NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 217 (class 1259 OID 73750)
-- Name: stock_location; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.stock_location (
    id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    name text NOT NULL,
    address_id text,
    metadata jsonb
);


--
-- TOC entry 216 (class 1259 OID 73740)
-- Name: stock_location_address; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.stock_location_address (
    id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    address_1 text NOT NULL,
    address_2 text,
    company text,
    city text,
    country_code text NOT NULL,
    phone text,
    province text,
    postal_code text,
    metadata jsonb
);


--
-- TOC entry 265 (class 1259 OID 74985)
-- Name: store; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.store (
    id text NOT NULL,
    name text DEFAULT 'Medusa Store'::text NOT NULL,
    default_sales_channel_id text,
    default_region_id text,
    default_location_id text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 266 (class 1259 OID 74997)
-- Name: store_currency; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.store_currency (
    id text NOT NULL,
    currency_code text NOT NULL,
    is_default boolean DEFAULT false NOT NULL,
    store_id text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 267 (class 1259 OID 75014)
-- Name: tax_provider; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.tax_provider (
    id text NOT NULL,
    is_enabled boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 269 (class 1259 OID 75036)
-- Name: tax_rate; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.tax_rate (
    id text NOT NULL,
    rate real,
    code text NOT NULL,
    name text NOT NULL,
    is_default boolean DEFAULT false NOT NULL,
    is_combinable boolean DEFAULT false NOT NULL,
    tax_region_id text NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    created_by text,
    deleted_at timestamp with time zone
);


--
-- TOC entry 270 (class 1259 OID 75050)
-- Name: tax_rate_rule; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.tax_rate_rule (
    id text NOT NULL,
    tax_rate_id text NOT NULL,
    reference_id text NOT NULL,
    reference text NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    created_by text,
    deleted_at timestamp with time zone
);


--
-- TOC entry 268 (class 1259 OID 75022)
-- Name: tax_region; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.tax_region (
    id text NOT NULL,
    provider_id text,
    country_code text NOT NULL,
    province_code text,
    parent_id text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    created_by text,
    deleted_at timestamp with time zone,
    CONSTRAINT "CK_tax_region_country_top_level" CHECK (((parent_id IS NULL) OR (province_code IS NOT NULL))),
    CONSTRAINT "CK_tax_region_provider_top_level" CHECK (((parent_id IS NULL) OR (provider_id IS NULL)))
);


--
-- TOC entry 312 (class 1259 OID 75794)
-- Name: user; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public."user" (
    id text NOT NULL,
    first_name text,
    last_name text,
    email text NOT NULL,
    avatar_url text,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);


--
-- TOC entry 327 (class 1259 OID 76078)
-- Name: workflow_execution; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.workflow_execution (
    id character varying NOT NULL,
    workflow_id character varying NOT NULL,
    transaction_id character varying NOT NULL,
    execution jsonb,
    context jsonb,
    state character varying NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    deleted_at timestamp without time zone,
    retention_time integer,
    run_id text DEFAULT '01JZMCJV6Y9SVK73CEZBM8EB31'::text NOT NULL
);


--
-- TOC entry 4076 (class 2604 OID 76104)
-- Name: link_module_migrations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.link_module_migrations ALTER COLUMN id SET DEFAULT nextval('public.link_module_migrations_id_seq'::regclass);


--
-- TOC entry 3787 (class 2604 OID 73736)
-- Name: mikro_orm_migrations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.mikro_orm_migrations ALTER COLUMN id SET DEFAULT nextval('public.mikro_orm_migrations_id_seq'::regclass);


--
-- TOC entry 3960 (class 2604 OID 75286)
-- Name: order display_id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public."order" ALTER COLUMN display_id SET DEFAULT nextval('public.order_display_id_seq'::regclass);


--
-- TOC entry 3972 (class 2604 OID 75353)
-- Name: order_change_action ordering; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_change_action ALTER COLUMN ordering SET DEFAULT nextval('public.order_change_action_ordering_seq'::regclass);


--
-- TOC entry 4019 (class 2604 OID 75639)
-- Name: order_claim display_id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_claim ALTER COLUMN display_id SET DEFAULT nextval('public.order_claim_display_id_seq'::regclass);


--
-- TOC entry 4013 (class 2604 OID 75605)
-- Name: order_exchange display_id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_exchange ALTER COLUMN display_id SET DEFAULT nextval('public.order_exchange_display_id_seq'::regclass);


--
-- TOC entry 4005 (class 2604 OID 75575)
-- Name: return display_id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.return ALTER COLUMN display_id SET DEFAULT nextval('public.return_display_id_seq'::regclass);


--
-- TOC entry 4116 (class 2604 OID 76352)
-- Name: script_migrations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.script_migrations ALTER COLUMN id SET DEFAULT nextval('public.script_migrations_id_seq'::regclass);


--
-- TOC entry 5066 (class 0 OID 75248)
-- Dependencies: 280
-- Data for Name: account_holder; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.account_holder (id, provider_id, external_id, email, data, metadata, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5050 (class 0 OID 74971)
-- Dependencies: 264
-- Data for Name: api_key; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.api_key (id, token, salt, redacted, title, type, last_used_at, created_by, created_at, revoked_by, revoked_at, updated_at, deleted_at) FROM stdin;
apk_01JZMCKAXRVQR78Q84G3E3Z0W5	pk_2c7c19e4ace03ba7f2d04f93936f2c37425b96a02d491e5d0d2c5b698b1b311c		pk_2c7***11c	Webshop	publishable	\N		2025-07-08 06:47:00.28+00	\N	\N	2025-07-08 06:47:00.28+00	\N
\.


--
-- TOC entry 5032 (class 0 OID 74506)
-- Dependencies: 246
-- Data for Name: application_method_buy_rules; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.application_method_buy_rules (application_method_id, promotion_rule_id) FROM stdin;
\.


--
-- TOC entry 5031 (class 0 OID 74499)
-- Dependencies: 245
-- Data for Name: application_method_target_rules; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.application_method_target_rules (application_method_id, promotion_rule_id) FROM stdin;
\.


--
-- TOC entry 5095 (class 0 OID 75752)
-- Dependencies: 309
-- Data for Name: auth_identity; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.auth_identity (id, app_metadata, created_at, updated_at, deleted_at) FROM stdin;
authid_01JZMCVZ4P8NN0ZN3MX85GCV4V	{"user_id": "user_01JZMCVZ7952TEQF6MG8QJX755"}	2025-07-08 06:51:43.127+00	2025-07-08 06:51:43.238+00	\N
\.


--
-- TOC entry 5064 (class 0 OID 75164)
-- Dependencies: 278
-- Data for Name: capture; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.capture (id, amount, raw_amount, payment_id, created_at, updated_at, deleted_at, created_by, metadata) FROM stdin;
\.


--
-- TOC entry 5039 (class 0 OID 74724)
-- Dependencies: 253
-- Data for Name: cart; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.cart (id, region_id, customer_id, sales_channel_id, email, currency_code, shipping_address_id, billing_address_id, metadata, created_at, updated_at, deleted_at, completed_at) FROM stdin;
\.


--
-- TOC entry 5040 (class 0 OID 74739)
-- Dependencies: 254
-- Data for Name: cart_address; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.cart_address (id, customer_id, company, first_name, last_name, address_1, address_2, city, country_code, province, postal_code, phone, metadata, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5041 (class 0 OID 74748)
-- Dependencies: 255
-- Data for Name: cart_line_item; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.cart_line_item (id, cart_id, title, subtitle, thumbnail, quantity, variant_id, product_id, product_title, product_description, product_subtitle, product_type, product_collection, product_handle, variant_sku, variant_barcode, variant_title, variant_option_values, requires_shipping, is_discountable, is_tax_inclusive, compare_at_unit_price, raw_compare_at_unit_price, unit_price, raw_unit_price, metadata, created_at, updated_at, deleted_at, product_type_id, is_custom_price, is_giftcard) FROM stdin;
\.


--
-- TOC entry 5042 (class 0 OID 74774)
-- Dependencies: 256
-- Data for Name: cart_line_item_adjustment; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.cart_line_item_adjustment (id, description, promotion_id, code, amount, raw_amount, provider_id, metadata, created_at, updated_at, deleted_at, item_id, is_tax_inclusive) FROM stdin;
\.


--
-- TOC entry 5043 (class 0 OID 74786)
-- Dependencies: 257
-- Data for Name: cart_line_item_tax_line; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.cart_line_item_tax_line (id, description, tax_rate_id, code, rate, provider_id, metadata, created_at, updated_at, deleted_at, item_id) FROM stdin;
\.


--
-- TOC entry 5133 (class 0 OID 76282)
-- Dependencies: 347
-- Data for Name: cart_payment_collection; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.cart_payment_collection (cart_id, payment_collection_id, id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5116 (class 0 OID 76113)
-- Dependencies: 330
-- Data for Name: cart_promotion; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.cart_promotion (cart_id, promotion_id, id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5044 (class 0 OID 74797)
-- Dependencies: 258
-- Data for Name: cart_shipping_method; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.cart_shipping_method (id, cart_id, name, description, amount, raw_amount, is_tax_inclusive, shipping_option_id, data, metadata, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5045 (class 0 OID 74810)
-- Dependencies: 259
-- Data for Name: cart_shipping_method_adjustment; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.cart_shipping_method_adjustment (id, description, promotion_id, code, amount, raw_amount, provider_id, metadata, created_at, updated_at, deleted_at, shipping_method_id) FROM stdin;
\.


--
-- TOC entry 5046 (class 0 OID 74821)
-- Dependencies: 260
-- Data for Name: cart_shipping_method_tax_line; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.cart_shipping_method_tax_line (id, description, tax_rate_id, code, rate, provider_id, metadata, created_at, updated_at, deleted_at, shipping_method_id) FROM stdin;
\.


--
-- TOC entry 5047 (class 0 OID 74924)
-- Dependencies: 261
-- Data for Name: credit_line; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.credit_line (id, cart_id, reference, reference_id, amount, raw_amount, metadata, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5057 (class 0 OID 75089)
-- Dependencies: 271
-- Data for Name: currency; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.currency (code, symbol, symbol_native, decimal_digits, rounding, raw_rounding, name, created_at, updated_at, deleted_at) FROM stdin;
usd	$	$	2	0	{"value": "0", "precision": 20}	US Dollar	2025-07-08 06:46:50.919+00	2025-07-08 06:46:50.919+00	\N
cad	CA$	$	2	0	{"value": "0", "precision": 20}	Canadian Dollar	2025-07-08 06:46:50.92+00	2025-07-08 06:46:50.92+00	\N
eur	€	€	2	0	{"value": "0", "precision": 20}	Euro	2025-07-08 06:46:50.92+00	2025-07-08 06:46:50.92+00	\N
aed	AED	د.إ.‏	2	0	{"value": "0", "precision": 20}	United Arab Emirates Dirham	2025-07-08 06:46:50.92+00	2025-07-08 06:46:50.92+00	\N
afn	Af	؋	0	0	{"value": "0", "precision": 20}	Afghan Afghani	2025-07-08 06:46:50.92+00	2025-07-08 06:46:50.92+00	\N
all	ALL	Lek	0	0	{"value": "0", "precision": 20}	Albanian Lek	2025-07-08 06:46:50.92+00	2025-07-08 06:46:50.92+00	\N
amd	AMD	դր.	0	0	{"value": "0", "precision": 20}	Armenian Dram	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
ars	AR$	$	2	0	{"value": "0", "precision": 20}	Argentine Peso	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
aud	AU$	$	2	0	{"value": "0", "precision": 20}	Australian Dollar	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
azn	man.	ман.	2	0	{"value": "0", "precision": 20}	Azerbaijani Manat	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
bam	KM	KM	2	0	{"value": "0", "precision": 20}	Bosnia-Herzegovina Convertible Mark	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
bdt	Tk	৳	2	0	{"value": "0", "precision": 20}	Bangladeshi Taka	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
bgn	BGN	лв.	2	0	{"value": "0", "precision": 20}	Bulgarian Lev	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
bhd	BD	د.ب.‏	3	0	{"value": "0", "precision": 20}	Bahraini Dinar	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
bif	FBu	FBu	0	0	{"value": "0", "precision": 20}	Burundian Franc	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
bnd	BN$	$	2	0	{"value": "0", "precision": 20}	Brunei Dollar	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
bob	Bs	Bs	2	0	{"value": "0", "precision": 20}	Bolivian Boliviano	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
brl	R$	R$	2	0	{"value": "0", "precision": 20}	Brazilian Real	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
bwp	BWP	P	2	0	{"value": "0", "precision": 20}	Botswanan Pula	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
byn	Br	руб.	2	0	{"value": "0", "precision": 20}	Belarusian Ruble	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
bzd	BZ$	$	2	0	{"value": "0", "precision": 20}	Belize Dollar	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
cdf	CDF	FrCD	2	0	{"value": "0", "precision": 20}	Congolese Franc	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
chf	CHF	CHF	2	0.05	{"value": "0.05", "precision": 20}	Swiss Franc	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
clp	CL$	$	0	0	{"value": "0", "precision": 20}	Chilean Peso	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
cny	CN¥	CN¥	2	0	{"value": "0", "precision": 20}	Chinese Yuan	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
cop	CO$	$	0	0	{"value": "0", "precision": 20}	Colombian Peso	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
crc	₡	₡	0	0	{"value": "0", "precision": 20}	Costa Rican Colón	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
cve	CV$	CV$	2	0	{"value": "0", "precision": 20}	Cape Verdean Escudo	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
czk	Kč	Kč	2	0	{"value": "0", "precision": 20}	Czech Republic Koruna	2025-07-08 06:46:50.921+00	2025-07-08 06:46:50.921+00	\N
djf	Fdj	Fdj	0	0	{"value": "0", "precision": 20}	Djiboutian Franc	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
dkk	Dkr	kr	2	0	{"value": "0", "precision": 20}	Danish Krone	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
dop	RD$	RD$	2	0	{"value": "0", "precision": 20}	Dominican Peso	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
dzd	DA	د.ج.‏	2	0	{"value": "0", "precision": 20}	Algerian Dinar	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
eek	Ekr	kr	2	0	{"value": "0", "precision": 20}	Estonian Kroon	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
egp	EGP	ج.م.‏	2	0	{"value": "0", "precision": 20}	Egyptian Pound	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
ern	Nfk	Nfk	2	0	{"value": "0", "precision": 20}	Eritrean Nakfa	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
etb	Br	Br	2	0	{"value": "0", "precision": 20}	Ethiopian Birr	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
gbp	£	£	2	0	{"value": "0", "precision": 20}	British Pound Sterling	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
gel	GEL	GEL	2	0	{"value": "0", "precision": 20}	Georgian Lari	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
ghs	GH₵	GH₵	2	0	{"value": "0", "precision": 20}	Ghanaian Cedi	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
gnf	FG	FG	0	0	{"value": "0", "precision": 20}	Guinean Franc	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
gtq	GTQ	Q	2	0	{"value": "0", "precision": 20}	Guatemalan Quetzal	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
hkd	HK$	$	2	0	{"value": "0", "precision": 20}	Hong Kong Dollar	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
hnl	HNL	L	2	0	{"value": "0", "precision": 20}	Honduran Lempira	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
hrk	kn	kn	2	0	{"value": "0", "precision": 20}	Croatian Kuna	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
huf	Ft	Ft	0	0	{"value": "0", "precision": 20}	Hungarian Forint	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
idr	Rp	Rp	0	0	{"value": "0", "precision": 20}	Indonesian Rupiah	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
ils	₪	₪	2	0	{"value": "0", "precision": 20}	Israeli New Sheqel	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
inr	Rs	₹	2	0	{"value": "0", "precision": 20}	Indian Rupee	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
iqd	IQD	د.ع.‏	0	0	{"value": "0", "precision": 20}	Iraqi Dinar	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
irr	IRR	﷼	0	0	{"value": "0", "precision": 20}	Iranian Rial	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
isk	Ikr	kr	0	0	{"value": "0", "precision": 20}	Icelandic Króna	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
jmd	J$	$	2	0	{"value": "0", "precision": 20}	Jamaican Dollar	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
jod	JD	د.أ.‏	3	0	{"value": "0", "precision": 20}	Jordanian Dinar	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
jpy	¥	￥	0	0	{"value": "0", "precision": 20}	Japanese Yen	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
kes	Ksh	Ksh	2	0	{"value": "0", "precision": 20}	Kenyan Shilling	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
khr	KHR	៛	2	0	{"value": "0", "precision": 20}	Cambodian Riel	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
kmf	CF	FC	0	0	{"value": "0", "precision": 20}	Comorian Franc	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
krw	₩	₩	0	0	{"value": "0", "precision": 20}	South Korean Won	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
kwd	KD	د.ك.‏	3	0	{"value": "0", "precision": 20}	Kuwaiti Dinar	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
kzt	KZT	тңг.	2	0	{"value": "0", "precision": 20}	Kazakhstani Tenge	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
lbp	LB£	ل.ل.‏	0	0	{"value": "0", "precision": 20}	Lebanese Pound	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
lkr	SLRs	SL Re	2	0	{"value": "0", "precision": 20}	Sri Lankan Rupee	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
ltl	Lt	Lt	2	0	{"value": "0", "precision": 20}	Lithuanian Litas	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
lvl	Ls	Ls	2	0	{"value": "0", "precision": 20}	Latvian Lats	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
lyd	LD	د.ل.‏	3	0	{"value": "0", "precision": 20}	Libyan Dinar	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
mad	MAD	د.م.‏	2	0	{"value": "0", "precision": 20}	Moroccan Dirham	2025-07-08 06:46:50.922+00	2025-07-08 06:46:50.922+00	\N
mdl	MDL	MDL	2	0	{"value": "0", "precision": 20}	Moldovan Leu	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
mga	MGA	MGA	0	0	{"value": "0", "precision": 20}	Malagasy Ariary	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
mkd	MKD	MKD	2	0	{"value": "0", "precision": 20}	Macedonian Denar	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
mmk	MMK	K	0	0	{"value": "0", "precision": 20}	Myanma Kyat	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
mnt	MNT	₮	0	0	{"value": "0", "precision": 20}	Mongolian Tugrig	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
mop	MOP$	MOP$	2	0	{"value": "0", "precision": 20}	Macanese Pataca	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
mur	MURs	MURs	0	0	{"value": "0", "precision": 20}	Mauritian Rupee	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
mxn	MX$	$	2	0	{"value": "0", "precision": 20}	Mexican Peso	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
myr	RM	RM	2	0	{"value": "0", "precision": 20}	Malaysian Ringgit	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
mzn	MTn	MTn	2	0	{"value": "0", "precision": 20}	Mozambican Metical	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
nad	N$	N$	2	0	{"value": "0", "precision": 20}	Namibian Dollar	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
ngn	₦	₦	2	0	{"value": "0", "precision": 20}	Nigerian Naira	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
nio	C$	C$	2	0	{"value": "0", "precision": 20}	Nicaraguan Córdoba	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
nok	Nkr	kr	2	0	{"value": "0", "precision": 20}	Norwegian Krone	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
npr	NPRs	नेरू	2	0	{"value": "0", "precision": 20}	Nepalese Rupee	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
nzd	NZ$	$	2	0	{"value": "0", "precision": 20}	New Zealand Dollar	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
omr	OMR	ر.ع.‏	3	0	{"value": "0", "precision": 20}	Omani Rial	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
pab	B/.	B/.	2	0	{"value": "0", "precision": 20}	Panamanian Balboa	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
pen	S/.	S/.	2	0	{"value": "0", "precision": 20}	Peruvian Nuevo Sol	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
php	₱	₱	2	0	{"value": "0", "precision": 20}	Philippine Peso	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
pkr	PKRs	₨	0	0	{"value": "0", "precision": 20}	Pakistani Rupee	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
pln	zł	zł	2	0	{"value": "0", "precision": 20}	Polish Zloty	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
pyg	₲	₲	0	0	{"value": "0", "precision": 20}	Paraguayan Guarani	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
qar	QR	ر.ق.‏	2	0	{"value": "0", "precision": 20}	Qatari Rial	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
ron	RON	RON	2	0	{"value": "0", "precision": 20}	Romanian Leu	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
rsd	din.	дин.	0	0	{"value": "0", "precision": 20}	Serbian Dinar	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
rub	RUB	₽.	2	0	{"value": "0", "precision": 20}	Russian Ruble	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
rwf	RWF	FR	0	0	{"value": "0", "precision": 20}	Rwandan Franc	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
sar	SR	ر.س.‏	2	0	{"value": "0", "precision": 20}	Saudi Riyal	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
sdg	SDG	SDG	2	0	{"value": "0", "precision": 20}	Sudanese Pound	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
sek	Skr	kr	2	0	{"value": "0", "precision": 20}	Swedish Krona	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
sgd	S$	$	2	0	{"value": "0", "precision": 20}	Singapore Dollar	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
sos	Ssh	Ssh	0	0	{"value": "0", "precision": 20}	Somali Shilling	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
syp	SY£	ل.س.‏	0	0	{"value": "0", "precision": 20}	Syrian Pound	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
thb	฿	฿	2	0	{"value": "0", "precision": 20}	Thai Baht	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
tnd	DT	د.ت.‏	3	0	{"value": "0", "precision": 20}	Tunisian Dinar	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
top	T$	T$	2	0	{"value": "0", "precision": 20}	Tongan Paʻanga	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
try	₺	₺	2	0	{"value": "0", "precision": 20}	Turkish Lira	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
ttd	TT$	$	2	0	{"value": "0", "precision": 20}	Trinidad and Tobago Dollar	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
twd	NT$	NT$	2	0	{"value": "0", "precision": 20}	New Taiwan Dollar	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
tzs	TSh	TSh	0	0	{"value": "0", "precision": 20}	Tanzanian Shilling	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
uah	₴	₴	2	0	{"value": "0", "precision": 20}	Ukrainian Hryvnia	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
ugx	USh	USh	0	0	{"value": "0", "precision": 20}	Ugandan Shilling	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
uyu	$U	$	2	0	{"value": "0", "precision": 20}	Uruguayan Peso	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
uzs	UZS	UZS	0	0	{"value": "0", "precision": 20}	Uzbekistan Som	2025-07-08 06:46:50.923+00	2025-07-08 06:46:50.923+00	\N
vef	Bs.F.	Bs.F.	2	0	{"value": "0", "precision": 20}	Venezuelan Bolívar	2025-07-08 06:46:50.924+00	2025-07-08 06:46:50.924+00	\N
vnd	₫	₫	0	0	{"value": "0", "precision": 20}	Vietnamese Dong	2025-07-08 06:46:50.924+00	2025-07-08 06:46:50.924+00	\N
xaf	FCFA	FCFA	0	0	{"value": "0", "precision": 20}	CFA Franc BEAC	2025-07-08 06:46:50.924+00	2025-07-08 06:46:50.924+00	\N
xof	CFA	CFA	0	0	{"value": "0", "precision": 20}	CFA Franc BCEAO	2025-07-08 06:46:50.924+00	2025-07-08 06:46:50.924+00	\N
yer	YR	ر.ي.‏	0	0	{"value": "0", "precision": 20}	Yemeni Rial	2025-07-08 06:46:50.924+00	2025-07-08 06:46:50.924+00	\N
zar	R	R	2	0	{"value": "0", "precision": 20}	South African Rand	2025-07-08 06:46:50.924+00	2025-07-08 06:46:50.924+00	\N
zmk	ZK	ZK	0	0	{"value": "0", "precision": 20}	Zambian Kwacha	2025-07-08 06:46:50.924+00	2025-07-08 06:46:50.924+00	\N
zwl	ZWL$	ZWL$	0	0	{"value": "0", "precision": 20}	Zimbabwean Dollar	2025-07-08 06:46:50.924+00	2025-07-08 06:46:50.924+00	\N
\.


--
-- TOC entry 5034 (class 0 OID 74636)
-- Dependencies: 248
-- Data for Name: customer; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.customer (id, company_name, first_name, last_name, email, phone, has_account, metadata, created_at, updated_at, deleted_at, created_by) FROM stdin;
\.


--
-- TOC entry 5132 (class 0 OID 76275)
-- Dependencies: 346
-- Data for Name: customer_account_holder; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.customer_account_holder (customer_id, account_holder_id, id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5035 (class 0 OID 74646)
-- Dependencies: 249
-- Data for Name: customer_address; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.customer_address (id, customer_id, address_name, is_default_shipping, is_default_billing, company, first_name, last_name, address_1, address_2, city, country_code, province, postal_code, phone, metadata, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5036 (class 0 OID 74660)
-- Dependencies: 250
-- Data for Name: customer_group; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.customer_group (id, name, metadata, created_by, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5037 (class 0 OID 74670)
-- Dependencies: 251
-- Data for Name: customer_group_customer; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.customer_group_customer (id, customer_id, customer_group_id, metadata, created_at, updated_at, created_by, deleted_at) FROM stdin;
\.


--
-- TOC entry 5108 (class 0 OID 75918)
-- Dependencies: 322
-- Data for Name: fulfillment; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.fulfillment (id, location_id, packed_at, shipped_at, delivered_at, canceled_at, data, provider_id, shipping_option_id, metadata, delivery_address_id, created_at, updated_at, deleted_at, marked_shipped_by, created_by, requires_shipping) FROM stdin;
\.


--
-- TOC entry 5099 (class 0 OID 75808)
-- Dependencies: 313
-- Data for Name: fulfillment_address; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.fulfillment_address (id, company, first_name, last_name, address_1, address_2, city, country_code, province, postal_code, phone, metadata, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5110 (class 0 OID 75944)
-- Dependencies: 324
-- Data for Name: fulfillment_item; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.fulfillment_item (id, title, sku, barcode, quantity, raw_quantity, line_item_id, inventory_item_id, fulfillment_id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5109 (class 0 OID 75933)
-- Dependencies: 323
-- Data for Name: fulfillment_label; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.fulfillment_label (id, tracking_number, tracking_url, label_url, fulfillment_id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5100 (class 0 OID 75818)
-- Dependencies: 314
-- Data for Name: fulfillment_provider; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.fulfillment_provider (id, is_enabled, created_at, updated_at, deleted_at) FROM stdin;
manual_manual	t	2025-07-08 06:46:51.012+00	2025-07-08 06:46:51.012+00	\N
\.


--
-- TOC entry 5101 (class 0 OID 75827)
-- Dependencies: 315
-- Data for Name: fulfillment_set; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.fulfillment_set (id, name, type, metadata, created_at, updated_at, deleted_at) FROM stdin;
fuset_01JZMCKAQVR63W8XTVQE1K3GJX	European Warehouse delivery	shipping	\N	2025-07-08 06:47:00.091+00	2025-07-08 06:47:00.091+00	\N
\.


--
-- TOC entry 5103 (class 0 OID 75851)
-- Dependencies: 317
-- Data for Name: geo_zone; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.geo_zone (id, type, country_code, province_code, city, service_zone_id, postal_expression, metadata, created_at, updated_at, deleted_at) FROM stdin;
fgz_01JZMCKAQT162SXXFRPES933NK	country	gb	\N	\N	serzo_01JZMCKAQTT6G3MJMBKQ1245MP	\N	\N	2025-07-08 06:47:00.092+00	2025-07-08 06:47:00.092+00	\N
fgz_01JZMCKAQTJJQ3W9XXZPMY37CC	country	de	\N	\N	serzo_01JZMCKAQTT6G3MJMBKQ1245MP	\N	\N	2025-07-08 06:47:00.092+00	2025-07-08 06:47:00.092+00	\N
fgz_01JZMCKAQTB0N8JHPGZ29J3FCA	country	dk	\N	\N	serzo_01JZMCKAQTT6G3MJMBKQ1245MP	\N	\N	2025-07-08 06:47:00.092+00	2025-07-08 06:47:00.092+00	\N
fgz_01JZMCKAQTM4P9666MVE407780	country	se	\N	\N	serzo_01JZMCKAQTT6G3MJMBKQ1245MP	\N	\N	2025-07-08 06:47:00.092+00	2025-07-08 06:47:00.092+00	\N
fgz_01JZMCKAQTDK5P858T20016GFT	country	fr	\N	\N	serzo_01JZMCKAQTT6G3MJMBKQ1245MP	\N	\N	2025-07-08 06:47:00.092+00	2025-07-08 06:47:00.092+00	\N
fgz_01JZMCKAQTYBK51JHMF2KENF79	country	es	\N	\N	serzo_01JZMCKAQTT6G3MJMBKQ1245MP	\N	\N	2025-07-08 06:47:00.092+00	2025-07-08 06:47:00.092+00	\N
fgz_01JZMCKAQT15BHYX030A0NPB7G	country	it	\N	\N	serzo_01JZMCKAQTT6G3MJMBKQ1245MP	\N	\N	2025-07-08 06:47:00.092+00	2025-07-08 06:47:00.092+00	\N
\.


--
-- TOC entry 5011 (class 0 OID 73933)
-- Dependencies: 225
-- Data for Name: image; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.image (id, url, metadata, created_at, updated_at, deleted_at, rank, product_id) FROM stdin;
img_01JZMCKB0FQ1MACYK2KV6Y32K5	https://medusa-public-images.s3.eu-west-1.amazonaws.com/tee-black-front.png	\N	2025-07-08 06:47:00.376+00	2025-07-08 06:47:00.376+00	\N	0	prod_01JZMCKB0A4ANCXPDK4Y7Y05VG
img_01JZMCKB0GCAKP4BPW072X6T9M	https://medusa-public-images.s3.eu-west-1.amazonaws.com/tee-black-back.png	\N	2025-07-08 06:47:00.376+00	2025-07-08 06:47:00.376+00	\N	1	prod_01JZMCKB0A4ANCXPDK4Y7Y05VG
img_01JZMCKB0G5EZB7QFH5AJHDGCS	https://medusa-public-images.s3.eu-west-1.amazonaws.com/tee-white-front.png	\N	2025-07-08 06:47:00.376+00	2025-07-08 06:47:00.376+00	\N	2	prod_01JZMCKB0A4ANCXPDK4Y7Y05VG
img_01JZMCKB0G53QMFBPXCH1B4KRG	https://medusa-public-images.s3.eu-west-1.amazonaws.com/tee-white-back.png	\N	2025-07-08 06:47:00.376+00	2025-07-08 06:47:00.376+00	\N	3	prod_01JZMCKB0A4ANCXPDK4Y7Y05VG
img_01JZMCKB0JN7B66JP1ZN4BTMNX	https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatshirt-vintage-front.png	\N	2025-07-08 06:47:00.377+00	2025-07-08 06:47:00.377+00	\N	0	prod_01JZMCKB0AW33BVGK0ER596HE2
img_01JZMCKB0JYQP6TTT8HXJ8FHMR	https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatshirt-vintage-back.png	\N	2025-07-08 06:47:00.377+00	2025-07-08 06:47:00.377+00	\N	1	prod_01JZMCKB0AW33BVGK0ER596HE2
img_01JZMCKB0MR85MGF6EMZ6F8QCK	https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-front.png	\N	2025-07-08 06:47:00.377+00	2025-07-08 06:47:00.377+00	\N	0	prod_01JZMCKB0A4F2607V32YST0Z6C
img_01JZMCKB0M37K2PKGKGFDPGQ6S	https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-back.png	\N	2025-07-08 06:47:00.377+00	2025-07-08 06:47:00.377+00	\N	1	prod_01JZMCKB0A4F2607V32YST0Z6C
img_01JZMCKB0PZ8WMMHT1HX97Z06C	https://medusa-public-images.s3.eu-west-1.amazonaws.com/shorts-vintage-front.png	\N	2025-07-08 06:47:00.377+00	2025-07-08 06:47:00.377+00	\N	0	prod_01JZMCKB0AVXF193XRKHS93MXK
img_01JZMCKB0P956REA0XDJSB107E	https://medusa-public-images.s3.eu-west-1.amazonaws.com/shorts-vintage-back.png	\N	2025-07-08 06:47:00.377+00	2025-07-08 06:47:00.377+00	\N	1	prod_01JZMCKB0AVXF193XRKHS93MXK
\.


--
-- TOC entry 5004 (class 0 OID 73774)
-- Dependencies: 218
-- Data for Name: inventory_item; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.inventory_item (id, created_at, updated_at, deleted_at, sku, origin_country, hs_code, mid_code, material, weight, length, height, width, requires_shipping, description, title, thumbnail, metadata) FROM stdin;
iitem_01JZMCKB61HYV0YM0A58WD583V	2025-07-08 06:47:00.547+00	2025-07-08 06:47:00.547+00	\N	SHIRT-S-BLACK	\N	\N	\N	\N	\N	\N	\N	\N	t	S / Black	S / Black	\N	\N
iitem_01JZMCKB61VBPVQP4EQS9GYAA9	2025-07-08 06:47:00.547+00	2025-07-08 06:47:00.547+00	\N	SHIRT-S-WHITE	\N	\N	\N	\N	\N	\N	\N	\N	t	S / White	S / White	\N	\N
iitem_01JZMCKB61VBJYXFEZPF0E9DZ2	2025-07-08 06:47:00.547+00	2025-07-08 06:47:00.547+00	\N	SHIRT-M-BLACK	\N	\N	\N	\N	\N	\N	\N	\N	t	M / Black	M / Black	\N	\N
iitem_01JZMCKB61B48Y9XBR02S2VSFZ	2025-07-08 06:47:00.547+00	2025-07-08 06:47:00.547+00	\N	SHIRT-M-WHITE	\N	\N	\N	\N	\N	\N	\N	\N	t	M / White	M / White	\N	\N
iitem_01JZMCKB61WGY5590T4C2DTRHD	2025-07-08 06:47:00.547+00	2025-07-08 06:47:00.547+00	\N	SHIRT-L-BLACK	\N	\N	\N	\N	\N	\N	\N	\N	t	L / Black	L / Black	\N	\N
iitem_01JZMCKB61Y487FBGZ0SS3VBKR	2025-07-08 06:47:00.547+00	2025-07-08 06:47:00.547+00	\N	SHIRT-L-WHITE	\N	\N	\N	\N	\N	\N	\N	\N	t	L / White	L / White	\N	\N
iitem_01JZMCKB61QH08E5JWM2YM9AEW	2025-07-08 06:47:00.548+00	2025-07-08 06:47:00.548+00	\N	SHIRT-XL-BLACK	\N	\N	\N	\N	\N	\N	\N	\N	t	XL / Black	XL / Black	\N	\N
iitem_01JZMCKB61EGCFSV754JRAP4TV	2025-07-08 06:47:00.548+00	2025-07-08 06:47:00.548+00	\N	SHIRT-XL-WHITE	\N	\N	\N	\N	\N	\N	\N	\N	t	XL / White	XL / White	\N	\N
iitem_01JZMCKB62HMYXW5FEGNXNBR0V	2025-07-08 06:47:00.548+00	2025-07-08 06:47:00.548+00	\N	SWEATSHIRT-S	\N	\N	\N	\N	\N	\N	\N	\N	t	S	S	\N	\N
iitem_01JZMCKB62MNZ7CBHE7MK62JT2	2025-07-08 06:47:00.548+00	2025-07-08 06:47:00.548+00	\N	SWEATSHIRT-M	\N	\N	\N	\N	\N	\N	\N	\N	t	M	M	\N	\N
iitem_01JZMCKB62XMNRX5Q8606XAGBK	2025-07-08 06:47:00.548+00	2025-07-08 06:47:00.548+00	\N	SWEATSHIRT-L	\N	\N	\N	\N	\N	\N	\N	\N	t	L	L	\N	\N
iitem_01JZMCKB62MAX8Y9DCABCE5C8J	2025-07-08 06:47:00.548+00	2025-07-08 06:47:00.548+00	\N	SWEATSHIRT-XL	\N	\N	\N	\N	\N	\N	\N	\N	t	XL	XL	\N	\N
iitem_01JZMCKB62D7W4TVR86MT9SKMZ	2025-07-08 06:47:00.548+00	2025-07-08 06:47:00.548+00	\N	SWEATPANTS-S	\N	\N	\N	\N	\N	\N	\N	\N	t	S	S	\N	\N
iitem_01JZMCKB62A7EHECKKGNNKCQSX	2025-07-08 06:47:00.548+00	2025-07-08 06:47:00.548+00	\N	SWEATPANTS-M	\N	\N	\N	\N	\N	\N	\N	\N	t	M	M	\N	\N
iitem_01JZMCKB62GCAZ9Q12930BB8F7	2025-07-08 06:47:00.548+00	2025-07-08 06:47:00.548+00	\N	SWEATPANTS-L	\N	\N	\N	\N	\N	\N	\N	\N	t	L	L	\N	\N
iitem_01JZMCKB62R06ZEK17RNDHDYF8	2025-07-08 06:47:00.548+00	2025-07-08 06:47:00.548+00	\N	SWEATPANTS-XL	\N	\N	\N	\N	\N	\N	\N	\N	t	XL	XL	\N	\N
iitem_01JZMCKB631RS61MWJMAAHCXA9	2025-07-08 06:47:00.548+00	2025-07-08 06:47:00.548+00	\N	SHORTS-S	\N	\N	\N	\N	\N	\N	\N	\N	t	S	S	\N	\N
iitem_01JZMCKB63JGQGF3PFZC3XYH6K	2025-07-08 06:47:00.548+00	2025-07-08 06:47:00.548+00	\N	SHORTS-M	\N	\N	\N	\N	\N	\N	\N	\N	t	M	M	\N	\N
iitem_01JZMCKB6342GCVMH3MS3XJKDY	2025-07-08 06:47:00.548+00	2025-07-08 06:47:00.548+00	\N	SHORTS-L	\N	\N	\N	\N	\N	\N	\N	\N	t	L	L	\N	\N
iitem_01JZMCKB63EVXC3TQ7E5JCBGWW	2025-07-08 06:47:00.548+00	2025-07-08 06:47:00.548+00	\N	SHORTS-XL	\N	\N	\N	\N	\N	\N	\N	\N	t	XL	XL	\N	\N
\.


--
-- TOC entry 5005 (class 0 OID 73786)
-- Dependencies: 219
-- Data for Name: inventory_level; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.inventory_level (id, created_at, updated_at, deleted_at, inventory_item_id, location_id, stocked_quantity, reserved_quantity, incoming_quantity, metadata, raw_stocked_quantity, raw_reserved_quantity, raw_incoming_quantity) FROM stdin;
ilev_01JZMCKBD9VP5Q6RDMYH70FZY9	2025-07-08 06:47:00.78+00	2025-07-08 06:47:00.78+00	\N	iitem_01JZMCKB61B48Y9XBR02S2VSFZ	sloc_01JZMCKAPGHGASVYX824CCG1T3	1000000	0	0	\N	{"value": "1000000", "precision": 20}	{"value": "0", "precision": 20}	{"value": "0", "precision": 20}
ilev_01JZMCKBD9XQER0GTH2JEWVE5D	2025-07-08 06:47:00.781+00	2025-07-08 06:47:00.781+00	\N	iitem_01JZMCKB61EGCFSV754JRAP4TV	sloc_01JZMCKAPGHGASVYX824CCG1T3	1000000	0	0	\N	{"value": "1000000", "precision": 20}	{"value": "0", "precision": 20}	{"value": "0", "precision": 20}
ilev_01JZMCKBD9RMMV5HE833GT085S	2025-07-08 06:47:00.781+00	2025-07-08 06:47:00.781+00	\N	iitem_01JZMCKB61HYV0YM0A58WD583V	sloc_01JZMCKAPGHGASVYX824CCG1T3	1000000	0	0	\N	{"value": "1000000", "precision": 20}	{"value": "0", "precision": 20}	{"value": "0", "precision": 20}
ilev_01JZMCKBD9KKBFP5B6MGNKZVHN	2025-07-08 06:47:00.781+00	2025-07-08 06:47:00.781+00	\N	iitem_01JZMCKB61QH08E5JWM2YM9AEW	sloc_01JZMCKAPGHGASVYX824CCG1T3	1000000	0	0	\N	{"value": "1000000", "precision": 20}	{"value": "0", "precision": 20}	{"value": "0", "precision": 20}
ilev_01JZMCKBD9JNWXNTV9PQJWJK3K	2025-07-08 06:47:00.781+00	2025-07-08 06:47:00.781+00	\N	iitem_01JZMCKB61VBJYXFEZPF0E9DZ2	sloc_01JZMCKAPGHGASVYX824CCG1T3	1000000	0	0	\N	{"value": "1000000", "precision": 20}	{"value": "0", "precision": 20}	{"value": "0", "precision": 20}
ilev_01JZMCKBDAHKFYTCPM07R4E0CP	2025-07-08 06:47:00.781+00	2025-07-08 06:47:00.781+00	\N	iitem_01JZMCKB61VBPVQP4EQS9GYAA9	sloc_01JZMCKAPGHGASVYX824CCG1T3	1000000	0	0	\N	{"value": "1000000", "precision": 20}	{"value": "0", "precision": 20}	{"value": "0", "precision": 20}
ilev_01JZMCKBDAD4V1N8BNA1F740BC	2025-07-08 06:47:00.781+00	2025-07-08 06:47:00.781+00	\N	iitem_01JZMCKB61WGY5590T4C2DTRHD	sloc_01JZMCKAPGHGASVYX824CCG1T3	1000000	0	0	\N	{"value": "1000000", "precision": 20}	{"value": "0", "precision": 20}	{"value": "0", "precision": 20}
ilev_01JZMCKBDA9D6D9W59XPCD2B6Y	2025-07-08 06:47:00.781+00	2025-07-08 06:47:00.781+00	\N	iitem_01JZMCKB61Y487FBGZ0SS3VBKR	sloc_01JZMCKAPGHGASVYX824CCG1T3	1000000	0	0	\N	{"value": "1000000", "precision": 20}	{"value": "0", "precision": 20}	{"value": "0", "precision": 20}
ilev_01JZMCKBDABSMV3DTET5Z9AVYD	2025-07-08 06:47:00.781+00	2025-07-08 06:47:00.781+00	\N	iitem_01JZMCKB62A7EHECKKGNNKCQSX	sloc_01JZMCKAPGHGASVYX824CCG1T3	1000000	0	0	\N	{"value": "1000000", "precision": 20}	{"value": "0", "precision": 20}	{"value": "0", "precision": 20}
ilev_01JZMCKBDAVGT510Q8XWQPXYB3	2025-07-08 06:47:00.781+00	2025-07-08 06:47:00.781+00	\N	iitem_01JZMCKB62D7W4TVR86MT9SKMZ	sloc_01JZMCKAPGHGASVYX824CCG1T3	1000000	0	0	\N	{"value": "1000000", "precision": 20}	{"value": "0", "precision": 20}	{"value": "0", "precision": 20}
ilev_01JZMCKBDARJVZQGRZ5BHHSNAF	2025-07-08 06:47:00.781+00	2025-07-08 06:47:00.781+00	\N	iitem_01JZMCKB62GCAZ9Q12930BB8F7	sloc_01JZMCKAPGHGASVYX824CCG1T3	1000000	0	0	\N	{"value": "1000000", "precision": 20}	{"value": "0", "precision": 20}	{"value": "0", "precision": 20}
ilev_01JZMCKBDAV7JZM211NDE5V50M	2025-07-08 06:47:00.781+00	2025-07-08 06:47:00.781+00	\N	iitem_01JZMCKB62HMYXW5FEGNXNBR0V	sloc_01JZMCKAPGHGASVYX824CCG1T3	1000000	0	0	\N	{"value": "1000000", "precision": 20}	{"value": "0", "precision": 20}	{"value": "0", "precision": 20}
ilev_01JZMCKBDB3XE6PM9HDW7BMKSP	2025-07-08 06:47:00.781+00	2025-07-08 06:47:00.781+00	\N	iitem_01JZMCKB62MAX8Y9DCABCE5C8J	sloc_01JZMCKAPGHGASVYX824CCG1T3	1000000	0	0	\N	{"value": "1000000", "precision": 20}	{"value": "0", "precision": 20}	{"value": "0", "precision": 20}
ilev_01JZMCKBDBGWWPGEVWH0RJEPNB	2025-07-08 06:47:00.781+00	2025-07-08 06:47:00.781+00	\N	iitem_01JZMCKB62MNZ7CBHE7MK62JT2	sloc_01JZMCKAPGHGASVYX824CCG1T3	1000000	0	0	\N	{"value": "1000000", "precision": 20}	{"value": "0", "precision": 20}	{"value": "0", "precision": 20}
ilev_01JZMCKBDBKYGAH08CNTMBRWSZ	2025-07-08 06:47:00.781+00	2025-07-08 06:47:00.781+00	\N	iitem_01JZMCKB62R06ZEK17RNDHDYF8	sloc_01JZMCKAPGHGASVYX824CCG1T3	1000000	0	0	\N	{"value": "1000000", "precision": 20}	{"value": "0", "precision": 20}	{"value": "0", "precision": 20}
ilev_01JZMCKBDBH9HSQEC46EAGK95C	2025-07-08 06:47:00.781+00	2025-07-08 06:47:00.781+00	\N	iitem_01JZMCKB62XMNRX5Q8606XAGBK	sloc_01JZMCKAPGHGASVYX824CCG1T3	1000000	0	0	\N	{"value": "1000000", "precision": 20}	{"value": "0", "precision": 20}	{"value": "0", "precision": 20}
ilev_01JZMCKBDC34STNGX5KZABF3P3	2025-07-08 06:47:00.781+00	2025-07-08 06:47:00.781+00	\N	iitem_01JZMCKB631RS61MWJMAAHCXA9	sloc_01JZMCKAPGHGASVYX824CCG1T3	1000000	0	0	\N	{"value": "1000000", "precision": 20}	{"value": "0", "precision": 20}	{"value": "0", "precision": 20}
ilev_01JZMCKBDCP9JH0VQE44KJ7H2M	2025-07-08 06:47:00.781+00	2025-07-08 06:47:00.781+00	\N	iitem_01JZMCKB6342GCVMH3MS3XJKDY	sloc_01JZMCKAPGHGASVYX824CCG1T3	1000000	0	0	\N	{"value": "1000000", "precision": 20}	{"value": "0", "precision": 20}	{"value": "0", "precision": 20}
ilev_01JZMCKBDCYZBXEX1KPWZ4J9PC	2025-07-08 06:47:00.781+00	2025-07-08 06:47:00.781+00	\N	iitem_01JZMCKB63EVXC3TQ7E5JCBGWW	sloc_01JZMCKAPGHGASVYX824CCG1T3	1000000	0	0	\N	{"value": "1000000", "precision": 20}	{"value": "0", "precision": 20}	{"value": "0", "precision": 20}
ilev_01JZMCKBDC1JQZ64RZBH12864X	2025-07-08 06:47:00.781+00	2025-07-08 06:47:00.781+00	\N	iitem_01JZMCKB63JGQGF3PFZC3XYH6K	sloc_01JZMCKAPGHGASVYX824CCG1T3	1000000	0	0	\N	{"value": "1000000", "precision": 20}	{"value": "0", "precision": 20}	{"value": "0", "precision": 20}
\.


--
-- TOC entry 5097 (class 0 OID 75781)
-- Dependencies: 311
-- Data for Name: invite; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.invite (id, email, accepted, token, expires_at, metadata, created_at, updated_at, deleted_at) FROM stdin;
invite_01JZMCK70TNQQ8Z9B4CWXCXC1W	<EMAIL>	f	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************.c8_nZJRj6PA1UpE4Gx-3p1B1JKsXxr9piJoH8paFE9Y	2025-07-09 06:46:56.282+00	\N	2025-07-08 06:46:56.285+00	2025-07-08 06:51:43.231+00	2025-07-08 06:51:43.23+00
\.


--
-- TOC entry 5115 (class 0 OID 76101)
-- Dependencies: 329
-- Data for Name: link_module_migrations; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.link_module_migrations (id, table_name, link_descriptor, created_at) FROM stdin;
1	cart_promotion	{"toModel": "promotions", "toModule": "promotion", "fromModel": "cart", "fromModule": "cart"}	2025-07-08 06:46:45.746397
3	location_fulfillment_set	{"toModel": "fulfillment_set", "toModule": "fulfillment", "fromModel": "location", "fromModule": "stock_location"}	2025-07-08 06:46:45.746538
4	order_cart	{"toModel": "cart", "toModule": "cart", "fromModel": "order", "fromModule": "order"}	2025-07-08 06:46:45.747714
5	order_fulfillment	{"toModel": "fulfillments", "toModule": "fulfillment", "fromModel": "order", "fromModule": "order"}	2025-07-08 06:46:45.747846
11	product_variant_price_set	{"toModel": "price_set", "toModule": "pricing", "fromModel": "variant", "fromModule": "product"}	2025-07-08 06:46:47.257087
12	publishable_api_key_sales_channel	{"toModel": "sales_channel", "toModule": "sales_channel", "fromModel": "api_key", "fromModule": "api_key"}	2025-07-08 06:46:47.31626
15	shipping_option_price_set	{"toModel": "price_set", "toModule": "pricing", "fromModel": "shipping_option", "fromModule": "fulfillment"}	2025-07-08 06:46:47.397881
6	order_payment_collection	{"toModel": "payment_collection", "toModule": "payment", "fromModel": "order", "fromModule": "order"}	2025-07-08 06:46:45.749984
16	product_shipping_profile	{"toModel": "shipping_profile", "toModule": "fulfillment", "fromModel": "product", "fromModule": "product"}	2025-07-08 06:46:47.398168
7	order_promotion	{"toModel": "promotion", "toModule": "promotion", "fromModel": "order", "fromModule": "order"}	2025-07-08 06:46:45.753828
14	sales_channel_stock_location	{"toModel": "location", "toModule": "stock_location", "fromModel": "sales_channel", "fromModule": "sales_channel"}	2025-07-08 06:46:47.397666
8	product_variant_inventory_item	{"toModel": "inventory", "toModule": "inventory", "fromModel": "variant", "fromModule": "product"}	2025-07-08 06:46:45.753942
17	customer_account_holder	{"toModel": "account_holder", "toModule": "payment", "fromModel": "customer", "fromModule": "customer"}	2025-07-08 06:46:47.398646
9	product_sales_channel	{"toModel": "sales_channel", "toModule": "sales_channel", "fromModel": "product", "fromModule": "product"}	2025-07-08 06:46:45.754248
18	cart_payment_collection	{"toModel": "payment_collection", "toModule": "payment", "fromModel": "cart", "fromModule": "cart"}	2025-07-08 06:46:47.398843
10	return_fulfillment	{"toModel": "fulfillments", "toModule": "fulfillment", "fromModel": "return", "fromModule": "order"}	2025-07-08 06:46:45.755103
13	region_payment_provider	{"toModel": "payment_provider", "toModule": "payment", "fromModel": "region", "fromModule": "region"}	2025-07-08 06:46:47.397441
2	location_fulfillment_provider	{"toModel": "fulfillment_provider", "toModule": "fulfillment", "fromModel": "location", "fromModule": "stock_location"}	2025-07-08 06:46:45.746369
\.


--
-- TOC entry 5120 (class 0 OID 76121)
-- Dependencies: 334
-- Data for Name: location_fulfillment_provider; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.location_fulfillment_provider (stock_location_id, fulfillment_provider_id, id, created_at, updated_at, deleted_at) FROM stdin;
sloc_01JZMCKAPGHGASVYX824CCG1T3	manual_manual	locfp_01JZMCKAQ4K9NFWEEDKE5KTGVD	2025-07-08 06:47:00.067675+00	2025-07-08 06:47:00.067675+00	\N
\.


--
-- TOC entry 5117 (class 0 OID 76114)
-- Dependencies: 331
-- Data for Name: location_fulfillment_set; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.location_fulfillment_set (stock_location_id, fulfillment_set_id, id, created_at, updated_at, deleted_at) FROM stdin;
sloc_01JZMCKAPGHGASVYX824CCG1T3	fuset_01JZMCKAQVR63W8XTVQE1K3GJX	locfs_01JZMCKARHTT5FJK3MJH8AKAE8	2025-07-08 06:47:00.11302+00	2025-07-08 06:47:00.11302+00	\N
\.


--
-- TOC entry 5001 (class 0 OID 73733)
-- Dependencies: 215
-- Data for Name: mikro_orm_migrations; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.mikro_orm_migrations (id, name, executed_at) FROM stdin;
1	Migration20240307161216	2025-07-08 06:45:35.730055+00
2	Migration20241210073813	2025-07-08 06:45:35.730055+00
3	Migration20250106142624	2025-07-08 06:45:35.730055+00
4	Migration20250120110820	2025-07-08 06:45:35.730055+00
5	Migration20240307132720	2025-07-08 06:45:36.59076+00
6	Migration20240719123015	2025-07-08 06:45:36.59076+00
7	Migration20241213063611	2025-07-08 06:45:36.59076+00
8	InitialSetup20240401153642	2025-07-08 06:45:39.801827+00
9	Migration20240601111544	2025-07-08 06:45:39.801827+00
10	Migration202408271511	2025-07-08 06:45:39.801827+00
11	Migration20241122120331	2025-07-08 06:45:39.801827+00
12	Migration20241125090957	2025-07-08 06:45:39.801827+00
13	Migration20250411073236	2025-07-08 06:45:39.801827+00
14	Migration20250516081326	2025-07-08 06:45:39.801827+00
15	Migration20230929122253	2025-07-08 06:45:49.863899+00
16	Migration20240322094407	2025-07-08 06:45:49.863899+00
17	Migration20240322113359	2025-07-08 06:45:49.863899+00
18	Migration20240322120125	2025-07-08 06:45:49.863899+00
19	Migration20240626133555	2025-07-08 06:45:49.863899+00
20	Migration20240704094505	2025-07-08 06:45:49.863899+00
21	Migration20241127114534	2025-07-08 06:45:49.863899+00
22	Migration20241127223829	2025-07-08 06:45:49.863899+00
23	Migration20241128055359	2025-07-08 06:45:49.863899+00
24	Migration20241212190401	2025-07-08 06:45:49.863899+00
25	Migration20250408145122	2025-07-08 06:45:49.863899+00
26	Migration20250409122219	2025-07-08 06:45:49.863899+00
27	Migration20240227120221	2025-07-08 06:45:56.443111+00
28	Migration20240617102917	2025-07-08 06:45:56.443111+00
29	Migration20240624153824	2025-07-08 06:45:56.443111+00
30	Migration20241211061114	2025-07-08 06:45:56.443111+00
31	Migration20250113094144	2025-07-08 06:45:56.443111+00
32	Migration20250120110700	2025-07-08 06:45:56.443111+00
33	Migration20250226130616	2025-07-08 06:45:56.443111+00
34	Migration20250508081510	2025-07-08 06:45:56.443111+00
35	Migration20240124154000	2025-07-08 06:46:02.126969+00
36	Migration20240524123112	2025-07-08 06:46:02.126969+00
37	Migration20240602110946	2025-07-08 06:46:02.126969+00
38	Migration20241211074630	2025-07-08 06:46:02.126969+00
39	Migration20240115152146	2025-07-08 06:46:04.141145+00
40	Migration20240222170223	2025-07-08 06:46:04.696432+00
41	Migration20240831125857	2025-07-08 06:46:04.696432+00
42	Migration20241106085918	2025-07-08 06:46:04.696432+00
43	Migration20241205095237	2025-07-08 06:46:04.696432+00
44	Migration20241216183049	2025-07-08 06:46:04.696432+00
45	Migration20241218091938	2025-07-08 06:46:04.696432+00
46	Migration20250120115059	2025-07-08 06:46:04.696432+00
47	Migration20250212131240	2025-07-08 06:46:04.696432+00
48	Migration20250326151602	2025-07-08 06:46:04.696432+00
49	Migration20250508081553	2025-07-08 06:46:04.696432+00
50	Migration20240205173216	2025-07-08 06:46:11.459656+00
51	Migration20240624200006	2025-07-08 06:46:11.459656+00
52	Migration20250120110744	2025-07-08 06:46:11.459656+00
53	InitialSetup20240221144943	2025-07-08 06:46:12.373335+00
54	Migration20240604080145	2025-07-08 06:46:12.373335+00
55	Migration20241205122700	2025-07-08 06:46:12.373335+00
56	InitialSetup20240227075933	2025-07-08 06:46:12.942385+00
57	Migration20240621145944	2025-07-08 06:46:12.942385+00
58	Migration20241206083313	2025-07-08 06:46:12.942385+00
59	Migration20240227090331	2025-07-08 06:46:13.638768+00
60	Migration20240710135844	2025-07-08 06:46:13.638768+00
61	Migration20240924114005	2025-07-08 06:46:13.638768+00
62	Migration20241212052837	2025-07-08 06:46:13.638768+00
63	InitialSetup20240228133303	2025-07-08 06:46:16.479956+00
64	Migration20240624082354	2025-07-08 06:46:16.479956+00
65	Migration20240225134525	2025-07-08 06:46:16.851399+00
66	Migration20240806072619	2025-07-08 06:46:16.851399+00
67	Migration20241211151053	2025-07-08 06:46:16.851399+00
68	Migration20250115160517	2025-07-08 06:46:16.851399+00
69	Migration20250120110552	2025-07-08 06:46:16.851399+00
70	Migration20250123122334	2025-07-08 06:46:16.851399+00
71	Migration20250206105639	2025-07-08 06:46:16.851399+00
72	Migration20250207132723	2025-07-08 06:46:16.851399+00
73	Migration20250625084134	2025-07-08 06:46:16.851399+00
74	Migration20240219102530	2025-07-08 06:46:20.490936+00
75	Migration20240604100512	2025-07-08 06:46:20.490936+00
76	Migration20240715102100	2025-07-08 06:46:20.490936+00
77	Migration20240715174100	2025-07-08 06:46:20.490936+00
78	Migration20240716081800	2025-07-08 06:46:20.490936+00
79	Migration20240801085921	2025-07-08 06:46:20.490936+00
80	Migration20240821164505	2025-07-08 06:46:20.490936+00
81	Migration20240821170920	2025-07-08 06:46:20.490936+00
82	Migration20240827133639	2025-07-08 06:46:20.490936+00
83	Migration20240902195921	2025-07-08 06:46:20.490936+00
84	Migration20240913092514	2025-07-08 06:46:20.490936+00
85	Migration20240930122627	2025-07-08 06:46:20.490936+00
86	Migration20241014142943	2025-07-08 06:46:20.490936+00
87	Migration20241106085223	2025-07-08 06:46:20.490936+00
88	Migration20241129124827	2025-07-08 06:46:20.490936+00
89	Migration20241217162224	2025-07-08 06:46:20.490936+00
90	Migration20250326151554	2025-07-08 06:46:20.490936+00
91	Migration20250522181137	2025-07-08 06:46:20.490936+00
92	Migration20240205025928	2025-07-08 06:46:33.174461+00
93	Migration20240529080336	2025-07-08 06:46:33.174461+00
94	Migration20241202100304	2025-07-08 06:46:33.174461+00
95	Migration20240214033943	2025-07-08 06:46:34.095298+00
96	Migration20240703095850	2025-07-08 06:46:34.095298+00
97	Migration20241202103352	2025-07-08 06:46:34.095298+00
98	Migration20240311145700_InitialSetupMigration	2025-07-08 06:46:35.555611+00
99	Migration20240821170957	2025-07-08 06:46:35.555611+00
100	Migration20240917161003	2025-07-08 06:46:35.555611+00
101	Migration20241217110416	2025-07-08 06:46:35.555611+00
102	Migration20250113122235	2025-07-08 06:46:35.555611+00
103	Migration20250120115002	2025-07-08 06:46:35.555611+00
104	Migration20240509083918_InitialSetupMigration	2025-07-08 06:46:42.120751+00
105	Migration20240628075401	2025-07-08 06:46:42.120751+00
106	Migration20240830094712	2025-07-08 06:46:42.120751+00
107	Migration20250120110514	2025-07-08 06:46:42.120751+00
108	Migration20231228143900	2025-07-08 06:46:43.299893+00
109	Migration20241206101446	2025-07-08 06:46:43.299893+00
110	Migration20250128174331	2025-07-08 06:46:43.299893+00
111	Migration20250505092459	2025-07-08 06:46:43.299893+00
\.


--
-- TOC entry 5112 (class 0 OID 76053)
-- Dependencies: 326
-- Data for Name: notification; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.notification (id, "to", channel, template, data, trigger_type, resource_id, resource_type, receiver_id, original_notification_id, idempotency_key, external_id, provider_id, created_at, updated_at, deleted_at, status) FROM stdin;
\.


--
-- TOC entry 5111 (class 0 OID 76045)
-- Dependencies: 325
-- Data for Name: notification_provider; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.notification_provider (id, handle, name, is_enabled, channels, created_at, updated_at, deleted_at) FROM stdin;
local	local	local	t	{feed}	2025-07-08 06:46:51.009+00	2025-07-08 06:46:51.009+00	\N
\.


--
-- TOC entry 5069 (class 0 OID 75283)
-- Dependencies: 283
-- Data for Name: order; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public."order" (id, region_id, display_id, customer_id, version, sales_channel_id, status, is_draft_order, email, currency_code, shipping_address_id, billing_address_id, no_notification, metadata, created_at, updated_at, deleted_at, canceled_at) FROM stdin;
\.


--
-- TOC entry 5067 (class 0 OID 75272)
-- Dependencies: 281
-- Data for Name: order_address; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_address (id, customer_id, company, first_name, last_name, address_1, address_2, city, country_code, province, postal_code, phone, metadata, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5119 (class 0 OID 76117)
-- Dependencies: 333
-- Data for Name: order_cart; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_cart (order_id, cart_id, id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5071 (class 0 OID 75335)
-- Dependencies: 285
-- Data for Name: order_change; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_change (id, order_id, version, description, status, internal_note, created_by, requested_by, requested_at, confirmed_by, confirmed_at, declined_by, declined_reason, metadata, declined_at, canceled_by, canceled_at, created_at, updated_at, change_type, deleted_at, return_id, claim_id, exchange_id) FROM stdin;
\.


--
-- TOC entry 5073 (class 0 OID 75350)
-- Dependencies: 287
-- Data for Name: order_change_action; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_change_action (id, order_id, version, ordering, order_change_id, reference, reference_id, action, details, amount, raw_amount, internal_note, applied, created_at, updated_at, deleted_at, return_id, claim_id, exchange_id) FROM stdin;
\.


--
-- TOC entry 5091 (class 0 OID 75636)
-- Dependencies: 305
-- Data for Name: order_claim; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_claim (id, order_id, return_id, order_version, display_id, type, no_notification, refund_amount, raw_refund_amount, metadata, created_at, updated_at, deleted_at, canceled_at, created_by) FROM stdin;
\.


--
-- TOC entry 5092 (class 0 OID 75659)
-- Dependencies: 306
-- Data for Name: order_claim_item; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_claim_item (id, claim_id, item_id, is_additional_item, reason, quantity, raw_quantity, note, metadata, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5093 (class 0 OID 75672)
-- Dependencies: 307
-- Data for Name: order_claim_item_image; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_claim_item_image (id, claim_item_id, url, metadata, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5094 (class 0 OID 75730)
-- Dependencies: 308
-- Data for Name: order_credit_line; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_credit_line (id, order_id, reference, reference_id, amount, raw_amount, metadata, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5088 (class 0 OID 75602)
-- Dependencies: 302
-- Data for Name: order_exchange; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_exchange (id, order_id, return_id, order_version, display_id, no_notification, allow_backorder, difference_due, raw_difference_due, metadata, created_at, updated_at, deleted_at, canceled_at, created_by) FROM stdin;
\.


--
-- TOC entry 5089 (class 0 OID 75617)
-- Dependencies: 303
-- Data for Name: order_exchange_item; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_exchange_item (id, exchange_id, item_id, quantity, raw_quantity, note, metadata, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5118 (class 0 OID 76116)
-- Dependencies: 332
-- Data for Name: order_fulfillment; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_fulfillment (order_id, fulfillment_id, id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5074 (class 0 OID 75364)
-- Dependencies: 288
-- Data for Name: order_item; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_item (id, order_id, version, item_id, quantity, raw_quantity, fulfilled_quantity, raw_fulfilled_quantity, shipped_quantity, raw_shipped_quantity, return_requested_quantity, raw_return_requested_quantity, return_received_quantity, raw_return_received_quantity, return_dismissed_quantity, raw_return_dismissed_quantity, written_off_quantity, raw_written_off_quantity, metadata, created_at, updated_at, deleted_at, delivered_quantity, raw_delivered_quantity, unit_price, raw_unit_price, compare_at_unit_price, raw_compare_at_unit_price) FROM stdin;
\.


--
-- TOC entry 5076 (class 0 OID 75388)
-- Dependencies: 290
-- Data for Name: order_line_item; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_line_item (id, totals_id, title, subtitle, thumbnail, variant_id, product_id, product_title, product_description, product_subtitle, product_type, product_collection, product_handle, variant_sku, variant_barcode, variant_title, variant_option_values, requires_shipping, is_discountable, is_tax_inclusive, compare_at_unit_price, raw_compare_at_unit_price, unit_price, raw_unit_price, metadata, created_at, updated_at, deleted_at, is_custom_price, product_type_id, is_giftcard) FROM stdin;
\.


--
-- TOC entry 5078 (class 0 OID 75412)
-- Dependencies: 292
-- Data for Name: order_line_item_adjustment; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_line_item_adjustment (id, description, promotion_id, code, amount, raw_amount, provider_id, created_at, updated_at, item_id, deleted_at) FROM stdin;
\.


--
-- TOC entry 5077 (class 0 OID 75402)
-- Dependencies: 291
-- Data for Name: order_line_item_tax_line; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_line_item_tax_line (id, description, tax_rate_id, code, rate, raw_rate, provider_id, created_at, updated_at, item_id, deleted_at) FROM stdin;
\.


--
-- TOC entry 5121 (class 0 OID 76132)
-- Dependencies: 335
-- Data for Name: order_payment_collection; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_payment_collection (order_id, payment_collection_id, id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5122 (class 0 OID 76151)
-- Dependencies: 336
-- Data for Name: order_promotion; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_promotion (order_id, promotion_id, id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5075 (class 0 OID 75376)
-- Dependencies: 289
-- Data for Name: order_shipping; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_shipping (id, order_id, version, shipping_method_id, created_at, updated_at, deleted_at, return_id, claim_id, exchange_id) FROM stdin;
\.


--
-- TOC entry 5079 (class 0 OID 75422)
-- Dependencies: 293
-- Data for Name: order_shipping_method; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_shipping_method (id, name, description, amount, raw_amount, is_tax_inclusive, shipping_option_id, data, metadata, created_at, updated_at, deleted_at, is_custom_amount) FROM stdin;
\.


--
-- TOC entry 5080 (class 0 OID 75433)
-- Dependencies: 294
-- Data for Name: order_shipping_method_adjustment; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_shipping_method_adjustment (id, description, promotion_id, code, amount, raw_amount, provider_id, created_at, updated_at, shipping_method_id, deleted_at) FROM stdin;
\.


--
-- TOC entry 5081 (class 0 OID 75443)
-- Dependencies: 295
-- Data for Name: order_shipping_method_tax_line; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_shipping_method_tax_line (id, description, tax_rate_id, code, rate, raw_rate, provider_id, created_at, updated_at, shipping_method_id, deleted_at) FROM stdin;
\.


--
-- TOC entry 5070 (class 0 OID 75324)
-- Dependencies: 284
-- Data for Name: order_summary; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_summary (id, order_id, version, totals, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5082 (class 0 OID 75453)
-- Dependencies: 296
-- Data for Name: order_transaction; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.order_transaction (id, order_id, version, amount, raw_amount, currency_code, reference, reference_id, created_at, updated_at, deleted_at, return_id, claim_id, exchange_id) FROM stdin;
\.


--
-- TOC entry 5062 (class 0 OID 75146)
-- Dependencies: 276
-- Data for Name: payment; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.payment (id, amount, raw_amount, currency_code, provider_id, data, created_at, updated_at, deleted_at, captured_at, canceled_at, payment_collection_id, payment_session_id, metadata) FROM stdin;
\.


--
-- TOC entry 5058 (class 0 OID 75100)
-- Dependencies: 272
-- Data for Name: payment_collection; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.payment_collection (id, currency_code, amount, raw_amount, authorized_amount, raw_authorized_amount, captured_amount, raw_captured_amount, refunded_amount, raw_refunded_amount, created_at, updated_at, deleted_at, completed_at, status, metadata) FROM stdin;
\.


--
-- TOC entry 5060 (class 0 OID 75128)
-- Dependencies: 274
-- Data for Name: payment_collection_payment_providers; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.payment_collection_payment_providers (payment_collection_id, payment_provider_id) FROM stdin;
\.


--
-- TOC entry 5059 (class 0 OID 75120)
-- Dependencies: 273
-- Data for Name: payment_provider; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.payment_provider (id, is_enabled, created_at, updated_at, deleted_at) FROM stdin;
pp_system_default	t	2025-07-08 06:46:51.007+00	2025-07-08 06:46:51.007+00	\N
\.


--
-- TOC entry 5061 (class 0 OID 75135)
-- Dependencies: 275
-- Data for Name: payment_session; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.payment_session (id, currency_code, amount, raw_amount, provider_id, data, context, status, authorized_at, payment_collection_id, metadata, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5020 (class 0 OID 74199)
-- Dependencies: 234
-- Data for Name: price; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.price (id, title, price_set_id, currency_code, raw_amount, rules_count, created_at, updated_at, deleted_at, price_list_id, amount, min_quantity, max_quantity) FROM stdin;
price_01JZMCKAV7CETVMJ6T2WJWJPRD	\N	pset_01JZMCKAV8CCD8WQGSCSBRZY2D	usd	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.201+00	2025-07-08 06:47:00.201+00	\N	\N	10	\N	\N
price_01JZMCKAV788YF9YX7VQZ2D5D0	\N	pset_01JZMCKAV8CCD8WQGSCSBRZY2D	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.201+00	2025-07-08 06:47:00.201+00	\N	\N	10	\N	\N
price_01JZMCKAV802ATEA7N8P094RJD	\N	pset_01JZMCKAV8CCD8WQGSCSBRZY2D	eur	{"value": "10", "precision": 20}	1	2025-07-08 06:47:00.202+00	2025-07-08 06:47:00.202+00	\N	\N	10	\N	\N
price_01JZMCKAV82Y4C5461TNCF85SX	\N	pset_01JZMCKAV9GP5W2AFMENNEPENK	usd	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.202+00	2025-07-08 06:47:00.202+00	\N	\N	10	\N	\N
price_01JZMCKAV8547NP50QNWY3BRB4	\N	pset_01JZMCKAV9GP5W2AFMENNEPENK	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.202+00	2025-07-08 06:47:00.202+00	\N	\N	10	\N	\N
price_01JZMCKAV8M87YS3KB9GFVD4K0	\N	pset_01JZMCKAV9GP5W2AFMENNEPENK	eur	{"value": "10", "precision": 20}	1	2025-07-08 06:47:00.202+00	2025-07-08 06:47:00.202+00	\N	\N	10	\N	\N
price_01JZMCKB7YZFCDJQEVEK0508RN	\N	pset_01JZMCKB7Y0MWRDNRW5KW9JX51	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N	\N	10	\N	\N
price_01JZMCKB7YGECXKW15FXYYS33P	\N	pset_01JZMCKB7Y0MWRDNRW5KW9JX51	usd	{"value": "15", "precision": 20}	0	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N	\N	15	\N	\N
price_01JZMCKB7ZR1EG8ABHJ3CRP9QP	\N	pset_01JZMCKB7Z0W5PVN25DW73YXMY	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N	\N	10	\N	\N
price_01JZMCKB7ZX01KSWXHK5Y9BWF2	\N	pset_01JZMCKB7Z0W5PVN25DW73YXMY	usd	{"value": "15", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	15	\N	\N
price_01JZMCKB7Z7289GTTJ7VDFQEDY	\N	pset_01JZMCKB7ZCKYC845FK3SDX8T2	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	10	\N	\N
price_01JZMCKB7Z7807N8YDFBG6F9WG	\N	pset_01JZMCKB7ZCKYC845FK3SDX8T2	usd	{"value": "15", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	15	\N	\N
price_01JZMCKB80VZP7HRFGNBAT9ZF3	\N	pset_01JZMCKB801CSB480S8A1CB8EW	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	10	\N	\N
price_01JZMCKB80YGH0XD0ARD092B1A	\N	pset_01JZMCKB801CSB480S8A1CB8EW	usd	{"value": "15", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	15	\N	\N
price_01JZMCKB80F5CTRX9CDNVC500W	\N	pset_01JZMCKB80W4RVJ9YBE0PT344J	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	10	\N	\N
price_01JZMCKB80Z7KXGW03W6EA7GF9	\N	pset_01JZMCKB80W4RVJ9YBE0PT344J	usd	{"value": "15", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	15	\N	\N
price_01JZMCKB80A42V0HPKD4C15JH2	\N	pset_01JZMCKB81RRB8VEBNV2K0TJNA	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	10	\N	\N
price_01JZMCKB815E2SXXKPPPFH87YV	\N	pset_01JZMCKB81RRB8VEBNV2K0TJNA	usd	{"value": "15", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	15	\N	\N
price_01JZMCKB81VHZBBVVWQ14GMVNB	\N	pset_01JZMCKB814MP60FSVTEYMN4R9	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	10	\N	\N
price_01JZMCKB81KP1R6360DEGAWP62	\N	pset_01JZMCKB814MP60FSVTEYMN4R9	usd	{"value": "15", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	15	\N	\N
price_01JZMCKB81EC2WQQDHC1PHTGVX	\N	pset_01JZMCKB812Z8GTJ8HYZJJB1KY	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	10	\N	\N
price_01JZMCKB81V8SVY3P4GRP1X2JD	\N	pset_01JZMCKB812Z8GTJ8HYZJJB1KY	usd	{"value": "15", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	15	\N	\N
price_01JZMCKB82S2VDE9VR2ZVSMENT	\N	pset_01JZMCKB82MQ4YMPMD4GQWG12P	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	10	\N	\N
price_01JZMCKB8278ESASK57GEWT1HP	\N	pset_01JZMCKB82MQ4YMPMD4GQWG12P	usd	{"value": "15", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	15	\N	\N
price_01JZMCKB823RN61JBK8JZYEGH2	\N	pset_01JZMCKB82K3RNR4P6WTZRN64A	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	10	\N	\N
price_01JZMCKB824FCFJB7XCEE2DZ04	\N	pset_01JZMCKB82K3RNR4P6WTZRN64A	usd	{"value": "15", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	15	\N	\N
price_01JZMCKB838GAFB44N8X94B854	\N	pset_01JZMCKB83WYBD7XK0G6MT8S4M	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	10	\N	\N
price_01JZMCKB83JBTW6YMVEM6H7966	\N	pset_01JZMCKB83WYBD7XK0G6MT8S4M	usd	{"value": "15", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	15	\N	\N
price_01JZMCKB83WT3ABA8WWD1KGMH2	\N	pset_01JZMCKB83VH2JNYM4CYYBR3B8	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	10	\N	\N
price_01JZMCKB83XK6YVF8EAYDA250V	\N	pset_01JZMCKB83VH2JNYM4CYYBR3B8	usd	{"value": "15", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	15	\N	\N
price_01JZMCKB83T6FW38B5D8P0KEET	\N	pset_01JZMCKB84F415H8EE7E3CZ92Z	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	10	\N	\N
price_01JZMCKB83MK84CATBG0ZKA5SF	\N	pset_01JZMCKB84F415H8EE7E3CZ92Z	usd	{"value": "15", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	15	\N	\N
price_01JZMCKB84RGFXPAZJ272ZZSBK	\N	pset_01JZMCKB84W0E8JBR2KFP2V3D3	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	10	\N	\N
price_01JZMCKB847MTKKMSWGRZB651G	\N	pset_01JZMCKB84W0E8JBR2KFP2V3D3	usd	{"value": "15", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	15	\N	\N
price_01JZMCKB84SH580HZAT0W51Q9W	\N	pset_01JZMCKB84768DYFXZPGH9D7SC	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	10	\N	\N
price_01JZMCKB84XHDM3GW3M1RH74CJ	\N	pset_01JZMCKB84768DYFXZPGH9D7SC	usd	{"value": "15", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	15	\N	\N
price_01JZMCKB85VTEZKW47KXBJWK8Y	\N	pset_01JZMCKB85BQCEHJXV5ZJVAKMJ	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	10	\N	\N
price_01JZMCKB85MWTSET00E3NGAG13	\N	pset_01JZMCKB85BQCEHJXV5ZJVAKMJ	usd	{"value": "15", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	15	\N	\N
price_01JZMCKB85G6KFHE06XN6N4FWH	\N	pset_01JZMCKB85Q1VJXPJ9KVFVZD56	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	10	\N	\N
price_01JZMCKB85A10CD07SER7TH0Y5	\N	pset_01JZMCKB85Q1VJXPJ9KVFVZD56	usd	{"value": "15", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	15	\N	\N
price_01JZMCKB859XZ1H9J41ARRZDTJ	\N	pset_01JZMCKB8653P42S9DEDWSZA6F	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	10	\N	\N
price_01JZMCKB86VS3ARDZHPTN7HSJ1	\N	pset_01JZMCKB8653P42S9DEDWSZA6F	usd	{"value": "15", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	15	\N	\N
price_01JZMCKB86Q8CTD9KEF39TFP7Z	\N	pset_01JZMCKB86VBZ3BBAT5DS0P83H	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	10	\N	\N
price_01JZMCKB86AM2TP43BN1P8HG1K	\N	pset_01JZMCKB86VBZ3BBAT5DS0P83H	usd	{"value": "15", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	15	\N	\N
price_01JZMCKB86ZB23TT3HADW27RV7	\N	pset_01JZMCKB86ZYZJWB73BHCGB0SK	eur	{"value": "10", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	10	\N	\N
price_01JZMCKB86ZVM4M2VNJ4DA8RFA	\N	pset_01JZMCKB86ZYZJWB73BHCGB0SK	usd	{"value": "15", "precision": 20}	0	2025-07-08 06:47:00.616+00	2025-07-08 06:47:00.616+00	\N	\N	15	\N	\N
\.


--
-- TOC entry 5022 (class 0 OID 74275)
-- Dependencies: 236
-- Data for Name: price_list; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.price_list (id, status, starts_at, ends_at, rules_count, title, description, type, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5023 (class 0 OID 74285)
-- Dependencies: 237
-- Data for Name: price_list_rule; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.price_list_rule (id, price_list_id, created_at, updated_at, deleted_at, value, attribute) FROM stdin;
\.


--
-- TOC entry 5024 (class 0 OID 74380)
-- Dependencies: 238
-- Data for Name: price_preference; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.price_preference (id, attribute, value, is_tax_inclusive, created_at, updated_at, deleted_at) FROM stdin;
prpref_01JZMCK7070ZV68QTP8PCCZCQ0	currency_code	eur	f	2025-07-08 06:46:56.263+00	2025-07-08 06:46:56.263+00	\N
prpref_01JZMCKAHVHM1F09MZKWWRPWCF	currency_code	usd	f	2025-07-08 06:46:59.899+00	2025-07-08 06:46:59.899+00	\N
prpref_01JZMCKAMJJB8A50XH08M863H0	region_id	reg_01JZMCKAK3AKK5N6B3HTY92YQF	f	2025-07-08 06:46:59.986+00	2025-07-08 06:46:59.986+00	\N
\.


--
-- TOC entry 5021 (class 0 OID 74230)
-- Dependencies: 235
-- Data for Name: price_rule; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.price_rule (id, value, priority, price_id, created_at, updated_at, deleted_at, attribute, operator) FROM stdin;
prule_01JZMCKAV7AK5YV5XG4NS3GVJ0	reg_01JZMCKAK3AKK5N6B3HTY92YQF	0	price_01JZMCKAV802ATEA7N8P094RJD	2025-07-08 06:47:00.202+00	2025-07-08 06:47:00.202+00	\N	region_id	eq
prule_01JZMCKAV81JEWF2Q8B5J0F504	reg_01JZMCKAK3AKK5N6B3HTY92YQF	0	price_01JZMCKAV8M87YS3KB9GFVD4K0	2025-07-08 06:47:00.202+00	2025-07-08 06:47:00.202+00	\N	region_id	eq
\.


--
-- TOC entry 5019 (class 0 OID 74190)
-- Dependencies: 233
-- Data for Name: price_set; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.price_set (id, created_at, updated_at, deleted_at) FROM stdin;
pset_01JZMCKAV8CCD8WQGSCSBRZY2D	2025-07-08 06:47:00.201+00	2025-07-08 06:47:00.201+00	\N
pset_01JZMCKAV9GP5W2AFMENNEPENK	2025-07-08 06:47:00.201+00	2025-07-08 06:47:00.201+00	\N
pset_01JZMCKB7Y0MWRDNRW5KW9JX51	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N
pset_01JZMCKB7Z0W5PVN25DW73YXMY	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N
pset_01JZMCKB7ZCKYC845FK3SDX8T2	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N
pset_01JZMCKB801CSB480S8A1CB8EW	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N
pset_01JZMCKB80W4RVJ9YBE0PT344J	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N
pset_01JZMCKB81RRB8VEBNV2K0TJNA	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N
pset_01JZMCKB814MP60FSVTEYMN4R9	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N
pset_01JZMCKB812Z8GTJ8HYZJJB1KY	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N
pset_01JZMCKB82MQ4YMPMD4GQWG12P	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N
pset_01JZMCKB82K3RNR4P6WTZRN64A	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N
pset_01JZMCKB83WYBD7XK0G6MT8S4M	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N
pset_01JZMCKB83VH2JNYM4CYYBR3B8	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N
pset_01JZMCKB84F415H8EE7E3CZ92Z	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N
pset_01JZMCKB84W0E8JBR2KFP2V3D3	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N
pset_01JZMCKB84768DYFXZPGH9D7SC	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N
pset_01JZMCKB85BQCEHJXV5ZJVAKMJ	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N
pset_01JZMCKB85Q1VJXPJ9KVFVZD56	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N
pset_01JZMCKB8653P42S9DEDWSZA6F	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N
pset_01JZMCKB86VBZ3BBAT5DS0P83H	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N
pset_01JZMCKB86ZYZJWB73BHCGB0SK	2025-07-08 06:47:00.615+00	2025-07-08 06:47:00.615+00	\N
\.


--
-- TOC entry 5007 (class 0 OID 73877)
-- Dependencies: 221
-- Data for Name: product; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.product (id, title, handle, subtitle, description, is_giftcard, status, thumbnail, weight, length, height, width, origin_country, hs_code, mid_code, material, collection_id, type_id, discountable, external_id, created_at, updated_at, deleted_at, metadata) FROM stdin;
prod_01JZMCKB0AW33BVGK0ER596HE2	Medusa Sweatshirt	sweatshirt	\N	Reimagine the feeling of a classic sweatshirt. With our cotton sweatshirt, everyday essentials no longer have to be ordinary.	f	published	https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatshirt-vintage-front.png	400	\N	\N	\N	\N	\N	\N	\N	\N	\N	t	\N	2025-07-08 06:47:00.376+00	2025-07-08 06:47:00.376+00	\N	\N
prod_01JZMCKB0A4F2607V32YST0Z6C	Medusa Sweatpants	sweatpants	\N	Reimagine the feeling of classic sweatpants. With our cotton sweatpants, everyday essentials no longer have to be ordinary.	f	published	https://medusa-public-images.s3.eu-west-1.amazonaws.com/sweatpants-gray-front.png	400	\N	\N	\N	\N	\N	\N	\N	\N	\N	t	\N	2025-07-08 06:47:00.376+00	2025-07-08 06:47:00.376+00	\N	\N
prod_01JZMCKB0AVXF193XRKHS93MXK	Medusa Shorts	shorts	\N	Reimagine the feeling of classic shorts. With our cotton shorts, everyday essentials no longer have to be ordinary.	f	published	https://medusa-public-images.s3.eu-west-1.amazonaws.com/shorts-vintage-front.png	400	\N	\N	\N	\N	\N	\N	\N	\N	\N	t	\N	2025-07-08 06:47:00.376+00	2025-07-08 06:47:00.376+00	\N	\N
prod_01JZMCKB0A4ANCXPDK4Y7Y05VG	Medusa T-Shirt	t-shirt	\N	Reimagine the feeling of a classic T-shirt. With our cotton T-shirts, everyday essentials no longer have to be ordinary.	f	published	https://medusa-public-images.s3.eu-west-1.amazonaws.com/tee-black-front.png	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	t	\N	2025-07-08 06:47:00.375+00	2025-07-11 08:47:55.503+00	\N	{"additional_data": {"product_sale_price": 3560, "product_original_price": 3900}}
\.


--
-- TOC entry 5015 (class 0 OID 73977)
-- Dependencies: 229
-- Data for Name: product_category; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.product_category (id, name, description, handle, mpath, is_active, is_internal, rank, parent_category_id, created_at, updated_at, deleted_at, metadata) FROM stdin;
pcat_01JZMCKAZ4FEXGPT3BNZQ90WGZ	Shirts		shirts	pcat_01JZMCKAZ4FEXGPT3BNZQ90WGZ	t	f	0	\N	2025-07-08 06:47:00.327+00	2025-07-08 06:47:00.327+00	\N	\N
pcat_01JZMCKAZ52TC50XPAZ0DZCB5Q	Sweatshirts		sweatshirts	pcat_01JZMCKAZ52TC50XPAZ0DZCB5Q	t	f	1	\N	2025-07-08 06:47:00.328+00	2025-07-08 06:47:00.328+00	\N	\N
pcat_01JZMCKAZ612NNSN2DWMMRAVCW	Pants		pants	pcat_01JZMCKAZ612NNSN2DWMMRAVCW	t	f	2	\N	2025-07-08 06:47:00.328+00	2025-07-08 06:47:00.328+00	\N	\N
pcat_01JZMCKAZ7F2MXX6Z74GJRFMJ3	Merch		merch	pcat_01JZMCKAZ7F2MXX6Z74GJRFMJ3	t	f	3	\N	2025-07-08 06:47:00.328+00	2025-07-08 06:47:00.328+00	\N	\N
\.


--
-- TOC entry 5017 (class 0 OID 74007)
-- Dependencies: 231
-- Data for Name: product_category_product; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.product_category_product (product_id, product_category_id) FROM stdin;
prod_01JZMCKB0A4ANCXPDK4Y7Y05VG	pcat_01JZMCKAZ4FEXGPT3BNZQ90WGZ
prod_01JZMCKB0AW33BVGK0ER596HE2	pcat_01JZMCKAZ52TC50XPAZ0DZCB5Q
prod_01JZMCKB0A4F2607V32YST0Z6C	pcat_01JZMCKAZ612NNSN2DWMMRAVCW
prod_01JZMCKB0AVXF193XRKHS93MXK	pcat_01JZMCKAZ7F2MXX6Z74GJRFMJ3
\.


--
-- TOC entry 5014 (class 0 OID 73966)
-- Dependencies: 228
-- Data for Name: product_collection; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.product_collection (id, title, handle, metadata, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5009 (class 0 OID 73911)
-- Dependencies: 223
-- Data for Name: product_option; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.product_option (id, title, product_id, metadata, created_at, updated_at, deleted_at) FROM stdin;
opt_01JZMCKB0EQR4Q0EZZW8ZK71P6	Size	prod_01JZMCKB0A4ANCXPDK4Y7Y05VG	\N	2025-07-08 06:47:00.376+00	2025-07-08 06:47:00.376+00	\N
opt_01JZMCKB0FV4WNXK892R68YVX6	Color	prod_01JZMCKB0A4ANCXPDK4Y7Y05VG	\N	2025-07-08 06:47:00.376+00	2025-07-08 06:47:00.376+00	\N
opt_01JZMCKB0JJJRYEPRNA1W04B0Z	Size	prod_01JZMCKB0AW33BVGK0ER596HE2	\N	2025-07-08 06:47:00.376+00	2025-07-08 06:47:00.376+00	\N
opt_01JZMCKB0MM6RGM2ZYFZYKSV4A	Size	prod_01JZMCKB0A4F2607V32YST0Z6C	\N	2025-07-08 06:47:00.377+00	2025-07-08 06:47:00.377+00	\N
opt_01JZMCKB0PJ119GRVH2MJC8GRW	Size	prod_01JZMCKB0AVXF193XRKHS93MXK	\N	2025-07-08 06:47:00.377+00	2025-07-08 06:47:00.377+00	\N
\.


--
-- TOC entry 5010 (class 0 OID 73922)
-- Dependencies: 224
-- Data for Name: product_option_value; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.product_option_value (id, value, option_id, metadata, created_at, updated_at, deleted_at) FROM stdin;
optval_01JZMCKB0DDB3S821SJF5Q2FMD	S	opt_01JZMCKB0EQR4Q0EZZW8ZK71P6	\N	2025-07-08 06:47:00.376+00	2025-07-08 06:47:00.376+00	\N
optval_01JZMCKB0D6EST662E112CZEHF	M	opt_01JZMCKB0EQR4Q0EZZW8ZK71P6	\N	2025-07-08 06:47:00.376+00	2025-07-08 06:47:00.376+00	\N
optval_01JZMCKB0E8SCVYWNHM21754R6	L	opt_01JZMCKB0EQR4Q0EZZW8ZK71P6	\N	2025-07-08 06:47:00.376+00	2025-07-08 06:47:00.376+00	\N
optval_01JZMCKB0EXHE9VYK51C0G5CB7	XL	opt_01JZMCKB0EQR4Q0EZZW8ZK71P6	\N	2025-07-08 06:47:00.376+00	2025-07-08 06:47:00.376+00	\N
optval_01JZMCKB0EEG0SN8WVP3H05KPF	Black	opt_01JZMCKB0FV4WNXK892R68YVX6	\N	2025-07-08 06:47:00.376+00	2025-07-08 06:47:00.376+00	\N
optval_01JZMCKB0E2YCRTPDDC8YKE8XH	White	opt_01JZMCKB0FV4WNXK892R68YVX6	\N	2025-07-08 06:47:00.376+00	2025-07-08 06:47:00.376+00	\N
optval_01JZMCKB0HDQFDYFNYWTWVENCV	S	opt_01JZMCKB0JJJRYEPRNA1W04B0Z	\N	2025-07-08 06:47:00.376+00	2025-07-08 06:47:00.377+00	\N
optval_01JZMCKB0H0PWZ42ENQBX6GFBB	M	opt_01JZMCKB0JJJRYEPRNA1W04B0Z	\N	2025-07-08 06:47:00.377+00	2025-07-08 06:47:00.377+00	\N
optval_01JZMCKB0HN9N6RRKN0PTQR9WW	L	opt_01JZMCKB0JJJRYEPRNA1W04B0Z	\N	2025-07-08 06:47:00.377+00	2025-07-08 06:47:00.377+00	\N
optval_01JZMCKB0JZ8K5GFEBX24CA8WF	XL	opt_01JZMCKB0JJJRYEPRNA1W04B0Z	\N	2025-07-08 06:47:00.377+00	2025-07-08 06:47:00.377+00	\N
optval_01JZMCKB0KM00R579SJZ56RRPS	S	opt_01JZMCKB0MM6RGM2ZYFZYKSV4A	\N	2025-07-08 06:47:00.377+00	2025-07-08 06:47:00.377+00	\N
optval_01JZMCKB0KHKG7AVCHC9SQE318	M	opt_01JZMCKB0MM6RGM2ZYFZYKSV4A	\N	2025-07-08 06:47:00.377+00	2025-07-08 06:47:00.377+00	\N
optval_01JZMCKB0KCRWYHJ82S30XAJ7X	L	opt_01JZMCKB0MM6RGM2ZYFZYKSV4A	\N	2025-07-08 06:47:00.377+00	2025-07-08 06:47:00.377+00	\N
optval_01JZMCKB0M4DWKXFDXDC6WAMRC	XL	opt_01JZMCKB0MM6RGM2ZYFZYKSV4A	\N	2025-07-08 06:47:00.377+00	2025-07-08 06:47:00.377+00	\N
optval_01JZMCKB0NPYS7ZRS9Z4GC0P25	S	opt_01JZMCKB0PJ119GRVH2MJC8GRW	\N	2025-07-08 06:47:00.377+00	2025-07-08 06:47:00.377+00	\N
optval_01JZMCKB0N1PQ8P7QMEFCKGDNV	M	opt_01JZMCKB0PJ119GRVH2MJC8GRW	\N	2025-07-08 06:47:00.377+00	2025-07-08 06:47:00.377+00	\N
optval_01JZMCKB0NCS7CHJY6A3P6G1JB	L	opt_01JZMCKB0PJ119GRVH2MJC8GRW	\N	2025-07-08 06:47:00.377+00	2025-07-08 06:47:00.377+00	\N
optval_01JZMCKB0N4PA2PBV2YBG77N12	XL	opt_01JZMCKB0PJ119GRVH2MJC8GRW	\N	2025-07-08 06:47:00.377+00	2025-07-08 06:47:00.377+00	\N
\.


--
-- TOC entry 5124 (class 0 OID 76161)
-- Dependencies: 338
-- Data for Name: product_sales_channel; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.product_sales_channel (product_id, sales_channel_id, id, created_at, updated_at, deleted_at) FROM stdin;
prod_01JZMCKB0A4ANCXPDK4Y7Y05VG	sc_01JZMCK6SBNHSP5XBG6N7A5BBT	prodsc_01JZMCKB24QVB6QQX6CWXS9GN1	2025-07-08 06:47:00.419735+00	2025-07-08 06:47:00.419735+00	\N
prod_01JZMCKB0AW33BVGK0ER596HE2	sc_01JZMCK6SBNHSP5XBG6N7A5BBT	prodsc_01JZMCKB25V505V5M3HYKX3SXF	2025-07-08 06:47:00.419735+00	2025-07-08 06:47:00.419735+00	\N
prod_01JZMCKB0A4F2607V32YST0Z6C	sc_01JZMCK6SBNHSP5XBG6N7A5BBT	prodsc_01JZMCKB25WRT6KGEWE97DJSVV	2025-07-08 06:47:00.419735+00	2025-07-08 06:47:00.419735+00	\N
prod_01JZMCKB0AVXF193XRKHS93MXK	sc_01JZMCK6SBNHSP5XBG6N7A5BBT	prodsc_01JZMCKB25FCAWAERGAD83ZTQ9	2025-07-08 06:47:00.419735+00	2025-07-08 06:47:00.419735+00	\N
\.


--
-- TOC entry 5131 (class 0 OID 76267)
-- Dependencies: 345
-- Data for Name: product_shipping_profile; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.product_shipping_profile (product_id, shipping_profile_id, id, created_at, updated_at, deleted_at) FROM stdin;
prod_01JZMCKB0A4ANCXPDK4Y7Y05VG	sp_01JZMCK38RDK6Y4EVX4ANQAKB5	prodsp_01JZMCKB2WBTD46TMPNF3KN86S	2025-07-08 06:47:00.443828+00	2025-07-08 06:47:00.443828+00	\N
prod_01JZMCKB0AW33BVGK0ER596HE2	sp_01JZMCK38RDK6Y4EVX4ANQAKB5	prodsp_01JZMCKB2XX85SMZXYVSDEBXH3	2025-07-08 06:47:00.443828+00	2025-07-08 06:47:00.443828+00	\N
prod_01JZMCKB0A4F2607V32YST0Z6C	sp_01JZMCK38RDK6Y4EVX4ANQAKB5	prodsp_01JZMCKB2XVSEPRXG51RR6PYR6	2025-07-08 06:47:00.443828+00	2025-07-08 06:47:00.443828+00	\N
prod_01JZMCKB0AVXF193XRKHS93MXK	sp_01JZMCK38RDK6Y4EVX4ANQAKB5	prodsp_01JZMCKB2YS867ZX68V6JQ6SX5	2025-07-08 06:47:00.443828+00	2025-07-08 06:47:00.443828+00	\N
\.


--
-- TOC entry 5012 (class 0 OID 73944)
-- Dependencies: 226
-- Data for Name: product_tag; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.product_tag (id, value, metadata, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5016 (class 0 OID 73993)
-- Dependencies: 230
-- Data for Name: product_tags; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.product_tags (product_id, product_tag_id) FROM stdin;
\.


--
-- TOC entry 5013 (class 0 OID 73955)
-- Dependencies: 227
-- Data for Name: product_type; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.product_type (id, value, metadata, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5008 (class 0 OID 73893)
-- Dependencies: 222
-- Data for Name: product_variant; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.product_variant (id, title, sku, barcode, ean, upc, allow_backorder, manage_inventory, hs_code, origin_country, mid_code, material, weight, length, height, width, metadata, variant_rank, product_id, created_at, updated_at, deleted_at) FROM stdin;
variant_01JZMCKB498GS10SSVATAJB6PN	S / Black	SHIRT-S-BLACK	\N	\N	\N	f	t	\N	\N	\N	\N	\N	\N	\N	\N	\N	0	prod_01JZMCKB0A4ANCXPDK4Y7Y05VG	2025-07-08 06:47:00.495+00	2025-07-08 06:47:00.495+00	\N
variant_01JZMCKB49DMDSC84CPBX02XV8	S / White	SHIRT-S-WHITE	\N	\N	\N	f	t	\N	\N	\N	\N	\N	\N	\N	\N	\N	0	prod_01JZMCKB0A4ANCXPDK4Y7Y05VG	2025-07-08 06:47:00.495+00	2025-07-08 06:47:00.495+00	\N
variant_01JZMCKB49WKF7HMFJ6GTKRHYB	M / Black	SHIRT-M-BLACK	\N	\N	\N	f	t	\N	\N	\N	\N	\N	\N	\N	\N	\N	0	prod_01JZMCKB0A4ANCXPDK4Y7Y05VG	2025-07-08 06:47:00.495+00	2025-07-08 06:47:00.495+00	\N
variant_01JZMCKB4AABM3PJAAX0T8XEKM	M / White	SHIRT-M-WHITE	\N	\N	\N	f	t	\N	\N	\N	\N	\N	\N	\N	\N	\N	0	prod_01JZMCKB0A4ANCXPDK4Y7Y05VG	2025-07-08 06:47:00.495+00	2025-07-08 06:47:00.495+00	\N
variant_01JZMCKB4ANPYYDFDNM25AJPR2	L / Black	SHIRT-L-BLACK	\N	\N	\N	f	t	\N	\N	\N	\N	\N	\N	\N	\N	\N	0	prod_01JZMCKB0A4ANCXPDK4Y7Y05VG	2025-07-08 06:47:00.495+00	2025-07-08 06:47:00.495+00	\N
variant_01JZMCKB4B7EV3045M8SD1SCHW	L / White	SHIRT-L-WHITE	\N	\N	\N	f	t	\N	\N	\N	\N	\N	\N	\N	\N	\N	0	prod_01JZMCKB0A4ANCXPDK4Y7Y05VG	2025-07-08 06:47:00.495+00	2025-07-08 06:47:00.495+00	\N
variant_01JZMCKB4B7K2EX1FTDP9GN0Y8	XL / Black	SHIRT-XL-BLACK	\N	\N	\N	f	t	\N	\N	\N	\N	\N	\N	\N	\N	\N	0	prod_01JZMCKB0A4ANCXPDK4Y7Y05VG	2025-07-08 06:47:00.495+00	2025-07-08 06:47:00.495+00	\N
variant_01JZMCKB4BDX6H0EHFGKC5C6X0	XL / White	SHIRT-XL-WHITE	\N	\N	\N	f	t	\N	\N	\N	\N	\N	\N	\N	\N	\N	0	prod_01JZMCKB0A4ANCXPDK4Y7Y05VG	2025-07-08 06:47:00.495+00	2025-07-08 06:47:00.495+00	\N
variant_01JZMCKB4CA3GCBSXEKWW1GKPA	S	SWEATSHIRT-S	\N	\N	\N	f	t	\N	\N	\N	\N	\N	\N	\N	\N	\N	0	prod_01JZMCKB0AW33BVGK0ER596HE2	2025-07-08 06:47:00.495+00	2025-07-08 06:47:00.495+00	\N
variant_01JZMCKB4C7XCBN2X5EXJSW94P	M	SWEATSHIRT-M	\N	\N	\N	f	t	\N	\N	\N	\N	\N	\N	\N	\N	\N	0	prod_01JZMCKB0AW33BVGK0ER596HE2	2025-07-08 06:47:00.495+00	2025-07-08 06:47:00.496+00	\N
variant_01JZMCKB4CZAZWH1Z2FQGDSCJC	L	SWEATSHIRT-L	\N	\N	\N	f	t	\N	\N	\N	\N	\N	\N	\N	\N	\N	0	prod_01JZMCKB0AW33BVGK0ER596HE2	2025-07-08 06:47:00.496+00	2025-07-08 06:47:00.496+00	\N
variant_01JZMCKB4CTAH4D2KKCWCFGNX0	XL	SWEATSHIRT-XL	\N	\N	\N	f	t	\N	\N	\N	\N	\N	\N	\N	\N	\N	0	prod_01JZMCKB0AW33BVGK0ER596HE2	2025-07-08 06:47:00.496+00	2025-07-08 06:47:00.496+00	\N
variant_01JZMCKB4DRJT1GX8X3Q91ETM6	S	SWEATPANTS-S	\N	\N	\N	f	t	\N	\N	\N	\N	\N	\N	\N	\N	\N	0	prod_01JZMCKB0A4F2607V32YST0Z6C	2025-07-08 06:47:00.496+00	2025-07-08 06:47:00.496+00	\N
variant_01JZMCKB4D002XNVF4XVK693VE	M	SWEATPANTS-M	\N	\N	\N	f	t	\N	\N	\N	\N	\N	\N	\N	\N	\N	0	prod_01JZMCKB0A4F2607V32YST0Z6C	2025-07-08 06:47:00.496+00	2025-07-08 06:47:00.496+00	\N
variant_01JZMCKB4D903SN1FABZNHK2SZ	L	SWEATPANTS-L	\N	\N	\N	f	t	\N	\N	\N	\N	\N	\N	\N	\N	\N	0	prod_01JZMCKB0A4F2607V32YST0Z6C	2025-07-08 06:47:00.496+00	2025-07-08 06:47:00.496+00	\N
variant_01JZMCKB4D4VNWYTEZZJ9XQG8Q	XL	SWEATPANTS-XL	\N	\N	\N	f	t	\N	\N	\N	\N	\N	\N	\N	\N	\N	0	prod_01JZMCKB0A4F2607V32YST0Z6C	2025-07-08 06:47:00.496+00	2025-07-08 06:47:00.496+00	\N
variant_01JZMCKB4E9AV1KMM3FXSP04R5	S	SHORTS-S	\N	\N	\N	f	t	\N	\N	\N	\N	\N	\N	\N	\N	\N	0	prod_01JZMCKB0AVXF193XRKHS93MXK	2025-07-08 06:47:00.496+00	2025-07-08 06:47:00.496+00	\N
variant_01JZMCKB4EW60K420MARAMC8WW	M	SHORTS-M	\N	\N	\N	f	t	\N	\N	\N	\N	\N	\N	\N	\N	\N	0	prod_01JZMCKB0AVXF193XRKHS93MXK	2025-07-08 06:47:00.496+00	2025-07-08 06:47:00.496+00	\N
variant_01JZMCKB4END3H9RYSNSPZP12C	L	SHORTS-L	\N	\N	\N	f	t	\N	\N	\N	\N	\N	\N	\N	\N	\N	0	prod_01JZMCKB0AVXF193XRKHS93MXK	2025-07-08 06:47:00.496+00	2025-07-08 06:47:00.496+00	\N
variant_01JZMCKB4ED6S7NEPVBYV7ZYY1	XL	SHORTS-XL	\N	\N	\N	f	t	\N	\N	\N	\N	\N	\N	\N	\N	\N	0	prod_01JZMCKB0AVXF193XRKHS93MXK	2025-07-08 06:47:00.496+00	2025-07-08 06:47:00.496+00	\N
\.


--
-- TOC entry 5123 (class 0 OID 76152)
-- Dependencies: 337
-- Data for Name: product_variant_inventory_item; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.product_variant_inventory_item (variant_id, inventory_item_id, id, required_quantity, created_at, updated_at, deleted_at) FROM stdin;
variant_01JZMCKB498GS10SSVATAJB6PN	iitem_01JZMCKB61HYV0YM0A58WD583V	pvitem_01JZMCKB754G46DPY16NRFQSP4	1	2025-07-08 06:47:00.580906+00	2025-07-08 06:47:00.580906+00	\N
variant_01JZMCKB49DMDSC84CPBX02XV8	iitem_01JZMCKB61VBPVQP4EQS9GYAA9	pvitem_01JZMCKB764ZWHTMKPNV60VEX3	1	2025-07-08 06:47:00.580906+00	2025-07-08 06:47:00.580906+00	\N
variant_01JZMCKB49WKF7HMFJ6GTKRHYB	iitem_01JZMCKB61VBJYXFEZPF0E9DZ2	pvitem_01JZMCKB769CX0R4HGXQ853FSG	1	2025-07-08 06:47:00.580906+00	2025-07-08 06:47:00.580906+00	\N
variant_01JZMCKB4AABM3PJAAX0T8XEKM	iitem_01JZMCKB61B48Y9XBR02S2VSFZ	pvitem_01JZMCKB765T8KMPC5SE8A6J15	1	2025-07-08 06:47:00.580906+00	2025-07-08 06:47:00.580906+00	\N
variant_01JZMCKB4ANPYYDFDNM25AJPR2	iitem_01JZMCKB61WGY5590T4C2DTRHD	pvitem_01JZMCKB7664D695KD8AXR75NF	1	2025-07-08 06:47:00.580906+00	2025-07-08 06:47:00.580906+00	\N
variant_01JZMCKB4B7EV3045M8SD1SCHW	iitem_01JZMCKB61Y487FBGZ0SS3VBKR	pvitem_01JZMCKB76TWG4AKG8TB0QGG2Q	1	2025-07-08 06:47:00.580906+00	2025-07-08 06:47:00.580906+00	\N
variant_01JZMCKB4B7K2EX1FTDP9GN0Y8	iitem_01JZMCKB61QH08E5JWM2YM9AEW	pvitem_01JZMCKB77X6E9T8GHTG4C49D7	1	2025-07-08 06:47:00.580906+00	2025-07-08 06:47:00.580906+00	\N
variant_01JZMCKB4BDX6H0EHFGKC5C6X0	iitem_01JZMCKB61EGCFSV754JRAP4TV	pvitem_01JZMCKB77XA3CXNWJHZJZPWZ6	1	2025-07-08 06:47:00.580906+00	2025-07-08 06:47:00.580906+00	\N
variant_01JZMCKB4CA3GCBSXEKWW1GKPA	iitem_01JZMCKB62HMYXW5FEGNXNBR0V	pvitem_01JZMCKB775Y1RS2YWYVFM30F4	1	2025-07-08 06:47:00.580906+00	2025-07-08 06:47:00.580906+00	\N
variant_01JZMCKB4C7XCBN2X5EXJSW94P	iitem_01JZMCKB62MNZ7CBHE7MK62JT2	pvitem_01JZMCKB77YDQKB0V7YSEA5R7P	1	2025-07-08 06:47:00.580906+00	2025-07-08 06:47:00.580906+00	\N
variant_01JZMCKB4CZAZWH1Z2FQGDSCJC	iitem_01JZMCKB62XMNRX5Q8606XAGBK	pvitem_01JZMCKB77BGEA5XYFH3GXPX31	1	2025-07-08 06:47:00.580906+00	2025-07-08 06:47:00.580906+00	\N
variant_01JZMCKB4CTAH4D2KKCWCFGNX0	iitem_01JZMCKB62MAX8Y9DCABCE5C8J	pvitem_01JZMCKB77BH32S0MTDM2Q82VT	1	2025-07-08 06:47:00.580906+00	2025-07-08 06:47:00.580906+00	\N
variant_01JZMCKB4DRJT1GX8X3Q91ETM6	iitem_01JZMCKB62D7W4TVR86MT9SKMZ	pvitem_01JZMCKB77NTMJ0PW2DAXPGKBY	1	2025-07-08 06:47:00.580906+00	2025-07-08 06:47:00.580906+00	\N
variant_01JZMCKB4D002XNVF4XVK693VE	iitem_01JZMCKB62A7EHECKKGNNKCQSX	pvitem_01JZMCKB78XH7KZYQP9XPTTMVT	1	2025-07-08 06:47:00.580906+00	2025-07-08 06:47:00.580906+00	\N
variant_01JZMCKB4D903SN1FABZNHK2SZ	iitem_01JZMCKB62GCAZ9Q12930BB8F7	pvitem_01JZMCKB78B4BGX7PWXRTN814T	1	2025-07-08 06:47:00.580906+00	2025-07-08 06:47:00.580906+00	\N
variant_01JZMCKB4D4VNWYTEZZJ9XQG8Q	iitem_01JZMCKB62R06ZEK17RNDHDYF8	pvitem_01JZMCKB78ZRP9WNVEMZ8G0TE1	1	2025-07-08 06:47:00.580906+00	2025-07-08 06:47:00.580906+00	\N
variant_01JZMCKB4E9AV1KMM3FXSP04R5	iitem_01JZMCKB631RS61MWJMAAHCXA9	pvitem_01JZMCKB78QNPVVS2YP4P5X7C4	1	2025-07-08 06:47:00.580906+00	2025-07-08 06:47:00.580906+00	\N
variant_01JZMCKB4EW60K420MARAMC8WW	iitem_01JZMCKB63JGQGF3PFZC3XYH6K	pvitem_01JZMCKB78M7G5ZJCRV0X4AT6K	1	2025-07-08 06:47:00.580906+00	2025-07-08 06:47:00.580906+00	\N
variant_01JZMCKB4END3H9RYSNSPZP12C	iitem_01JZMCKB6342GCVMH3MS3XJKDY	pvitem_01JZMCKB78RVWWMA60H90QNBWC	1	2025-07-08 06:47:00.580906+00	2025-07-08 06:47:00.580906+00	\N
variant_01JZMCKB4ED6S7NEPVBYV7ZYY1	iitem_01JZMCKB63EVXC3TQ7E5JCBGWW	pvitem_01JZMCKB78C57X94WJE2AF1MZT	1	2025-07-08 06:47:00.580906+00	2025-07-08 06:47:00.580906+00	\N
\.


--
-- TOC entry 5018 (class 0 OID 74014)
-- Dependencies: 232
-- Data for Name: product_variant_option; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.product_variant_option (variant_id, option_value_id) FROM stdin;
variant_01JZMCKB498GS10SSVATAJB6PN	optval_01JZMCKB0DDB3S821SJF5Q2FMD
variant_01JZMCKB498GS10SSVATAJB6PN	optval_01JZMCKB0EEG0SN8WVP3H05KPF
variant_01JZMCKB49DMDSC84CPBX02XV8	optval_01JZMCKB0DDB3S821SJF5Q2FMD
variant_01JZMCKB49DMDSC84CPBX02XV8	optval_01JZMCKB0E2YCRTPDDC8YKE8XH
variant_01JZMCKB49WKF7HMFJ6GTKRHYB	optval_01JZMCKB0D6EST662E112CZEHF
variant_01JZMCKB49WKF7HMFJ6GTKRHYB	optval_01JZMCKB0EEG0SN8WVP3H05KPF
variant_01JZMCKB4AABM3PJAAX0T8XEKM	optval_01JZMCKB0D6EST662E112CZEHF
variant_01JZMCKB4AABM3PJAAX0T8XEKM	optval_01JZMCKB0E2YCRTPDDC8YKE8XH
variant_01JZMCKB4ANPYYDFDNM25AJPR2	optval_01JZMCKB0E8SCVYWNHM21754R6
variant_01JZMCKB4ANPYYDFDNM25AJPR2	optval_01JZMCKB0EEG0SN8WVP3H05KPF
variant_01JZMCKB4B7EV3045M8SD1SCHW	optval_01JZMCKB0E8SCVYWNHM21754R6
variant_01JZMCKB4B7EV3045M8SD1SCHW	optval_01JZMCKB0E2YCRTPDDC8YKE8XH
variant_01JZMCKB4B7K2EX1FTDP9GN0Y8	optval_01JZMCKB0EXHE9VYK51C0G5CB7
variant_01JZMCKB4B7K2EX1FTDP9GN0Y8	optval_01JZMCKB0EEG0SN8WVP3H05KPF
variant_01JZMCKB4BDX6H0EHFGKC5C6X0	optval_01JZMCKB0EXHE9VYK51C0G5CB7
variant_01JZMCKB4BDX6H0EHFGKC5C6X0	optval_01JZMCKB0E2YCRTPDDC8YKE8XH
variant_01JZMCKB4CA3GCBSXEKWW1GKPA	optval_01JZMCKB0HDQFDYFNYWTWVENCV
variant_01JZMCKB4C7XCBN2X5EXJSW94P	optval_01JZMCKB0H0PWZ42ENQBX6GFBB
variant_01JZMCKB4CZAZWH1Z2FQGDSCJC	optval_01JZMCKB0HN9N6RRKN0PTQR9WW
variant_01JZMCKB4CTAH4D2KKCWCFGNX0	optval_01JZMCKB0JZ8K5GFEBX24CA8WF
variant_01JZMCKB4DRJT1GX8X3Q91ETM6	optval_01JZMCKB0KM00R579SJZ56RRPS
variant_01JZMCKB4D002XNVF4XVK693VE	optval_01JZMCKB0KHKG7AVCHC9SQE318
variant_01JZMCKB4D903SN1FABZNHK2SZ	optval_01JZMCKB0KCRWYHJ82S30XAJ7X
variant_01JZMCKB4D4VNWYTEZZJ9XQG8Q	optval_01JZMCKB0M4DWKXFDXDC6WAMRC
variant_01JZMCKB4E9AV1KMM3FXSP04R5	optval_01JZMCKB0NPYS7ZRS9Z4GC0P25
variant_01JZMCKB4EW60K420MARAMC8WW	optval_01JZMCKB0N1PQ8P7QMEFCKGDNV
variant_01JZMCKB4END3H9RYSNSPZP12C	optval_01JZMCKB0NCS7CHJY6A3P6G1JB
variant_01JZMCKB4ED6S7NEPVBYV7ZYY1	optval_01JZMCKB0N4PA2PBV2YBG77N12
\.


--
-- TOC entry 5126 (class 0 OID 76244)
-- Dependencies: 340
-- Data for Name: product_variant_price_set; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.product_variant_price_set (variant_id, price_set_id, id, created_at, updated_at, deleted_at) FROM stdin;
variant_01JZMCKB498GS10SSVATAJB6PN	pset_01JZMCKB7Y0MWRDNRW5KW9JX51	pvps_01JZMCKBAEW75BY16JH4SGAVRB	2025-07-08 06:47:00.684941+00	2025-07-08 06:47:00.684941+00	\N
variant_01JZMCKB49DMDSC84CPBX02XV8	pset_01JZMCKB7Z0W5PVN25DW73YXMY	pvps_01JZMCKBAFF7FDMFB3RGGB9EH3	2025-07-08 06:47:00.684941+00	2025-07-08 06:47:00.684941+00	\N
variant_01JZMCKB49WKF7HMFJ6GTKRHYB	pset_01JZMCKB7ZCKYC845FK3SDX8T2	pvps_01JZMCKBAF0VW85VJC2J7ZDMYQ	2025-07-08 06:47:00.684941+00	2025-07-08 06:47:00.684941+00	\N
variant_01JZMCKB4AABM3PJAAX0T8XEKM	pset_01JZMCKB801CSB480S8A1CB8EW	pvps_01JZMCKBAFB647ZC41D7MBN8W6	2025-07-08 06:47:00.684941+00	2025-07-08 06:47:00.684941+00	\N
variant_01JZMCKB4ANPYYDFDNM25AJPR2	pset_01JZMCKB80W4RVJ9YBE0PT344J	pvps_01JZMCKBAFF9RCWTNWWP5X1ED2	2025-07-08 06:47:00.684941+00	2025-07-08 06:47:00.684941+00	\N
variant_01JZMCKB4B7EV3045M8SD1SCHW	pset_01JZMCKB81RRB8VEBNV2K0TJNA	pvps_01JZMCKBAFFN1AMQPH0A366JR3	2025-07-08 06:47:00.684941+00	2025-07-08 06:47:00.684941+00	\N
variant_01JZMCKB4B7K2EX1FTDP9GN0Y8	pset_01JZMCKB814MP60FSVTEYMN4R9	pvps_01JZMCKBAG4RTA5ZP14C027J29	2025-07-08 06:47:00.684941+00	2025-07-08 06:47:00.684941+00	\N
variant_01JZMCKB4BDX6H0EHFGKC5C6X0	pset_01JZMCKB812Z8GTJ8HYZJJB1KY	pvps_01JZMCKBAGYJY2XNC51C9C0AQD	2025-07-08 06:47:00.684941+00	2025-07-08 06:47:00.684941+00	\N
variant_01JZMCKB4CA3GCBSXEKWW1GKPA	pset_01JZMCKB82MQ4YMPMD4GQWG12P	pvps_01JZMCKBAG0PQC54CBGKARKZM5	2025-07-08 06:47:00.684941+00	2025-07-08 06:47:00.684941+00	\N
variant_01JZMCKB4C7XCBN2X5EXJSW94P	pset_01JZMCKB82K3RNR4P6WTZRN64A	pvps_01JZMCKBAG7T46GK4KH698484G	2025-07-08 06:47:00.684941+00	2025-07-08 06:47:00.684941+00	\N
variant_01JZMCKB4CZAZWH1Z2FQGDSCJC	pset_01JZMCKB83WYBD7XK0G6MT8S4M	pvps_01JZMCKBAH8VM8AK4MMCWPGE9H	2025-07-08 06:47:00.684941+00	2025-07-08 06:47:00.684941+00	\N
variant_01JZMCKB4CTAH4D2KKCWCFGNX0	pset_01JZMCKB83VH2JNYM4CYYBR3B8	pvps_01JZMCKBAHGJ6B3F2AFEH4GVM8	2025-07-08 06:47:00.684941+00	2025-07-08 06:47:00.684941+00	\N
variant_01JZMCKB4DRJT1GX8X3Q91ETM6	pset_01JZMCKB84F415H8EE7E3CZ92Z	pvps_01JZMCKBAHPV0KFZAVYNAJSVGX	2025-07-08 06:47:00.684941+00	2025-07-08 06:47:00.684941+00	\N
variant_01JZMCKB4D002XNVF4XVK693VE	pset_01JZMCKB84W0E8JBR2KFP2V3D3	pvps_01JZMCKBAHRNHSCQ2G0YJY4GQS	2025-07-08 06:47:00.684941+00	2025-07-08 06:47:00.684941+00	\N
variant_01JZMCKB4D903SN1FABZNHK2SZ	pset_01JZMCKB84768DYFXZPGH9D7SC	pvps_01JZMCKBAJQ14DY1PE5Z8TBZP4	2025-07-08 06:47:00.684941+00	2025-07-08 06:47:00.684941+00	\N
variant_01JZMCKB4D4VNWYTEZZJ9XQG8Q	pset_01JZMCKB85BQCEHJXV5ZJVAKMJ	pvps_01JZMCKBAJ3XYESG91321TDEAS	2025-07-08 06:47:00.684941+00	2025-07-08 06:47:00.684941+00	\N
variant_01JZMCKB4E9AV1KMM3FXSP04R5	pset_01JZMCKB85Q1VJXPJ9KVFVZD56	pvps_01JZMCKBAJXZ32WVPPGYCJ7N5A	2025-07-08 06:47:00.684941+00	2025-07-08 06:47:00.684941+00	\N
variant_01JZMCKB4EW60K420MARAMC8WW	pset_01JZMCKB8653P42S9DEDWSZA6F	pvps_01JZMCKBAJCCMX5CHJDJHP30YG	2025-07-08 06:47:00.684941+00	2025-07-08 06:47:00.684941+00	\N
variant_01JZMCKB4END3H9RYSNSPZP12C	pset_01JZMCKB86VBZ3BBAT5DS0P83H	pvps_01JZMCKBAJYVH3PDE4F3ZTRYD7	2025-07-08 06:47:00.684941+00	2025-07-08 06:47:00.684941+00	\N
variant_01JZMCKB4ED6S7NEPVBYV7ZYY1	pset_01JZMCKB86ZYZJWB73BHCGB0SK	pvps_01JZMCKBAKB5MYP872B3FS37AN	2025-07-08 06:47:00.684941+00	2025-07-08 06:47:00.684941+00	\N
\.


--
-- TOC entry 5027 (class 0 OID 74448)
-- Dependencies: 241
-- Data for Name: promotion; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.promotion (id, code, campaign_id, is_automatic, type, created_at, updated_at, deleted_at, status, is_tax_inclusive) FROM stdin;
\.


--
-- TOC entry 5028 (class 0 OID 74463)
-- Dependencies: 242
-- Data for Name: promotion_application_method; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.promotion_application_method (id, value, raw_value, max_quantity, apply_to_quantity, buy_rules_min_quantity, type, target_type, allocation, promotion_id, created_at, updated_at, deleted_at, currency_code) FROM stdin;
\.


--
-- TOC entry 5025 (class 0 OID 74423)
-- Dependencies: 239
-- Data for Name: promotion_campaign; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.promotion_campaign (id, name, description, campaign_identifier, starts_at, ends_at, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5026 (class 0 OID 74434)
-- Dependencies: 240
-- Data for Name: promotion_campaign_budget; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.promotion_campaign_budget (id, type, campaign_id, "limit", raw_limit, used, raw_used, created_at, updated_at, deleted_at, currency_code) FROM stdin;
\.


--
-- TOC entry 5030 (class 0 OID 74492)
-- Dependencies: 244
-- Data for Name: promotion_promotion_rule; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.promotion_promotion_rule (promotion_id, promotion_rule_id) FROM stdin;
\.


--
-- TOC entry 5029 (class 0 OID 74480)
-- Dependencies: 243
-- Data for Name: promotion_rule; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.promotion_rule (id, description, attribute, operator, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5033 (class 0 OID 74513)
-- Dependencies: 247
-- Data for Name: promotion_rule_value; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.promotion_rule_value (id, promotion_rule_id, value, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5096 (class 0 OID 75761)
-- Dependencies: 310
-- Data for Name: provider_identity; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.provider_identity (id, entity_id, provider, auth_identity_id, user_metadata, provider_metadata, created_at, updated_at, deleted_at) FROM stdin;
01JZMCVZ4N8VG60ER4FX9EQN2Q	<EMAIL>	emailpass	authid_01JZMCVZ4P8NN0ZN3MX85GCV4V	\N	{"password": "c2NyeXB0AA8AAAAIAAAAAVUfCNcfUto79i3z4gq0yKJ04mCvW/lfsPHT3cW4/8Qt6GT+GLC3z6XVwP13nMdc+bYhPgfDkhlnoqsGSPy6Twh/50EZAHxuyfUNWTtkaAQb"}	2025-07-08 06:51:43.127+00	2025-07-08 06:51:43.127+00	\N
\.


--
-- TOC entry 5127 (class 0 OID 76251)
-- Dependencies: 341
-- Data for Name: publishable_api_key_sales_channel; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.publishable_api_key_sales_channel (publishable_key_id, sales_channel_id, id, created_at, updated_at, deleted_at) FROM stdin;
apk_01JZMCKAXRVQR78Q84G3E3Z0W5	sc_01JZMCK6SBNHSP5XBG6N7A5BBT	pksc_01JZMCKAYB9C8RFGQ5QYCE9Q2J	2025-07-08 06:47:00.298777+00	2025-07-08 06:47:00.298777+00	\N
\.


--
-- TOC entry 5063 (class 0 OID 75155)
-- Dependencies: 277
-- Data for Name: refund; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.refund (id, amount, raw_amount, payment_id, created_at, updated_at, deleted_at, created_by, metadata, refund_reason_id, note) FROM stdin;
\.


--
-- TOC entry 5065 (class 0 OID 75214)
-- Dependencies: 279
-- Data for Name: refund_reason; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.refund_reason (id, label, description, metadata, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5048 (class 0 OID 74943)
-- Dependencies: 262
-- Data for Name: region; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.region (id, name, currency_code, metadata, created_at, updated_at, deleted_at, automatic_taxes) FROM stdin;
reg_01JZMCKAK3AKK5N6B3HTY92YQF	Europe	eur	\N	2025-07-08 06:46:59.949+00	2025-07-08 06:46:59.949+00	\N	t
\.


--
-- TOC entry 5049 (class 0 OID 74954)
-- Dependencies: 263
-- Data for Name: region_country; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.region_country (iso_2, iso_3, num_code, name, display_name, region_id, metadata, created_at, updated_at, deleted_at) FROM stdin;
af	afg	004	AFGHANISTAN	Afghanistan	\N	\N	2025-07-08 06:46:50.988+00	2025-07-08 06:46:50.988+00	\N
al	alb	008	ALBANIA	Albania	\N	\N	2025-07-08 06:46:50.988+00	2025-07-08 06:46:50.988+00	\N
dz	dza	012	ALGERIA	Algeria	\N	\N	2025-07-08 06:46:50.988+00	2025-07-08 06:46:50.988+00	\N
as	asm	016	AMERICAN SAMOA	American Samoa	\N	\N	2025-07-08 06:46:50.988+00	2025-07-08 06:46:50.988+00	\N
ad	and	020	ANDORRA	Andorra	\N	\N	2025-07-08 06:46:50.988+00	2025-07-08 06:46:50.988+00	\N
ao	ago	024	ANGOLA	Angola	\N	\N	2025-07-08 06:46:50.988+00	2025-07-08 06:46:50.988+00	\N
ai	aia	660	ANGUILLA	Anguilla	\N	\N	2025-07-08 06:46:50.988+00	2025-07-08 06:46:50.988+00	\N
aq	ata	010	ANTARCTICA	Antarctica	\N	\N	2025-07-08 06:46:50.988+00	2025-07-08 06:46:50.988+00	\N
ag	atg	028	ANTIGUA AND BARBUDA	Antigua and Barbuda	\N	\N	2025-07-08 06:46:50.988+00	2025-07-08 06:46:50.988+00	\N
ar	arg	032	ARGENTINA	Argentina	\N	\N	2025-07-08 06:46:50.988+00	2025-07-08 06:46:50.988+00	\N
am	arm	051	ARMENIA	Armenia	\N	\N	2025-07-08 06:46:50.988+00	2025-07-08 06:46:50.988+00	\N
aw	abw	533	ARUBA	Aruba	\N	\N	2025-07-08 06:46:50.988+00	2025-07-08 06:46:50.988+00	\N
au	aus	036	AUSTRALIA	Australia	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
at	aut	040	AUSTRIA	Austria	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
az	aze	031	AZERBAIJAN	Azerbaijan	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
bs	bhs	044	BAHAMAS	Bahamas	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
bh	bhr	048	BAHRAIN	Bahrain	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
bd	bgd	050	BANGLADESH	Bangladesh	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
bb	brb	052	BARBADOS	Barbados	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
by	blr	112	BELARUS	Belarus	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
be	bel	056	BELGIUM	Belgium	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
bz	blz	084	BELIZE	Belize	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
bj	ben	204	BENIN	Benin	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
bm	bmu	060	BERMUDA	Bermuda	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
bt	btn	064	BHUTAN	Bhutan	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
bo	bol	068	BOLIVIA	Bolivia	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
bq	bes	535	BONAIRE, SINT EUSTATIUS AND SABA	Bonaire, Sint Eustatius and Saba	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
ba	bih	070	BOSNIA AND HERZEGOVINA	Bosnia and Herzegovina	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
bw	bwa	072	BOTSWANA	Botswana	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
bv	bvd	074	BOUVET ISLAND	Bouvet Island	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
br	bra	076	BRAZIL	Brazil	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
io	iot	086	BRITISH INDIAN OCEAN TERRITORY	British Indian Ocean Territory	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
bn	brn	096	BRUNEI DARUSSALAM	Brunei Darussalam	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
bg	bgr	100	BULGARIA	Bulgaria	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
bf	bfa	854	BURKINA FASO	Burkina Faso	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
bi	bdi	108	BURUNDI	Burundi	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
kh	khm	116	CAMBODIA	Cambodia	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
cm	cmr	120	CAMEROON	Cameroon	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
ca	can	124	CANADA	Canada	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
cv	cpv	132	CAPE VERDE	Cape Verde	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
ky	cym	136	CAYMAN ISLANDS	Cayman Islands	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
cf	caf	140	CENTRAL AFRICAN REPUBLIC	Central African Republic	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
td	tcd	148	CHAD	Chad	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
cl	chl	152	CHILE	Chile	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
cn	chn	156	CHINA	China	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
cx	cxr	162	CHRISTMAS ISLAND	Christmas Island	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
cc	cck	166	COCOS (KEELING) ISLANDS	Cocos (Keeling) Islands	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
co	col	170	COLOMBIA	Colombia	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
km	com	174	COMOROS	Comoros	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
cg	cog	178	CONGO	Congo	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
cd	cod	180	CONGO, THE DEMOCRATIC REPUBLIC OF THE	Congo, the Democratic Republic of the	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
ck	cok	184	COOK ISLANDS	Cook Islands	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
cr	cri	188	COSTA RICA	Costa Rica	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
ci	civ	384	COTE D'IVOIRE	Cote D'Ivoire	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
hr	hrv	191	CROATIA	Croatia	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
cu	cub	192	CUBA	Cuba	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
cw	cuw	531	CURAÇAO	Curaçao	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
cy	cyp	196	CYPRUS	Cyprus	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
cz	cze	203	CZECH REPUBLIC	Czech Republic	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
dj	dji	262	DJIBOUTI	Djibouti	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
dm	dma	212	DOMINICA	Dominica	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
do	dom	214	DOMINICAN REPUBLIC	Dominican Republic	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
ec	ecu	218	ECUADOR	Ecuador	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
eg	egy	818	EGYPT	Egypt	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
sv	slv	222	EL SALVADOR	El Salvador	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
gq	gnq	226	EQUATORIAL GUINEA	Equatorial Guinea	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
er	eri	232	ERITREA	Eritrea	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
ee	est	233	ESTONIA	Estonia	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
et	eth	231	ETHIOPIA	Ethiopia	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
fk	flk	238	FALKLAND ISLANDS (MALVINAS)	Falkland Islands (Malvinas)	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
fo	fro	234	FAROE ISLANDS	Faroe Islands	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
fj	fji	242	FIJI	Fiji	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
fi	fin	246	FINLAND	Finland	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
gf	guf	254	FRENCH GUIANA	French Guiana	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
pf	pyf	258	FRENCH POLYNESIA	French Polynesia	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
tf	atf	260	FRENCH SOUTHERN TERRITORIES	French Southern Territories	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
ga	gab	266	GABON	Gabon	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
gm	gmb	270	GAMBIA	Gambia	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
ge	geo	268	GEORGIA	Georgia	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
gh	gha	288	GHANA	Ghana	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
gi	gib	292	GIBRALTAR	Gibraltar	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
gr	grc	300	GREECE	Greece	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
gl	grl	304	GREENLAND	Greenland	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
gd	grd	308	GRENADA	Grenada	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
gp	glp	312	GUADELOUPE	Guadeloupe	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
gu	gum	316	GUAM	Guam	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
gt	gtm	320	GUATEMALA	Guatemala	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
gg	ggy	831	GUERNSEY	Guernsey	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
gn	gin	324	GUINEA	Guinea	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
gw	gnb	624	GUINEA-BISSAU	Guinea-Bissau	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
gy	guy	328	GUYANA	Guyana	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
ht	hti	332	HAITI	Haiti	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
hm	hmd	334	HEARD ISLAND AND MCDONALD ISLANDS	Heard Island And Mcdonald Islands	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
va	vat	336	HOLY SEE (VATICAN CITY STATE)	Holy See (Vatican City State)	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
hn	hnd	340	HONDURAS	Honduras	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
hk	hkg	344	HONG KONG	Hong Kong	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
hu	hun	348	HUNGARY	Hungary	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
is	isl	352	ICELAND	Iceland	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
in	ind	356	INDIA	India	\N	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:50.989+00	\N
id	idn	360	INDONESIA	Indonesia	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
ir	irn	364	IRAN, ISLAMIC REPUBLIC OF	Iran, Islamic Republic of	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
iq	irq	368	IRAQ	Iraq	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
ie	irl	372	IRELAND	Ireland	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
im	imn	833	ISLE OF MAN	Isle Of Man	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
il	isr	376	ISRAEL	Israel	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
jm	jam	388	JAMAICA	Jamaica	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
jp	jpn	392	JAPAN	Japan	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
je	jey	832	JERSEY	Jersey	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
jo	jor	400	JORDAN	Jordan	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
kz	kaz	398	KAZAKHSTAN	Kazakhstan	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
ke	ken	404	KENYA	Kenya	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
ki	kir	296	KIRIBATI	Kiribati	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
kp	prk	408	KOREA, DEMOCRATIC PEOPLE'S REPUBLIC OF	Korea, Democratic People's Republic of	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
kr	kor	410	KOREA, REPUBLIC OF	Korea, Republic of	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
xk	xkx	900	KOSOVO	Kosovo	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
kw	kwt	414	KUWAIT	Kuwait	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
kg	kgz	417	KYRGYZSTAN	Kyrgyzstan	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
la	lao	418	LAO PEOPLE'S DEMOCRATIC REPUBLIC	Lao People's Democratic Republic	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
lv	lva	428	LATVIA	Latvia	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
lb	lbn	422	LEBANON	Lebanon	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
ls	lso	426	LESOTHO	Lesotho	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
lr	lbr	430	LIBERIA	Liberia	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
ly	lby	434	LIBYA	Libya	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
li	lie	438	LIECHTENSTEIN	Liechtenstein	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
lt	ltu	440	LITHUANIA	Lithuania	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
lu	lux	442	LUXEMBOURG	Luxembourg	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
mo	mac	446	MACAO	Macao	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
mg	mdg	450	MADAGASCAR	Madagascar	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
mw	mwi	454	MALAWI	Malawi	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
my	mys	458	MALAYSIA	Malaysia	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
mv	mdv	462	MALDIVES	Maldives	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
ml	mli	466	MALI	Mali	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
mt	mlt	470	MALTA	Malta	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
mh	mhl	584	MARSHALL ISLANDS	Marshall Islands	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
mq	mtq	474	MARTINIQUE	Martinique	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
mr	mrt	478	MAURITANIA	Mauritania	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
mu	mus	480	MAURITIUS	Mauritius	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
yt	myt	175	MAYOTTE	Mayotte	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
mx	mex	484	MEXICO	Mexico	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
fm	fsm	583	MICRONESIA, FEDERATED STATES OF	Micronesia, Federated States of	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
md	mda	498	MOLDOVA, REPUBLIC OF	Moldova, Republic of	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
mc	mco	492	MONACO	Monaco	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
mn	mng	496	MONGOLIA	Mongolia	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
me	mne	499	MONTENEGRO	Montenegro	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
ms	msr	500	MONTSERRAT	Montserrat	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
ma	mar	504	MOROCCO	Morocco	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
mz	moz	508	MOZAMBIQUE	Mozambique	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
mm	mmr	104	MYANMAR	Myanmar	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
na	nam	516	NAMIBIA	Namibia	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
nr	nru	520	NAURU	Nauru	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
np	npl	524	NEPAL	Nepal	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
nl	nld	528	NETHERLANDS	Netherlands	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
nc	ncl	540	NEW CALEDONIA	New Caledonia	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
nz	nzl	554	NEW ZEALAND	New Zealand	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
ni	nic	558	NICARAGUA	Nicaragua	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
ne	ner	562	NIGER	Niger	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
ng	nga	566	NIGERIA	Nigeria	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
nu	niu	570	NIUE	Niue	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
nf	nfk	574	NORFOLK ISLAND	Norfolk Island	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
mk	mkd	807	NORTH MACEDONIA	North Macedonia	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
mp	mnp	580	NORTHERN MARIANA ISLANDS	Northern Mariana Islands	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
no	nor	578	NORWAY	Norway	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
om	omn	512	OMAN	Oman	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
pk	pak	586	PAKISTAN	Pakistan	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
pw	plw	585	PALAU	Palau	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
ps	pse	275	PALESTINIAN TERRITORY, OCCUPIED	Palestinian Territory, Occupied	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
pa	pan	591	PANAMA	Panama	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
pg	png	598	PAPUA NEW GUINEA	Papua New Guinea	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
py	pry	600	PARAGUAY	Paraguay	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
pe	per	604	PERU	Peru	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
ph	phl	608	PHILIPPINES	Philippines	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
pn	pcn	612	PITCAIRN	Pitcairn	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
pl	pol	616	POLAND	Poland	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
pt	prt	620	PORTUGAL	Portugal	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
pr	pri	630	PUERTO RICO	Puerto Rico	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
qa	qat	634	QATAR	Qatar	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
re	reu	638	REUNION	Reunion	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
ro	rom	642	ROMANIA	Romania	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
ru	rus	643	RUSSIAN FEDERATION	Russian Federation	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
rw	rwa	646	RWANDA	Rwanda	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
bl	blm	652	SAINT BARTHÉLEMY	Saint Barthélemy	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
sh	shn	654	SAINT HELENA	Saint Helena	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
kn	kna	659	SAINT KITTS AND NEVIS	Saint Kitts and Nevis	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
lc	lca	662	SAINT LUCIA	Saint Lucia	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
mf	maf	663	SAINT MARTIN (FRENCH PART)	Saint Martin (French part)	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
pm	spm	666	SAINT PIERRE AND MIQUELON	Saint Pierre and Miquelon	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
vc	vct	670	SAINT VINCENT AND THE GRENADINES	Saint Vincent and the Grenadines	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
ws	wsm	882	SAMOA	Samoa	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
sm	smr	674	SAN MARINO	San Marino	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
st	stp	678	SAO TOME AND PRINCIPE	Sao Tome and Principe	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
sa	sau	682	SAUDI ARABIA	Saudi Arabia	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
sn	sen	686	SENEGAL	Senegal	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
rs	srb	688	SERBIA	Serbia	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
sc	syc	690	SEYCHELLES	Seychelles	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
sl	sle	694	SIERRA LEONE	Sierra Leone	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
sg	sgp	702	SINGAPORE	Singapore	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
sx	sxm	534	SINT MAARTEN	Sint Maarten	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
sk	svk	703	SLOVAKIA	Slovakia	\N	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:50.99+00	\N
si	svn	705	SLOVENIA	Slovenia	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
sb	slb	090	SOLOMON ISLANDS	Solomon Islands	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
so	som	706	SOMALIA	Somalia	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
za	zaf	710	SOUTH AFRICA	South Africa	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
gs	sgs	239	SOUTH GEORGIA AND THE SOUTH SANDWICH ISLANDS	South Georgia and the South Sandwich Islands	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
ss	ssd	728	SOUTH SUDAN	South Sudan	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
lk	lka	144	SRI LANKA	Sri Lanka	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
sd	sdn	729	SUDAN	Sudan	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
sr	sur	740	SURINAME	Suriname	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
sj	sjm	744	SVALBARD AND JAN MAYEN	Svalbard and Jan Mayen	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
sz	swz	748	SWAZILAND	Swaziland	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
ch	che	756	SWITZERLAND	Switzerland	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
sy	syr	760	SYRIAN ARAB REPUBLIC	Syrian Arab Republic	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
tw	twn	158	TAIWAN, PROVINCE OF CHINA	Taiwan, Province of China	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
tj	tjk	762	TAJIKISTAN	Tajikistan	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
tz	tza	834	TANZANIA, UNITED REPUBLIC OF	Tanzania, United Republic of	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
th	tha	764	THAILAND	Thailand	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
tl	tls	626	TIMOR LESTE	Timor Leste	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
tg	tgo	768	TOGO	Togo	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
tk	tkl	772	TOKELAU	Tokelau	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
to	ton	776	TONGA	Tonga	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
tt	tto	780	TRINIDAD AND TOBAGO	Trinidad and Tobago	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
tn	tun	788	TUNISIA	Tunisia	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
tr	tur	792	TURKEY	Turkey	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
tm	tkm	795	TURKMENISTAN	Turkmenistan	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
tc	tca	796	TURKS AND CAICOS ISLANDS	Turks and Caicos Islands	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
tv	tuv	798	TUVALU	Tuvalu	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
ug	uga	800	UGANDA	Uganda	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
ua	ukr	804	UKRAINE	Ukraine	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
ae	are	784	UNITED ARAB EMIRATES	United Arab Emirates	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
us	usa	840	UNITED STATES	United States	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
um	umi	581	UNITED STATES MINOR OUTLYING ISLANDS	United States Minor Outlying Islands	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
uy	ury	858	URUGUAY	Uruguay	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
uz	uzb	860	UZBEKISTAN	Uzbekistan	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
vu	vut	548	VANUATU	Vanuatu	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
ve	ven	862	VENEZUELA	Venezuela	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
vn	vnm	704	VIET NAM	Viet Nam	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
vg	vgb	092	VIRGIN ISLANDS, BRITISH	Virgin Islands, British	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
vi	vir	850	VIRGIN ISLANDS, U.S.	Virgin Islands, U.S.	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
wf	wlf	876	WALLIS AND FUTUNA	Wallis and Futuna	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
eh	esh	732	WESTERN SAHARA	Western Sahara	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
ye	yem	887	YEMEN	Yemen	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
zm	zmb	894	ZAMBIA	Zambia	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
zw	zwe	716	ZIMBABWE	Zimbabwe	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
ax	ala	248	ÅLAND ISLANDS	Åland Islands	\N	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:50.991+00	\N
dk	dnk	208	DENMARK	Denmark	reg_01JZMCKAK3AKK5N6B3HTY92YQF	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:59.949+00	\N
fr	fra	250	FRANCE	France	reg_01JZMCKAK3AKK5N6B3HTY92YQF	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:59.949+00	\N
de	deu	276	GERMANY	Germany	reg_01JZMCKAK3AKK5N6B3HTY92YQF	\N	2025-07-08 06:46:50.989+00	2025-07-08 06:46:59.949+00	\N
it	ita	380	ITALY	Italy	reg_01JZMCKAK3AKK5N6B3HTY92YQF	\N	2025-07-08 06:46:50.99+00	2025-07-08 06:46:59.95+00	\N
es	esp	724	SPAIN	Spain	reg_01JZMCKAK3AKK5N6B3HTY92YQF	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:59.949+00	\N
se	swe	752	SWEDEN	Sweden	reg_01JZMCKAK3AKK5N6B3HTY92YQF	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:59.95+00	\N
gb	gbr	826	UNITED KINGDOM	United Kingdom	reg_01JZMCKAK3AKK5N6B3HTY92YQF	\N	2025-07-08 06:46:50.991+00	2025-07-08 06:46:59.949+00	\N
\.


--
-- TOC entry 5129 (class 0 OID 76259)
-- Dependencies: 343
-- Data for Name: region_payment_provider; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.region_payment_provider (region_id, payment_provider_id, id, created_at, updated_at, deleted_at) FROM stdin;
reg_01JZMCKAK3AKK5N6B3HTY92YQF	pp_system_default	regpp_01JZMCKAMWSK23XPXKKH7HN66J	2025-07-08 06:46:59.994945+00	2025-07-08 06:46:59.994945+00	\N
\.


--
-- TOC entry 5006 (class 0 OID 73801)
-- Dependencies: 220
-- Data for Name: reservation_item; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.reservation_item (id, created_at, updated_at, deleted_at, line_item_id, location_id, quantity, external_id, description, created_by, metadata, inventory_item_id, allow_backorder, raw_quantity) FROM stdin;
\.


--
-- TOC entry 5085 (class 0 OID 75572)
-- Dependencies: 299
-- Data for Name: return; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.return (id, order_id, claim_id, exchange_id, order_version, display_id, status, no_notification, refund_amount, raw_refund_amount, metadata, created_at, updated_at, deleted_at, received_at, canceled_at, location_id, requested_at, created_by) FROM stdin;
\.


--
-- TOC entry 5125 (class 0 OID 76162)
-- Dependencies: 339
-- Data for Name: return_fulfillment; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.return_fulfillment (return_id, fulfillment_id, id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5086 (class 0 OID 75587)
-- Dependencies: 300
-- Data for Name: return_item; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.return_item (id, return_id, reason_id, item_id, quantity, raw_quantity, received_quantity, raw_received_quantity, note, metadata, created_at, updated_at, deleted_at, damaged_quantity, raw_damaged_quantity) FROM stdin;
\.


--
-- TOC entry 5083 (class 0 OID 75466)
-- Dependencies: 297
-- Data for Name: return_reason; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.return_reason (id, value, label, description, metadata, parent_return_reason_id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- TOC entry 5038 (class 0 OID 74713)
-- Dependencies: 252
-- Data for Name: sales_channel; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.sales_channel (id, name, description, is_disabled, metadata, created_at, updated_at, deleted_at) FROM stdin;
sc_01JZMCK6SBNHSP5XBG6N7A5BBT	Default Sales Channel	Created by Medusa	f	\N	2025-07-08 06:46:56.044+00	2025-07-08 06:46:56.044+00	\N
\.


--
-- TOC entry 5128 (class 0 OID 76258)
-- Dependencies: 342
-- Data for Name: sales_channel_stock_location; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.sales_channel_stock_location (sales_channel_id, stock_location_id, id, created_at, updated_at, deleted_at) FROM stdin;
sc_01JZMCK6SBNHSP5XBG6N7A5BBT	sloc_01JZMCKAPGHGASVYX824CCG1T3	scloc_01JZMCKAX73XN7YGPA3H6X4TB1	2025-07-08 06:47:00.262393+00	2025-07-08 06:47:00.262393+00	\N
\.


--
-- TOC entry 5135 (class 0 OID 76349)
-- Dependencies: 349
-- Data for Name: script_migrations; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.script_migrations (id, script_name, created_at, finished_at) FROM stdin;
1	migrate-tax-region-provider.js	2025-07-08 06:46:52.238396+00	2025-07-08 06:46:52.392002+00
2	migrate-product-shipping-profile.js	2025-07-08 06:46:52.417482+00	2025-07-08 06:46:52.460268+00
\.


--
-- TOC entry 5102 (class 0 OID 75839)
-- Dependencies: 316
-- Data for Name: service_zone; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.service_zone (id, name, metadata, fulfillment_set_id, created_at, updated_at, deleted_at) FROM stdin;
serzo_01JZMCKAQTT6G3MJMBKQ1245MP	Europe	\N	fuset_01JZMCKAQVR63W8XTVQE1K3GJX	2025-07-08 06:47:00.091+00	2025-07-08 06:47:00.091+00	\N
\.


--
-- TOC entry 5106 (class 0 OID 75888)
-- Dependencies: 320
-- Data for Name: shipping_option; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.shipping_option (id, name, price_type, service_zone_id, shipping_profile_id, provider_id, data, metadata, shipping_option_type_id, created_at, updated_at, deleted_at) FROM stdin;
so_01JZMCKAT74HM9AV8M7DJS82ET	Standard Shipping	flat	serzo_01JZMCKAQTT6G3MJMBKQ1245MP	sp_01JZMCK38RDK6Y4EVX4ANQAKB5	manual_manual	\N	\N	sotype_01JZMCKAT6ENGC1HH9R4GFTNJJ	2025-07-08 06:47:00.169+00	2025-07-08 06:47:00.169+00	\N
so_01JZMCKAT8XBDR72CM6K2393PP	Express Shipping	flat	serzo_01JZMCKAQTT6G3MJMBKQ1245MP	sp_01JZMCK38RDK6Y4EVX4ANQAKB5	manual_manual	\N	\N	sotype_01JZMCKAT7Z5YQ7TPG3CA8A1JB	2025-07-08 06:47:00.169+00	2025-07-08 06:47:00.169+00	\N
\.


--
-- TOC entry 5130 (class 0 OID 76264)
-- Dependencies: 344
-- Data for Name: shipping_option_price_set; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.shipping_option_price_set (shipping_option_id, price_set_id, id, created_at, updated_at, deleted_at) FROM stdin;
so_01JZMCKAT74HM9AV8M7DJS82ET	pset_01JZMCKAV8CCD8WQGSCSBRZY2D	sops_01JZMCKAWPCKVJSNVCF76GR7KQ	2025-07-08 06:47:00.245518+00	2025-07-08 06:47:00.245518+00	\N
so_01JZMCKAT8XBDR72CM6K2393PP	pset_01JZMCKAV9GP5W2AFMENNEPENK	sops_01JZMCKAWQ5T037AK56AH5KJWW	2025-07-08 06:47:00.245518+00	2025-07-08 06:47:00.245518+00	\N
\.


--
-- TOC entry 5107 (class 0 OID 75906)
-- Dependencies: 321
-- Data for Name: shipping_option_rule; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.shipping_option_rule (id, attribute, operator, value, shipping_option_id, created_at, updated_at, deleted_at) FROM stdin;
sorul_01JZMCKAT7F81PJTZJP4C30ZBG	enabled_in_store	eq	"true"	so_01JZMCKAT74HM9AV8M7DJS82ET	2025-07-08 06:47:00.169+00	2025-07-08 06:47:00.169+00	\N
sorul_01JZMCKAT7DJT56XSBCC26BS1F	is_return	eq	"false"	so_01JZMCKAT74HM9AV8M7DJS82ET	2025-07-08 06:47:00.169+00	2025-07-08 06:47:00.169+00	\N
sorul_01JZMCKAT7TXBN9404NJQ56GPB	enabled_in_store	eq	"true"	so_01JZMCKAT8XBDR72CM6K2393PP	2025-07-08 06:47:00.169+00	2025-07-08 06:47:00.169+00	\N
sorul_01JZMCKAT834ZSEC43346V0CJK	is_return	eq	"false"	so_01JZMCKAT8XBDR72CM6K2393PP	2025-07-08 06:47:00.169+00	2025-07-08 06:47:00.169+00	\N
\.


--
-- TOC entry 5104 (class 0 OID 75867)
-- Dependencies: 318
-- Data for Name: shipping_option_type; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.shipping_option_type (id, label, description, code, created_at, updated_at, deleted_at) FROM stdin;
sotype_01JZMCKAT6ENGC1HH9R4GFTNJJ	Standard	Ship in 2-3 days.	standard	2025-07-08 06:47:00.168+00	2025-07-08 06:47:00.168+00	\N
sotype_01JZMCKAT7Z5YQ7TPG3CA8A1JB	Express	Ship in 24 hours.	express	2025-07-08 06:47:00.169+00	2025-07-08 06:47:00.169+00	\N
\.


--
-- TOC entry 5105 (class 0 OID 75877)
-- Dependencies: 319
-- Data for Name: shipping_profile; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.shipping_profile (id, name, type, metadata, created_at, updated_at, deleted_at) FROM stdin;
sp_01JZMCK38RDK6Y4EVX4ANQAKB5	Default Shipping Profile	default	\N	2025-07-08 06:46:52.442+00	2025-07-08 06:46:52.442+00	\N
\.


--
-- TOC entry 5003 (class 0 OID 73750)
-- Dependencies: 217
-- Data for Name: stock_location; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.stock_location (id, created_at, updated_at, deleted_at, name, address_id, metadata) FROM stdin;
sloc_01JZMCKAPGHGASVYX824CCG1T3	2025-07-08 06:47:00.049+00	2025-07-08 06:47:00.049+00	\N	European Warehouse	laddr_01JZMCKAPGK4V0RP2GBHEA8K3D	\N
\.


--
-- TOC entry 5002 (class 0 OID 73740)
-- Dependencies: 216
-- Data for Name: stock_location_address; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.stock_location_address (id, created_at, updated_at, deleted_at, address_1, address_2, company, city, country_code, phone, province, postal_code, metadata) FROM stdin;
laddr_01JZMCKAPGK4V0RP2GBHEA8K3D	2025-07-08 06:47:00.049+00	2025-07-08 06:47:00.049+00	\N		\N	\N	Copenhagen	DK	\N	\N	\N	\N
\.


--
-- TOC entry 5051 (class 0 OID 74985)
-- Dependencies: 265
-- Data for Name: store; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.store (id, name, default_sales_channel_id, default_region_id, default_location_id, metadata, created_at, updated_at, deleted_at) FROM stdin;
store_01JZMCK6YE534P1VK201B2N5PD	Medusa Store	sc_01JZMCK6SBNHSP5XBG6N7A5BBT	\N	\N	\N	2025-07-08 06:46:56.205067+00	2025-07-08 06:46:56.205067+00	\N
\.


--
-- TOC entry 5052 (class 0 OID 74997)
-- Dependencies: 266
-- Data for Name: store_currency; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.store_currency (id, currency_code, is_default, store_id, created_at, updated_at, deleted_at) FROM stdin;
stocur_01JZMCKAD6CZXTPQ5YZFGNYGTG	eur	t	store_01JZMCK6YE534P1VK201B2N5PD	2025-07-08 06:46:59.740303+00	2025-07-08 06:46:59.740303+00	\N
stocur_01JZMCKAD6R0RWHF15XSHW21SJ	usd	f	store_01JZMCK6YE534P1VK201B2N5PD	2025-07-08 06:46:59.740303+00	2025-07-08 06:46:59.740303+00	\N
\.


--
-- TOC entry 5053 (class 0 OID 75014)
-- Dependencies: 267
-- Data for Name: tax_provider; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.tax_provider (id, is_enabled, created_at, updated_at, deleted_at) FROM stdin;
tp_system	t	2025-07-08 06:46:51.004+00	2025-07-08 06:46:51.004+00	\N
\.


--
-- TOC entry 5055 (class 0 OID 75036)
-- Dependencies: 269
-- Data for Name: tax_rate; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.tax_rate (id, rate, code, name, is_default, is_combinable, tax_region_id, metadata, created_at, updated_at, created_by, deleted_at) FROM stdin;
\.


--
-- TOC entry 5056 (class 0 OID 75050)
-- Dependencies: 270
-- Data for Name: tax_rate_rule; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.tax_rate_rule (id, tax_rate_id, reference_id, reference, metadata, created_at, updated_at, created_by, deleted_at) FROM stdin;
\.


--
-- TOC entry 5054 (class 0 OID 75022)
-- Dependencies: 268
-- Data for Name: tax_region; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.tax_region (id, provider_id, country_code, province_code, parent_id, metadata, created_at, updated_at, created_by, deleted_at) FROM stdin;
txreg_01JZMCKANQXAVFVJ7EY9J88Y94	tp_system	gb	\N	\N	\N	2025-07-08 06:47:00.025+00	2025-07-08 06:47:00.025+00	\N	\N
txreg_01JZMCKANR247N01S4SCCQ3KGW	tp_system	de	\N	\N	\N	2025-07-08 06:47:00.026+00	2025-07-08 06:47:00.026+00	\N	\N
txreg_01JZMCKANRTMC62ZRGSA1K0DRZ	tp_system	dk	\N	\N	\N	2025-07-08 06:47:00.026+00	2025-07-08 06:47:00.026+00	\N	\N
txreg_01JZMCKANRJFM468Z3GW0DBBKC	tp_system	se	\N	\N	\N	2025-07-08 06:47:00.026+00	2025-07-08 06:47:00.026+00	\N	\N
txreg_01JZMCKANRM3A3EFEKDH6D34SB	tp_system	fr	\N	\N	\N	2025-07-08 06:47:00.026+00	2025-07-08 06:47:00.026+00	\N	\N
txreg_01JZMCKANS4SB20MNV3MGCWRTA	tp_system	es	\N	\N	\N	2025-07-08 06:47:00.026+00	2025-07-08 06:47:00.026+00	\N	\N
txreg_01JZMCKANS85XGCFH92VTE8MXH	tp_system	it	\N	\N	\N	2025-07-08 06:47:00.026+00	2025-07-08 06:47:00.026+00	\N	\N
\.


--
-- TOC entry 5098 (class 0 OID 75794)
-- Dependencies: 312
-- Data for Name: user; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public."user" (id, first_name, last_name, email, avatar_url, metadata, created_at, updated_at, deleted_at) FROM stdin;
user_01JZMCVZ7952TEQF6MG8QJX755	Admin	admin	<EMAIL>	\N	\N	2025-07-08 06:51:43.21+00	2025-07-08 06:51:43.21+00	\N
\.


--
-- TOC entry 5113 (class 0 OID 76078)
-- Dependencies: 327
-- Data for Name: workflow_execution; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.workflow_execution (id, workflow_id, transaction_id, execution, context, state, created_at, updated_at, deleted_at, retention_time, run_id) FROM stdin;
\.


--
-- TOC entry 5149 (class 0 OID 0)
-- Dependencies: 328
-- Name: link_module_migrations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.link_module_migrations_id_seq', 36, true);


--
-- TOC entry 5150 (class 0 OID 0)
-- Dependencies: 214
-- Name: mikro_orm_migrations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.mikro_orm_migrations_id_seq', 111, true);


--
-- TOC entry 5151 (class 0 OID 0)
-- Dependencies: 286
-- Name: order_change_action_ordering_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.order_change_action_ordering_seq', 1, false);


--
-- TOC entry 5152 (class 0 OID 0)
-- Dependencies: 304
-- Name: order_claim_display_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.order_claim_display_id_seq', 1, false);


--
-- TOC entry 5153 (class 0 OID 0)
-- Dependencies: 282
-- Name: order_display_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.order_display_id_seq', 1, false);


--
-- TOC entry 5154 (class 0 OID 0)
-- Dependencies: 301
-- Name: order_exchange_display_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.order_exchange_display_id_seq', 1, false);


--
-- TOC entry 5155 (class 0 OID 0)
-- Dependencies: 298
-- Name: return_display_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.return_display_id_seq', 1, false);


--
-- TOC entry 5156 (class 0 OID 0)
-- Dependencies: 348
-- Name: script_migrations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.script_migrations_id_seq', 2, true);


--
-- TOC entry 4446 (class 2606 OID 75257)
-- Name: account_holder account_holder_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.account_holder
    ADD CONSTRAINT account_holder_pkey PRIMARY KEY (id);


--
-- TOC entry 4381 (class 2606 OID 74978)
-- Name: api_key api_key_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.api_key
    ADD CONSTRAINT api_key_pkey PRIMARY KEY (id);


--
-- TOC entry 4286 (class 2606 OID 74512)
-- Name: application_method_buy_rules application_method_buy_rules_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.application_method_buy_rules
    ADD CONSTRAINT application_method_buy_rules_pkey PRIMARY KEY (application_method_id, promotion_rule_id);


--
-- TOC entry 4284 (class 2606 OID 74505)
-- Name: application_method_target_rules application_method_target_rules_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.application_method_target_rules
    ADD CONSTRAINT application_method_target_rules_pkey PRIMARY KEY (application_method_id, promotion_rule_id);


--
-- TOC entry 4573 (class 2606 OID 75758)
-- Name: auth_identity auth_identity_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.auth_identity
    ADD CONSTRAINT auth_identity_pkey PRIMARY KEY (id);


--
-- TOC entry 4439 (class 2606 OID 75172)
-- Name: capture capture_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.capture
    ADD CONSTRAINT capture_pkey PRIMARY KEY (id);


--
-- TOC entry 4325 (class 2606 OID 74747)
-- Name: cart_address cart_address_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_address
    ADD CONSTRAINT cart_address_pkey PRIMARY KEY (id);


--
-- TOC entry 4339 (class 2606 OID 74783)
-- Name: cart_line_item_adjustment cart_line_item_adjustment_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_line_item_adjustment
    ADD CONSTRAINT cart_line_item_adjustment_pkey PRIMARY KEY (id);


--
-- TOC entry 4333 (class 2606 OID 74760)
-- Name: cart_line_item cart_line_item_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_line_item
    ADD CONSTRAINT cart_line_item_pkey PRIMARY KEY (id);


--
-- TOC entry 4345 (class 2606 OID 74794)
-- Name: cart_line_item_tax_line cart_line_item_tax_line_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_line_item_tax_line
    ADD CONSTRAINT cart_line_item_tax_line_pkey PRIMARY KEY (id);


--
-- TOC entry 4770 (class 2606 OID 76315)
-- Name: cart_payment_collection cart_payment_collection_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_payment_collection
    ADD CONSTRAINT cart_payment_collection_pkey PRIMARY KEY (cart_id, payment_collection_id);


--
-- TOC entry 4322 (class 2606 OID 74732)
-- Name: cart cart_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart
    ADD CONSTRAINT cart_pkey PRIMARY KEY (id);


--
-- TOC entry 4668 (class 2606 OID 76185)
-- Name: cart_promotion cart_promotion_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_promotion
    ADD CONSTRAINT cart_promotion_pkey PRIMARY KEY (cart_id, promotion_id);


--
-- TOC entry 4357 (class 2606 OID 74818)
-- Name: cart_shipping_method_adjustment cart_shipping_method_adjustment_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_shipping_method_adjustment
    ADD CONSTRAINT cart_shipping_method_adjustment_pkey PRIMARY KEY (id);


--
-- TOC entry 4351 (class 2606 OID 74807)
-- Name: cart_shipping_method cart_shipping_method_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_shipping_method
    ADD CONSTRAINT cart_shipping_method_pkey PRIMARY KEY (id);


--
-- TOC entry 4363 (class 2606 OID 74829)
-- Name: cart_shipping_method_tax_line cart_shipping_method_tax_line_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_shipping_method_tax_line
    ADD CONSTRAINT cart_shipping_method_tax_line_pkey PRIMARY KEY (id);


--
-- TOC entry 4368 (class 2606 OID 74932)
-- Name: credit_line credit_line_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.credit_line
    ADD CONSTRAINT credit_line_pkey PRIMARY KEY (id);


--
-- TOC entry 4411 (class 2606 OID 75099)
-- Name: currency currency_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.currency
    ADD CONSTRAINT currency_pkey PRIMARY KEY (code);


--
-- TOC entry 4764 (class 2606 OID 76313)
-- Name: customer_account_holder customer_account_holder_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_account_holder
    ADD CONSTRAINT customer_account_holder_pkey PRIMARY KEY (customer_id, account_holder_id);


--
-- TOC entry 4300 (class 2606 OID 74656)
-- Name: customer_address customer_address_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_address
    ADD CONSTRAINT customer_address_pkey PRIMARY KEY (id);


--
-- TOC entry 4310 (class 2606 OID 74678)
-- Name: customer_group_customer customer_group_customer_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_group_customer
    ADD CONSTRAINT customer_group_customer_pkey PRIMARY KEY (id);


--
-- TOC entry 4305 (class 2606 OID 74668)
-- Name: customer_group customer_group_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_group
    ADD CONSTRAINT customer_group_pkey PRIMARY KEY (id);


--
-- TOC entry 4294 (class 2606 OID 74645)
-- Name: customer customer_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer
    ADD CONSTRAINT customer_pkey PRIMARY KEY (id);


--
-- TOC entry 4590 (class 2606 OID 75816)
-- Name: fulfillment_address fulfillment_address_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillment_address
    ADD CONSTRAINT fulfillment_address_pkey PRIMARY KEY (id);


--
-- TOC entry 4641 (class 2606 OID 75952)
-- Name: fulfillment_item fulfillment_item_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillment_item
    ADD CONSTRAINT fulfillment_item_pkey PRIMARY KEY (id);


--
-- TOC entry 4635 (class 2606 OID 75941)
-- Name: fulfillment_label fulfillment_label_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillment_label
    ADD CONSTRAINT fulfillment_label_pkey PRIMARY KEY (id);


--
-- TOC entry 4631 (class 2606 OID 75926)
-- Name: fulfillment fulfillment_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillment
    ADD CONSTRAINT fulfillment_pkey PRIMARY KEY (id);


--
-- TOC entry 4593 (class 2606 OID 75825)
-- Name: fulfillment_provider fulfillment_provider_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillment_provider
    ADD CONSTRAINT fulfillment_provider_pkey PRIMARY KEY (id);


--
-- TOC entry 4597 (class 2606 OID 75835)
-- Name: fulfillment_set fulfillment_set_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillment_set
    ADD CONSTRAINT fulfillment_set_pkey PRIMARY KEY (id);


--
-- TOC entry 4609 (class 2606 OID 75861)
-- Name: geo_zone geo_zone_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.geo_zone
    ADD CONSTRAINT geo_zone_pkey PRIMARY KEY (id);


--
-- TOC entry 4197 (class 2606 OID 73941)
-- Name: image image_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.image
    ADD CONSTRAINT image_pkey PRIMARY KEY (id);


--
-- TOC entry 4154 (class 2606 OID 73783)
-- Name: inventory_item inventory_item_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inventory_item
    ADD CONSTRAINT inventory_item_pkey PRIMARY KEY (id);


--
-- TOC entry 4161 (class 2606 OID 73797)
-- Name: inventory_level inventory_level_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inventory_level
    ADD CONSTRAINT inventory_level_pkey PRIMARY KEY (id);


--
-- TOC entry 4583 (class 2606 OID 75790)
-- Name: invite invite_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invite
    ADD CONSTRAINT invite_pkey PRIMARY KEY (id);


--
-- TOC entry 4660 (class 2606 OID 76110)
-- Name: link_module_migrations link_module_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.link_module_migrations
    ADD CONSTRAINT link_module_migrations_pkey PRIMARY KEY (id);


--
-- TOC entry 4662 (class 2606 OID 76112)
-- Name: link_module_migrations link_module_migrations_table_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.link_module_migrations
    ADD CONSTRAINT link_module_migrations_table_name_key UNIQUE (table_name);


--
-- TOC entry 4692 (class 2606 OID 76197)
-- Name: location_fulfillment_provider location_fulfillment_provider_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_fulfillment_provider
    ADD CONSTRAINT location_fulfillment_provider_pkey PRIMARY KEY (stock_location_id, fulfillment_provider_id);


--
-- TOC entry 4674 (class 2606 OID 76192)
-- Name: location_fulfillment_set location_fulfillment_set_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_fulfillment_set
    ADD CONSTRAINT location_fulfillment_set_pkey PRIMARY KEY (stock_location_id, fulfillment_set_id);


--
-- TOC entry 4143 (class 2606 OID 73739)
-- Name: mikro_orm_migrations mikro_orm_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.mikro_orm_migrations
    ADD CONSTRAINT mikro_orm_migrations_pkey PRIMARY KEY (id);


--
-- TOC entry 4650 (class 2606 OID 76060)
-- Name: notification notification_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notification
    ADD CONSTRAINT notification_pkey PRIMARY KEY (id);


--
-- TOC entry 4644 (class 2606 OID 76052)
-- Name: notification_provider notification_provider_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notification_provider
    ADD CONSTRAINT notification_provider_pkey PRIMARY KEY (id);


--
-- TOC entry 4450 (class 2606 OID 75280)
-- Name: order_address order_address_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_address
    ADD CONSTRAINT order_address_pkey PRIMARY KEY (id);


--
-- TOC entry 4686 (class 2606 OID 76187)
-- Name: order_cart order_cart_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_cart
    ADD CONSTRAINT order_cart_pkey PRIMARY KEY (order_id, cart_id);


--
-- TOC entry 4483 (class 2606 OID 75360)
-- Name: order_change_action order_change_action_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_change_action
    ADD CONSTRAINT order_change_action_pkey PRIMARY KEY (id);


--
-- TOC entry 4474 (class 2606 OID 75345)
-- Name: order_change order_change_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_change
    ADD CONSTRAINT order_change_pkey PRIMARY KEY (id);


--
-- TOC entry 4566 (class 2606 OID 75680)
-- Name: order_claim_item_image order_claim_item_image_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_claim_item_image
    ADD CONSTRAINT order_claim_item_image_pkey PRIMARY KEY (id);


--
-- TOC entry 4562 (class 2606 OID 75668)
-- Name: order_claim_item order_claim_item_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_claim_item
    ADD CONSTRAINT order_claim_item_pkey PRIMARY KEY (id);


--
-- TOC entry 4557 (class 2606 OID 75645)
-- Name: order_claim order_claim_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_claim
    ADD CONSTRAINT order_claim_pkey PRIMARY KEY (id);


--
-- TOC entry 4570 (class 2606 OID 75738)
-- Name: order_credit_line order_credit_line_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_credit_line
    ADD CONSTRAINT order_credit_line_pkey PRIMARY KEY (id);


--
-- TOC entry 4551 (class 2606 OID 75625)
-- Name: order_exchange_item order_exchange_item_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_exchange_item
    ADD CONSTRAINT order_exchange_item_pkey PRIMARY KEY (id);


--
-- TOC entry 4546 (class 2606 OID 75612)
-- Name: order_exchange order_exchange_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_exchange
    ADD CONSTRAINT order_exchange_pkey PRIMARY KEY (id);


--
-- TOC entry 4680 (class 2606 OID 76200)
-- Name: order_fulfillment order_fulfillment_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_fulfillment
    ADD CONSTRAINT order_fulfillment_pkey PRIMARY KEY (order_id, fulfillment_id);


--
-- TOC entry 4489 (class 2606 OID 75372)
-- Name: order_item order_item_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_item
    ADD CONSTRAINT order_item_pkey PRIMARY KEY (id);


--
-- TOC entry 4508 (class 2606 OID 75420)
-- Name: order_line_item_adjustment order_line_item_adjustment_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_line_item_adjustment
    ADD CONSTRAINT order_line_item_adjustment_pkey PRIMARY KEY (id);


--
-- TOC entry 4502 (class 2606 OID 75399)
-- Name: order_line_item order_line_item_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_line_item
    ADD CONSTRAINT order_line_item_pkey PRIMARY KEY (id);


--
-- TOC entry 4505 (class 2606 OID 75410)
-- Name: order_line_item_tax_line order_line_item_tax_line_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_line_item_tax_line
    ADD CONSTRAINT order_line_item_tax_line_pkey PRIMARY KEY (id);


--
-- TOC entry 4698 (class 2606 OID 76198)
-- Name: order_payment_collection order_payment_collection_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_payment_collection
    ADD CONSTRAINT order_payment_collection_pkey PRIMARY KEY (order_id, payment_collection_id);


--
-- TOC entry 4460 (class 2606 OID 75294)
-- Name: order order_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public."order"
    ADD CONSTRAINT order_pkey PRIMARY KEY (id);


--
-- TOC entry 4704 (class 2606 OID 76199)
-- Name: order_promotion order_promotion_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_promotion
    ADD CONSTRAINT order_promotion_pkey PRIMARY KEY (order_id, promotion_id);


--
-- TOC entry 4514 (class 2606 OID 75441)
-- Name: order_shipping_method_adjustment order_shipping_method_adjustment_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_shipping_method_adjustment
    ADD CONSTRAINT order_shipping_method_adjustment_pkey PRIMARY KEY (id);


--
-- TOC entry 4511 (class 2606 OID 75431)
-- Name: order_shipping_method order_shipping_method_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_shipping_method
    ADD CONSTRAINT order_shipping_method_pkey PRIMARY KEY (id);


--
-- TOC entry 4517 (class 2606 OID 75451)
-- Name: order_shipping_method_tax_line order_shipping_method_tax_line_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_shipping_method_tax_line
    ADD CONSTRAINT order_shipping_method_tax_line_pkey PRIMARY KEY (id);


--
-- TOC entry 4498 (class 2606 OID 75384)
-- Name: order_shipping order_shipping_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_shipping
    ADD CONSTRAINT order_shipping_pkey PRIMARY KEY (id);


--
-- TOC entry 4464 (class 2606 OID 75333)
-- Name: order_summary order_summary_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_summary
    ADD CONSTRAINT order_summary_pkey PRIMARY KEY (id);


--
-- TOC entry 4525 (class 2606 OID 75462)
-- Name: order_transaction order_transaction_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_transaction
    ADD CONSTRAINT order_transaction_pkey PRIMARY KEY (id);


--
-- TOC entry 4419 (class 2606 OID 75134)
-- Name: payment_collection_payment_providers payment_collection_payment_providers_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payment_collection_payment_providers
    ADD CONSTRAINT payment_collection_payment_providers_pkey PRIMARY KEY (payment_collection_id, payment_provider_id);


--
-- TOC entry 4414 (class 2606 OID 75110)
-- Name: payment_collection payment_collection_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payment_collection
    ADD CONSTRAINT payment_collection_pkey PRIMARY KEY (id);


--
-- TOC entry 4430 (class 2606 OID 75154)
-- Name: payment payment_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payment
    ADD CONSTRAINT payment_pkey PRIMARY KEY (id);


--
-- TOC entry 4417 (class 2606 OID 75127)
-- Name: payment_provider payment_provider_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payment_provider
    ADD CONSTRAINT payment_provider_pkey PRIMARY KEY (id);


--
-- TOC entry 4423 (class 2606 OID 75145)
-- Name: payment_session payment_session_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payment_session
    ADD CONSTRAINT payment_session_pkey PRIMARY KEY (id);


--
-- TOC entry 4242 (class 2606 OID 74284)
-- Name: price_list price_list_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.price_list
    ADD CONSTRAINT price_list_pkey PRIMARY KEY (id);


--
-- TOC entry 4247 (class 2606 OID 74293)
-- Name: price_list_rule price_list_rule_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.price_list_rule
    ADD CONSTRAINT price_list_rule_pkey PRIMARY KEY (id);


--
-- TOC entry 4230 (class 2606 OID 74208)
-- Name: price price_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.price
    ADD CONSTRAINT price_pkey PRIMARY KEY (id);


--
-- TOC entry 4251 (class 2606 OID 74389)
-- Name: price_preference price_preference_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.price_preference
    ADD CONSTRAINT price_preference_pkey PRIMARY KEY (id);


--
-- TOC entry 4239 (class 2606 OID 74239)
-- Name: price_rule price_rule_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.price_rule
    ADD CONSTRAINT price_rule_pkey PRIMARY KEY (id);


--
-- TOC entry 4224 (class 2606 OID 74198)
-- Name: price_set price_set_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.price_set
    ADD CONSTRAINT price_set_pkey PRIMARY KEY (id);


--
-- TOC entry 4215 (class 2606 OID 73989)
-- Name: product_category product_category_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_category
    ADD CONSTRAINT product_category_pkey PRIMARY KEY (id);


--
-- TOC entry 4219 (class 2606 OID 74013)
-- Name: product_category_product product_category_product_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_category_product
    ADD CONSTRAINT product_category_product_pkey PRIMARY KEY (product_id, product_category_id);


--
-- TOC entry 4210 (class 2606 OID 73974)
-- Name: product_collection product_collection_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_collection
    ADD CONSTRAINT product_collection_pkey PRIMARY KEY (id);


--
-- TOC entry 4187 (class 2606 OID 73919)
-- Name: product_option product_option_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_option
    ADD CONSTRAINT product_option_pkey PRIMARY KEY (id);


--
-- TOC entry 4192 (class 2606 OID 73930)
-- Name: product_option_value product_option_value_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_option_value
    ADD CONSTRAINT product_option_value_pkey PRIMARY KEY (id);


--
-- TOC entry 4173 (class 2606 OID 73888)
-- Name: product product_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product
    ADD CONSTRAINT product_pkey PRIMARY KEY (id);


--
-- TOC entry 4716 (class 2606 OID 76203)
-- Name: product_sales_channel product_sales_channel_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_sales_channel
    ADD CONSTRAINT product_sales_channel_pkey PRIMARY KEY (product_id, sales_channel_id);


--
-- TOC entry 4758 (class 2606 OID 76307)
-- Name: product_shipping_profile product_shipping_profile_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_shipping_profile
    ADD CONSTRAINT product_shipping_profile_pkey PRIMARY KEY (product_id, shipping_profile_id);


--
-- TOC entry 4201 (class 2606 OID 73952)
-- Name: product_tag product_tag_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_tag
    ADD CONSTRAINT product_tag_pkey PRIMARY KEY (id);


--
-- TOC entry 4217 (class 2606 OID 73999)
-- Name: product_tags product_tags_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_tags
    ADD CONSTRAINT product_tags_pkey PRIMARY KEY (product_id, product_tag_id);


--
-- TOC entry 4205 (class 2606 OID 73963)
-- Name: product_type product_type_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_type
    ADD CONSTRAINT product_type_pkey PRIMARY KEY (id);


--
-- TOC entry 4710 (class 2606 OID 76201)
-- Name: product_variant_inventory_item product_variant_inventory_item_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_variant_inventory_item
    ADD CONSTRAINT product_variant_inventory_item_pkey PRIMARY KEY (variant_id, inventory_item_id);


--
-- TOC entry 4221 (class 2606 OID 74020)
-- Name: product_variant_option product_variant_option_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_variant_option
    ADD CONSTRAINT product_variant_option_pkey PRIMARY KEY (variant_id, option_value_id);


--
-- TOC entry 4182 (class 2606 OID 73904)
-- Name: product_variant product_variant_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_variant
    ADD CONSTRAINT product_variant_pkey PRIMARY KEY (id);


--
-- TOC entry 4728 (class 2606 OID 76301)
-- Name: product_variant_price_set product_variant_price_set_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_variant_price_set
    ADD CONSTRAINT product_variant_price_set_pkey PRIMARY KEY (variant_id, price_set_id);


--
-- TOC entry 4275 (class 2606 OID 74474)
-- Name: promotion_application_method promotion_application_method_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.promotion_application_method
    ADD CONSTRAINT promotion_application_method_pkey PRIMARY KEY (id);


--
-- TOC entry 4260 (class 2606 OID 74444)
-- Name: promotion_campaign_budget promotion_campaign_budget_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.promotion_campaign_budget
    ADD CONSTRAINT promotion_campaign_budget_pkey PRIMARY KEY (id);


--
-- TOC entry 4255 (class 2606 OID 74431)
-- Name: promotion_campaign promotion_campaign_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.promotion_campaign
    ADD CONSTRAINT promotion_campaign_pkey PRIMARY KEY (id);


--
-- TOC entry 4267 (class 2606 OID 74458)
-- Name: promotion promotion_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.promotion
    ADD CONSTRAINT promotion_pkey PRIMARY KEY (id);


--
-- TOC entry 4282 (class 2606 OID 74498)
-- Name: promotion_promotion_rule promotion_promotion_rule_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.promotion_promotion_rule
    ADD CONSTRAINT promotion_promotion_rule_pkey PRIMARY KEY (promotion_id, promotion_rule_id);


--
-- TOC entry 4280 (class 2606 OID 74489)
-- Name: promotion_rule promotion_rule_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.promotion_rule
    ADD CONSTRAINT promotion_rule_pkey PRIMARY KEY (id);


--
-- TOC entry 4290 (class 2606 OID 74521)
-- Name: promotion_rule_value promotion_rule_value_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.promotion_rule_value
    ADD CONSTRAINT promotion_rule_value_pkey PRIMARY KEY (id);


--
-- TOC entry 4578 (class 2606 OID 75769)
-- Name: provider_identity provider_identity_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.provider_identity
    ADD CONSTRAINT provider_identity_pkey PRIMARY KEY (id);


--
-- TOC entry 4734 (class 2606 OID 76311)
-- Name: publishable_api_key_sales_channel publishable_api_key_sales_channel_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.publishable_api_key_sales_channel
    ADD CONSTRAINT publishable_api_key_sales_channel_pkey PRIMARY KEY (publishable_key_id, sales_channel_id);


--
-- TOC entry 4435 (class 2606 OID 75163)
-- Name: refund refund_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.refund
    ADD CONSTRAINT refund_pkey PRIMARY KEY (id);


--
-- TOC entry 4442 (class 2606 OID 75222)
-- Name: refund_reason refund_reason_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.refund_reason
    ADD CONSTRAINT refund_reason_pkey PRIMARY KEY (id);


--
-- TOC entry 4376 (class 2606 OID 74960)
-- Name: region_country region_country_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.region_country
    ADD CONSTRAINT region_country_pkey PRIMARY KEY (iso_2);


--
-- TOC entry 4746 (class 2606 OID 76312)
-- Name: region_payment_provider region_payment_provider_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.region_payment_provider
    ADD CONSTRAINT region_payment_provider_pkey PRIMARY KEY (region_id, payment_provider_id);


--
-- TOC entry 4371 (class 2606 OID 74951)
-- Name: region region_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.region
    ADD CONSTRAINT region_pkey PRIMARY KEY (id);


--
-- TOC entry 4167 (class 2606 OID 73809)
-- Name: reservation_item reservation_item_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reservation_item
    ADD CONSTRAINT reservation_item_pkey PRIMARY KEY (id);


--
-- TOC entry 4722 (class 2606 OID 76193)
-- Name: return_fulfillment return_fulfillment_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.return_fulfillment
    ADD CONSTRAINT return_fulfillment_pkey PRIMARY KEY (return_id, fulfillment_id);


--
-- TOC entry 4540 (class 2606 OID 75596)
-- Name: return_item return_item_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.return_item
    ADD CONSTRAINT return_item_pkey PRIMARY KEY (id);


--
-- TOC entry 4534 (class 2606 OID 75582)
-- Name: return return_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.return
    ADD CONSTRAINT return_pkey PRIMARY KEY (id);


--
-- TOC entry 4528 (class 2606 OID 75474)
-- Name: return_reason return_reason_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.return_reason
    ADD CONSTRAINT return_reason_pkey PRIMARY KEY (id);


--
-- TOC entry 4313 (class 2606 OID 74722)
-- Name: sales_channel sales_channel_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sales_channel
    ADD CONSTRAINT sales_channel_pkey PRIMARY KEY (id);


--
-- TOC entry 4740 (class 2606 OID 76309)
-- Name: sales_channel_stock_location sales_channel_stock_location_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sales_channel_stock_location
    ADD CONSTRAINT sales_channel_stock_location_pkey PRIMARY KEY (sales_channel_id, stock_location_id);


--
-- TOC entry 4773 (class 2606 OID 76355)
-- Name: script_migrations script_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.script_migrations
    ADD CONSTRAINT script_migrations_pkey PRIMARY KEY (id);


--
-- TOC entry 4602 (class 2606 OID 75847)
-- Name: service_zone service_zone_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.service_zone
    ADD CONSTRAINT service_zone_pkey PRIMARY KEY (id);


--
-- TOC entry 4622 (class 2606 OID 75898)
-- Name: shipping_option shipping_option_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shipping_option
    ADD CONSTRAINT shipping_option_pkey PRIMARY KEY (id);


--
-- TOC entry 4752 (class 2606 OID 76310)
-- Name: shipping_option_price_set shipping_option_price_set_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shipping_option_price_set
    ADD CONSTRAINT shipping_option_price_set_pkey PRIMARY KEY (shipping_option_id, price_set_id);


--
-- TOC entry 4626 (class 2606 OID 75915)
-- Name: shipping_option_rule shipping_option_rule_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shipping_option_rule
    ADD CONSTRAINT shipping_option_rule_pkey PRIMARY KEY (id);


--
-- TOC entry 4612 (class 2606 OID 75875)
-- Name: shipping_option_type shipping_option_type_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shipping_option_type
    ADD CONSTRAINT shipping_option_type_pkey PRIMARY KEY (id);


--
-- TOC entry 4616 (class 2606 OID 75885)
-- Name: shipping_profile shipping_profile_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shipping_profile
    ADD CONSTRAINT shipping_profile_pkey PRIMARY KEY (id);


--
-- TOC entry 4146 (class 2606 OID 73748)
-- Name: stock_location_address stock_location_address_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.stock_location_address
    ADD CONSTRAINT stock_location_address_pkey PRIMARY KEY (id);


--
-- TOC entry 4150 (class 2606 OID 73758)
-- Name: stock_location stock_location_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.stock_location
    ADD CONSTRAINT stock_location_pkey PRIMARY KEY (id);


--
-- TOC entry 4388 (class 2606 OID 75006)
-- Name: store_currency store_currency_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.store_currency
    ADD CONSTRAINT store_currency_pkey PRIMARY KEY (id);


--
-- TOC entry 4384 (class 2606 OID 74995)
-- Name: store store_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.store
    ADD CONSTRAINT store_pkey PRIMARY KEY (id);


--
-- TOC entry 4391 (class 2606 OID 75021)
-- Name: tax_provider tax_provider_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tax_provider
    ADD CONSTRAINT tax_provider_pkey PRIMARY KEY (id);


--
-- TOC entry 4403 (class 2606 OID 75046)
-- Name: tax_rate tax_rate_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tax_rate
    ADD CONSTRAINT tax_rate_pkey PRIMARY KEY (id);


--
-- TOC entry 4409 (class 2606 OID 75058)
-- Name: tax_rate_rule tax_rate_rule_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tax_rate_rule
    ADD CONSTRAINT tax_rate_rule_pkey PRIMARY KEY (id);


--
-- TOC entry 4398 (class 2606 OID 75032)
-- Name: tax_region tax_region_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tax_region
    ADD CONSTRAINT tax_region_pkey PRIMARY KEY (id);


--
-- TOC entry 4587 (class 2606 OID 75802)
-- Name: user user_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public."user"
    ADD CONSTRAINT user_pkey PRIMARY KEY (id);


--
-- TOC entry 4658 (class 2606 OID 76099)
-- Name: workflow_execution workflow_execution_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.workflow_execution
    ADD CONSTRAINT workflow_execution_pkey PRIMARY KEY (workflow_id, transaction_id, run_id);


--
-- TOC entry 4443 (class 1259 OID 75258)
-- Name: IDX_account_holder_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_account_holder_deleted_at" ON public.account_holder USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4759 (class 1259 OID 76322)
-- Name: IDX_account_holder_id_5cb3a0c0; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_account_holder_id_5cb3a0c0" ON public.customer_account_holder USING btree (account_holder_id);


--
-- TOC entry 4444 (class 1259 OID 75259)
-- Name: IDX_account_holder_provider_id_external_id_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_account_holder_provider_id_external_id_unique" ON public.account_holder USING btree (provider_id, external_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4334 (class 1259 OID 74784)
-- Name: IDX_adjustment_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_adjustment_item_id" ON public.cart_line_item_adjustment USING btree (item_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4352 (class 1259 OID 74819)
-- Name: IDX_adjustment_shipping_method_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_adjustment_shipping_method_id" ON public.cart_shipping_method_adjustment USING btree (shipping_method_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4377 (class 1259 OID 74984)
-- Name: IDX_api_key_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_api_key_deleted_at" ON public.api_key USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4378 (class 1259 OID 74979)
-- Name: IDX_api_key_token_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_api_key_token_unique" ON public.api_key USING btree (token);


--
-- TOC entry 4379 (class 1259 OID 74982)
-- Name: IDX_api_key_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_api_key_type" ON public.api_key USING btree (type);


--
-- TOC entry 4268 (class 1259 OID 74477)
-- Name: IDX_application_method_allocation; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_application_method_allocation" ON public.promotion_application_method USING btree (allocation);


--
-- TOC entry 4269 (class 1259 OID 74476)
-- Name: IDX_application_method_target_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_application_method_target_type" ON public.promotion_application_method USING btree (target_type);


--
-- TOC entry 4270 (class 1259 OID 74475)
-- Name: IDX_application_method_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_application_method_type" ON public.promotion_application_method USING btree (type);


--
-- TOC entry 4571 (class 1259 OID 75779)
-- Name: IDX_auth_identity_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_auth_identity_deleted_at" ON public.auth_identity USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4256 (class 1259 OID 74445)
-- Name: IDX_campaign_budget_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_campaign_budget_type" ON public.promotion_campaign_budget USING btree (type);


--
-- TOC entry 4436 (class 1259 OID 75229)
-- Name: IDX_capture_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_capture_deleted_at" ON public.capture USING btree (deleted_at);


--
-- TOC entry 4437 (class 1259 OID 75181)
-- Name: IDX_capture_payment_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_capture_payment_id" ON public.capture USING btree (payment_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4323 (class 1259 OID 74863)
-- Name: IDX_cart_address_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_address_deleted_at" ON public.cart_address USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4314 (class 1259 OID 74735)
-- Name: IDX_cart_billing_address_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_billing_address_id" ON public.cart USING btree (billing_address_id) WHERE ((deleted_at IS NULL) AND (billing_address_id IS NOT NULL));


--
-- TOC entry 4364 (class 1259 OID 74935)
-- Name: IDX_cart_credit_line_reference_reference_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_credit_line_reference_reference_id" ON public.credit_line USING btree (reference, reference_id) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4315 (class 1259 OID 74738)
-- Name: IDX_cart_currency_code; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_currency_code" ON public.cart USING btree (currency_code);


--
-- TOC entry 4316 (class 1259 OID 74733)
-- Name: IDX_cart_customer_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_customer_id" ON public.cart USING btree (customer_id) WHERE ((deleted_at IS NULL) AND (customer_id IS NOT NULL));


--
-- TOC entry 4317 (class 1259 OID 74862)
-- Name: IDX_cart_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_deleted_at" ON public.cart USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4765 (class 1259 OID 76333)
-- Name: IDX_cart_id_-4a39f6c9; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_id_-4a39f6c9" ON public.cart_payment_collection USING btree (cart_id);


--
-- TOC entry 4681 (class 1259 OID 76205)
-- Name: IDX_cart_id_-71069c16; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_id_-71069c16" ON public.order_cart USING btree (cart_id);


--
-- TOC entry 4663 (class 1259 OID 76225)
-- Name: IDX_cart_id_-a9d4a70b; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_id_-a9d4a70b" ON public.cart_promotion USING btree (cart_id);


--
-- TOC entry 4335 (class 1259 OID 74864)
-- Name: IDX_cart_line_item_adjustment_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_line_item_adjustment_deleted_at" ON public.cart_line_item_adjustment USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4336 (class 1259 OID 74888)
-- Name: IDX_cart_line_item_adjustment_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_line_item_adjustment_item_id" ON public.cart_line_item_adjustment USING btree (item_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4326 (class 1259 OID 74882)
-- Name: IDX_cart_line_item_cart_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_line_item_cart_id" ON public.cart_line_item USING btree (cart_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4327 (class 1259 OID 74869)
-- Name: IDX_cart_line_item_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_line_item_deleted_at" ON public.cart_line_item USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4340 (class 1259 OID 74866)
-- Name: IDX_cart_line_item_tax_line_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_line_item_tax_line_deleted_at" ON public.cart_line_item_tax_line USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4341 (class 1259 OID 74894)
-- Name: IDX_cart_line_item_tax_line_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_line_item_tax_line_item_id" ON public.cart_line_item_tax_line USING btree (item_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4318 (class 1259 OID 74736)
-- Name: IDX_cart_region_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_region_id" ON public.cart USING btree (region_id) WHERE ((deleted_at IS NULL) AND (region_id IS NOT NULL));


--
-- TOC entry 4319 (class 1259 OID 74737)
-- Name: IDX_cart_sales_channel_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_sales_channel_id" ON public.cart USING btree (sales_channel_id) WHERE ((deleted_at IS NULL) AND (sales_channel_id IS NOT NULL));


--
-- TOC entry 4320 (class 1259 OID 74734)
-- Name: IDX_cart_shipping_address_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_shipping_address_id" ON public.cart USING btree (shipping_address_id) WHERE ((deleted_at IS NULL) AND (shipping_address_id IS NOT NULL));


--
-- TOC entry 4353 (class 1259 OID 74865)
-- Name: IDX_cart_shipping_method_adjustment_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_shipping_method_adjustment_deleted_at" ON public.cart_shipping_method_adjustment USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4354 (class 1259 OID 74901)
-- Name: IDX_cart_shipping_method_adjustment_shipping_method_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_shipping_method_adjustment_shipping_method_id" ON public.cart_shipping_method_adjustment USING btree (shipping_method_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4346 (class 1259 OID 74900)
-- Name: IDX_cart_shipping_method_cart_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_shipping_method_cart_id" ON public.cart_shipping_method USING btree (cart_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4347 (class 1259 OID 74868)
-- Name: IDX_cart_shipping_method_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_shipping_method_deleted_at" ON public.cart_shipping_method USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4358 (class 1259 OID 74867)
-- Name: IDX_cart_shipping_method_tax_line_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_shipping_method_tax_line_deleted_at" ON public.cart_shipping_method_tax_line USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4359 (class 1259 OID 74902)
-- Name: IDX_cart_shipping_method_tax_line_shipping_method_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_cart_shipping_method_tax_line_shipping_method_id" ON public.cart_shipping_method_tax_line USING btree (shipping_method_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4211 (class 1259 OID 73990)
-- Name: IDX_category_handle_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_category_handle_unique" ON public.product_category USING btree (handle) WHERE (deleted_at IS NULL);


--
-- TOC entry 4206 (class 1259 OID 73975)
-- Name: IDX_collection_handle_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_collection_handle_unique" ON public.product_collection USING btree (handle) WHERE (deleted_at IS NULL);


--
-- TOC entry 4365 (class 1259 OID 74933)
-- Name: IDX_credit_line_cart_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_credit_line_cart_id" ON public.credit_line USING btree (cart_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4366 (class 1259 OID 74934)
-- Name: IDX_credit_line_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_credit_line_deleted_at" ON public.credit_line USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4295 (class 1259 OID 74657)
-- Name: IDX_customer_address_customer_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_customer_address_customer_id" ON public.customer_address USING btree (customer_id);


--
-- TOC entry 4296 (class 1259 OID 74699)
-- Name: IDX_customer_address_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_customer_address_deleted_at" ON public.customer_address USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4297 (class 1259 OID 74658)
-- Name: IDX_customer_address_unique_customer_billing; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_customer_address_unique_customer_billing" ON public.customer_address USING btree (customer_id) WHERE (is_default_billing = true);


--
-- TOC entry 4298 (class 1259 OID 74659)
-- Name: IDX_customer_address_unique_customer_shipping; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_customer_address_unique_customer_shipping" ON public.customer_address USING btree (customer_id) WHERE (is_default_shipping = true);


--
-- TOC entry 4291 (class 1259 OID 74698)
-- Name: IDX_customer_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_customer_deleted_at" ON public.customer USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4292 (class 1259 OID 74696)
-- Name: IDX_customer_email_has_account_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_customer_email_has_account_unique" ON public.customer USING btree (email, has_account) WHERE (deleted_at IS NULL);


--
-- TOC entry 4306 (class 1259 OID 74711)
-- Name: IDX_customer_group_customer_customer_group_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_customer_group_customer_customer_group_id" ON public.customer_group_customer USING btree (customer_group_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4307 (class 1259 OID 74680)
-- Name: IDX_customer_group_customer_customer_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_customer_group_customer_customer_id" ON public.customer_group_customer USING btree (customer_id);


--
-- TOC entry 4308 (class 1259 OID 74712)
-- Name: IDX_customer_group_customer_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_customer_group_customer_deleted_at" ON public.customer_group_customer USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4301 (class 1259 OID 74700)
-- Name: IDX_customer_group_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_customer_group_deleted_at" ON public.customer_group USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4302 (class 1259 OID 74669)
-- Name: IDX_customer_group_name; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_customer_group_name" ON public.customer_group USING btree (name) WHERE (deleted_at IS NULL);


--
-- TOC entry 4303 (class 1259 OID 74697)
-- Name: IDX_customer_group_name_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_customer_group_name_unique" ON public.customer_group USING btree (name) WHERE (deleted_at IS NULL);


--
-- TOC entry 4760 (class 1259 OID 76334)
-- Name: IDX_customer_id_5cb3a0c0; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_customer_id_5cb3a0c0" ON public.customer_account_holder USING btree (customer_id);


--
-- TOC entry 4729 (class 1259 OID 76343)
-- Name: IDX_deleted_at_-1d67bae40; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_deleted_at_-1d67bae40" ON public.publishable_api_key_sales_channel USING btree (deleted_at);


--
-- TOC entry 4687 (class 1259 OID 76237)
-- Name: IDX_deleted_at_-1e5992737; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_deleted_at_-1e5992737" ON public.location_fulfillment_provider USING btree (deleted_at);


--
-- TOC entry 4717 (class 1259 OID 76236)
-- Name: IDX_deleted_at_-31ea43a; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_deleted_at_-31ea43a" ON public.return_fulfillment USING btree (deleted_at);


--
-- TOC entry 4766 (class 1259 OID 76345)
-- Name: IDX_deleted_at_-4a39f6c9; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_deleted_at_-4a39f6c9" ON public.cart_payment_collection USING btree (deleted_at);


--
-- TOC entry 4682 (class 1259 OID 76235)
-- Name: IDX_deleted_at_-71069c16; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_deleted_at_-71069c16" ON public.order_cart USING btree (deleted_at);


--
-- TOC entry 4699 (class 1259 OID 76240)
-- Name: IDX_deleted_at_-71518339; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_deleted_at_-71518339" ON public.order_promotion USING btree (deleted_at);


--
-- TOC entry 4664 (class 1259 OID 76234)
-- Name: IDX_deleted_at_-a9d4a70b; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_deleted_at_-a9d4a70b" ON public.cart_promotion USING btree (deleted_at);


--
-- TOC entry 4669 (class 1259 OID 76238)
-- Name: IDX_deleted_at_-e88adb96; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_deleted_at_-e88adb96" ON public.location_fulfillment_set USING btree (deleted_at);


--
-- TOC entry 4675 (class 1259 OID 76241)
-- Name: IDX_deleted_at_-e8d2543e; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_deleted_at_-e8d2543e" ON public.order_fulfillment USING btree (deleted_at);


--
-- TOC entry 4753 (class 1259 OID 76342)
-- Name: IDX_deleted_at_17a262437; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_deleted_at_17a262437" ON public.product_shipping_profile USING btree (deleted_at);


--
-- TOC entry 4705 (class 1259 OID 76242)
-- Name: IDX_deleted_at_17b4c4e35; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_deleted_at_17b4c4e35" ON public.product_variant_inventory_item USING btree (deleted_at);


--
-- TOC entry 4741 (class 1259 OID 76346)
-- Name: IDX_deleted_at_1c934dab0; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_deleted_at_1c934dab0" ON public.region_payment_provider USING btree (deleted_at);


--
-- TOC entry 4711 (class 1259 OID 76243)
-- Name: IDX_deleted_at_20b454295; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_deleted_at_20b454295" ON public.product_sales_channel USING btree (deleted_at);


--
-- TOC entry 4735 (class 1259 OID 76344)
-- Name: IDX_deleted_at_26d06f470; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_deleted_at_26d06f470" ON public.sales_channel_stock_location USING btree (deleted_at);


--
-- TOC entry 4723 (class 1259 OID 76340)
-- Name: IDX_deleted_at_52b23597; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_deleted_at_52b23597" ON public.product_variant_price_set USING btree (deleted_at);


--
-- TOC entry 4761 (class 1259 OID 76341)
-- Name: IDX_deleted_at_5cb3a0c0; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_deleted_at_5cb3a0c0" ON public.customer_account_holder USING btree (deleted_at);


--
-- TOC entry 4747 (class 1259 OID 76347)
-- Name: IDX_deleted_at_ba32fa9c; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_deleted_at_ba32fa9c" ON public.shipping_option_price_set USING btree (deleted_at);


--
-- TOC entry 4693 (class 1259 OID 76239)
-- Name: IDX_deleted_at_f42b9949; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_deleted_at_f42b9949" ON public.order_payment_collection USING btree (deleted_at);


--
-- TOC entry 4588 (class 1259 OID 75817)
-- Name: IDX_fulfillment_address_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_fulfillment_address_deleted_at" ON public.fulfillment_address USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4627 (class 1259 OID 75932)
-- Name: IDX_fulfillment_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_fulfillment_deleted_at" ON public.fulfillment USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4718 (class 1259 OID 76209)
-- Name: IDX_fulfillment_id_-31ea43a; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_fulfillment_id_-31ea43a" ON public.return_fulfillment USING btree (fulfillment_id);


--
-- TOC entry 4676 (class 1259 OID 76207)
-- Name: IDX_fulfillment_id_-e8d2543e; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_fulfillment_id_-e8d2543e" ON public.order_fulfillment USING btree (fulfillment_id);


--
-- TOC entry 4636 (class 1259 OID 75956)
-- Name: IDX_fulfillment_item_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_fulfillment_item_deleted_at" ON public.fulfillment_item USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4637 (class 1259 OID 75955)
-- Name: IDX_fulfillment_item_fulfillment_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_fulfillment_item_fulfillment_id" ON public.fulfillment_item USING btree (fulfillment_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4638 (class 1259 OID 75954)
-- Name: IDX_fulfillment_item_inventory_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_fulfillment_item_inventory_item_id" ON public.fulfillment_item USING btree (inventory_item_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4639 (class 1259 OID 75953)
-- Name: IDX_fulfillment_item_line_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_fulfillment_item_line_item_id" ON public.fulfillment_item USING btree (line_item_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4632 (class 1259 OID 75943)
-- Name: IDX_fulfillment_label_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_fulfillment_label_deleted_at" ON public.fulfillment_label USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4633 (class 1259 OID 75942)
-- Name: IDX_fulfillment_label_fulfillment_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_fulfillment_label_fulfillment_id" ON public.fulfillment_label USING btree (fulfillment_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4628 (class 1259 OID 75929)
-- Name: IDX_fulfillment_location_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_fulfillment_location_id" ON public.fulfillment USING btree (location_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4591 (class 1259 OID 76023)
-- Name: IDX_fulfillment_provider_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_fulfillment_provider_deleted_at" ON public.fulfillment_provider USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4688 (class 1259 OID 76212)
-- Name: IDX_fulfillment_provider_id_-1e5992737; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_fulfillment_provider_id_-1e5992737" ON public.location_fulfillment_provider USING btree (fulfillment_provider_id);


--
-- TOC entry 4594 (class 1259 OID 75838)
-- Name: IDX_fulfillment_set_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_fulfillment_set_deleted_at" ON public.fulfillment_set USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4670 (class 1259 OID 76211)
-- Name: IDX_fulfillment_set_id_-e88adb96; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_fulfillment_set_id_-e88adb96" ON public.location_fulfillment_set USING btree (fulfillment_set_id);


--
-- TOC entry 4595 (class 1259 OID 75836)
-- Name: IDX_fulfillment_set_name_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_fulfillment_set_name_unique" ON public.fulfillment_set USING btree (name) WHERE (deleted_at IS NULL);


--
-- TOC entry 4629 (class 1259 OID 75931)
-- Name: IDX_fulfillment_shipping_option_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_fulfillment_shipping_option_id" ON public.fulfillment USING btree (shipping_option_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4603 (class 1259 OID 75864)
-- Name: IDX_geo_zone_city; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_geo_zone_city" ON public.geo_zone USING btree (city) WHERE ((deleted_at IS NULL) AND (city IS NOT NULL));


--
-- TOC entry 4604 (class 1259 OID 75862)
-- Name: IDX_geo_zone_country_code; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_geo_zone_country_code" ON public.geo_zone USING btree (country_code) WHERE (deleted_at IS NULL);


--
-- TOC entry 4605 (class 1259 OID 75866)
-- Name: IDX_geo_zone_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_geo_zone_deleted_at" ON public.geo_zone USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4606 (class 1259 OID 75863)
-- Name: IDX_geo_zone_province_code; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_geo_zone_province_code" ON public.geo_zone USING btree (province_code) WHERE ((deleted_at IS NULL) AND (province_code IS NOT NULL));


--
-- TOC entry 4607 (class 1259 OID 75865)
-- Name: IDX_geo_zone_service_zone_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_geo_zone_service_zone_id" ON public.geo_zone USING btree (service_zone_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4730 (class 1259 OID 76328)
-- Name: IDX_id_-1d67bae40; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_id_-1d67bae40" ON public.publishable_api_key_sales_channel USING btree (id);


--
-- TOC entry 4689 (class 1259 OID 76216)
-- Name: IDX_id_-1e5992737; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_id_-1e5992737" ON public.location_fulfillment_provider USING btree (id);


--
-- TOC entry 4719 (class 1259 OID 76217)
-- Name: IDX_id_-31ea43a; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_id_-31ea43a" ON public.return_fulfillment USING btree (id);


--
-- TOC entry 4767 (class 1259 OID 76325)
-- Name: IDX_id_-4a39f6c9; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_id_-4a39f6c9" ON public.cart_payment_collection USING btree (id);


--
-- TOC entry 4683 (class 1259 OID 76214)
-- Name: IDX_id_-71069c16; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_id_-71069c16" ON public.order_cart USING btree (id);


--
-- TOC entry 4700 (class 1259 OID 76219)
-- Name: IDX_id_-71518339; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_id_-71518339" ON public.order_promotion USING btree (id);


--
-- TOC entry 4665 (class 1259 OID 76215)
-- Name: IDX_id_-a9d4a70b; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_id_-a9d4a70b" ON public.cart_promotion USING btree (id);


--
-- TOC entry 4671 (class 1259 OID 76220)
-- Name: IDX_id_-e88adb96; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_id_-e88adb96" ON public.location_fulfillment_set USING btree (id);


--
-- TOC entry 4677 (class 1259 OID 76222)
-- Name: IDX_id_-e8d2543e; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_id_-e8d2543e" ON public.order_fulfillment USING btree (id);


--
-- TOC entry 4754 (class 1259 OID 76331)
-- Name: IDX_id_17a262437; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_id_17a262437" ON public.product_shipping_profile USING btree (id);


--
-- TOC entry 4706 (class 1259 OID 76221)
-- Name: IDX_id_17b4c4e35; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_id_17b4c4e35" ON public.product_variant_inventory_item USING btree (id);


--
-- TOC entry 4742 (class 1259 OID 76326)
-- Name: IDX_id_1c934dab0; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_id_1c934dab0" ON public.region_payment_provider USING btree (id);


--
-- TOC entry 4712 (class 1259 OID 76218)
-- Name: IDX_id_20b454295; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_id_20b454295" ON public.product_sales_channel USING btree (id);


--
-- TOC entry 4736 (class 1259 OID 76330)
-- Name: IDX_id_26d06f470; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_id_26d06f470" ON public.sales_channel_stock_location USING btree (id);


--
-- TOC entry 4724 (class 1259 OID 76324)
-- Name: IDX_id_52b23597; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_id_52b23597" ON public.product_variant_price_set USING btree (id);


--
-- TOC entry 4762 (class 1259 OID 76327)
-- Name: IDX_id_5cb3a0c0; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_id_5cb3a0c0" ON public.customer_account_holder USING btree (id);


--
-- TOC entry 4748 (class 1259 OID 76329)
-- Name: IDX_id_ba32fa9c; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_id_ba32fa9c" ON public.shipping_option_price_set USING btree (id);


--
-- TOC entry 4694 (class 1259 OID 76223)
-- Name: IDX_id_f42b9949; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_id_f42b9949" ON public.order_payment_collection USING btree (id);


--
-- TOC entry 4193 (class 1259 OID 74120)
-- Name: IDX_image_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_image_deleted_at" ON public.image USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4194 (class 1259 OID 74179)
-- Name: IDX_image_product_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_image_product_id" ON public.image USING btree (product_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4151 (class 1259 OID 73784)
-- Name: IDX_inventory_item_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_inventory_item_deleted_at" ON public.inventory_item USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4707 (class 1259 OID 76213)
-- Name: IDX_inventory_item_id_17b4c4e35; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_inventory_item_id_17b4c4e35" ON public.product_variant_inventory_item USING btree (inventory_item_id);


--
-- TOC entry 4152 (class 1259 OID 73872)
-- Name: IDX_inventory_item_sku; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_inventory_item_sku" ON public.inventory_item USING btree (sku) WHERE (deleted_at IS NULL);


--
-- TOC entry 4155 (class 1259 OID 73798)
-- Name: IDX_inventory_level_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_inventory_level_deleted_at" ON public.inventory_level USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4156 (class 1259 OID 73866)
-- Name: IDX_inventory_level_inventory_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_inventory_level_inventory_item_id" ON public.inventory_level USING btree (inventory_item_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4157 (class 1259 OID 73871)
-- Name: IDX_inventory_level_item_location; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_inventory_level_item_location" ON public.inventory_level USING btree (inventory_item_id, location_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4158 (class 1259 OID 73867)
-- Name: IDX_inventory_level_location_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_inventory_level_location_id" ON public.inventory_level USING btree (location_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4159 (class 1259 OID 73876)
-- Name: IDX_inventory_level_location_id_inventory_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_inventory_level_location_id_inventory_item_id" ON public.inventory_level USING btree (inventory_item_id, location_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4579 (class 1259 OID 75793)
-- Name: IDX_invite_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_invite_deleted_at" ON public.invite USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4580 (class 1259 OID 75806)
-- Name: IDX_invite_email_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_invite_email_unique" ON public.invite USING btree (email) WHERE (deleted_at IS NULL);


--
-- TOC entry 4581 (class 1259 OID 75792)
-- Name: IDX_invite_token; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_invite_token" ON public.invite USING btree (token) WHERE (deleted_at IS NULL);


--
-- TOC entry 4337 (class 1259 OID 74785)
-- Name: IDX_line_item_adjustment_promotion_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_line_item_adjustment_promotion_id" ON public.cart_line_item_adjustment USING btree (promotion_id) WHERE ((deleted_at IS NULL) AND (promotion_id IS NOT NULL));


--
-- TOC entry 4328 (class 1259 OID 74771)
-- Name: IDX_line_item_cart_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_line_item_cart_id" ON public.cart_line_item USING btree (cart_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4329 (class 1259 OID 74772)
-- Name: IDX_line_item_product_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_line_item_product_id" ON public.cart_line_item USING btree (product_id) WHERE ((deleted_at IS NULL) AND (product_id IS NOT NULL));


--
-- TOC entry 4330 (class 1259 OID 74870)
-- Name: IDX_line_item_product_type_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_line_item_product_type_id" ON public.cart_line_item USING btree (product_type_id) WHERE ((deleted_at IS NULL) AND (product_type_id IS NOT NULL));


--
-- TOC entry 4342 (class 1259 OID 74796)
-- Name: IDX_line_item_tax_line_tax_rate_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_line_item_tax_line_tax_rate_id" ON public.cart_line_item_tax_line USING btree (tax_rate_id) WHERE ((deleted_at IS NULL) AND (tax_rate_id IS NOT NULL));


--
-- TOC entry 4331 (class 1259 OID 74773)
-- Name: IDX_line_item_variant_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_line_item_variant_id" ON public.cart_line_item USING btree (variant_id) WHERE ((deleted_at IS NULL) AND (variant_id IS NOT NULL));


--
-- TOC entry 4645 (class 1259 OID 76077)
-- Name: IDX_notification_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_notification_deleted_at" ON public.notification USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4646 (class 1259 OID 76073)
-- Name: IDX_notification_idempotency_key_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_notification_idempotency_key_unique" ON public.notification USING btree (idempotency_key) WHERE (deleted_at IS NULL);


--
-- TOC entry 4642 (class 1259 OID 76076)
-- Name: IDX_notification_provider_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_notification_provider_deleted_at" ON public.notification_provider USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4647 (class 1259 OID 76061)
-- Name: IDX_notification_provider_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_notification_provider_id" ON public.notification USING btree (provider_id);


--
-- TOC entry 4648 (class 1259 OID 76063)
-- Name: IDX_notification_receiver_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_notification_receiver_id" ON public.notification USING btree (receiver_id);


--
-- TOC entry 4183 (class 1259 OID 74123)
-- Name: IDX_option_product_id_title_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_option_product_id_title_unique" ON public.product_option USING btree (product_id, title) WHERE (deleted_at IS NULL);


--
-- TOC entry 4188 (class 1259 OID 73931)
-- Name: IDX_option_value_option_id_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_option_value_option_id_unique" ON public.product_option_value USING btree (option_id, value) WHERE (deleted_at IS NULL);


--
-- TOC entry 4447 (class 1259 OID 75281)
-- Name: IDX_order_address_customer_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_address_customer_id" ON public.order_address USING btree (customer_id);


--
-- TOC entry 4448 (class 1259 OID 75729)
-- Name: IDX_order_address_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_address_deleted_at" ON public.order_address USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4451 (class 1259 OID 75321)
-- Name: IDX_order_billing_address_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_billing_address_id" ON public."order" USING btree (billing_address_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4475 (class 1259 OID 75559)
-- Name: IDX_order_change_action_claim_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_change_action_claim_id" ON public.order_change_action USING btree (claim_id) WHERE ((claim_id IS NOT NULL) AND (deleted_at IS NULL));


--
-- TOC entry 4476 (class 1259 OID 75557)
-- Name: IDX_order_change_action_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_change_action_deleted_at" ON public.order_change_action USING btree (deleted_at);


--
-- TOC entry 4477 (class 1259 OID 75560)
-- Name: IDX_order_change_action_exchange_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_change_action_exchange_id" ON public.order_change_action USING btree (exchange_id) WHERE ((exchange_id IS NOT NULL) AND (deleted_at IS NULL));


--
-- TOC entry 4478 (class 1259 OID 75361)
-- Name: IDX_order_change_action_order_change_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_change_action_order_change_id" ON public.order_change_action USING btree (order_change_id);


--
-- TOC entry 4479 (class 1259 OID 75362)
-- Name: IDX_order_change_action_order_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_change_action_order_id" ON public.order_change_action USING btree (order_id);


--
-- TOC entry 4480 (class 1259 OID 75363)
-- Name: IDX_order_change_action_ordering; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_change_action_ordering" ON public.order_change_action USING btree (ordering);


--
-- TOC entry 4481 (class 1259 OID 75558)
-- Name: IDX_order_change_action_return_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_change_action_return_id" ON public.order_change_action USING btree (return_id) WHERE ((return_id IS NOT NULL) AND (deleted_at IS NULL));


--
-- TOC entry 4465 (class 1259 OID 75546)
-- Name: IDX_order_change_change_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_change_change_type" ON public.order_change USING btree (change_type);


--
-- TOC entry 4466 (class 1259 OID 75555)
-- Name: IDX_order_change_claim_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_change_claim_id" ON public.order_change USING btree (claim_id) WHERE ((claim_id IS NOT NULL) AND (deleted_at IS NULL));


--
-- TOC entry 4467 (class 1259 OID 75547)
-- Name: IDX_order_change_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_change_deleted_at" ON public.order_change USING btree (deleted_at);


--
-- TOC entry 4468 (class 1259 OID 75556)
-- Name: IDX_order_change_exchange_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_change_exchange_id" ON public.order_change USING btree (exchange_id) WHERE ((exchange_id IS NOT NULL) AND (deleted_at IS NULL));


--
-- TOC entry 4469 (class 1259 OID 75346)
-- Name: IDX_order_change_order_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_change_order_id" ON public.order_change USING btree (order_id);


--
-- TOC entry 4470 (class 1259 OID 75347)
-- Name: IDX_order_change_order_id_version; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_change_order_id_version" ON public.order_change USING btree (order_id, version);


--
-- TOC entry 4471 (class 1259 OID 75554)
-- Name: IDX_order_change_return_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_change_return_id" ON public.order_change USING btree (return_id) WHERE ((return_id IS NOT NULL) AND (deleted_at IS NULL));


--
-- TOC entry 4472 (class 1259 OID 75348)
-- Name: IDX_order_change_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_change_status" ON public.order_change USING btree (status);


--
-- TOC entry 4552 (class 1259 OID 75647)
-- Name: IDX_order_claim_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_claim_deleted_at" ON public.order_claim USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4553 (class 1259 OID 75646)
-- Name: IDX_order_claim_display_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_claim_display_id" ON public.order_claim USING btree (display_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4558 (class 1259 OID 75670)
-- Name: IDX_order_claim_item_claim_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_claim_item_claim_id" ON public.order_claim_item USING btree (claim_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4559 (class 1259 OID 75669)
-- Name: IDX_order_claim_item_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_claim_item_deleted_at" ON public.order_claim_item USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4563 (class 1259 OID 75681)
-- Name: IDX_order_claim_item_image_claim_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_claim_item_image_claim_item_id" ON public.order_claim_item_image USING btree (claim_item_id) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4564 (class 1259 OID 75682)
-- Name: IDX_order_claim_item_image_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_claim_item_image_deleted_at" ON public.order_claim_item_image USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4560 (class 1259 OID 75671)
-- Name: IDX_order_claim_item_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_claim_item_item_id" ON public.order_claim_item USING btree (item_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4554 (class 1259 OID 75648)
-- Name: IDX_order_claim_order_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_claim_order_id" ON public.order_claim USING btree (order_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4555 (class 1259 OID 75649)
-- Name: IDX_order_claim_return_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_claim_return_id" ON public.order_claim USING btree (return_id) WHERE ((return_id IS NOT NULL) AND (deleted_at IS NULL));


--
-- TOC entry 4567 (class 1259 OID 75740)
-- Name: IDX_order_credit_line_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_credit_line_deleted_at" ON public.order_credit_line USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4568 (class 1259 OID 75739)
-- Name: IDX_order_credit_line_order_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_credit_line_order_id" ON public.order_credit_line USING btree (order_id) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4452 (class 1259 OID 75319)
-- Name: IDX_order_currency_code; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_currency_code" ON public."order" USING btree (currency_code) WHERE (deleted_at IS NULL);


--
-- TOC entry 4453 (class 1259 OID 75318)
-- Name: IDX_order_customer_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_customer_id" ON public."order" USING btree (customer_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4454 (class 1259 OID 75322)
-- Name: IDX_order_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_deleted_at" ON public."order" USING btree (deleted_at);


--
-- TOC entry 4455 (class 1259 OID 75316)
-- Name: IDX_order_display_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_display_id" ON public."order" USING btree (display_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4541 (class 1259 OID 75614)
-- Name: IDX_order_exchange_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_exchange_deleted_at" ON public.order_exchange USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4542 (class 1259 OID 75613)
-- Name: IDX_order_exchange_display_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_exchange_display_id" ON public.order_exchange USING btree (display_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4547 (class 1259 OID 75626)
-- Name: IDX_order_exchange_item_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_exchange_item_deleted_at" ON public.order_exchange_item USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4548 (class 1259 OID 75627)
-- Name: IDX_order_exchange_item_exchange_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_exchange_item_exchange_id" ON public.order_exchange_item USING btree (exchange_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4549 (class 1259 OID 75628)
-- Name: IDX_order_exchange_item_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_exchange_item_item_id" ON public.order_exchange_item USING btree (item_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4543 (class 1259 OID 75615)
-- Name: IDX_order_exchange_order_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_exchange_order_id" ON public.order_exchange USING btree (order_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4544 (class 1259 OID 75616)
-- Name: IDX_order_exchange_return_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_exchange_return_id" ON public.order_exchange USING btree (return_id) WHERE ((return_id IS NOT NULL) AND (deleted_at IS NULL));


--
-- TOC entry 4684 (class 1259 OID 76224)
-- Name: IDX_order_id_-71069c16; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_id_-71069c16" ON public.order_cart USING btree (order_id);


--
-- TOC entry 4701 (class 1259 OID 76226)
-- Name: IDX_order_id_-71518339; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_id_-71518339" ON public.order_promotion USING btree (order_id);


--
-- TOC entry 4678 (class 1259 OID 76233)
-- Name: IDX_order_id_-e8d2543e; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_id_-e8d2543e" ON public.order_fulfillment USING btree (order_id);


--
-- TOC entry 4695 (class 1259 OID 76232)
-- Name: IDX_order_id_f42b9949; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_id_f42b9949" ON public.order_payment_collection USING btree (order_id);


--
-- TOC entry 4456 (class 1259 OID 75323)
-- Name: IDX_order_is_draft_order; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_is_draft_order" ON public."order" USING btree (is_draft_order) WHERE (deleted_at IS NULL);


--
-- TOC entry 4484 (class 1259 OID 75722)
-- Name: IDX_order_item_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_item_deleted_at" ON public.order_item USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4485 (class 1259 OID 75375)
-- Name: IDX_order_item_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_item_item_id" ON public.order_item USING btree (item_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4486 (class 1259 OID 75373)
-- Name: IDX_order_item_order_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_item_order_id" ON public.order_item USING btree (order_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4487 (class 1259 OID 75374)
-- Name: IDX_order_item_order_id_version; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_item_order_id_version" ON public.order_item USING btree (order_id, version) WHERE (deleted_at IS NULL);


--
-- TOC entry 4506 (class 1259 OID 75689)
-- Name: IDX_order_line_item_adjustment_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_line_item_adjustment_item_id" ON public.order_line_item_adjustment USING btree (item_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4499 (class 1259 OID 75685)
-- Name: IDX_order_line_item_product_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_line_item_product_id" ON public.order_line_item USING btree (product_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4503 (class 1259 OID 75688)
-- Name: IDX_order_line_item_tax_line_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_line_item_tax_line_item_id" ON public.order_line_item_tax_line USING btree (item_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4500 (class 1259 OID 75684)
-- Name: IDX_order_line_item_variant_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_line_item_variant_id" ON public.order_line_item USING btree (variant_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4457 (class 1259 OID 75317)
-- Name: IDX_order_region_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_region_id" ON public."order" USING btree (region_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4458 (class 1259 OID 75320)
-- Name: IDX_order_shipping_address_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_shipping_address_id" ON public."order" USING btree (shipping_address_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4490 (class 1259 OID 75552)
-- Name: IDX_order_shipping_claim_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_shipping_claim_id" ON public.order_shipping USING btree (claim_id) WHERE ((claim_id IS NOT NULL) AND (deleted_at IS NULL));


--
-- TOC entry 4491 (class 1259 OID 75724)
-- Name: IDX_order_shipping_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_shipping_deleted_at" ON public.order_shipping USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4492 (class 1259 OID 75553)
-- Name: IDX_order_shipping_exchange_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_shipping_exchange_id" ON public.order_shipping USING btree (exchange_id) WHERE ((exchange_id IS NOT NULL) AND (deleted_at IS NULL));


--
-- TOC entry 4493 (class 1259 OID 75387)
-- Name: IDX_order_shipping_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_shipping_item_id" ON public.order_shipping USING btree (shipping_method_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4512 (class 1259 OID 75687)
-- Name: IDX_order_shipping_method_adjustment_shipping_method_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_shipping_method_adjustment_shipping_method_id" ON public.order_shipping_method_adjustment USING btree (shipping_method_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4509 (class 1259 OID 75683)
-- Name: IDX_order_shipping_method_shipping_option_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_shipping_method_shipping_option_id" ON public.order_shipping_method USING btree (shipping_option_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4515 (class 1259 OID 75686)
-- Name: IDX_order_shipping_method_tax_line_shipping_method_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_shipping_method_tax_line_shipping_method_id" ON public.order_shipping_method_tax_line USING btree (shipping_method_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4494 (class 1259 OID 75385)
-- Name: IDX_order_shipping_order_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_shipping_order_id" ON public.order_shipping USING btree (order_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4495 (class 1259 OID 75386)
-- Name: IDX_order_shipping_order_id_version; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_shipping_order_id_version" ON public.order_shipping USING btree (order_id, version) WHERE (deleted_at IS NULL);


--
-- TOC entry 4496 (class 1259 OID 75551)
-- Name: IDX_order_shipping_return_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_shipping_return_id" ON public.order_shipping USING btree (return_id) WHERE ((return_id IS NOT NULL) AND (deleted_at IS NULL));


--
-- TOC entry 4461 (class 1259 OID 75723)
-- Name: IDX_order_summary_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_summary_deleted_at" ON public.order_summary USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4462 (class 1259 OID 75334)
-- Name: IDX_order_summary_order_id_version; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_summary_order_id_version" ON public.order_summary USING btree (order_id, version) WHERE (deleted_at IS NULL);


--
-- TOC entry 4518 (class 1259 OID 75549)
-- Name: IDX_order_transaction_claim_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_transaction_claim_id" ON public.order_transaction USING btree (claim_id) WHERE ((claim_id IS NOT NULL) AND (deleted_at IS NULL));


--
-- TOC entry 4519 (class 1259 OID 75464)
-- Name: IDX_order_transaction_currency_code; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_transaction_currency_code" ON public.order_transaction USING btree (currency_code) WHERE (deleted_at IS NULL);


--
-- TOC entry 4520 (class 1259 OID 75550)
-- Name: IDX_order_transaction_exchange_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_transaction_exchange_id" ON public.order_transaction USING btree (exchange_id) WHERE ((exchange_id IS NOT NULL) AND (deleted_at IS NULL));


--
-- TOC entry 4521 (class 1259 OID 75463)
-- Name: IDX_order_transaction_order_id_version; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_transaction_order_id_version" ON public.order_transaction USING btree (order_id, version) WHERE (deleted_at IS NULL);


--
-- TOC entry 4522 (class 1259 OID 75465)
-- Name: IDX_order_transaction_reference_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_transaction_reference_id" ON public.order_transaction USING btree (reference_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4523 (class 1259 OID 75548)
-- Name: IDX_order_transaction_return_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_order_transaction_return_id" ON public.order_transaction USING btree (return_id) WHERE ((return_id IS NOT NULL) AND (deleted_at IS NULL));


--
-- TOC entry 4412 (class 1259 OID 75178)
-- Name: IDX_payment_collection_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_payment_collection_deleted_at" ON public.payment_collection USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4768 (class 1259 OID 76319)
-- Name: IDX_payment_collection_id_-4a39f6c9; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_payment_collection_id_-4a39f6c9" ON public.cart_payment_collection USING btree (payment_collection_id);


--
-- TOC entry 4696 (class 1259 OID 76208)
-- Name: IDX_payment_collection_id_f42b9949; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_payment_collection_id_f42b9949" ON public.order_payment_collection USING btree (payment_collection_id);


--
-- TOC entry 4424 (class 1259 OID 75173)
-- Name: IDX_payment_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_payment_deleted_at" ON public.payment USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4425 (class 1259 OID 75174)
-- Name: IDX_payment_payment_collection_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_payment_payment_collection_id" ON public.payment USING btree (payment_collection_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4426 (class 1259 OID 75226)
-- Name: IDX_payment_payment_session_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_payment_payment_session_id" ON public.payment USING btree (payment_session_id);


--
-- TOC entry 4427 (class 1259 OID 75247)
-- Name: IDX_payment_payment_session_id_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_payment_payment_session_id_unique" ON public.payment USING btree (payment_session_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4415 (class 1259 OID 75233)
-- Name: IDX_payment_provider_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_payment_provider_deleted_at" ON public.payment_provider USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4428 (class 1259 OID 75176)
-- Name: IDX_payment_provider_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_payment_provider_id" ON public.payment USING btree (provider_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4743 (class 1259 OID 76321)
-- Name: IDX_payment_provider_id_1c934dab0; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_payment_provider_id_1c934dab0" ON public.region_payment_provider USING btree (payment_provider_id);


--
-- TOC entry 4420 (class 1259 OID 75225)
-- Name: IDX_payment_session_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_payment_session_deleted_at" ON public.payment_session USING btree (deleted_at);


--
-- TOC entry 4421 (class 1259 OID 75183)
-- Name: IDX_payment_session_payment_collection_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_payment_session_payment_collection_id" ON public.payment_session USING btree (payment_collection_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4225 (class 1259 OID 74369)
-- Name: IDX_price_currency_code; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_price_currency_code" ON public.price USING btree (currency_code) WHERE (deleted_at IS NULL);


--
-- TOC entry 4226 (class 1259 OID 74329)
-- Name: IDX_price_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_price_deleted_at" ON public.price USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4240 (class 1259 OID 74324)
-- Name: IDX_price_list_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_price_list_deleted_at" ON public.price_list USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4243 (class 1259 OID 74419)
-- Name: IDX_price_list_rule_attribute; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_price_list_rule_attribute" ON public.price_list_rule USING btree (attribute) WHERE (deleted_at IS NULL);


--
-- TOC entry 4244 (class 1259 OID 74341)
-- Name: IDX_price_list_rule_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_price_list_rule_deleted_at" ON public.price_list_rule USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4245 (class 1259 OID 74340)
-- Name: IDX_price_list_rule_price_list_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_price_list_rule_price_list_id" ON public.price_list_rule USING btree (price_list_id) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4248 (class 1259 OID 74391)
-- Name: IDX_price_preference_attribute_value; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_price_preference_attribute_value" ON public.price_preference USING btree (attribute, value) WHERE (deleted_at IS NULL);


--
-- TOC entry 4249 (class 1259 OID 74390)
-- Name: IDX_price_preference_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_price_preference_deleted_at" ON public.price_preference USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4227 (class 1259 OID 74328)
-- Name: IDX_price_price_list_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_price_price_list_id" ON public.price USING btree (price_list_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4228 (class 1259 OID 74326)
-- Name: IDX_price_price_set_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_price_price_set_id" ON public.price USING btree (price_set_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4231 (class 1259 OID 74422)
-- Name: IDX_price_rule_attribute; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_price_rule_attribute" ON public.price_rule USING btree (attribute) WHERE (deleted_at IS NULL);


--
-- TOC entry 4232 (class 1259 OID 74420)
-- Name: IDX_price_rule_attribute_value; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_price_rule_attribute_value" ON public.price_rule USING btree (attribute, value) WHERE (deleted_at IS NULL);


--
-- TOC entry 4233 (class 1259 OID 74338)
-- Name: IDX_price_rule_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_price_rule_deleted_at" ON public.price_rule USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4234 (class 1259 OID 74394)
-- Name: IDX_price_rule_operator; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_price_rule_operator" ON public.price_rule USING btree (operator);


--
-- TOC entry 4235 (class 1259 OID 74421)
-- Name: IDX_price_rule_operator_value; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_price_rule_operator_value" ON public.price_rule USING btree (operator, value) WHERE (deleted_at IS NULL);


--
-- TOC entry 4236 (class 1259 OID 74418)
-- Name: IDX_price_rule_price_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_price_rule_price_id" ON public.price_rule USING btree (price_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4237 (class 1259 OID 74395)
-- Name: IDX_price_rule_price_id_attribute_operator_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_price_rule_price_id_attribute_operator_unique" ON public.price_rule USING btree (price_id, attribute, operator) WHERE (deleted_at IS NULL);


--
-- TOC entry 4222 (class 1259 OID 74325)
-- Name: IDX_price_set_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_price_set_deleted_at" ON public.price_set USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4725 (class 1259 OID 76316)
-- Name: IDX_price_set_id_52b23597; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_price_set_id_52b23597" ON public.product_variant_price_set USING btree (price_set_id);


--
-- TOC entry 4749 (class 1259 OID 76317)
-- Name: IDX_price_set_id_ba32fa9c; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_price_set_id_ba32fa9c" ON public.shipping_option_price_set USING btree (price_set_id);


--
-- TOC entry 4207 (class 1259 OID 73992)
-- Name: IDX_product_category_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_product_category_deleted_at" ON public.product_collection USING btree (deleted_at);


--
-- TOC entry 4212 (class 1259 OID 74119)
-- Name: IDX_product_category_parent_category_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_product_category_parent_category_id" ON public.product_category USING btree (parent_category_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4213 (class 1259 OID 73991)
-- Name: IDX_product_category_path; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_product_category_path" ON public.product_category USING btree (mpath) WHERE (deleted_at IS NULL);


--
-- TOC entry 4208 (class 1259 OID 73976)
-- Name: IDX_product_collection_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_product_collection_deleted_at" ON public.product_collection USING btree (deleted_at);


--
-- TOC entry 4168 (class 1259 OID 73891)
-- Name: IDX_product_collection_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_product_collection_id" ON public.product USING btree (collection_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4169 (class 1259 OID 73892)
-- Name: IDX_product_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_product_deleted_at" ON public.product USING btree (deleted_at);


--
-- TOC entry 4170 (class 1259 OID 73889)
-- Name: IDX_product_handle_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_product_handle_unique" ON public.product USING btree (handle) WHERE (deleted_at IS NULL);


--
-- TOC entry 4755 (class 1259 OID 76338)
-- Name: IDX_product_id_17a262437; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_product_id_17a262437" ON public.product_shipping_profile USING btree (product_id);


--
-- TOC entry 4713 (class 1259 OID 76228)
-- Name: IDX_product_id_20b454295; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_product_id_20b454295" ON public.product_sales_channel USING btree (product_id);


--
-- TOC entry 4195 (class 1259 OID 73942)
-- Name: IDX_product_image_url; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_product_image_url" ON public.image USING btree (url) WHERE (deleted_at IS NULL);


--
-- TOC entry 4184 (class 1259 OID 73921)
-- Name: IDX_product_option_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_product_option_deleted_at" ON public.product_option USING btree (deleted_at);


--
-- TOC entry 4185 (class 1259 OID 74129)
-- Name: IDX_product_option_product_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_product_option_product_id" ON public.product_option USING btree (product_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4189 (class 1259 OID 73932)
-- Name: IDX_product_option_value_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_product_option_value_deleted_at" ON public.product_option_value USING btree (deleted_at);


--
-- TOC entry 4190 (class 1259 OID 74130)
-- Name: IDX_product_option_value_option_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_product_option_value_option_id" ON public.product_option_value USING btree (option_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4198 (class 1259 OID 73954)
-- Name: IDX_product_tag_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_product_tag_deleted_at" ON public.product_tag USING btree (deleted_at);


--
-- TOC entry 4202 (class 1259 OID 73965)
-- Name: IDX_product_type_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_product_type_deleted_at" ON public.product_type USING btree (deleted_at);


--
-- TOC entry 4171 (class 1259 OID 73890)
-- Name: IDX_product_type_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_product_type_id" ON public.product USING btree (type_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4174 (class 1259 OID 73908)
-- Name: IDX_product_variant_barcode_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_product_variant_barcode_unique" ON public.product_variant USING btree (barcode) WHERE (deleted_at IS NULL);


--
-- TOC entry 4175 (class 1259 OID 73910)
-- Name: IDX_product_variant_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_product_variant_deleted_at" ON public.product_variant USING btree (deleted_at);


--
-- TOC entry 4176 (class 1259 OID 73905)
-- Name: IDX_product_variant_ean_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_product_variant_ean_unique" ON public.product_variant USING btree (ean) WHERE (deleted_at IS NULL);


--
-- TOC entry 4177 (class 1259 OID 74180)
-- Name: IDX_product_variant_id_product_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_product_variant_id_product_id" ON public.product_variant USING btree (id, product_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4178 (class 1259 OID 73909)
-- Name: IDX_product_variant_product_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_product_variant_product_id" ON public.product_variant USING btree (product_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4179 (class 1259 OID 73907)
-- Name: IDX_product_variant_sku_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_product_variant_sku_unique" ON public.product_variant USING btree (sku) WHERE (deleted_at IS NULL);


--
-- TOC entry 4180 (class 1259 OID 73906)
-- Name: IDX_product_variant_upc_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_product_variant_upc_unique" ON public.product_variant USING btree (upc) WHERE (deleted_at IS NULL);


--
-- TOC entry 4271 (class 1259 OID 74579)
-- Name: IDX_promotion_application_method_currency_code; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_promotion_application_method_currency_code" ON public.promotion_application_method USING btree (currency_code) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4272 (class 1259 OID 74625)
-- Name: IDX_promotion_application_method_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_promotion_application_method_deleted_at" ON public.promotion_application_method USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4273 (class 1259 OID 74633)
-- Name: IDX_promotion_application_method_promotion_id_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_promotion_application_method_promotion_id_unique" ON public.promotion_application_method USING btree (promotion_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4257 (class 1259 OID 74632)
-- Name: IDX_promotion_campaign_budget_campaign_id_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_promotion_campaign_budget_campaign_id_unique" ON public.promotion_campaign_budget USING btree (campaign_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4258 (class 1259 OID 74588)
-- Name: IDX_promotion_campaign_budget_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_promotion_campaign_budget_deleted_at" ON public.promotion_campaign_budget USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4252 (class 1259 OID 74580)
-- Name: IDX_promotion_campaign_campaign_identifier_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_promotion_campaign_campaign_identifier_unique" ON public.promotion_campaign USING btree (campaign_identifier) WHERE (deleted_at IS NULL);


--
-- TOC entry 4253 (class 1259 OID 74581)
-- Name: IDX_promotion_campaign_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_promotion_campaign_deleted_at" ON public.promotion_campaign USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4261 (class 1259 OID 74589)
-- Name: IDX_promotion_campaign_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_promotion_campaign_id" ON public.promotion USING btree (campaign_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4262 (class 1259 OID 74590)
-- Name: IDX_promotion_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_promotion_deleted_at" ON public.promotion USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4702 (class 1259 OID 76206)
-- Name: IDX_promotion_id_-71518339; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_promotion_id_-71518339" ON public.order_promotion USING btree (promotion_id);


--
-- TOC entry 4666 (class 1259 OID 76204)
-- Name: IDX_promotion_id_-a9d4a70b; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_promotion_id_-a9d4a70b" ON public.cart_promotion USING btree (promotion_id);


--
-- TOC entry 4276 (class 1259 OID 74490)
-- Name: IDX_promotion_rule_attribute; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_promotion_rule_attribute" ON public.promotion_rule USING btree (attribute);


--
-- TOC entry 4277 (class 1259 OID 74626)
-- Name: IDX_promotion_rule_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_promotion_rule_deleted_at" ON public.promotion_rule USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4278 (class 1259 OID 74491)
-- Name: IDX_promotion_rule_operator; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_promotion_rule_operator" ON public.promotion_rule USING btree (operator);


--
-- TOC entry 4287 (class 1259 OID 74628)
-- Name: IDX_promotion_rule_value_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_promotion_rule_value_deleted_at" ON public.promotion_rule_value USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4288 (class 1259 OID 74627)
-- Name: IDX_promotion_rule_value_promotion_rule_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_promotion_rule_value_promotion_rule_id" ON public.promotion_rule_value USING btree (promotion_rule_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4263 (class 1259 OID 74631)
-- Name: IDX_promotion_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_promotion_status" ON public.promotion USING btree (status) WHERE (deleted_at IS NULL);


--
-- TOC entry 4264 (class 1259 OID 74460)
-- Name: IDX_promotion_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_promotion_type" ON public.promotion USING btree (type);


--
-- TOC entry 4574 (class 1259 OID 75770)
-- Name: IDX_provider_identity_auth_identity_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_provider_identity_auth_identity_id" ON public.provider_identity USING btree (auth_identity_id);


--
-- TOC entry 4575 (class 1259 OID 75780)
-- Name: IDX_provider_identity_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_provider_identity_deleted_at" ON public.provider_identity USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4576 (class 1259 OID 75771)
-- Name: IDX_provider_identity_provider_entity_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_provider_identity_provider_entity_id" ON public.provider_identity USING btree (entity_id, provider);


--
-- TOC entry 4731 (class 1259 OID 76339)
-- Name: IDX_publishable_key_id_-1d67bae40; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_publishable_key_id_-1d67bae40" ON public.publishable_api_key_sales_channel USING btree (publishable_key_id);


--
-- TOC entry 4431 (class 1259 OID 75230)
-- Name: IDX_refund_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_refund_deleted_at" ON public.refund USING btree (deleted_at);


--
-- TOC entry 4432 (class 1259 OID 75179)
-- Name: IDX_refund_payment_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_refund_payment_id" ON public.refund USING btree (payment_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4440 (class 1259 OID 75245)
-- Name: IDX_refund_reason_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_refund_reason_deleted_at" ON public.refund_reason USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4433 (class 1259 OID 75246)
-- Name: IDX_refund_refund_reason_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_refund_refund_reason_id" ON public.refund USING btree (refund_reason_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4372 (class 1259 OID 74970)
-- Name: IDX_region_country_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_region_country_deleted_at" ON public.region_country USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4373 (class 1259 OID 74969)
-- Name: IDX_region_country_region_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_region_country_region_id" ON public.region_country USING btree (region_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4374 (class 1259 OID 74961)
-- Name: IDX_region_country_region_id_iso_2_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_region_country_region_id_iso_2_unique" ON public.region_country USING btree (region_id, iso_2);


--
-- TOC entry 4369 (class 1259 OID 74953)
-- Name: IDX_region_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_region_deleted_at" ON public.region USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4744 (class 1259 OID 76335)
-- Name: IDX_region_id_1c934dab0; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_region_id_1c934dab0" ON public.region_payment_provider USING btree (region_id);


--
-- TOC entry 4162 (class 1259 OID 73811)
-- Name: IDX_reservation_item_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_reservation_item_deleted_at" ON public.reservation_item USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4163 (class 1259 OID 73870)
-- Name: IDX_reservation_item_inventory_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_reservation_item_inventory_item_id" ON public.reservation_item USING btree (inventory_item_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4164 (class 1259 OID 73868)
-- Name: IDX_reservation_item_line_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_reservation_item_line_item_id" ON public.reservation_item USING btree (line_item_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4165 (class 1259 OID 73869)
-- Name: IDX_reservation_item_location_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_reservation_item_location_id" ON public.reservation_item USING btree (location_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4529 (class 1259 OID 75584)
-- Name: IDX_return_claim_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_return_claim_id" ON public.return USING btree (claim_id) WHERE ((claim_id IS NOT NULL) AND (deleted_at IS NULL));


--
-- TOC entry 4530 (class 1259 OID 75586)
-- Name: IDX_return_display_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_return_display_id" ON public.return USING btree (display_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4531 (class 1259 OID 75585)
-- Name: IDX_return_exchange_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_return_exchange_id" ON public.return USING btree (exchange_id) WHERE ((exchange_id IS NOT NULL) AND (deleted_at IS NULL));


--
-- TOC entry 4720 (class 1259 OID 76227)
-- Name: IDX_return_id_-31ea43a; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_return_id_-31ea43a" ON public.return_fulfillment USING btree (return_id);


--
-- TOC entry 4535 (class 1259 OID 75597)
-- Name: IDX_return_item_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_return_item_deleted_at" ON public.return_item USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4536 (class 1259 OID 75599)
-- Name: IDX_return_item_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_return_item_item_id" ON public.return_item USING btree (item_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4537 (class 1259 OID 75600)
-- Name: IDX_return_item_reason_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_return_item_reason_id" ON public.return_item USING btree (reason_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4538 (class 1259 OID 75598)
-- Name: IDX_return_item_return_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_return_item_return_id" ON public.return_item USING btree (return_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4532 (class 1259 OID 75583)
-- Name: IDX_return_order_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_return_order_id" ON public.return USING btree (order_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4526 (class 1259 OID 75480)
-- Name: IDX_return_reason_value; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_return_reason_value" ON public.return_reason USING btree (value) WHERE (deleted_at IS NULL);


--
-- TOC entry 4311 (class 1259 OID 74723)
-- Name: IDX_sales_channel_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_sales_channel_deleted_at" ON public.sales_channel USING btree (deleted_at);


--
-- TOC entry 4732 (class 1259 OID 76323)
-- Name: IDX_sales_channel_id_-1d67bae40; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_sales_channel_id_-1d67bae40" ON public.publishable_api_key_sales_channel USING btree (sales_channel_id);


--
-- TOC entry 4714 (class 1259 OID 76210)
-- Name: IDX_sales_channel_id_20b454295; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_sales_channel_id_20b454295" ON public.product_sales_channel USING btree (sales_channel_id);


--
-- TOC entry 4737 (class 1259 OID 76336)
-- Name: IDX_sales_channel_id_26d06f470; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_sales_channel_id_26d06f470" ON public.sales_channel_stock_location USING btree (sales_channel_id);


--
-- TOC entry 4598 (class 1259 OID 75850)
-- Name: IDX_service_zone_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_service_zone_deleted_at" ON public.service_zone USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4599 (class 1259 OID 75849)
-- Name: IDX_service_zone_fulfillment_set_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_service_zone_fulfillment_set_id" ON public.service_zone USING btree (fulfillment_set_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4600 (class 1259 OID 75848)
-- Name: IDX_service_zone_name_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_service_zone_name_unique" ON public.service_zone USING btree (name) WHERE (deleted_at IS NULL);


--
-- TOC entry 4355 (class 1259 OID 74820)
-- Name: IDX_shipping_method_adjustment_promotion_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_shipping_method_adjustment_promotion_id" ON public.cart_shipping_method_adjustment USING btree (promotion_id) WHERE ((deleted_at IS NULL) AND (promotion_id IS NOT NULL));


--
-- TOC entry 4348 (class 1259 OID 74808)
-- Name: IDX_shipping_method_cart_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_shipping_method_cart_id" ON public.cart_shipping_method USING btree (cart_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4349 (class 1259 OID 74809)
-- Name: IDX_shipping_method_option_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_shipping_method_option_id" ON public.cart_shipping_method USING btree (shipping_option_id) WHERE ((deleted_at IS NULL) AND (shipping_option_id IS NOT NULL));


--
-- TOC entry 4360 (class 1259 OID 74831)
-- Name: IDX_shipping_method_tax_line_tax_rate_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_shipping_method_tax_line_tax_rate_id" ON public.cart_shipping_method_tax_line USING btree (tax_rate_id) WHERE ((deleted_at IS NULL) AND (tax_rate_id IS NOT NULL));


--
-- TOC entry 4617 (class 1259 OID 75905)
-- Name: IDX_shipping_option_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_shipping_option_deleted_at" ON public.shipping_option USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4750 (class 1259 OID 76337)
-- Name: IDX_shipping_option_id_ba32fa9c; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_shipping_option_id_ba32fa9c" ON public.shipping_option_price_set USING btree (shipping_option_id);


--
-- TOC entry 4618 (class 1259 OID 76034)
-- Name: IDX_shipping_option_provider_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_shipping_option_provider_id" ON public.shipping_option USING btree (provider_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4623 (class 1259 OID 75917)
-- Name: IDX_shipping_option_rule_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_shipping_option_rule_deleted_at" ON public.shipping_option_rule USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4624 (class 1259 OID 75916)
-- Name: IDX_shipping_option_rule_shipping_option_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_shipping_option_rule_shipping_option_id" ON public.shipping_option_rule USING btree (shipping_option_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4619 (class 1259 OID 75901)
-- Name: IDX_shipping_option_service_zone_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_shipping_option_service_zone_id" ON public.shipping_option USING btree (service_zone_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4620 (class 1259 OID 75902)
-- Name: IDX_shipping_option_shipping_profile_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_shipping_option_shipping_profile_id" ON public.shipping_option USING btree (shipping_profile_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4610 (class 1259 OID 75876)
-- Name: IDX_shipping_option_type_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_shipping_option_type_deleted_at" ON public.shipping_option_type USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4613 (class 1259 OID 75887)
-- Name: IDX_shipping_profile_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_shipping_profile_deleted_at" ON public.shipping_profile USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4756 (class 1259 OID 76320)
-- Name: IDX_shipping_profile_id_17a262437; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_shipping_profile_id_17a262437" ON public.product_shipping_profile USING btree (shipping_profile_id);


--
-- TOC entry 4614 (class 1259 OID 75886)
-- Name: IDX_shipping_profile_name_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_shipping_profile_name_unique" ON public.shipping_profile USING btree (name) WHERE (deleted_at IS NULL);


--
-- TOC entry 4399 (class 1259 OID 75049)
-- Name: IDX_single_default_region; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_single_default_region" ON public.tax_rate USING btree (tax_region_id) WHERE ((is_default = true) AND (deleted_at IS NULL));


--
-- TOC entry 4144 (class 1259 OID 73749)
-- Name: IDX_stock_location_address_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_stock_location_address_deleted_at" ON public.stock_location_address USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4147 (class 1259 OID 73773)
-- Name: IDX_stock_location_address_id_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_stock_location_address_id_unique" ON public.stock_location USING btree (address_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4148 (class 1259 OID 73759)
-- Name: IDX_stock_location_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_stock_location_deleted_at" ON public.stock_location USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4690 (class 1259 OID 76230)
-- Name: IDX_stock_location_id_-1e5992737; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_stock_location_id_-1e5992737" ON public.location_fulfillment_provider USING btree (stock_location_id);


--
-- TOC entry 4672 (class 1259 OID 76231)
-- Name: IDX_stock_location_id_-e88adb96; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_stock_location_id_-e88adb96" ON public.location_fulfillment_set USING btree (stock_location_id);


--
-- TOC entry 4738 (class 1259 OID 76318)
-- Name: IDX_stock_location_id_26d06f470; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_stock_location_id_26d06f470" ON public.sales_channel_stock_location USING btree (stock_location_id);


--
-- TOC entry 4385 (class 1259 OID 75007)
-- Name: IDX_store_currency_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_store_currency_deleted_at" ON public.store_currency USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4386 (class 1259 OID 75013)
-- Name: IDX_store_currency_store_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_store_currency_store_id" ON public.store_currency USING btree (store_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4382 (class 1259 OID 74996)
-- Name: IDX_store_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_store_deleted_at" ON public.store USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4199 (class 1259 OID 73953)
-- Name: IDX_tag_value_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_tag_value_unique" ON public.product_tag USING btree (value) WHERE (deleted_at IS NULL);


--
-- TOC entry 4343 (class 1259 OID 74795)
-- Name: IDX_tax_line_item_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_tax_line_item_id" ON public.cart_line_item_tax_line USING btree (item_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4361 (class 1259 OID 74830)
-- Name: IDX_tax_line_shipping_method_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_tax_line_shipping_method_id" ON public.cart_shipping_method_tax_line USING btree (shipping_method_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4389 (class 1259 OID 75087)
-- Name: IDX_tax_provider_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_tax_provider_deleted_at" ON public.tax_provider USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4400 (class 1259 OID 75048)
-- Name: IDX_tax_rate_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_tax_rate_deleted_at" ON public.tax_rate USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4404 (class 1259 OID 75061)
-- Name: IDX_tax_rate_rule_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_tax_rate_rule_deleted_at" ON public.tax_rate_rule USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4405 (class 1259 OID 75060)
-- Name: IDX_tax_rate_rule_reference_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_tax_rate_rule_reference_id" ON public.tax_rate_rule USING btree (reference_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4406 (class 1259 OID 75059)
-- Name: IDX_tax_rate_rule_tax_rate_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_tax_rate_rule_tax_rate_id" ON public.tax_rate_rule USING btree (tax_rate_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4407 (class 1259 OID 75062)
-- Name: IDX_tax_rate_rule_unique_rate_reference; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_tax_rate_rule_unique_rate_reference" ON public.tax_rate_rule USING btree (tax_rate_id, reference_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4401 (class 1259 OID 75047)
-- Name: IDX_tax_rate_tax_region_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_tax_rate_tax_region_id" ON public.tax_rate USING btree (tax_region_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4392 (class 1259 OID 75034)
-- Name: IDX_tax_region_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_tax_region_deleted_at" ON public.tax_region USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4393 (class 1259 OID 75033)
-- Name: IDX_tax_region_parent_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_tax_region_parent_id" ON public.tax_region USING btree (parent_id);


--
-- TOC entry 4394 (class 1259 OID 75088)
-- Name: IDX_tax_region_provider_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_tax_region_provider_id" ON public.tax_region USING btree (provider_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4395 (class 1259 OID 75084)
-- Name: IDX_tax_region_unique_country_nullable_province; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_tax_region_unique_country_nullable_province" ON public.tax_region USING btree (country_code) WHERE ((province_code IS NULL) AND (deleted_at IS NULL));


--
-- TOC entry 4396 (class 1259 OID 75083)
-- Name: IDX_tax_region_unique_country_province; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_tax_region_unique_country_province" ON public.tax_region USING btree (country_code, province_code) WHERE (deleted_at IS NULL);


--
-- TOC entry 4203 (class 1259 OID 73964)
-- Name: IDX_type_value_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_type_value_unique" ON public.product_type USING btree (value) WHERE (deleted_at IS NULL);


--
-- TOC entry 4265 (class 1259 OID 74634)
-- Name: IDX_unique_promotion_code; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_unique_promotion_code" ON public.promotion USING btree (code) WHERE (deleted_at IS NULL);


--
-- TOC entry 4584 (class 1259 OID 75804)
-- Name: IDX_user_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_user_deleted_at" ON public."user" USING btree (deleted_at) WHERE (deleted_at IS NOT NULL);


--
-- TOC entry 4585 (class 1259 OID 75807)
-- Name: IDX_user_email_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_user_email_unique" ON public."user" USING btree (email) WHERE (deleted_at IS NULL);


--
-- TOC entry 4708 (class 1259 OID 76229)
-- Name: IDX_variant_id_17b4c4e35; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_variant_id_17b4c4e35" ON public.product_variant_inventory_item USING btree (variant_id);


--
-- TOC entry 4726 (class 1259 OID 76332)
-- Name: IDX_variant_id_52b23597; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_variant_id_52b23597" ON public.product_variant_price_set USING btree (variant_id);


--
-- TOC entry 4651 (class 1259 OID 76091)
-- Name: IDX_workflow_execution_deleted_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_workflow_execution_deleted_at" ON public.workflow_execution USING btree (deleted_at) WHERE (deleted_at IS NULL);


--
-- TOC entry 4652 (class 1259 OID 76092)
-- Name: IDX_workflow_execution_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_workflow_execution_id" ON public.workflow_execution USING btree (id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4653 (class 1259 OID 76095)
-- Name: IDX_workflow_execution_state; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_workflow_execution_state" ON public.workflow_execution USING btree (state) WHERE (deleted_at IS NULL);


--
-- TOC entry 4654 (class 1259 OID 76094)
-- Name: IDX_workflow_execution_transaction_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_workflow_execution_transaction_id" ON public.workflow_execution USING btree (transaction_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4655 (class 1259 OID 76093)
-- Name: IDX_workflow_execution_workflow_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_workflow_execution_workflow_id" ON public.workflow_execution USING btree (workflow_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4656 (class 1259 OID 76097)
-- Name: IDX_workflow_execution_workflow_id_transaction_id_run_id_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX "IDX_workflow_execution_workflow_id_transaction_id_run_id_unique" ON public.workflow_execution USING btree (workflow_id, transaction_id, run_id) WHERE (deleted_at IS NULL);


--
-- TOC entry 4771 (class 1259 OID 76356)
-- Name: idx_script_name_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_script_name_unique ON public.script_migrations USING btree (script_name);


--
-- TOC entry 4821 (class 2606 OID 75078)
-- Name: tax_rate_rule FK_tax_rate_rule_tax_rate_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tax_rate_rule
    ADD CONSTRAINT "FK_tax_rate_rule_tax_rate_id" FOREIGN KEY (tax_rate_id) REFERENCES public.tax_rate(id) ON DELETE CASCADE;


--
-- TOC entry 4820 (class 2606 OID 75073)
-- Name: tax_rate FK_tax_rate_tax_region_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tax_rate
    ADD CONSTRAINT "FK_tax_rate_tax_region_id" FOREIGN KEY (tax_region_id) REFERENCES public.tax_region(id) ON DELETE CASCADE;


--
-- TOC entry 4818 (class 2606 OID 75068)
-- Name: tax_region FK_tax_region_parent_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tax_region
    ADD CONSTRAINT "FK_tax_region_parent_id" FOREIGN KEY (parent_id) REFERENCES public.tax_region(id) ON DELETE CASCADE;


--
-- TOC entry 4819 (class 2606 OID 75063)
-- Name: tax_region FK_tax_region_provider_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tax_region
    ADD CONSTRAINT "FK_tax_region_provider_id" FOREIGN KEY (provider_id) REFERENCES public.tax_provider(id) ON DELETE SET NULL;


--
-- TOC entry 4801 (class 2606 OID 74558)
-- Name: application_method_buy_rules application_method_buy_rules_application_method_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.application_method_buy_rules
    ADD CONSTRAINT application_method_buy_rules_application_method_id_foreign FOREIGN KEY (application_method_id) REFERENCES public.promotion_application_method(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4802 (class 2606 OID 74563)
-- Name: application_method_buy_rules application_method_buy_rules_promotion_rule_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.application_method_buy_rules
    ADD CONSTRAINT application_method_buy_rules_promotion_rule_id_foreign FOREIGN KEY (promotion_rule_id) REFERENCES public.promotion_rule(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4799 (class 2606 OID 74548)
-- Name: application_method_target_rules application_method_target_rules_application_method_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.application_method_target_rules
    ADD CONSTRAINT application_method_target_rules_application_method_id_foreign FOREIGN KEY (application_method_id) REFERENCES public.promotion_application_method(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4800 (class 2606 OID 74553)
-- Name: application_method_target_rules application_method_target_rules_promotion_rule_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.application_method_target_rules
    ADD CONSTRAINT application_method_target_rules_promotion_rule_id_foreign FOREIGN KEY (promotion_rule_id) REFERENCES public.promotion_rule(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4827 (class 2606 OID 75204)
-- Name: capture capture_payment_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.capture
    ADD CONSTRAINT capture_payment_id_foreign FOREIGN KEY (payment_id) REFERENCES public.payment(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4807 (class 2606 OID 74766)
-- Name: cart cart_billing_address_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart
    ADD CONSTRAINT cart_billing_address_id_foreign FOREIGN KEY (billing_address_id) REFERENCES public.cart_address(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 4810 (class 2606 OID 74883)
-- Name: cart_line_item_adjustment cart_line_item_adjustment_item_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_line_item_adjustment
    ADD CONSTRAINT cart_line_item_adjustment_item_id_foreign FOREIGN KEY (item_id) REFERENCES public.cart_line_item(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4809 (class 2606 OID 74877)
-- Name: cart_line_item cart_line_item_cart_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_line_item
    ADD CONSTRAINT cart_line_item_cart_id_foreign FOREIGN KEY (cart_id) REFERENCES public.cart(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4811 (class 2606 OID 74889)
-- Name: cart_line_item_tax_line cart_line_item_tax_line_item_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_line_item_tax_line
    ADD CONSTRAINT cart_line_item_tax_line_item_id_foreign FOREIGN KEY (item_id) REFERENCES public.cart_line_item(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4808 (class 2606 OID 74761)
-- Name: cart cart_shipping_address_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart
    ADD CONSTRAINT cart_shipping_address_id_foreign FOREIGN KEY (shipping_address_id) REFERENCES public.cart_address(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 4813 (class 2606 OID 74852)
-- Name: cart_shipping_method_adjustment cart_shipping_method_adjustment_shipping_method_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_shipping_method_adjustment
    ADD CONSTRAINT cart_shipping_method_adjustment_shipping_method_id_foreign FOREIGN KEY (shipping_method_id) REFERENCES public.cart_shipping_method(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4812 (class 2606 OID 74895)
-- Name: cart_shipping_method cart_shipping_method_cart_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_shipping_method
    ADD CONSTRAINT cart_shipping_method_cart_id_foreign FOREIGN KEY (cart_id) REFERENCES public.cart(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4814 (class 2606 OID 74857)
-- Name: cart_shipping_method_tax_line cart_shipping_method_tax_line_shipping_method_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_shipping_method_tax_line
    ADD CONSTRAINT cart_shipping_method_tax_line_shipping_method_id_foreign FOREIGN KEY (shipping_method_id) REFERENCES public.cart_shipping_method(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4815 (class 2606 OID 74936)
-- Name: credit_line credit_line_cart_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.credit_line
    ADD CONSTRAINT credit_line_cart_id_foreign FOREIGN KEY (cart_id) REFERENCES public.cart(id) ON UPDATE CASCADE;


--
-- TOC entry 4804 (class 2606 OID 74681)
-- Name: customer_address customer_address_customer_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_address
    ADD CONSTRAINT customer_address_customer_id_foreign FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4805 (class 2606 OID 74701)
-- Name: customer_group_customer customer_group_customer_customer_group_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_group_customer
    ADD CONSTRAINT customer_group_customer_customer_group_id_foreign FOREIGN KEY (customer_group_id) REFERENCES public.customer_group(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4806 (class 2606 OID 74706)
-- Name: customer_group_customer customer_group_customer_customer_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_group_customer
    ADD CONSTRAINT customer_group_customer_customer_id_foreign FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4852 (class 2606 OID 76040)
-- Name: fulfillment fulfillment_delivery_address_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillment
    ADD CONSTRAINT fulfillment_delivery_address_id_foreign FOREIGN KEY (delivery_address_id) REFERENCES public.fulfillment_address(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 4856 (class 2606 OID 76012)
-- Name: fulfillment_item fulfillment_item_fulfillment_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillment_item
    ADD CONSTRAINT fulfillment_item_fulfillment_id_foreign FOREIGN KEY (fulfillment_id) REFERENCES public.fulfillment(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4855 (class 2606 OID 76007)
-- Name: fulfillment_label fulfillment_label_fulfillment_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillment_label
    ADD CONSTRAINT fulfillment_label_fulfillment_id_foreign FOREIGN KEY (fulfillment_id) REFERENCES public.fulfillment(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4853 (class 2606 OID 76035)
-- Name: fulfillment fulfillment_provider_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillment
    ADD CONSTRAINT fulfillment_provider_id_foreign FOREIGN KEY (provider_id) REFERENCES public.fulfillment_provider(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 4854 (class 2606 OID 75997)
-- Name: fulfillment fulfillment_shipping_option_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillment
    ADD CONSTRAINT fulfillment_shipping_option_id_foreign FOREIGN KEY (shipping_option_id) REFERENCES public.shipping_option(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 4846 (class 2606 OID 75962)
-- Name: geo_zone geo_zone_service_zone_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.geo_zone
    ADD CONSTRAINT geo_zone_service_zone_id_foreign FOREIGN KEY (service_zone_id) REFERENCES public.service_zone(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4782 (class 2606 OID 74114)
-- Name: image image_product_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.image
    ADD CONSTRAINT image_product_id_foreign FOREIGN KEY (product_id) REFERENCES public.product(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4775 (class 2606 OID 73815)
-- Name: inventory_level inventory_level_inventory_item_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inventory_level
    ADD CONSTRAINT inventory_level_inventory_item_id_foreign FOREIGN KEY (inventory_item_id) REFERENCES public.inventory_item(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4857 (class 2606 OID 76064)
-- Name: notification notification_provider_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notification
    ADD CONSTRAINT notification_provider_id_foreign FOREIGN KEY (provider_id) REFERENCES public.notification_provider(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 4828 (class 2606 OID 75486)
-- Name: order order_billing_address_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public."order"
    ADD CONSTRAINT order_billing_address_id_foreign FOREIGN KEY (billing_address_id) REFERENCES public.order_address(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4832 (class 2606 OID 75496)
-- Name: order_change_action order_change_action_order_change_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_change_action
    ADD CONSTRAINT order_change_action_order_change_id_foreign FOREIGN KEY (order_change_id) REFERENCES public.order_change(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4831 (class 2606 OID 75491)
-- Name: order_change order_change_order_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_change
    ADD CONSTRAINT order_change_order_id_foreign FOREIGN KEY (order_id) REFERENCES public."order"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4843 (class 2606 OID 75741)
-- Name: order_credit_line order_credit_line_order_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_credit_line
    ADD CONSTRAINT order_credit_line_order_id_foreign FOREIGN KEY (order_id) REFERENCES public."order"(id) ON UPDATE CASCADE;


--
-- TOC entry 4833 (class 2606 OID 75506)
-- Name: order_item order_item_item_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_item
    ADD CONSTRAINT order_item_item_id_foreign FOREIGN KEY (item_id) REFERENCES public.order_line_item(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4834 (class 2606 OID 75501)
-- Name: order_item order_item_order_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_item
    ADD CONSTRAINT order_item_order_id_foreign FOREIGN KEY (order_id) REFERENCES public."order"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4838 (class 2606 OID 75521)
-- Name: order_line_item_adjustment order_line_item_adjustment_item_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_line_item_adjustment
    ADD CONSTRAINT order_line_item_adjustment_item_id_foreign FOREIGN KEY (item_id) REFERENCES public.order_line_item(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4837 (class 2606 OID 75516)
-- Name: order_line_item_tax_line order_line_item_tax_line_item_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_line_item_tax_line
    ADD CONSTRAINT order_line_item_tax_line_item_id_foreign FOREIGN KEY (item_id) REFERENCES public.order_line_item(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4836 (class 2606 OID 75511)
-- Name: order_line_item order_line_item_totals_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_line_item
    ADD CONSTRAINT order_line_item_totals_id_foreign FOREIGN KEY (totals_id) REFERENCES public.order_item(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4829 (class 2606 OID 75481)
-- Name: order order_shipping_address_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public."order"
    ADD CONSTRAINT order_shipping_address_id_foreign FOREIGN KEY (shipping_address_id) REFERENCES public.order_address(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4839 (class 2606 OID 75531)
-- Name: order_shipping_method_adjustment order_shipping_method_adjustment_shipping_method_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_shipping_method_adjustment
    ADD CONSTRAINT order_shipping_method_adjustment_shipping_method_id_foreign FOREIGN KEY (shipping_method_id) REFERENCES public.order_shipping_method(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4840 (class 2606 OID 75536)
-- Name: order_shipping_method_tax_line order_shipping_method_tax_line_shipping_method_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_shipping_method_tax_line
    ADD CONSTRAINT order_shipping_method_tax_line_shipping_method_id_foreign FOREIGN KEY (shipping_method_id) REFERENCES public.order_shipping_method(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4835 (class 2606 OID 75526)
-- Name: order_shipping order_shipping_order_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_shipping
    ADD CONSTRAINT order_shipping_order_id_foreign FOREIGN KEY (order_id) REFERENCES public."order"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4830 (class 2606 OID 75747)
-- Name: order_summary order_summary_order_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_summary
    ADD CONSTRAINT order_summary_order_id_foreign FOREIGN KEY (order_id) REFERENCES public."order"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4841 (class 2606 OID 75541)
-- Name: order_transaction order_transaction_order_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_transaction
    ADD CONSTRAINT order_transaction_order_id_foreign FOREIGN KEY (order_id) REFERENCES public."order"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4822 (class 2606 OID 75260)
-- Name: payment_collection_payment_providers payment_collection_payment_providers_payment_col_aa276_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payment_collection_payment_providers
    ADD CONSTRAINT payment_collection_payment_providers_payment_col_aa276_foreign FOREIGN KEY (payment_collection_id) REFERENCES public.payment_collection(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4823 (class 2606 OID 75265)
-- Name: payment_collection_payment_providers payment_collection_payment_providers_payment_pro_2d555_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payment_collection_payment_providers
    ADD CONSTRAINT payment_collection_payment_providers_payment_pro_2d555_foreign FOREIGN KEY (payment_provider_id) REFERENCES public.payment_provider(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4825 (class 2606 OID 75240)
-- Name: payment payment_payment_collection_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payment
    ADD CONSTRAINT payment_payment_collection_id_foreign FOREIGN KEY (payment_collection_id) REFERENCES public.payment_collection(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4824 (class 2606 OID 75235)
-- Name: payment_session payment_session_payment_collection_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payment_session
    ADD CONSTRAINT payment_session_payment_collection_id_foreign FOREIGN KEY (payment_collection_id) REFERENCES public.payment_collection(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4793 (class 2606 OID 74359)
-- Name: price_list_rule price_list_rule_price_list_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.price_list_rule
    ADD CONSTRAINT price_list_rule_price_list_id_foreign FOREIGN KEY (price_list_id) REFERENCES public.price_list(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4790 (class 2606 OID 74344)
-- Name: price price_price_list_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.price
    ADD CONSTRAINT price_price_list_id_foreign FOREIGN KEY (price_list_id) REFERENCES public.price_list(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4791 (class 2606 OID 74240)
-- Name: price price_price_set_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.price
    ADD CONSTRAINT price_price_set_id_foreign FOREIGN KEY (price_set_id) REFERENCES public.price_set(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4792 (class 2606 OID 74370)
-- Name: price_rule price_rule_price_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.price_rule
    ADD CONSTRAINT price_rule_price_id_foreign FOREIGN KEY (price_id) REFERENCES public.price(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4783 (class 2606 OID 74086)
-- Name: product_category product_category_parent_category_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_category
    ADD CONSTRAINT product_category_parent_category_id_foreign FOREIGN KEY (parent_category_id) REFERENCES public.product_category(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4786 (class 2606 OID 74081)
-- Name: product_category_product product_category_product_product_category_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_category_product
    ADD CONSTRAINT product_category_product_product_category_id_foreign FOREIGN KEY (product_category_id) REFERENCES public.product_category(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4787 (class 2606 OID 74076)
-- Name: product_category_product product_category_product_product_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_category_product
    ADD CONSTRAINT product_category_product_product_id_foreign FOREIGN KEY (product_id) REFERENCES public.product(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4777 (class 2606 OID 74021)
-- Name: product product_collection_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product
    ADD CONSTRAINT product_collection_id_foreign FOREIGN KEY (collection_id) REFERENCES public.product_collection(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 4780 (class 2606 OID 74124)
-- Name: product_option product_option_product_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_option
    ADD CONSTRAINT product_option_product_id_foreign FOREIGN KEY (product_id) REFERENCES public.product(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4781 (class 2606 OID 74041)
-- Name: product_option_value product_option_value_option_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_option_value
    ADD CONSTRAINT product_option_value_option_id_foreign FOREIGN KEY (option_id) REFERENCES public.product_option(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4784 (class 2606 OID 74066)
-- Name: product_tags product_tags_product_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_tags
    ADD CONSTRAINT product_tags_product_id_foreign FOREIGN KEY (product_id) REFERENCES public.product(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4785 (class 2606 OID 74071)
-- Name: product_tags product_tags_product_tag_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_tags
    ADD CONSTRAINT product_tags_product_tag_id_foreign FOREIGN KEY (product_tag_id) REFERENCES public.product_tag(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4778 (class 2606 OID 74026)
-- Name: product product_type_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product
    ADD CONSTRAINT product_type_id_foreign FOREIGN KEY (type_id) REFERENCES public.product_type(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 4788 (class 2606 OID 74051)
-- Name: product_variant_option product_variant_option_option_value_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_variant_option
    ADD CONSTRAINT product_variant_option_option_value_id_foreign FOREIGN KEY (option_value_id) REFERENCES public.product_option_value(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4789 (class 2606 OID 74046)
-- Name: product_variant_option product_variant_option_variant_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_variant_option
    ADD CONSTRAINT product_variant_option_variant_id_foreign FOREIGN KEY (variant_id) REFERENCES public.product_variant(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4779 (class 2606 OID 74031)
-- Name: product_variant product_variant_product_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_variant
    ADD CONSTRAINT product_variant_product_id_foreign FOREIGN KEY (product_id) REFERENCES public.product(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4796 (class 2606 OID 74533)
-- Name: promotion_application_method promotion_application_method_promotion_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.promotion_application_method
    ADD CONSTRAINT promotion_application_method_promotion_id_foreign FOREIGN KEY (promotion_id) REFERENCES public.promotion(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4794 (class 2606 OID 74582)
-- Name: promotion_campaign_budget promotion_campaign_budget_campaign_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.promotion_campaign_budget
    ADD CONSTRAINT promotion_campaign_budget_campaign_id_foreign FOREIGN KEY (campaign_id) REFERENCES public.promotion_campaign(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4795 (class 2606 OID 74573)
-- Name: promotion promotion_campaign_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.promotion
    ADD CONSTRAINT promotion_campaign_id_foreign FOREIGN KEY (campaign_id) REFERENCES public.promotion_campaign(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 4797 (class 2606 OID 74538)
-- Name: promotion_promotion_rule promotion_promotion_rule_promotion_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.promotion_promotion_rule
    ADD CONSTRAINT promotion_promotion_rule_promotion_id_foreign FOREIGN KEY (promotion_id) REFERENCES public.promotion(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4798 (class 2606 OID 74543)
-- Name: promotion_promotion_rule promotion_promotion_rule_promotion_rule_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.promotion_promotion_rule
    ADD CONSTRAINT promotion_promotion_rule_promotion_rule_id_foreign FOREIGN KEY (promotion_rule_id) REFERENCES public.promotion_rule(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4803 (class 2606 OID 74568)
-- Name: promotion_rule_value promotion_rule_value_promotion_rule_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.promotion_rule_value
    ADD CONSTRAINT promotion_rule_value_promotion_rule_id_foreign FOREIGN KEY (promotion_rule_id) REFERENCES public.promotion_rule(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4844 (class 2606 OID 75772)
-- Name: provider_identity provider_identity_auth_identity_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.provider_identity
    ADD CONSTRAINT provider_identity_auth_identity_id_foreign FOREIGN KEY (auth_identity_id) REFERENCES public.auth_identity(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4826 (class 2606 OID 75209)
-- Name: refund refund_payment_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.refund
    ADD CONSTRAINT refund_payment_id_foreign FOREIGN KEY (payment_id) REFERENCES public.payment(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4816 (class 2606 OID 74962)
-- Name: region_country region_country_region_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.region_country
    ADD CONSTRAINT region_country_region_id_foreign FOREIGN KEY (region_id) REFERENCES public.region(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 4776 (class 2606 OID 73820)
-- Name: reservation_item reservation_item_inventory_item_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reservation_item
    ADD CONSTRAINT reservation_item_inventory_item_id_foreign FOREIGN KEY (inventory_item_id) REFERENCES public.inventory_item(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4842 (class 2606 OID 75475)
-- Name: return_reason return_reason_parent_return_reason_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.return_reason
    ADD CONSTRAINT return_reason_parent_return_reason_id_foreign FOREIGN KEY (parent_return_reason_id) REFERENCES public.return_reason(id);


--
-- TOC entry 4845 (class 2606 OID 75957)
-- Name: service_zone service_zone_fulfillment_set_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.service_zone
    ADD CONSTRAINT service_zone_fulfillment_set_id_foreign FOREIGN KEY (fulfillment_set_id) REFERENCES public.fulfillment_set(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4847 (class 2606 OID 76024)
-- Name: shipping_option shipping_option_provider_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shipping_option
    ADD CONSTRAINT shipping_option_provider_id_foreign FOREIGN KEY (provider_id) REFERENCES public.fulfillment_provider(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 4851 (class 2606 OID 75987)
-- Name: shipping_option_rule shipping_option_rule_shipping_option_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shipping_option_rule
    ADD CONSTRAINT shipping_option_rule_shipping_option_id_foreign FOREIGN KEY (shipping_option_id) REFERENCES public.shipping_option(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4848 (class 2606 OID 75967)
-- Name: shipping_option shipping_option_service_zone_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shipping_option
    ADD CONSTRAINT shipping_option_service_zone_id_foreign FOREIGN KEY (service_zone_id) REFERENCES public.service_zone(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4849 (class 2606 OID 76029)
-- Name: shipping_option shipping_option_shipping_option_type_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shipping_option
    ADD CONSTRAINT shipping_option_shipping_option_type_id_foreign FOREIGN KEY (shipping_option_type_id) REFERENCES public.shipping_option_type(id) ON UPDATE CASCADE;


--
-- TOC entry 4850 (class 2606 OID 75972)
-- Name: shipping_option shipping_option_shipping_profile_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shipping_option
    ADD CONSTRAINT shipping_option_shipping_profile_id_foreign FOREIGN KEY (shipping_profile_id) REFERENCES public.shipping_profile(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 4774 (class 2606 OID 73765)
-- Name: stock_location stock_location_address_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.stock_location
    ADD CONSTRAINT stock_location_address_id_foreign FOREIGN KEY (address_id) REFERENCES public.stock_location_address(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4817 (class 2606 OID 75008)
-- Name: store_currency store_currency_store_id_foreign; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.store_currency
    ADD CONSTRAINT store_currency_store_id_foreign FOREIGN KEY (store_id) REFERENCES public.store(id) ON UPDATE CASCADE ON DELETE CASCADE;


-- Completed on 2025-09-03 17:53:57 IST

--
-- PostgreSQL database dump complete
--

