{"mcpServers": {"medusa-mcp-server": {"command": "uvx", "args": ["medusa-mcp-server@latest"], "env": {"API_BASE_URL": "http://localhost:9000", "BEARER_TOKEN_BEARERAUTH": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY3Rvcl9pZCI6InVzZXJfMDFKWjRWVkdFSlg2S1RRM1JaTUI3OFk1TTEiLCJhY3Rvcl90eXBlIjoidXNlciIsImF1dGhfaWRlbnRpdHlfaWQiOiJhdXRoaWRfMDFKWjRWVkdKSlRLVzNSWUdNTUUwRVg0NDMiLCJhcHBfbWV0YWRhdGEiOnsidXNlcl9pZCI6InVzZXJfMDFKWjRWVkdFSlg2S1RRM1JaTUI3OFk1TTEifSwiaWF0IjoxNzUzMDc2NzU4LCJleHAiOjE3NTMxNjMxNTh9.8AjS0eE9ADjOhYkrp-QBYLRGlOf24wvETZR_a6_MRS0", "API_KEY_PUBLISHABLEAPIKEY": "pk_3d67561dece2d466dc798c18c1f80523f84f3b2f01316e0bf915e51f3a59b98b"}, "disabled": false, "autoApprove": []}}}