#!/bin/bash

# Comprehensive Test Script for Enhanced Medusa v2 Store API Query Parameters
# Tests all the new query parameter features across all endpoints

set -e

BACKEND_URL="http://localhost:9000"
TENANT_ID="my-kirana-store"
API_KEY="pk_3d67561dece2d466dc798c18c1f80523f84f3b2f01316e0bf915e51f3a59b98b"

echo "🧪 Testing Enhanced Query Parameters for Medusa v2 Store API"
echo "==========================================================="
echo "Backend URL: $BACKEND_URL"
echo "Tenant ID: $TENANT_ID"
echo ""

# Function to test API endpoint with query parameters
test_endpoint() {
    local endpoint="$1"
    local description="$2"
    local query_params="$3"
    
    echo "🔍 Testing: $description"
    echo "   Endpoint: $endpoint"
    echo "   Params: $query_params"
    
    local url="$BACKEND_URL$endpoint"
    if [ -n "$query_params" ]; then
        url="$url?$query_params"
    fi
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
        --location "$url" \
        --header "Accept: application/json" \
        --header "Content-Type: application/json" \
        --header "x-tenant-id: $TENANT_ID" \
        --header "x-publishable-api-key: $API_KEY")
    
    http_code=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo "$response" | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        echo "   ✅ Status: $http_code (Success)"
        
        if command -v jq >/dev/null 2>&1; then
            count=$(echo "$body" | jq -r '.count // 0' 2>/dev/null || echo "0")
            total=$(echo "$body" | jq -r '.total // 0' 2>/dev/null || echo "0")
            echo "   📊 Results: $count/$total items"
            
            # Show metadata if available
            meta=$(echo "$body" | jq -r '._meta // empty' 2>/dev/null)
            if [ -n "$meta" ] && [ "$meta" != "null" ]; then
                echo "   📋 Enhanced metadata available"
                pagination=$(echo "$body" | jq -r '._meta.pagination.page // empty' 2>/dev/null)
                if [ -n "$pagination" ] && [ "$pagination" != "null" ]; then
                    echo "   📄 Page: $pagination"
                fi
            fi
        fi
    else
        echo "   ❌ Status: $http_code (Error)"
        if command -v jq >/dev/null 2>&1; then
            error_msg=$(echo "$body" | jq -r '.error // .message // "Unknown error"' 2>/dev/null || echo "Parse error")
            echo "   💬 Error: $error_msg"
        fi
    fi
    echo ""
}

echo "🏥 1. Basic Health Check"
echo "------------------------"
test_endpoint "/health" "Backend Health Check" ""

echo "📁 2. Product Categories - Basic & Advanced Query Parameters"
echo "------------------------------------------------------------"

# Basic pagination
test_endpoint "/store/product-categories" "Basic Categories" "limit=5&offset=0"

# Advanced filtering
test_endpoint "/store/product-categories" "Active Categories Only" "is_active=true&limit=10"
test_endpoint "/store/product-categories" "Categories by Name Filter" "name__like=grocery&limit=5"

# Sorting
test_endpoint "/store/product-categories" "Categories Sorted by Name" "sort=name:asc&limit=5"
test_endpoint "/store/product-categories" "Categories Sorted by Created Date" "sort=created_at:desc&limit=5"

# Field selection
test_endpoint "/store/product-categories" "Categories with Selected Fields" "fields=id,name,handle&limit=5"

# Relation expansion
test_endpoint "/store/product-categories" "Categories with Relations" "expand=parent_category,category_children&limit=3"

# Search functionality
test_endpoint "/store/product-categories" "Categories Search" "q=grocery&limit=5"

# Date filtering
test_endpoint "/store/product-categories" "Categories Created After Date" "created_after=2024-01-01&limit=5"

# Combined parameters
test_endpoint "/store/product-categories" "Categories Combined Query" "is_active=true&sort=name:asc&fields=id,name&limit=3"

echo "🛍️  3. Products - Advanced Query Parameters"
echo "--------------------------------------------"

# Basic pagination
test_endpoint "/store/products" "Basic Products" "limit=5&offset=0"

# Status filtering
test_endpoint "/store/products" "Published Products Only" "status=published&limit=5"

# Category filtering
test_endpoint "/store/products" "Products by Category" "category_id=cat_kirana_001&limit=5"

# Sorting
test_endpoint "/store/products" "Products by Title" "sort=title:asc&limit=5"
test_endpoint "/store/products" "Products by Date" "sort=created_at:desc&limit=5"

# Search functionality
test_endpoint "/store/products" "Products Search" "q=rice&limit=5"

# Field selection
test_endpoint "/store/products" "Products with Selected Fields" "fields=id,title,handle&limit=5"

# Relation expansion
test_endpoint "/store/products" "Products with Variants" "expand=variants,images&limit=3"

# Combined parameters
test_endpoint "/store/products" "Products Combined Query" "status=published&sort=title:asc&fields=id,title&limit=3"

echo "📦 4. Collections - Query Parameters"
echo "------------------------------------"

# Basic pagination
test_endpoint "/store/collections" "Basic Collections" "limit=5&offset=0"

# Sorting
test_endpoint "/store/collections" "Collections by Title" "sort=title:asc&limit=5"

# Search functionality
test_endpoint "/store/collections" "Collections Search" "q=daily&limit=5"

# Field selection
test_endpoint "/store/collections" "Collections with Selected Fields" "fields=id,title,handle&limit=5"

echo "🏷️  5. Tags - Query Parameters"
echo "------------------------------"

# Basic pagination
test_endpoint "/store/tags" "Basic Tags" "limit=10&offset=0"

# Sorting
test_endpoint "/store/tags" "Tags by Value" "sort=value:asc&limit=10"

# Search functionality
test_endpoint "/store/tags" "Tags Search" "q=organic&limit=5"

echo "⚠️  6. Error Handling Tests"
echo "---------------------------"

# Invalid sort field
test_endpoint "/store/product-categories" "Invalid Sort Field" "sort=invalid_field:asc"

# Invalid expand relation
test_endpoint "/store/products" "Invalid Expand Relation" "expand=invalid_relation"

# Invalid field selection
test_endpoint "/store/collections" "Invalid Field Selection" "fields=invalid_field"

# Limit too high
test_endpoint "/store/tags" "Limit Too High" "limit=1000"

echo "📊 7. Pagination Tests"
echo "----------------------"

# Page-based pagination
test_endpoint "/store/product-categories" "Page 1" "page=1&limit=3"
test_endpoint "/store/product-categories" "Page 2" "page=2&limit=3"

# Offset-based pagination
test_endpoint "/store/products" "Offset 0" "offset=0&limit=3"
test_endpoint "/store/products" "Offset 3" "offset=3&limit=3"

echo "🔍 8. Advanced Filtering Tests"
echo "------------------------------"

# Comparison operators
test_endpoint "/store/product-categories" "Categories ID Greater Than" "id__gt=cat_kirana_001"

# IN operator
test_endpoint "/store/products" "Products by Multiple IDs" "id__in=prod_1,prod_2,prod_3"

# Date range filtering
test_endpoint "/store/collections" "Collections Date Range" "created_after=2024-01-01&created_before=2024-12-31"

echo "✅ Enhanced Query Parameter Testing Complete!"
echo ""
echo "📋 Summary:"
echo "- Tested pagination (limit/offset and page-based)"
echo "- Tested sorting with multiple fields and directions"
echo "- Tested filtering with operators (gt, lt, in, like, etc.)"
echo "- Tested field selection and relation expansion"
echo "- Tested search functionality"
echo "- Tested date filtering"
echo "- Tested error handling for invalid parameters"
echo "- Tested combined query parameters"
echo ""
echo "🔧 Next Steps:"
echo "- Review any failed tests and fix backend issues"
echo "- Add more sample data if needed for better testing"
echo "- Update frontend to use the new query parameters"
echo "- Monitor backend logs for any performance issues"
